{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1초 미만\",\n    other: \"{{count}}초 미만\",\n  },\n\n  xSeconds: {\n    one: \"1초\",\n    other: \"{{count}}초\",\n  },\n\n  halfAMinute: \"30초\",\n\n  lessThanXMinutes: {\n    one: \"1분 미만\",\n    other: \"{{count}}분 미만\",\n  },\n\n  xMinutes: {\n    one: \"1분\",\n    other: \"{{count}}분\",\n  },\n\n  aboutXHours: {\n    one: \"약 1시간\",\n    other: \"약 {{count}}시간\",\n  },\n\n  xHours: {\n    one: \"1시간\",\n    other: \"{{count}}시간\",\n  },\n\n  xDays: {\n    one: \"1일\",\n    other: \"{{count}}일\",\n  },\n\n  aboutXWeeks: {\n    one: \"약 1주\",\n    other: \"약 {{count}}주\",\n  },\n\n  xWeeks: {\n    one: \"1주\",\n    other: \"{{count}}주\",\n  },\n\n  aboutXMonths: {\n    one: \"약 1개월\",\n    other: \"약 {{count}}개월\",\n  },\n\n  xMonths: {\n    one: \"1개월\",\n    other: \"{{count}}개월\",\n  },\n\n  aboutXYears: {\n    one: \"약 1년\",\n    other: \"약 {{count}}년\",\n  },\n\n  xYears: {\n    one: \"1년\",\n    other: \"{{count}}년\",\n  },\n\n  overXYears: {\n    one: \"1년 이상\",\n    other: \"{{count}}년 이상\",\n  },\n\n  almostXYears: {\n    one: \"거의 1년\",\n    other: \"거의 {{count}}년\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" 후\";\n    } else {\n      return result + \" 전\";\n    }\n  }\n\n  return result;\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IAC3B,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;IAEb,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,OAAO;QACL,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;IAEA,SAAS;QACP,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,YAAY;QACV,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO;IAC3C,IAAI;IAEJ,MAAM,aAAa,oBAAoB,CAAC,MAAM;IAC9C,IAAI,OAAO,eAAe,UAAU;QAClC,SAAS;IACX,OAAO,IAAI,UAAU,GAAG;QACtB,SAAS,WAAW,GAAG;IACzB,OAAO;QACL,SAAS,WAAW,KAAK,CAAC,OAAO,CAAC,aAAa,MAAM,QAAQ;IAC/D;IAEA,IAAI,SAAS,WAAW;QACtB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAChD,OAAO,SAAS;QAClB,OAAO;YACL,OAAO,SAAS;QAClB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"y년 M월 d일 EEEE\",\n  long: \"y년 M월 d일\",\n  medium: \"y.MM.dd\",\n  short: \"y.MM.dd\",\n};\n\nconst timeFormats = {\n  full: \"a H시 mm분 ss초 zzzz\",\n  long: \"a H:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,kBAAkB;IACtB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,UAAU,IAAA,iNAAiB,EAAC;QAC1B,SAAS;QACT,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'지난' eeee p\",\n  yesterday: \"'어제' p\",\n  today: \"'오늘' p\",\n  tomorrow: \"'내일' p\",\n  nextWeek: \"'다음' eeee p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IAC3B,UAAU;IACV,WAAW;IACX,OAAO;IACP,UAAU;IACV,UAAU;IACV,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO,WAAW,WACtD,oBAAoB,CAAC,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"BC\", \"AD\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"기원전\", \"서기\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1분기\", \"2분기\", \"3분기\", \"4분기\"],\n};\n\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n\n  abbreviated: [\n    \"1월\",\n    \"2월\",\n    \"3월\",\n    \"4월\",\n    \"5월\",\n    \"6월\",\n    \"7월\",\n    \"8월\",\n    \"9월\",\n    \"10월\",\n    \"11월\",\n    \"12월\",\n  ],\n\n  wide: [\n    \"1월\",\n    \"2월\",\n    \"3월\",\n    \"4월\",\n    \"5월\",\n    \"6월\",\n    \"7월\",\n    \"8월\",\n    \"9월\",\n    \"10월\",\n    \"11월\",\n    \"12월\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  short: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  abbreviated: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  wide: [\"일요일\", \"월요일\", \"화요일\", \"수요일\", \"목요일\", \"금요일\", \"토요일\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n\n  switch (unit) {\n    case \"minute\":\n    case \"second\":\n      return String(number);\n    case \"date\":\n      return number + \"일\";\n    default:\n      return number + \"번째\";\n  }\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAM;KAAK;IACpB,aAAa;QAAC;QAAM;KAAK;IACzB,MAAM;QAAC;QAAO;KAAK;AACrB;AAEA,MAAM,gBAAgB;IACpB,QAAQ;QAAC;QAAK;QAAK;QAAK;KAAI;IAC5B,aAAa;QAAC;QAAM;QAAM;QAAM;KAAK;IACrC,MAAM;QAAC;QAAO;QAAO;QAAO;KAAM;AACpC;AAEA,MAAM,cAAc;IAClB,QAAQ;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;KAAK;IAEvE,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAC3C,OAAO;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAC1C,aAAa;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAChD,MAAM;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;AACzD;AAEA,MAAM,kBAAkB;IACtB,QAAQ;QACN,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,aAAa;QACX,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,4BAA4B;IAChC,QAAQ;QACN,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,aAAa;QACX,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,gBAAgB,CAAC,aAAa;IAClC,MAAM,SAAS,OAAO;IACtB,MAAM,OAAO,OAAO,SAAS;IAE7B,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO,OAAO;QAChB,KAAK;YACH,OAAO,SAAS;QAClB;YACE,OAAO,SAAS;IACpB;AACF;AAEO,MAAM,WAAW;IACtB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,SAAS,IAAA,6MAAe,EAAC;QACvB,QAAQ;QACR,cAAc;QACd,kBAAkB,CAAC,UAAY,UAAU;IAC3C;IAEA,OAAO,IAAA,6MAAe,EAAC;QACrB,QAAQ;QACR,cAAc;IAChB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,WAAW,IAAA,6MAAe,EAAC;QACzB,QAAQ;QACR,cAAc;QACd,kBAAkB;QAClB,wBAAwB;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(일|번째)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(기원전|서기)/i,\n};\nconst parseEraPatterns = {\n  any: [/^(bc|기원전)/i, /^(ad|서기)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234]사?분기/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^(1[012]|[123456789])/,\n  abbreviated: /^(1[012]|[123456789])월/i,\n  wide: /^(1[012]|[123456789])월/i,\n};\nconst parseMonthPatterns = {\n  any: [\n    /^1월?$/,\n    /^2/,\n    /^3/,\n    /^4/,\n    /^5/,\n    /^6/,\n    /^7/,\n    /^8/,\n    /^9/,\n    /^10/,\n    /^11/,\n    /^12/,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[일월화수목금토]/,\n  short: /^[일월화수목금토]/,\n  abbreviated: /^[일월화수목금토]/,\n  wide: /^[일월화수목금토]요일/,\n};\nconst parseDayPatterns = {\n  any: [/^일/, /^월/, /^화/, /^수/, /^목/, /^금/, /^토/],\n};\n\nconst matchDayPeriodPatterns = {\n  any: /^(am|pm|오전|오후|자정|정오|아침|저녁|밤)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^(am|오전)/i,\n    pm: /^(pm|오후)/i,\n    midnight: /^자정/i,\n    noon: /^정오/i,\n    morning: /^아침/i,\n    afternoon: /^오후/i,\n    evening: /^저녁/i,\n    night: /^밤/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAElC,MAAM,mBAAmB;IACvB,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,mBAAmB;IACvB,KAAK;QAAC;QAAc;KAAY;AAClC;AAEA,MAAM,uBAAuB;IAC3B,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,uBAAuB;IAC3B,KAAK;QAAC;QAAM;QAAM;QAAM;KAAK;AAC/B;AAEA,MAAM,qBAAqB;IACzB,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,qBAAqB;IACzB,KAAK;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,mBAAmB;IACvB,QAAQ;IACR,OAAO;IACP,aAAa;IACb,MAAM;AACR;AACA,MAAM,mBAAmB;IACvB,KAAK;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;AACjD;AAEA,MAAM,yBAAyB;IAC7B,KAAK;AACP;AACA,MAAM,yBAAyB;IAC7B,KAAK;QACH,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEO,MAAM,QAAQ;IACnB,eAAe,IAAA,qNAAmB,EAAC;QACjC,cAAc;QACd,cAAc;QACd,eAAe,CAAC,QAAU,SAAS,OAAO;IAC5C;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,SAAS,IAAA,uMAAY,EAAC;QACpB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,eAAe,CAAC,QAAU,QAAQ;IACpC;IAEA,OAAO,IAAA,uMAAY,EAAC;QAClB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,WAAW,IAAA,uMAAY,EAAC;QACtB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/ko.js"], "sourcesContent": ["import { formatDistance } from \"./ko/_lib/formatDistance.js\";\nimport { formatLong } from \"./ko/_lib/formatLong.js\";\nimport { formatRelative } from \"./ko/_lib/formatRelative.js\";\nimport { localize } from \"./ko/_lib/localize.js\";\nimport { match } from \"./ko/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Korean locale.\n * @language Korean\n * @iso-639-2 kor\n * <AUTHOR> [@angdev](https://github.com/angdev)\n * <AUTHOR> [@iamssen](https://github.com/iamssen)\n * <AUTHOR> [@so99ynoodles](https://github.com/so99ynoodles)\n */\nexport const ko = {\n  code: \"ko\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ko;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAWO,MAAM,KAAK;IAChB,MAAM;IACN,gBAAgB,iNAAc;IAC9B,YAAY,yMAAU;IACtB,gBAAgB,iNAAc;IAC9B,UAAU,qMAAQ;IAClB,OAAO,+LAAK;IACZ,SAAS;QACP,cAAc,EAAE,UAAU;QAC1B,uBAAuB;IACzB;AACF;uCAGe", "ignoreList": [0], "debugId": null}}]}