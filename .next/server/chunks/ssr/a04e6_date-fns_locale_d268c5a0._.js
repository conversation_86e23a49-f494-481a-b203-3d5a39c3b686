module.exports = [
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatDistance.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatDistance",
    ()=>formatDistance
]);
const formatDistanceLocale = {
    lessThanXSeconds: {
        one: "کمتر از یک ثانیه",
        other: "کمتر از {{count}} ثانیه"
    },
    xSeconds: {
        one: "1 ثانیه",
        other: "{{count}} ثانیه"
    },
    halfAMinute: "نیم دقیقه",
    lessThanXMinutes: {
        one: "کمتر از یک دقیقه",
        other: "کمتر از {{count}} دقیقه"
    },
    xMinutes: {
        one: "1 دقیقه",
        other: "{{count}} دقیقه"
    },
    aboutXHours: {
        one: "حدود 1 ساعت",
        other: "حدود {{count}} ساعت"
    },
    xHours: {
        one: "1 ساعت",
        other: "{{count}} ساعت"
    },
    xDays: {
        one: "1 روز",
        other: "{{count}} روز"
    },
    aboutXWeeks: {
        one: "حدود 1 هفته",
        other: "حدود {{count}} هفته"
    },
    xWeeks: {
        one: "1 هفته",
        other: "{{count}} هفته"
    },
    aboutXMonths: {
        one: "حدود 1 ماه",
        other: "حدود {{count}} ماه"
    },
    xMonths: {
        one: "1 ماه",
        other: "{{count}} ماه"
    },
    aboutXYears: {
        one: "حدود 1 سال",
        other: "حدود {{count}} سال"
    },
    xYears: {
        one: "1 سال",
        other: "{{count}} سال"
    },
    overXYears: {
        one: "بیشتر از 1 سال",
        other: "بیشتر از {{count}} سال"
    },
    almostXYears: {
        one: "نزدیک 1 سال",
        other: "نزدیک {{count}} سال"
    }
};
const formatDistance = (token, count, options)=>{
    let result;
    const tokenValue = formatDistanceLocale[token];
    if (typeof tokenValue === "string") {
        result = tokenValue;
    } else if (count === 1) {
        result = tokenValue.one;
    } else {
        result = tokenValue.other.replace("{{count}}", String(count));
    }
    if (options?.addSuffix) {
        if (options.comparison && options.comparison > 0) {
            return "در " + result;
        } else {
            return result + " قبل";
        }
    }
    return result;
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatLong.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatLong",
    ()=>formatLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildFormatLongFn.js [app-ssr] (ecmascript)");
;
const dateFormats = {
    full: "EEEE do MMMM y",
    long: "do MMMM y",
    medium: "d MMM y",
    short: "yyyy/MM/dd"
};
const timeFormats = {
    full: "h:mm:ss a zzzz",
    long: "h:mm:ss a z",
    medium: "h:mm:ss a",
    short: "h:mm a"
};
const dateTimeFormats = {
    full: "{{date}} 'در' {{time}}",
    long: "{{date}} 'در' {{time}}",
    medium: "{{date}}, {{time}}",
    short: "{{date}}, {{time}}"
};
const formatLong = {
    date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateFormats,
        defaultWidth: "full"
    }),
    time: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: timeFormats,
        defaultWidth: "full"
    }),
    dateTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateTimeFormats,
        defaultWidth: "full"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatRelative.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatRelative",
    ()=>formatRelative
]);
const formatRelativeLocale = {
    lastWeek: "eeee 'گذشته در' p",
    yesterday: "'دیروز در' p",
    today: "'امروز در' p",
    tomorrow: "'فردا در' p",
    nextWeek: "eeee 'در' p",
    other: "P"
};
const formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];
}),
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/localize.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "localize",
    ()=>localize
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildLocalizeFn.js [app-ssr] (ecmascript)");
;
const eraValues = {
    narrow: [
        "ق",
        "ب"
    ],
    abbreviated: [
        "ق.م.",
        "ب.م."
    ],
    wide: [
        "قبل از میلاد",
        "بعد از میلاد"
    ]
};
const quarterValues = {
    narrow: [
        "1",
        "2",
        "3",
        "4"
    ],
    abbreviated: [
        "س‌م1",
        "س‌م2",
        "س‌م3",
        "س‌م4"
    ],
    wide: [
        "سه‌ماهه 1",
        "سه‌ماهه 2",
        "سه‌ماهه 3",
        "سه‌ماهه 4"
    ]
};
// Note: in English, the names of days of the week and months are capitalized.
// If you are making a new locale based on this one, check if the same is true for the language you're working on.
// Generally, formatted dates should look like they are in the middle of a sentence,
// e.g. in Spanish language the weekdays and months should be in the lowercase.
const monthValues = {
    narrow: [
        "ژ",
        "ف",
        "م",
        "آ",
        "م",
        "ج",
        "ج",
        "آ",
        "س",
        "ا",
        "ن",
        "د"
    ],
    abbreviated: [
        "ژانـ",
        "فور",
        "مارس",
        "آپر",
        "می",
        "جون",
        "جولـ",
        "آگو",
        "سپتـ",
        "اکتـ",
        "نوامـ",
        "دسامـ"
    ],
    wide: [
        "ژانویه",
        "فوریه",
        "مارس",
        "آپریل",
        "می",
        "جون",
        "جولای",
        "آگوست",
        "سپتامبر",
        "اکتبر",
        "نوامبر",
        "دسامبر"
    ]
};
const dayValues = {
    narrow: [
        "ی",
        "د",
        "س",
        "چ",
        "پ",
        "ج",
        "ش"
    ],
    short: [
        "1ش",
        "2ش",
        "3ش",
        "4ش",
        "5ش",
        "ج",
        "ش"
    ],
    abbreviated: [
        "یکشنبه",
        "دوشنبه",
        "سه‌شنبه",
        "چهارشنبه",
        "پنجشنبه",
        "جمعه",
        "شنبه"
    ],
    wide: [
        "یکشنبه",
        "دوشنبه",
        "سه‌شنبه",
        "چهارشنبه",
        "پنجشنبه",
        "جمعه",
        "شنبه"
    ]
};
const dayPeriodValues = {
    narrow: {
        am: "ق",
        pm: "ب",
        midnight: "ن",
        noon: "ظ",
        morning: "ص",
        afternoon: "ب.ظ.",
        evening: "ع",
        night: "ش"
    },
    abbreviated: {
        am: "ق.ظ.",
        pm: "ب.ظ.",
        midnight: "نیمه‌شب",
        noon: "ظهر",
        morning: "صبح",
        afternoon: "بعدازظهر",
        evening: "عصر",
        night: "شب"
    },
    wide: {
        am: "قبل‌ازظهر",
        pm: "بعدازظهر",
        midnight: "نیمه‌شب",
        noon: "ظهر",
        morning: "صبح",
        afternoon: "بعدازظهر",
        evening: "عصر",
        night: "شب"
    }
};
const formattingDayPeriodValues = {
    narrow: {
        am: "ق",
        pm: "ب",
        midnight: "ن",
        noon: "ظ",
        morning: "ص",
        afternoon: "ب.ظ.",
        evening: "ع",
        night: "ش"
    },
    abbreviated: {
        am: "ق.ظ.",
        pm: "ب.ظ.",
        midnight: "نیمه‌شب",
        noon: "ظهر",
        morning: "صبح",
        afternoon: "بعدازظهر",
        evening: "عصر",
        night: "شب"
    },
    wide: {
        am: "قبل‌ازظهر",
        pm: "بعدازظهر",
        midnight: "نیمه‌شب",
        noon: "ظهر",
        morning: "صبح",
        afternoon: "بعدازظهر",
        evening: "عصر",
        night: "شب"
    }
};
const ordinalNumber = (dirtyNumber, _options)=>{
    return String(dirtyNumber);
};
const localize = {
    ordinalNumber,
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: eraValues,
        defaultWidth: "wide"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: quarterValues,
        defaultWidth: "wide",
        argumentCallback: (quarter)=>quarter - 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: monthValues,
        defaultWidth: "wide"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayValues,
        defaultWidth: "wide"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayPeriodValues,
        defaultWidth: "wide",
        formattingValues: formattingDayPeriodValues,
        defaultFormattingWidth: "wide"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/match.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "match",
    ()=>match
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchFn.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js [app-ssr] (ecmascript)");
;
;
const matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
const parseOrdinalNumberPattern = /\d+/i;
const matchEraPatterns = {
    narrow: /^(ق|ب)/i,
    abbreviated: /^(ق\.?\s?م\.?|ق\.?\s?د\.?\s?م\.?|م\.?\s?|د\.?\s?م\.?)/i,
    wide: /^(قبل از میلاد|قبل از دوران مشترک|میلادی|دوران مشترک|بعد از میلاد)/i
};
const parseEraPatterns = {
    any: [
        /^قبل/i,
        /^بعد/i
    ]
};
const matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^س‌م[1234]/i,
    wide: /^سه‌ماهه [1234]/i
};
const parseQuarterPatterns = {
    any: [
        /1/i,
        /2/i,
        /3/i,
        /4/i
    ]
};
const matchMonthPatterns = {
    narrow: /^[جژفمآاماسند]/i,
    abbreviated: /^(جنو|ژانـ|ژانویه|فوریه|فور|مارس|آوریل|آپر|مه|می|ژوئن|جون|جول|جولـ|ژوئیه|اوت|آگو|سپتمبر|سپتامبر|اکتبر|اکتوبر|نوامبر|نوامـ|دسامبر|دسامـ|دسم)/i,
    wide: /^(ژانویه|جنوری|فبروری|فوریه|مارچ|مارس|آپریل|اپریل|ایپریل|آوریل|مه|می|ژوئن|جون|جولای|ژوئیه|آگست|اگست|آگوست|اوت|سپتمبر|سپتامبر|اکتبر|اکتوبر|نوامبر|نومبر|دسامبر|دسمبر)/i
};
const parseMonthPatterns = {
    narrow: [
        /^(ژ|ج)/i,
        /^ف/i,
        /^م/i,
        /^(آ|ا)/i,
        /^م/i,
        /^(ژ|ج)/i,
        /^(ج|ژ)/i,
        /^(آ|ا)/i,
        /^س/i,
        /^ا/i,
        /^ن/i,
        /^د/i
    ],
    any: [
        /^ژا/i,
        /^ف/i,
        /^ما/i,
        /^آپ/i,
        /^(می|مه)/i,
        /^(ژوئن|جون)/i,
        /^(ژوئی|جول)/i,
        /^(اوت|آگ)/i,
        /^س/i,
        /^(اوک|اک)/i,
        /^ن/i,
        /^د/i
    ]
};
const matchDayPatterns = {
    narrow: /^[شیدسچپج]/i,
    short: /^(ش|ج|1ش|2ش|3ش|4ش|5ش)/i,
    abbreviated: /^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i,
    wide: /^(یکشنبه|دوشنبه|سه‌شنبه|چهارشنبه|پنج‌شنبه|جمعه|شنبه)/i
};
const parseDayPatterns = {
    narrow: [
        /^ی/i,
        /^دو/i,
        /^س/i,
        /^چ/i,
        /^پ/i,
        /^ج/i,
        /^ش/i
    ],
    any: [
        /^(ی|1ش|یکشنبه)/i,
        /^(د|2ش|دوشنبه)/i,
        /^(س|3ش|سه‌شنبه)/i,
        /^(چ|4ش|چهارشنبه)/i,
        /^(پ|5ش|پنجشنبه)/i,
        /^(ج|جمعه)/i,
        /^(ش|شنبه)/i
    ]
};
const matchDayPeriodPatterns = {
    narrow: /^(ب|ق|ن|ظ|ص|ب.ظ.|ع|ش)/i,
    abbreviated: /^(ق.ظ.|ب.ظ.|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i,
    wide: /^(قبل‌ازظهر|نیمه‌شب|ظهر|صبح|بعدازظهر|عصر|شب)/i
};
const parseDayPeriodPatterns = {
    any: {
        am: /^(ق|ق.ظ.|قبل‌ازظهر)/i,
        pm: /^(ب|ب.ظ.|بعدازظهر)/i,
        midnight: /^(‌نیمه‌شب|ن)/i,
        noon: /^(ظ|ظهر)/i,
        morning: /(ص|صبح)/i,
        afternoon: /(ب|ب.ظ.|بعدازظهر)/i,
        evening: /(ع|عصر)/i,
        night: /(ش|شب)/i
    }
};
const match = {
    ordinalNumber: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchPatternFn"])({
        matchPattern: matchOrdinalNumberPattern,
        parsePattern: parseOrdinalNumberPattern,
        valueCallback: (value)=>parseInt(value, 10)
    }),
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchEraPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseEraPatterns,
        defaultParseWidth: "any"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchQuarterPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseQuarterPatterns,
        defaultParseWidth: "any",
        valueCallback: (index)=>index + 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchMonthPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseMonthPatterns,
        defaultParseWidth: "any"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPatterns,
        defaultParseWidth: "any"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPeriodPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPeriodPatterns,
        defaultParseWidth: "any"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/fa-IR.js [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "faIR",
    ()=>faIR
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatDistance.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatLong.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/formatRelative.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$localize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/localize.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/fa-IR/_lib/match.js [app-ssr] (ecmascript)");
;
;
;
;
;
const faIR = {
    code: "fa-IR",
    formatDistance: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatDistance"],
    formatLong: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatLong"],
    formatRelative: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatRelative"],
    localize: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$localize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["localize"],
    match: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$fa$2d$IR$2f$_lib$2f$match$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["match"],
    options: {
        weekStartsOn: 6 /* Saturday */ ,
        firstWeekContainsDate: 1
    }
};
const __TURBOPACK__default__export__ = faIR;
}),
];

//# sourceMappingURL=a04e6_date-fns_locale_d268c5a0._.js.map