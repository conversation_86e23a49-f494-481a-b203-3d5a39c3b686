{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"น้อยกว่า 1 วินาที\",\n    other: \"น้อยกว่า {{count}} วินาที\",\n  },\n\n  xSeconds: {\n    one: \"1 วินาที\",\n    other: \"{{count}} วินาที\",\n  },\n\n  halfAMinute: \"ครึ่งนาที\",\n\n  lessThanXMinutes: {\n    one: \"น้อยกว่า 1 นาที\",\n    other: \"น้อยกว่า {{count}} นาที\",\n  },\n\n  xMinutes: {\n    one: \"1 นาที\",\n    other: \"{{count}} นาที\",\n  },\n\n  aboutXHours: {\n    one: \"ประมาณ 1 ชั่วโมง\",\n    other: \"ประมาณ {{count}} ชั่วโมง\",\n  },\n\n  xHours: {\n    one: \"1 ชั่วโมง\",\n    other: \"{{count}} ชั่วโมง\",\n  },\n\n  xDays: {\n    one: \"1 วัน\",\n    other: \"{{count}} วัน\",\n  },\n\n  aboutXWeeks: {\n    one: \"ประมาณ 1 สัปดาห์\",\n    other: \"ประมาณ {{count}} สัปดาห์\",\n  },\n\n  xWeeks: {\n    one: \"1 สัปดาห์\",\n    other: \"{{count}} สัปดาห์\",\n  },\n\n  aboutXMonths: {\n    one: \"ประมาณ 1 เดือน\",\n    other: \"ประมาณ {{count}} เดือน\",\n  },\n\n  xMonths: {\n    one: \"1 เดือน\",\n    other: \"{{count}} เดือน\",\n  },\n\n  aboutXYears: {\n    one: \"ประมาณ 1 ปี\",\n    other: \"ประมาณ {{count}} ปี\",\n  },\n\n  xYears: {\n    one: \"1 ปี\",\n    other: \"{{count}} ปี\",\n  },\n\n  overXYears: {\n    one: \"มากกว่า 1 ปี\",\n    other: \"มากกว่า {{count}} ปี\",\n  },\n\n  almostXYears: {\n    one: \"เกือบ 1 ปี\",\n    other: \"เกือบ {{count}} ปี\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (token === \"halfAMinute\") {\n        return \"ใน\" + result;\n      } else {\n        return \"ใน \" + result;\n      }\n    } else {\n      return result + \"ที่ผ่านมา\";\n    }\n  }\n\n  return result;\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IAC3B,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;IAEb,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,OAAO;QACL,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;IAEA,SAAS;QACP,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,YAAY;QACV,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO;IAC3C,IAAI;IAEJ,MAAM,aAAa,oBAAoB,CAAC,MAAM;IAC9C,IAAI,OAAO,eAAe,UAAU;QAClC,SAAS;IACX,OAAO,IAAI,UAAU,GAAG;QACtB,SAAS,WAAW,GAAG;IACzB,OAAO;QACL,SAAS,WAAW,KAAK,CAAC,OAAO,CAAC,aAAa,OAAO;IACxD;IAEA,IAAI,SAAS,WAAW;QACtB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAChD,IAAI,UAAU,eAAe;gBAC3B,OAAO,OAAO;YAChB,OAAO;gBACL,OAAO,QAAQ;YACjB;QACF,OAAO;YACL,OAAO,SAAS;QAClB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"วันEEEEที่ do MMMM y\",\n  long: \"do MMMM y\",\n  medium: \"d MMM y\",\n  short: \"dd/MM/yyyy\",\n};\n\nconst timeFormats = {\n  full: \"H:mm:ss น. zzzz\",\n  long: \"H:mm:ss น. z\",\n  medium: \"H:mm:ss น.\",\n  short: \"H:mm น.\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'เวลา' {{time}}\",\n  long: \"{{date}} 'เวลา' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"medium\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,kBAAkB;IACtB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,UAAU,IAAA,iNAAiB,EAAC;QAC1B,SAAS;QACT,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IAC3B,UAAU;IACV,WAAW;IACX,OAAO;IACP,UAAU;IACV,UAAU;IACV,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO,WAAW,WACtD,oBAAoB,CAAC,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"B\", \"คศ\"],\n  abbreviated: [\"BC\", \"ค.ศ.\"],\n  wide: [\"ปีก่อนคริสตกาล\", \"คริสต์ศักราช\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"ไตรมาสแรก\", \"ไตรมาสที่สอง\", \"ไตรมาสที่สาม\", \"ไตรมาสที่สี่\"],\n};\n\nconst dayValues = {\n  narrow: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  short: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  abbreviated: [\"อา.\", \"จ.\", \"อ.\", \"พ.\", \"พฤ.\", \"ศ.\", \"ส.\"],\n  wide: [\"อาทิตย์\", \"จันทร์\", \"อังคาร\", \"พุธ\", \"พฤหัสบดี\", \"ศุกร์\", \"เสาร์\"],\n};\n\nconst monthValues = {\n  narrow: [\n    \"ม.ค.\",\n    \"ก.พ.\",\n    \"มี.ค.\",\n    \"เม.ย.\",\n    \"พ.ค.\",\n    \"มิ.ย.\",\n    \"ก.ค.\",\n    \"ส.ค.\",\n    \"ก.ย.\",\n    \"ต.ค.\",\n    \"พ.ย.\",\n    \"ธ.ค.\",\n  ],\n\n  abbreviated: [\n    \"ม.ค.\",\n    \"ก.พ.\",\n    \"มี.ค.\",\n    \"เม.ย.\",\n    \"พ.ค.\",\n    \"มิ.ย.\",\n    \"ก.ค.\",\n    \"ส.ค.\",\n    \"ก.ย.\",\n    \"ต.ค.\",\n    \"พ.ย.\",\n    \"ธ.ค.\",\n  ],\n\n  wide: [\n    \"มกราคม\",\n    \"กุมภาพันธ์\",\n    \"มีนาคม\",\n    \"เมษายน\",\n    \"พฤษภาคม\",\n    \"มิถุนายน\",\n    \"กรกฎาคม\",\n    \"สิงหาคม\",\n    \"กันยายน\",\n    \"ตุลาคม\",\n    \"พฤศจิกายน\",\n    \"ธันวาคม\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"เช้า\",\n    afternoon: \"บ่าย\",\n    evening: \"เย็น\",\n    night: \"กลางคืน\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n  abbreviated: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n  wide: {\n    am: \"ก่อนเที่ยง\",\n    pm: \"หลังเที่ยง\",\n    midnight: \"เที่ยงคืน\",\n    noon: \"เที่ยง\",\n    morning: \"ตอนเช้า\",\n    afternoon: \"ตอนกลางวัน\",\n    evening: \"ตอนเย็น\",\n    night: \"ตอนกลางคืน\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAK;KAAK;IACnB,aAAa;QAAC;QAAM;KAAO;IAC3B,MAAM;QAAC;QAAkB;KAAe;AAC1C;AAEA,MAAM,gBAAgB;IACpB,QAAQ;QAAC;QAAK;QAAK;QAAK;KAAI;IAC5B,aAAa;QAAC;QAAM;QAAM;QAAM;KAAK;IACrC,MAAM;QAAC;QAAa;QAAgB;QAAgB;KAAe;AACrE;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAO;QAAM;QAAM;QAAM;QAAO;QAAM;KAAK;IACpD,OAAO;QAAC;QAAO;QAAM;QAAM;QAAM;QAAO;QAAM;KAAK;IACnD,aAAa;QAAC;QAAO;QAAM;QAAM;QAAM;QAAO;QAAM;KAAK;IACzD,MAAM;QAAC;QAAW;QAAU;QAAU;QAAO;QAAY;QAAS;KAAQ;AAC5E;AAEA,MAAM,cAAc;IAClB,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,kBAAkB;IACtB,QAAQ;QACN,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,aAAa;QACX,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,4BAA4B;IAChC,QAAQ;QACN,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,aAAa;QACX,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEA,MAAM,gBAAgB,CAAC,aAAa;IAClC,OAAO,OAAO;AAChB;AAEO,MAAM,WAAW;IACtB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,SAAS,IAAA,6MAAe,EAAC;QACvB,QAAQ;QACR,cAAc;QACd,kBAAkB,CAAC,UAAY,UAAU;IAC3C;IAEA,OAAO,IAAA,6MAAe,EAAC;QACrB,QAAQ;QACR,cAAc;IAChB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,WAAW,IAAA,6MAAe,EAAC;QACzB,QAAQ;QACR,cAAc;QACd,kBAAkB;QAClB,wBAAwB;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^([bB]|[aA]|คศ)/i,\n  abbreviated:\n    /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n  wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i,\n};\nconst parseEraPatterns = {\n  any: [/^[bB]/i, /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^ไตรมาส(ที่)? ?[1234]/i,\n};\nconst parseQuarterPatterns = {\n  any: [/(1|แรก|หนึ่ง)/i, /(2|สอง)/i, /(3|สาม)/i, /(4|สี่)/i],\n};\n\nconst matchMonthPatterns = {\n  narrow:\n    /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n  abbreviated:\n    /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n  wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i,\n};\nconst parseMonthPatterns = {\n  wide: [\n    /^มก/i,\n    /^กุม/i,\n    /^มี/i,\n    /^เม/i,\n    /^พฤษ/i,\n    /^มิ/i,\n    /^กรก/i,\n    /^ส/i,\n    /^กัน/i,\n    /^ต/i,\n    /^พฤศ/i,\n    /^ธ/i,\n  ],\n\n  any: [\n    /^ม\\.?ค\\.?/i,\n    /^ก\\.?พ\\.?/i,\n    /^มี\\.?ค\\.?/i,\n    /^เม\\.?ย\\.?/i,\n    /^พ\\.?ค\\.?/i,\n    /^มิ\\.?ย\\.?/i,\n    /^ก\\.?ค\\.?/i,\n    /^ส\\.?ค\\.?/i,\n    /^ก\\.?ย\\.?/i,\n    /^ต\\.?ค\\.?/i,\n    /^พ\\.?ย\\.?/i,\n    /^ธ\\.?ค\\.?/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i,\n};\nconst parseDayPatterns = {\n  wide: [/^อา/i, /^จั/i, /^อั/i, /^พุธ/i, /^พฤ/i, /^ศ/i, /^เส/i],\n  any: [/^อา/i, /^จ/i, /^อ/i, /^พ(?!ฤ)/i, /^พฤ/i, /^ศ/i, /^ส/i],\n};\n\nconst matchDayPeriodPatterns = {\n  any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^ก่อนเที่ยง/i,\n    pm: /^หลังเที่ยง/i,\n    midnight: /^เที่ยงคืน/i,\n    noon: /^เที่ยง/i,\n    morning: /เช้า/i,\n    afternoon: /บ่าย/i,\n    evening: /เย็น/i,\n    night: /กลางคืน/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAElC,MAAM,mBAAmB;IACvB,QAAQ;IACR,aACE;IACF,MAAM;AACR;AACA,MAAM,mBAAmB;IACvB,KAAK;QAAC;QAAU;KAA4C;AAC9D;AAEA,MAAM,uBAAuB;IAC3B,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,uBAAuB;IAC3B,KAAK;QAAC;QAAkB;QAAY;QAAY;KAAW;AAC7D;AAEA,MAAM,qBAAqB;IACzB,QACE;IACF,aACE;IACF,MAAM;AACR;AACA,MAAM,qBAAqB;IACzB,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,KAAK;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,mBAAmB;IACvB,QAAQ;IACR,OAAO;IACP,aAAa;IACb,MAAM;AACR;AACA,MAAM,mBAAmB;IACvB,MAAM;QAAC;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;QAAO;KAAO;IAC9D,KAAK;QAAC;QAAQ;QAAO;QAAO;QAAY;QAAQ;QAAO;KAAM;AAC/D;AAEA,MAAM,yBAAyB;IAC7B,KAAK;AACP;AACA,MAAM,yBAAyB;IAC7B,KAAK;QACH,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEO,MAAM,QAAQ;IACnB,eAAe,IAAA,qNAAmB,EAAC;QACjC,cAAc;QACd,cAAc;QACd,eAAe,CAAC,QAAU,SAAS,OAAO;IAC5C;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,SAAS,IAAA,uMAAY,EAAC;QACpB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,eAAe,CAAC,QAAU,QAAQ;IACpC;IAEA,OAAO,IAAA,uMAAY,EAAC;QAClB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,WAAW,IAAA,uMAAY,EAAC;QACtB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/th.js"], "sourcesContent": ["import { formatDistance } from \"./th/_lib/formatDistance.js\";\nimport { formatLong } from \"./th/_lib/formatLong.js\";\nimport { formatRelative } from \"./th/_lib/formatRelative.js\";\nimport { localize } from \"./th/_lib/localize.js\";\nimport { match } from \"./th/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> <PERSON> [@athivvat](https://github.com/athivvat)\n * <AUTHOR>\n * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)\n */\nexport const th = {\n  code: \"th\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default th;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAWO,MAAM,KAAK;IAChB,MAAM;IACN,gBAAgB,iNAAc;IAC9B,YAAY,yMAAU;IACtB,gBAAgB,iNAAc;IAC9B,UAAU,qMAAQ;IAClB,OAAO,+LAAK;IACZ,SAAS;QACP,cAAc,EAAE,UAAU;QAC1B,uBAAuB;IACzB;AACF;uCAGe", "ignoreList": [0], "debugId": null}}]}