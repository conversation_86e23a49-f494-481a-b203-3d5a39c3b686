{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/config/types.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { EditorProps } from '@monaco-editor/react'\nimport type { JSONSchema4 } from 'json-schema'\nimport type { CSSProperties } from 'react'\nimport type React from 'react'\nimport type { DeepUndefinable, MarkRequired } from 'ts-essentials'\n\nimport type {\n  Jo<PERSON>FieldClientProps,\n  JoinFieldErrorClientComponent,\n  JoinFieldErrorServerComponent,\n  JoinFieldLabelClientComponent,\n  JoinFieldLabelServerComponent,\n} from '../../admin/fields/Join.js'\nimport type { FieldClientComponent, FieldServerComponent } from '../../admin/forms/Field.js'\nimport type { RichTextAdapter, RichTextAdapterProvider } from '../../admin/RichText.js'\nimport type {\n  ArrayFieldClientProps,\n  ArrayFieldErrorClientComponent,\n  ArrayFieldErrorServerComponent,\n  ArrayFieldLabelClientComponent,\n  ArrayFieldLabelServerComponent,\n  BlocksFieldClientProps,\n  BlocksFieldErrorClientComponent,\n  BlocksFieldErrorServerComponent,\n  BlocksFieldLabelClientComponent,\n  BlocksFieldLabelServerComponent,\n  CheckboxFieldClientProps,\n  CheckboxFieldErrorClientComponent,\n  CheckboxFieldErrorServerComponent,\n  CheckboxFieldLabelClientComponent,\n  CheckboxFieldLabelServerComponent,\n  ClientTab,\n  CodeFieldClientProps,\n  CodeFieldErrorClientComponent,\n  CodeFieldErrorServerComponent,\n  CodeFieldLabelClientComponent,\n  CodeFieldLabelServerComponent,\n  CollapsibleFieldClientProps,\n  CollapsibleFieldLabelClientComponent,\n  CollapsibleFieldLabelServerComponent,\n  ConditionalDateProps,\n  Data,\n  DateFieldClientProps,\n  DateFieldErrorClientComponent,\n  DateFieldErrorServerComponent,\n  DateFieldLabelClientComponent,\n  DateFieldLabelServerComponent,\n  DefaultCellComponentProps,\n  DefaultServerCellComponentProps,\n  Description,\n  EmailFieldClientProps,\n  EmailFieldErrorClientComponent,\n  EmailFieldErrorServerComponent,\n  EmailFieldLabelClientComponent,\n  EmailFieldLabelServerComponent,\n  FieldDescriptionClientProps,\n  FieldDescriptionServerProps,\n  FieldDiffClientProps,\n  FieldDiffServerProps,\n  GroupFieldClientProps,\n  GroupFieldLabelClientComponent,\n  GroupFieldLabelServerComponent,\n  HiddenFieldProps,\n  JSONFieldClientProps,\n  JSONFieldErrorClientComponent,\n  JSONFieldErrorServerComponent,\n  JSONFieldLabelClientComponent,\n  JSONFieldLabelServerComponent,\n  NumberFieldClientProps,\n  NumberFieldErrorClientComponent,\n  NumberFieldErrorServerComponent,\n  NumberFieldLabelClientComponent,\n  NumberFieldLabelServerComponent,\n  PointFieldClientProps,\n  PointFieldErrorClientComponent,\n  PointFieldErrorServerComponent,\n  PointFieldLabelClientComponent,\n  PointFieldLabelServerComponent,\n  RadioFieldClientProps,\n  RadioFieldErrorClientComponent,\n  RadioFieldErrorServerComponent,\n  RadioFieldLabelClientComponent,\n  RadioFieldLabelServerComponent,\n  RelationshipFieldClientProps,\n  RelationshipFieldErrorClientComponent,\n  RelationshipFieldErrorServerComponent,\n  RelationshipFieldLabelClientComponent,\n  RelationshipFieldLabelServerComponent,\n  RichTextFieldClientProps,\n  RowFieldClientProps,\n  RowLabelComponent,\n  SelectFieldClientProps,\n  SelectFieldErrorClientComponent,\n  SelectFieldErrorServerComponent,\n  SelectFieldLabelClientComponent,\n  SelectFieldLabelServerComponent,\n  StaticDescription,\n  TabsFieldClientProps,\n  TextareaFieldClientProps,\n  TextareaFieldErrorClientComponent,\n  TextareaFieldErrorServerComponent,\n  TextareaFieldLabelClientComponent,\n  TextareaFieldLabelServerComponent,\n  TextFieldClientProps,\n  TextFieldErrorClientComponent,\n  TextFieldErrorServerComponent,\n  TextFieldLabelClientComponent,\n  TextFieldLabelServerComponent,\n  UploadFieldClientProps,\n} from '../../admin/types.js'\nimport type { SanitizedCollectionConfig, TypeWithID } from '../../collections/config/types.js'\nimport type {\n  CustomComponent,\n  LabelFunction,\n  PayloadComponent,\n  StaticLabel,\n} from '../../config/types.js'\nimport type { DBIdentifierName } from '../../database/types.js'\nimport type { SanitizedGlobalConfig } from '../../globals/config/types.js'\nimport type {\n  ArrayFieldValidation,\n  BlocksFieldValidation,\n  BlockSlug,\n  CheckboxFieldValidation,\n  CodeFieldValidation,\n  CollectionSlug,\n  DateFieldValidation,\n  EmailFieldValidation,\n  FieldCustom,\n  JSONFieldValidation,\n  PointFieldValidation,\n  RadioFieldValidation,\n  RequestContext,\n  Sort,\n  TextareaFieldValidation,\n} from '../../index.js'\nimport type { DocumentPreferences } from '../../preferences/types.js'\nimport type {\n  DefaultValue,\n  JsonObject,\n  Operation,\n  PayloadRequest,\n  PickPreserveOptional,\n  Where,\n} from '../../types/index.js'\nimport type {\n  NumberFieldManyValidation,\n  NumberFieldSingleValidation,\n  RelationshipFieldManyValidation,\n  RelationshipFieldSingleValidation,\n  SelectFieldManyValidation,\n  SelectFieldSingleValidation,\n  TextFieldManyValidation,\n  TextFieldSingleValidation,\n  UploadFieldManyValidation,\n  UploadFieldSingleValidation,\n} from '../validations.js'\n\nexport type FieldHookArgs<TData extends TypeWithID = any, TValue = any, TSiblingData = any> = {\n  /**\n   * The data of the nearest parent block. If the field is not within a block, `blockData` will be equal to `undefined`.\n   */\n  blockData: JsonObject | undefined\n  /** The collection which the field belongs to. If the field belongs to a global, this will be null. */\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  /**\n   * Only available in `afterRead` hooks\n   */\n  currentDepth?: number\n  /**\n   * Only available in `afterRead` hooks\n   */\n  /** The data passed to update the document within create and update operations, and the full document itself in the afterRead hook. */\n  data?: Partial<TData>\n  /**\n   * Only available in the `afterRead` hook.\n   */\n  depth?: number\n  draft?: boolean\n  /** The field which the hook is running against. */\n  field: FieldAffectingData\n  /** Boolean to denote if this hook is running against finding one, or finding many within the afterRead hook. */\n  findMany?: boolean\n  /** The global which the field belongs to. If the field belongs to a collection, this will be null. */\n  global: null | SanitizedGlobalConfig\n  indexPath: number[]\n  /** A string relating to which operation the field type is currently executing within. Useful within beforeValidate, beforeChange, and afterChange hooks to differentiate between create and update operations. */\n  operation?: 'create' | 'delete' | 'read' | 'update'\n  /** The full original document in `update` operations. In the `afterChange` hook, this is the resulting document of the operation. */\n  originalDoc?: TData\n  overrideAccess?: boolean\n  /**\n   * The path of the field, e.g. [\"group\", \"myArray\", 1, \"textField\"]. The path is the schemaPath but with indexes and would be used in the context of field data, not field schemas.\n   */\n  path: (number | string)[]\n  /** The document before changes were applied, only in `afterChange` hooks. */\n  previousDoc?: TData\n  /** The sibling data of the document before changes being applied, only in `beforeChange`, `beforeValidate`, `beforeDuplicate` and `afterChange` field hooks. */\n  previousSiblingDoc?: TData\n  /** The previous value of the field, before changes, only in `beforeChange`, `afterChange`, `beforeDuplicate` and `beforeValidate` field hooks. */\n  previousValue?: TValue\n  /** The Express request object. It is mocked for Local API operations. */\n  req: PayloadRequest\n  /**\n   * The schemaPath of the field, e.g. [\"group\", \"myArray\", \"textField\"]. The schemaPath is the path but without indexes and would be used in the context of field schemas, not field data.\n   */\n  schemaPath: string[]\n  /**\n   * Only available in the `afterRead` hook.\n   */\n  showHiddenFields?: boolean\n  /** The sibling data passed to a field that the hook is running against. */\n  siblingData: Partial<TSiblingData>\n  /**\n   * The original siblingData with locales (not modified by any hooks). Only available in `beforeChange` and `beforeDuplicate` field hooks.\n   */\n  siblingDocWithLocales?: Record<string, unknown>\n  /**\n   * The sibling fields of the field which the hook is running against.\n   */\n  siblingFields: (Field | TabAsField)[]\n  /** The value of the field. */\n  value?: TValue\n}\n\nexport type FieldHook<TData extends TypeWithID = any, TValue = any, TSiblingData = any> = (\n  args: FieldHookArgs<TData, TValue, TSiblingData>,\n) => Promise<TValue> | TValue\n\nexport type FieldAccessArgs<TData extends TypeWithID = any, TSiblingData = any> = {\n  /**\n   * The data of the nearest parent block. If the field is not within a block, `blockData` will be equal to `undefined`.\n   */\n  blockData?: JsonObject | undefined\n  /**\n   * The incoming, top-level document data used to `create` or `update` the document with.\n   */\n  data?: Partial<TData>\n  /**\n   * The original data of the document before the `update` is applied. `doc` is undefined during the `create` operation.\n   */\n  doc?: TData\n  /**\n   * The `id` of the current document being read or updated. `id` is undefined during the `create` operation.\n   */\n  id?: number | string\n  /** The `payload` object to interface with the payload API */\n  req: PayloadRequest\n  /**\n   * Immediately adjacent data to this field. For example, if this is a `group` field, then `siblingData` will be the other fields within the group.\n   */\n  siblingData?: Partial<TSiblingData>\n}\n\nexport type FieldAccess<TData extends TypeWithID = any, TSiblingData = any> = (\n  args: FieldAccessArgs<TData, TSiblingData>,\n) => boolean | Promise<boolean>\n\n//TODO: In 4.0, we should replace the three parameters of the condition function with a single, named parameter object\nexport type Condition<TData extends TypeWithID = any, TSiblingData = any> = (\n  /**\n   * The top-level document data\n   */\n  data: Partial<TData>,\n  /**\n   * Immediately adjacent data to this field. For example, if this is a `group` field, then `siblingData` will be the other fields within the group.\n   */\n  siblingData: Partial<TSiblingData>,\n  {\n    blockData,\n    operation,\n    path,\n    user,\n  }: {\n    /**\n     * The data of the nearest parent block. If the field is not within a block, `blockData` will be equal to `undefined`.\n     */\n    blockData: Partial<TData>\n    /**\n     * A string relating to which operation the field type is currently executing within.\n     */\n    operation: Operation\n    /**\n     * The path of the field, e.g. [\"group\", \"myArray\", 1, \"textField\"]. The path is the schemaPath but with indexes and would be used in the context of field data, not field schemas.\n     */\n    path: (number | string)[]\n    user: PayloadRequest['user']\n  },\n) => boolean\n\nexport type FilterOptionsProps<TData = any> = {\n  /**\n   * The data of the nearest parent block. Will be `undefined` if the field is not within a block or when called on a `Filter` component within the list view.\n   */\n  blockData: TData\n  /**\n   * An object containing the full collection or global document currently being edited. Will be an empty object when called on a `Filter` component within the list view.\n   */\n  data: TData\n  /**\n   * The `id` of the current document being edited. Will be undefined during the `create` operation or when called on a `Filter` component within the list view.\n   */\n  id: number | string\n  /**\n   * The collection `slug` to filter against, limited to this field's `relationTo` property.\n   */\n  relationTo: CollectionSlug\n  req: PayloadRequest\n  /**\n   * An object containing document data that is scoped to only fields within the same parent of this field. Will be an empty object when called on a `Filter` component within the list view.\n   */\n  siblingData: unknown\n  /**\n   * An object containing the currently authenticated user.\n   */\n  user: Partial<PayloadRequest['user']>\n}\n\nexport type FilterOptionsFunc<TData = any> = (\n  options: FilterOptionsProps<TData>,\n) => boolean | Promise<boolean | Where> | Where\n\nexport type FilterOptions<TData = any> = FilterOptionsFunc<TData> | null | Where\n\ntype BlockSlugOrString = (({} & string) | BlockSlug)[]\n\nexport type BlocksFilterOptionsProps<TData = any> = {\n  /**\n   * The `id` of the current document being edited. Will be undefined during the `create` operation.\n   */\n  id: number | string\n} & Pick<FilterOptionsProps<TData>, 'data' | 'req' | 'siblingData' | 'user'>\n\nexport type BlocksFilterOptions<TData = any> =\n  | ((\n      options: BlocksFilterOptionsProps<TData>,\n    ) => BlockSlugOrString | Promise<BlockSlugOrString | true> | true)\n  | BlockSlugOrString\n\nexport type FieldAdmin = {\n  className?: string\n  components?: {\n    Cell?: PayloadComponent<DefaultServerCellComponentProps, DefaultCellComponentProps>\n    Description?: PayloadComponent<FieldDescriptionServerProps, FieldDescriptionClientProps>\n    Diff?: PayloadComponent<FieldDiffServerProps, FieldDiffClientProps>\n    Field?: PayloadComponent<FieldClientComponent | FieldServerComponent>\n    /**\n     * The Filter component has to be a client component\n     */\n    Filter?: PayloadComponent\n  }\n  /**\n   * You can programmatically show / hide fields based on what other fields are doing.\n   * This is also run on the server, to determine if the field should be validated.\n   */\n  condition?: Condition\n  /** Extension point to add your custom data. Available in server and client. */\n  custom?: Record<string, any>\n  /**\n   * The field description will be displayed next to the field in the admin UI. Additionally,\n   * we use the field description to generate JSDoc comments for the generated TypeScript types.\n   */\n  description?: Description\n  disableBulkEdit?: boolean\n  disabled?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view groupBy options.\n   * @type boolean\n   */\n  disableGroupBy?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view column selector.\n   * @type boolean\n   */\n  disableListColumn?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view filter options.\n   * @type boolean\n   */\n  disableListFilter?: boolean\n  hidden?: boolean\n  position?: 'sidebar'\n  readOnly?: boolean\n  style?: CSSProperties\n  width?: CSSProperties['width']\n}\n\nexport type AdminClient = {\n  className?: string\n  /** Extension point to add your custom data. Available in server and client. */\n  custom?: Record<string, any>\n  description?: StaticDescription\n  disableBulkEdit?: boolean\n  disabled?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view groupBy options.\n   * @type boolean\n   */\n  disableGroupBy?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view column selector.\n   * @type boolean\n   */\n  disableListColumn?: boolean\n  /**\n   * Shows / hides fields from appearing in the list view filter options.\n   * @type boolean\n   */\n  disableListFilter?: boolean\n  hidden?: boolean\n  position?: 'sidebar'\n  readOnly?: boolean\n  style?: { '--field-width'?: CSSProperties['width'] } & CSSProperties\n  width?: CSSProperties['width']\n}\n\nexport type Labels = {\n  plural: LabelFunction | StaticLabel\n  singular: LabelFunction | StaticLabel\n}\n\nexport type LabelsClient = {\n  plural: StaticLabel\n  singular: StaticLabel\n}\n\nexport type BaseValidateOptions<TData, TSiblingData, TValue> = {\n  /**\n   * The data of the nearest parent block. If the field is not within a block, `blockData` will be equal to `undefined`.\n   */\n  blockData: Partial<TData>\n  collectionSlug?: string\n  data: Partial<TData>\n  event?: 'onChange' | 'submit'\n  id?: number | string\n  operation?: Operation\n  /**\n   * The `overrideAccess` flag that was attached to the request. This is used to bypass access control checks for fields.\n   */\n  overrideAccess?: boolean\n  /**\n   * The path of the field, e.g. [\"group\", \"myArray\", 1, \"textField\"]. The path is the schemaPath but with indexes and would be used in the context of field data, not field schemas.\n   */\n  path: (number | string)[]\n  preferences: DocumentPreferences\n  previousValue?: TValue\n  req: PayloadRequest\n  required?: boolean\n  siblingData: Partial<TSiblingData>\n}\n\nexport type ValidateOptions<\n  TData,\n  TSiblingData,\n  TFieldConfig extends object,\n  TValue,\n> = BaseValidateOptions<TData, TSiblingData, TValue> & TFieldConfig\n\nexport type Validate<\n  TValue = any,\n  TData = any,\n  TSiblingData = any,\n  TFieldConfig extends object = object,\n> = (\n  value: null | TValue | undefined,\n  options: ValidateOptions<TData, TSiblingData, TFieldConfig, TValue>,\n) => Promise<string | true> | string | true\n\nexport type OptionLabel =\n  | (() => React.JSX.Element)\n  | LabelFunction\n  | React.JSX.Element\n  | StaticLabel\n\nexport type OptionObject = {\n  label: OptionLabel\n  value: string\n}\n\nexport type Option = OptionObject | string\n\nexport type FieldGraphQLType = {\n  graphQL?: {\n    /**\n     * Complexity for the query. This is used to limit the complexity of the join query.\n     *\n     * @default 10\n     */\n    complexity?: number\n  }\n}\n\nexport interface FieldBase {\n  /**\n   * Do not set this property manually. This is set to true during sanitization, to avoid\n   * sanitizing the same field multiple times.\n   */\n  _sanitized?: boolean\n  access?: {\n    create?: FieldAccess\n    read?: FieldAccess\n    update?: FieldAccess\n  }\n  admin?: FieldAdmin\n  /** Extension point to add your custom data. Server only. */\n  custom?: FieldCustom\n  defaultValue?: DefaultValue\n  hidden?: boolean\n  hooks?: {\n    afterChange?: FieldHook[]\n    afterRead?: FieldHook[]\n    beforeChange?: FieldHook[]\n    /**\n     * Runs before a document is duplicated to prevent errors in unique fields or return null to use defaultValue.\n     */\n    beforeDuplicate?: FieldHook[]\n    beforeValidate?: FieldHook[]\n  }\n  index?: boolean\n  label?: false | LabelFunction | StaticLabel\n  localized?: boolean\n  /**\n   * The name of the field. Must be alphanumeric and cannot contain ' . '\n   *\n   * Must not be one of reserved field names: ['__v', 'salt', 'hash', 'file']\n   * @link https://payloadcms.com/docs/fields/overview#field-names\n   */\n  name: string\n  required?: boolean\n  saveToJWT?: boolean | string\n  /**\n   * Allows you to modify the base JSON schema that is generated during generate:types for this field.\n   * This JSON schema will be used to generate the TypeScript interface of this field.\n   */\n  typescriptSchema?: Array<(args: { jsonSchema: JSONSchema4 }) => JSONSchema4>\n  unique?: boolean\n  validate?: Validate\n  /**\n   * Pass `true` to disable field in the DB\n   * for [Virtual Fields](https://payloadcms.com/blog/learn-how-virtual-fields-can-help-solve-common-cms-challenges):\n   * A virtual field can be used in `admin.useAsTitle` only when linked to a relationship.\n   */\n  virtual?: boolean | string\n}\n\nexport interface FieldBaseClient {\n  admin?: AdminClient\n  hidden?: boolean\n  index?: boolean\n  label?: StaticLabel\n  localized?: boolean\n  /**\n   * The name of the field. Must be alphanumeric and cannot contain ' . '\n   *\n   * Must not be one of reserved field names: ['__v', 'salt', 'hash', 'file']\n   * @link https://payloadcms.com/docs/fields/overview#field-names\n   */\n  name: string\n  required?: boolean\n  saveToJWT?: boolean | string\n  /**\n   * Allows you to modify the base JSON schema that is generated during generate:types for this field.\n   * This JSON schema will be used to generate the TypeScript interface of this field.\n   */\n  typescriptSchema?: Array<(args: { jsonSchema: JSONSchema4 }) => JSONSchema4>\n  unique?: boolean\n}\n\nexport type NumberField = {\n  admin?: {\n    /** Set this property to a string that will be used for browser autocomplete. */\n    autoComplete?: string\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<NumberFieldErrorClientComponent | NumberFieldErrorServerComponent>\n      Label?: CustomComponent<NumberFieldLabelClientComponent | NumberFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    /** Set this property to define a placeholder string for the field. */\n    placeholder?: Record<string, string> | string\n    /** Set a value for the number field to increment / decrement using browser controls. */\n    step?: number\n  } & FieldAdmin\n  /** Maximum value accepted. Used in the default `validate` function. */\n  max?: number\n  /** Minimum value accepted. Used in the default `validate` function. */\n  min?: number\n  type: 'number'\n} & (\n  | {\n      /** Makes this field an ordered array of numbers instead of just a single number. */\n      hasMany: true\n      /** Maximum number of numbers in the numbers array, if `hasMany` is set to true. */\n      maxRows?: number\n      /** Minimum number of numbers in the numbers array, if `hasMany` is set to true. */\n      minRows?: number\n      validate?: NumberFieldManyValidation\n    }\n  | {\n      /** Makes this field an ordered array of numbers instead of just a single number. */\n      hasMany?: false | undefined\n      /** Maximum number of numbers in the numbers array, if `hasMany` is set to true. */\n      maxRows?: undefined\n      /** Minimum number of numbers in the numbers array, if `hasMany` is set to true. */\n      minRows?: undefined\n      validate?: NumberFieldSingleValidation\n    }\n) &\n  Omit<FieldBase, 'validate'>\n\nexport type NumberFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<NumberField['admin'], 'autoComplete' | 'placeholder' | 'step'>\n} & FieldBaseClient &\n  Pick<NumberField, 'hasMany' | 'max' | 'maxRows' | 'min' | 'minRows' | 'type'>\n\nexport type TextField = {\n  admin?: {\n    autoComplete?: string\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<TextFieldErrorClientComponent | TextFieldErrorServerComponent>\n      Label?: CustomComponent<TextFieldLabelClientComponent | TextFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    placeholder?: Record<string, string> | string\n    rtl?: boolean\n  } & FieldAdmin\n  maxLength?: number\n  minLength?: number\n  type: 'text'\n} & (\n  | {\n      /** Makes this field an ordered array of strings instead of just a single string. */\n      hasMany: true\n      /** Maximum number of strings in the strings array, if `hasMany` is set to true. */\n      maxRows?: number\n      /** Minimum number of strings in the strings array, if `hasMany` is set to true. */\n      minRows?: number\n      validate?: TextFieldManyValidation\n    }\n  | {\n      /** Makes this field an ordered array of strings instead of just a single string. */\n      hasMany?: false | undefined\n      /** Maximum number of strings in the strings array, if `hasMany` is set to true. */\n      maxRows?: undefined\n      /** Minimum number of strings in the strings array, if `hasMany` is set to true. */\n      minRows?: undefined\n      validate?: TextFieldSingleValidation\n    }\n) &\n  Omit<FieldBase, 'validate'>\n\nexport type TextFieldClient = {\n  admin?: AdminClient &\n    PickPreserveOptional<NonNullable<TextField['admin']>, 'autoComplete' | 'placeholder' | 'rtl'>\n} & FieldBaseClient &\n  Pick<TextField, 'hasMany' | 'maxLength' | 'maxRows' | 'minLength' | 'minRows' | 'type'>\n\nexport type EmailField = {\n  admin?: {\n    autoComplete?: string\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<EmailFieldErrorClientComponent | EmailFieldErrorServerComponent>\n      Label?: CustomComponent<EmailFieldLabelClientComponent | EmailFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    placeholder?: Record<string, string> | string\n  } & FieldAdmin\n  type: 'email'\n  validate?: EmailFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type EmailFieldClient = {\n  admin?: AdminClient &\n    PickPreserveOptional<NonNullable<EmailField['admin']>, 'autoComplete' | 'placeholder'>\n} & FieldBaseClient &\n  Pick<EmailField, 'type'>\n\nexport type TextareaField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<TextareaFieldErrorClientComponent | TextareaFieldErrorServerComponent>\n      Label?: CustomComponent<TextareaFieldLabelClientComponent | TextareaFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    placeholder?: Record<string, string> | string\n    rows?: number\n    rtl?: boolean\n  } & FieldAdmin\n  maxLength?: number\n  minLength?: number\n  type: 'textarea'\n  validate?: TextareaFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type TextareaFieldClient = {\n  admin?: AdminClient &\n    PickPreserveOptional<NonNullable<TextareaField['admin']>, 'placeholder' | 'rows' | 'rtl'>\n} & FieldBaseClient &\n  Pick<TextareaField, 'maxLength' | 'minLength' | 'type'>\n\nexport type CheckboxField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<CheckboxFieldErrorClientComponent | CheckboxFieldErrorServerComponent>\n      Label?: CustomComponent<CheckboxFieldLabelClientComponent | CheckboxFieldLabelServerComponent>\n    } & FieldAdmin['components']\n  } & FieldAdmin\n  type: 'checkbox'\n  validate?: CheckboxFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type CheckboxFieldClient = {\n  admin?: AdminClient\n} & FieldBaseClient &\n  Pick<CheckboxField, 'type'>\n\nexport type DateField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<DateFieldErrorClientComponent | DateFieldErrorServerComponent>\n      Label?: CustomComponent<DateFieldLabelClientComponent | DateFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    date?: ConditionalDateProps\n    placeholder?: Record<string, string> | string\n  } & FieldAdmin\n  /**\n   * Enable timezone selection in the admin interface.\n   */\n  timezone?: true\n  type: 'date'\n  validate?: DateFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type DateFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<DateField['admin'], 'date' | 'placeholder'>\n} & FieldBaseClient &\n  Pick<DateField, 'timezone' | 'type'>\n\nexport type GroupBase = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Label?: CustomComponent<GroupFieldLabelClientComponent | GroupFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    hideGutter?: boolean\n  } & FieldAdmin\n  fields: Field[]\n  type: 'group'\n  validate?: Validate<unknown, unknown, unknown, GroupField>\n} & Omit<FieldBase, 'validate'>\n\nexport type NamedGroupField = {\n  /** Customize generated GraphQL and Typescript schema names.\n   * By default, it is bound to the collection.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n} & GroupBase\n\nexport type UnnamedGroupField = {\n  interfaceName?: never\n  localized?: never\n} & Omit<GroupBase, 'hooks' | 'name' | 'virtual'>\n\nexport type GroupField = NamedGroupField | UnnamedGroupField\n\nexport type UnnamedGroupFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<UnnamedGroupField['admin'], 'hideGutter'>\n  fields: ClientField[]\n} & Omit<FieldBaseClient, 'name' | 'required'> &\n  Pick<UnnamedGroupField, 'label' | 'type'>\n\nexport type NamedGroupFieldClient = Pick<NamedGroupField, 'name'> & UnnamedGroupFieldClient\n\nexport type GroupFieldClient = NamedGroupFieldClient | UnnamedGroupFieldClient\n\nexport type RowField = {\n  admin?: Omit<FieldAdmin, 'description'>\n  fields: Field[]\n  type: 'row'\n} & Omit<FieldBase, 'admin' | 'hooks' | 'label' | 'localized' | 'name' | 'validate' | 'virtual'>\n\nexport type RowFieldClient = {\n  admin?: Omit<AdminClient, 'description'>\n  fields: ClientField[]\n} & Omit<FieldBaseClient, 'admin' | 'label' | 'name'> &\n  Pick<RowField, 'type'>\n\nexport type CollapsibleField = {\n  fields: Field[]\n  type: 'collapsible'\n} & (\n  | {\n      admin: {\n        components: {\n          afterInput?: CustomComponent[]\n          beforeInput?: CustomComponent[]\n          Label: CustomComponent<\n            CollapsibleFieldLabelClientComponent | CollapsibleFieldLabelServerComponent\n          >\n        } & FieldAdmin['components']\n        initCollapsed?: boolean\n      } & FieldAdmin\n      label?: Required<FieldBase['label']>\n    }\n  | {\n      admin?: {\n        components?: {\n          afterInput?: CustomComponent[]\n          beforeInput?: CustomComponent[]\n          Label?: CustomComponent<\n            CollapsibleFieldLabelClientComponent | CollapsibleFieldLabelServerComponent\n          >\n        } & FieldAdmin['components']\n        initCollapsed?: boolean\n      } & FieldAdmin\n      label: Required<FieldBase['label']>\n    }\n) &\n  Omit<FieldBase, 'hooks' | 'label' | 'localized' | 'name' | 'validate' | 'virtual'>\n\nexport type CollapsibleFieldClient = {\n  admin?: {\n    initCollapsed?: boolean\n  } & AdminClient\n  fields: ClientField[]\n  label: StaticLabel\n} & Omit<FieldBaseClient, 'label' | 'name' | 'validate'> &\n  Pick<CollapsibleField, 'type'>\n\ntype TabBase = {\n  /**\n   * @deprecated\n   * Use `admin.description` instead. This will be removed in a future major version.\n   */\n  description?: LabelFunction | StaticDescription\n  fields: Field[]\n  id?: string\n  interfaceName?: string\n  saveToJWT?: boolean | string\n} & Omit<FieldBase, 'required' | 'validate'>\n\nexport type NamedTab = {\n  /** Customize generated GraphQL and Typescript schema names.\n   * The slug is used by default.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n} & TabBase\n\nexport type UnnamedTab = {\n  interfaceName?: never\n  /**\n   * Can be either:\n   * - A string, which will be used as the tab's label.\n   * - An object, where the key is the language code and the value is the label.\n   */\n  label:\n    | {\n        [selectedLanguage: string]: string\n      }\n    | LabelFunction\n    | string\n  localized?: never\n} & Omit<TabBase, 'hooks' | 'name' | 'virtual'>\n\nexport type Tab = NamedTab | UnnamedTab\nexport type TabsField = {\n  admin?: Omit<FieldAdmin, 'description'>\n  type: 'tabs'\n} & {\n  tabs: Tab[]\n} & Omit<FieldBase, 'admin' | 'localized' | 'name' | 'saveToJWT' | 'virtual'>\n\nexport type TabsFieldClient = {\n  admin?: Omit<AdminClient, 'description'>\n  tabs: ClientTab[]\n} & Omit<FieldBaseClient, 'admin' | 'localized' | 'name' | 'saveToJWT'> &\n  Pick<TabsField, 'type'>\n\nexport type TabAsField = {\n  name?: string\n  type: 'tab'\n} & Tab\n\nexport type TabAsFieldClient = ClientTab & Pick<TabAsField, 'name' | 'type'>\n\nexport type UIField = {\n  admin: {\n    components?: {\n      /**\n       * Allow any custom components to be added to the UI field. This allows\n       * the UI field to be used as a vessel for getting components rendered.\n       */\n      [key: string]: PayloadComponent | undefined\n      Cell?: CustomComponent\n      // Can be optional, in case the UI field is just used as a vessel for custom components\n      Field?: CustomComponent\n      /**\n       * The Filter component has to be a client component\n       */\n      Filter?: PayloadComponent\n    } & FieldAdmin['components']\n    condition?: Condition\n    /** Extension point to add your custom data. Available in server and client. */\n    custom?: Record<string, any>\n    /**\n     * Set `false` make the UI field appear in the list view column selector. `true` by default for UI fields.\n     * @default true\n     */\n    disableBulkEdit?: boolean\n    /**\n     * Shows / hides fields from appearing in the list view column selector.\n     * @type boolean\n     */\n    disableListColumn?: boolean\n    position?: string\n    width?: CSSProperties['width']\n  }\n  /** Extension point to add your custom data. Server only. */\n  custom?: Record<string, any>\n  label?: Record<string, string> | string\n  name: string\n  type: 'ui'\n}\n\nexport type UIFieldClient = {\n  // still include FieldBaseClient.admin (even if it's undefinable) so that we don't need constant type checks (e.g. if('xy' in field))\n\n  admin: DeepUndefinable<FieldBaseClient['admin']> &\n    Pick<\n      UIField['admin'],\n      'custom' | 'disableBulkEdit' | 'disableListColumn' | 'position' | 'width'\n    >\n} & Omit<DeepUndefinable<FieldBaseClient>, 'admin'> & // still include FieldBaseClient (even if it's undefinable) so that we don't need constant type checks (e.g. if('xy' in field))\n  Pick<UIField, 'label' | 'name' | 'type'>\n\ntype SharedUploadProperties = {\n  /**\n   * Toggle the preview in the admin interface.\n   */\n  displayPreview?: boolean\n  filterOptions?: FilterOptions\n  /**\n   * Sets a maximum population depth for this field, regardless of the remaining depth when this field is reached.\n   *\n   * {@link https://payloadcms.com/docs/getting-started/concepts#field-level-max-depth}\n   */\n  maxDepth?: number\n  type: 'upload'\n} & (\n  | {\n      hasMany: true\n      /**\n       * @deprecated Use 'maxRows' instead\n       */\n      max?: number\n      maxRows?: number\n      /**\n       * @deprecated Use 'minRows' instead\n       */\n      min?: number\n      minRows?: number\n      validate?: UploadFieldManyValidation\n    }\n  | {\n      hasMany?: false | undefined\n      /**\n       * @deprecated Use 'maxRows' instead\n       */\n      max?: undefined\n      maxRows?: undefined\n      /**\n       * @deprecated Use 'minRows' instead\n       */\n      min?: undefined\n      minRows?: undefined\n      validate?: UploadFieldSingleValidation\n    }\n) &\n  FieldGraphQLType &\n  Omit<FieldBase, 'validate'>\n\ntype SharedUploadPropertiesClient = FieldBaseClient &\n  Pick<\n    SharedUploadProperties,\n    'hasMany' | 'max' | 'maxDepth' | 'maxRows' | 'min' | 'minRows' | 'type'\n  >\n\ntype UploadAdmin = {\n  allowCreate?: boolean\n  components?: {\n    Error?: CustomComponent<\n      RelationshipFieldErrorClientComponent | RelationshipFieldErrorServerComponent\n    >\n    Label?: CustomComponent<\n      RelationshipFieldLabelClientComponent | RelationshipFieldLabelServerComponent\n    >\n  } & FieldAdmin['components']\n  isSortable?: boolean\n} & FieldAdmin\n\ntype UploadAdminClient = AdminClient & Pick<UploadAdmin, 'allowCreate' | 'isSortable'>\n\nexport type PolymorphicUploadField = {\n  admin?: {\n    sortOptions?: Partial<Record<CollectionSlug, string>>\n  } & UploadAdmin\n  relationTo: CollectionSlug[]\n} & SharedUploadProperties\n\nexport type PolymorphicUploadFieldClient = {\n  admin?: {\n    // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n    sortOptions?: Pick<PolymorphicUploadField['admin'], 'sortOptions'>\n  } & UploadAdminClient\n} & Pick<PolymorphicUploadField, 'displayPreview' | 'maxDepth' | 'relationTo' | 'type'> &\n  SharedUploadPropertiesClient\n\nexport type SingleUploadField = {\n  admin?: {\n    sortOptions?: string\n  } & UploadAdmin\n  relationTo: CollectionSlug\n} & SharedUploadProperties\n\nexport type SingleUploadFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: Pick<SingleUploadField['admin'], 'sortOptions'> & UploadAdminClient\n} & Pick<SingleUploadField, 'displayPreview' | 'maxDepth' | 'relationTo' | 'type'> &\n  SharedUploadPropertiesClient\n\nexport type UploadField = /* PolymorphicUploadField | */ SingleUploadField\n\nexport type UploadFieldClient = /* PolymorphicUploadFieldClient | */ SingleUploadFieldClient\n\nexport type CodeField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<CodeFieldErrorClientComponent | CodeFieldErrorServerComponent>\n      Label?: CustomComponent<CodeFieldLabelClientComponent | CodeFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    editorOptions?: EditorProps['options']\n    language?: string\n  } & FieldAdmin\n  maxLength?: number\n  minLength?: number\n  type: 'code'\n  validate?: CodeFieldValidation\n} & Omit<FieldBase, 'admin' | 'validate'>\n\nexport type CodeFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<CodeField['admin'], 'editorOptions' | 'language'>\n} & Omit<FieldBaseClient, 'admin'> &\n  Pick<CodeField, 'maxLength' | 'minLength' | 'type'>\n\nexport type JSONField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<JSONFieldErrorClientComponent | JSONFieldErrorServerComponent>\n      Label?: CustomComponent<JSONFieldLabelClientComponent | JSONFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    editorOptions?: EditorProps['options']\n    maxHeight?: number\n  } & FieldAdmin\n\n  jsonSchema?: {\n    fileMatch: string[]\n    schema: JSONSchema4\n    uri: string\n  }\n  type: 'json'\n  validate?: JSONFieldValidation\n} & Omit<FieldBase, 'admin' | 'validate'>\n\nexport type JSONFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<JSONField['admin'], 'editorOptions' | 'maxHeight'>\n} & Omit<FieldBaseClient, 'admin'> &\n  Pick<JSONField, 'jsonSchema' | 'type'>\n\nexport type SelectField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<SelectFieldErrorClientComponent | SelectFieldErrorServerComponent>\n      Label?: CustomComponent<SelectFieldLabelClientComponent | SelectFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    isClearable?: boolean\n    isSortable?: boolean\n    placeholder?: LabelFunction | string\n  } & FieldAdmin\n  /**\n   * Customize the SQL table name\n   */\n  dbName?: DBIdentifierName\n  /**\n   * Customize the DB enum name\n   */\n  enumName?: DBIdentifierName\n  /**\n   * Reduce the available options based on the current user, value of another field, etc.\n   * Similar to the `filterOptions` property on `relationship` and `upload` fields, except with a different return type.\n   */\n  filterOptions?: (args: {\n    data: Data\n    options: Option[]\n    req: PayloadRequest\n    siblingData: Data\n  }) => Option[]\n  hasMany?: boolean\n  /**\n   * Customize generated GraphQL and Typescript schema names.\n   * By default, it is bound to the collection.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n  options: Option[]\n  type: 'select'\n} & (\n  | {\n      hasMany: true\n      validate?: SelectFieldManyValidation\n    }\n  | {\n      hasMany?: false | undefined\n      validate?: SelectFieldSingleValidation\n    }\n) &\n  Omit<FieldBase, 'validate'>\n\nexport type SelectFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<SelectField['admin'], 'isClearable' | 'isSortable' | 'placeholder'>\n} & FieldBaseClient &\n  Pick<SelectField, 'hasMany' | 'interfaceName' | 'options' | 'type'>\n\ntype SharedRelationshipProperties = {\n  filterOptions?: FilterOptions\n  /**\n   * Sets a maximum population depth for this field, regardless of the remaining depth when this field is reached.\n   *\n   * {@link https://payloadcms.com/docs/getting-started/concepts#field-level-max-depth}\n   */\n  maxDepth?: number\n  type: 'relationship'\n} & (\n  | {\n      hasMany: true\n      /**\n       * @deprecated Use 'maxRows' instead\n       */\n      max?: number\n      maxRows?: number\n      /**\n       * @deprecated Use 'minRows' instead\n       */\n      min?: number\n      minRows?: number\n      validate?: RelationshipFieldManyValidation\n    }\n  | {\n      hasMany?: false | undefined\n      /**\n       * @deprecated Use 'maxRows' instead\n       */\n      max?: undefined\n      maxRows?: undefined\n      /**\n       * @deprecated Use 'minRows' instead\n       */\n      min?: undefined\n      minRows?: undefined\n      validate?: RelationshipFieldSingleValidation\n    }\n) &\n  FieldGraphQLType &\n  Omit<FieldBase, 'validate'>\n\ntype SharedRelationshipPropertiesClient = FieldBaseClient &\n  Pick<\n    SharedRelationshipProperties,\n    'hasMany' | 'max' | 'maxDepth' | 'maxRows' | 'min' | 'minRows' | 'type'\n  >\n\ntype RelationshipAdmin = {\n  allowCreate?: boolean\n  allowEdit?: boolean\n  appearance?: 'drawer' | 'select'\n  components?: {\n    afterInput?: CustomComponent[]\n    beforeInput?: CustomComponent[]\n    Error?: CustomComponent<\n      RelationshipFieldErrorClientComponent | RelationshipFieldErrorServerComponent\n    >\n    Label?: CustomComponent<\n      RelationshipFieldLabelClientComponent | RelationshipFieldLabelServerComponent\n    >\n  } & FieldAdmin['components']\n  isSortable?: boolean\n  placeholder?: LabelFunction | string\n} & FieldAdmin\n\ntype RelationshipAdminClient = AdminClient &\n  Pick<RelationshipAdmin, 'allowCreate' | 'allowEdit' | 'appearance' | 'isSortable' | 'placeholder'>\n\nexport type PolymorphicRelationshipField = {\n  admin?: {\n    sortOptions?: Partial<Record<CollectionSlug, string>>\n  } & RelationshipAdmin\n  relationTo: CollectionSlug[]\n} & SharedRelationshipProperties\n\nexport type PolymorphicRelationshipFieldClient = {\n  admin?: {\n    // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n    sortOptions?: PolymorphicRelationshipField['admin']['sortOptions']\n  } & RelationshipAdminClient\n} & Pick<PolymorphicRelationshipField, 'relationTo'> &\n  SharedRelationshipPropertiesClient\n\nexport type SingleRelationshipField = {\n  admin?: {\n    sortOptions?: string\n  } & RelationshipAdmin\n  relationTo: CollectionSlug\n} & SharedRelationshipProperties\n\nexport type SingleRelationshipFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: Partial<Pick<SingleRelationshipField['admin'], 'sortOptions'>> & RelationshipAdminClient\n} & Pick<SingleRelationshipField, 'relationTo'> &\n  SharedRelationshipPropertiesClient\n\nexport type RelationshipField = PolymorphicRelationshipField | SingleRelationshipField\n\nexport type RelationshipFieldClient =\n  | PolymorphicRelationshipFieldClient\n  | SingleRelationshipFieldClient\n\nexport type ValueWithRelation = {\n  relationTo: CollectionSlug\n  value: number | string\n}\n\nexport function valueIsValueWithRelation(value: unknown): value is ValueWithRelation {\n  return value !== null && typeof value === 'object' && 'relationTo' in value && 'value' in value\n}\n\nexport type RelationshipValue = RelationshipValueMany | RelationshipValueSingle\n\nexport type RelationshipValueMany = (number | string)[] | ValueWithRelation[]\n\nexport type RelationshipValueSingle = number | string | ValueWithRelation\n\nexport type RichTextField<\n  TValue extends object = any,\n  TAdapterProps = any,\n  TExtraProperties = object,\n> = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent\n      Label?: CustomComponent\n    } & FieldAdmin['components']\n  } & FieldAdmin\n  editor?:\n    | RichTextAdapter<TValue, TAdapterProps, TExtraProperties>\n    | RichTextAdapterProvider<TValue, TAdapterProps, TExtraProperties>\n  /**\n   * Sets a maximum population depth for this field, regardless of the remaining depth when this field is reached.\n   *\n   * {@link https://payloadcms.com/docs/getting-started/concepts#field-level-max-depth}\n   */\n  maxDepth?: number\n  type: 'richText'\n} & FieldBase &\n  TExtraProperties\n\nexport type RichTextFieldClient<\n  TValue extends object = any,\n  TAdapterProps = any,\n  TExtraProperties = object,\n> = FieldBaseClient &\n  Pick<RichTextField<TValue, TAdapterProps, TExtraProperties>, 'maxDepth' | 'type'> &\n  TExtraProperties\n\nexport type ArrayField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<ArrayFieldErrorClientComponent | ArrayFieldErrorServerComponent>\n      Label?: CustomComponent<ArrayFieldLabelClientComponent | ArrayFieldLabelServerComponent>\n      RowLabel?: RowLabelComponent\n    } & FieldAdmin['components']\n    initCollapsed?: boolean\n    /**\n     * Disable drag and drop sorting\n     */\n    isSortable?: boolean\n  } & FieldAdmin\n  /**\n   * Customize the SQL table name\n   */\n  dbName?: DBIdentifierName\n  fields: Field[]\n  /** Customize generated GraphQL and Typescript schema names.\n   * By default, it is bound to the collection.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n  labels?: Labels\n  maxRows?: number\n  minRows?: number\n  type: 'array'\n  validate?: ArrayFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type ArrayFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<ArrayField['admin'], 'initCollapsed' | 'isSortable'>\n  fields: ClientField[]\n  labels?: LabelsClient\n} & FieldBaseClient &\n  Pick<ArrayField, 'interfaceName' | 'maxRows' | 'minRows' | 'type'>\n\nexport type RadioField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<RadioFieldErrorClientComponent | RadioFieldErrorServerComponent>\n      Label?: CustomComponent<RadioFieldLabelClientComponent | RadioFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    layout?: 'horizontal' | 'vertical'\n  } & FieldAdmin\n  /**\n   * Customize the SQL table name\n   */\n  dbName?: DBIdentifierName\n  /**\n   * Customize the DB enum name\n   */\n  enumName?: DBIdentifierName\n  /** Customize generated GraphQL and Typescript schema names.\n   * By default, it is bound to the collection.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n  options: Option[]\n  type: 'radio'\n  validate?: RadioFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type RadioFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<RadioField['admin'], 'layout'>\n} & FieldBaseClient &\n  Pick<RadioField, 'interfaceName' | 'options' | 'type'>\n\ntype BlockFields = {\n  [key: string]: any\n  blockName?: string\n  blockType?: string\n}\n\nexport type BlockJSX = {\n  /**\n   * Override the default regex used to search for the start of the block in the JSX.\n   * By default, it's <BlockSlugHere\n   */\n  customEndRegex?:\n    | {\n        /**\n         * Whether the end match is optional. If true, the end match is\n         * not required to match for the transformer to be triggered.\n         * The entire text from regexpStart to the end of the document will then be matched.\n         */\n        optional?: true\n        regExp: RegExp\n      }\n    | RegExp\n  /**\n   * Override the default regex used to search for the start of the block in the JSX.\n   * By default, it's <BlockSlugHere/>\n   */\n  customStartRegex?: RegExp\n  /**\n   * By default, all spaces at the beginning and end of every line of the\n   * children (text between the open and close match) are removed.\n   * Set this to true to disable this behavior.\n   */\n  doNotTrimChildren?: boolean\n  /**\n   * Function that receives the data for a given block and returns a JSX representation of it.\n   *\n   * This is used to convert Lexical => JSX\n   */\n  export: (props: {\n    fields: BlockFields\n    lexicalToMarkdown: (props: { editorState: Record<string, any> }) => string\n  }) =>\n    | {\n        children?: string\n        props?: object\n      }\n    | false\n    | string\n  /**\n   * Function that receives the markdown string and parsed\n   * JSX props for a given matched block and returns a Lexical representation of it.\n   *\n   * This is used to convert JSX => Lexical\n   */\n  import: (props: {\n    children: string\n    closeMatch: null | RegExpMatchArray // Only available when customEndRegex is set\n    htmlToLexical?: ((props: { html: string }) => any) | null\n    markdownToLexical: (props: { markdown: string }) => Record<string, any>\n    openMatch?: RegExpMatchArray\n    props: Record<string, any>\n  }) => BlockFields | false\n}\n\nexport type Block = {\n  /**\n   * Do not set this property manually. This is set to true during sanitization, to avoid\n   * sanitizing the same block multiple times.\n   */\n  _sanitized?: boolean\n  admin?: {\n    components?: {\n      /**\n       * This will replace the entire block component, including the block header / collapsible.\n       */\n      Block?: PayloadComponent<any, any>\n      Label?: PayloadComponent<any, any>\n    }\n    /** Extension point to add your custom data. Available in server and client. */\n    custom?: Record<string, any>\n    /**\n     * Hides the block name field from the Block's header\n     *\n     * @default false\n     */\n    disableBlockName?: boolean\n    group?: Record<string, string> | string\n    jsx?: PayloadComponent\n  }\n  /** Extension point to add your custom data. Server only. */\n  custom?: Record<string, any>\n  /**\n   * Customize the SQL table name\n   */\n  dbName?: DBIdentifierName\n  fields: Field[]\n  /** @deprecated - please migrate to the interfaceName property instead. */\n  graphQL?: {\n    singularName?: string\n  }\n  imageAltText?: string\n  /**\n   * Preferred aspect ratio of the image is 3 : 2\n   */\n  imageURL?: string\n  /** Customize generated GraphQL and Typescript schema names.\n   * The slug is used by default.\n   *\n   * This is useful if you would like to generate a top level type to share amongst collections/fields.\n   * **Note**: Top level types can collide, ensure they are unique amongst collections, arrays, groups, blocks, tabs.\n   */\n  interfaceName?: string\n  jsx?: BlockJSX\n  labels?: Labels\n  slug: string\n}\n\nexport type ClientBlock = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: Pick<Block['admin'], 'custom' | 'disableBlockName' | 'group'>\n  fields: ClientField[]\n  labels?: LabelsClient\n} & Pick<Block, 'imageAltText' | 'imageURL' | 'jsx' | 'slug'>\n\nexport type BlocksField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<BlocksFieldErrorClientComponent | BlocksFieldErrorServerComponent>\n      Label?: CustomComponent<BlocksFieldLabelClientComponent | BlocksFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    initCollapsed?: boolean\n    /**\n     * Disable drag and drop sorting\n     */\n    isSortable?: boolean\n  } & FieldAdmin\n  /**\n   * Like `blocks`, but allows you to also pass strings that are slugs of blocks defined in `config.blocks`.\n   *\n   * @todo `blockReferences` will be merged with `blocks` in 4.0\n   */\n  blockReferences?: (Block | BlockSlug)[]\n  blocks: Block[]\n  defaultValue?: DefaultValue\n  /**\n   * Blocks can be conditionally enabled using the `filterOptions` property on the blocks field.\n   * It allows you to provide a function that returns which block slugs should be available based on the given context.\n   *\n   * @behavior\n   *\n   * - `filterOptions` is re-evaluated as part of the form state request, whenever the document data changes.\n   * - If a block is present in the field but no longer allowed by `filterOptions`, a validation error will occur when saving.\n   *\n   * @example\n   *\n   * ```ts\n   * {\n   *   name: 'blocksWithDynamicFilterOptions',\n   *   type: 'blocks',\n   *   filterOptions: ({ siblingData }) => {\n   *     return siblingData?.enabledBlocks?.length\n   *       ? [siblingData.enabledBlocks] // allow only the matching block\n   *       : true // allow all blocks if no value is set\n   *   },\n   *   blocks: [\n   *     { slug: 'block1', fields: [{ type: 'text', name: 'block1Text' }] },\n   *     { slug: 'block2', fields: [{ type: 'text', name: 'block2Text' }] },\n   *     { slug: 'block3', fields: [{ type: 'text', name: 'block3Text' }] },\n   *   ],\n   * }\n   * ```\n   * In this example, the list of available blocks is determined by the enabledBlocks sibling field. If no value is set, all blocks remain available.\n   */\n  filterOptions?: BlocksFilterOptions\n  labels?: Labels\n  maxRows?: number\n  minRows?: number\n  type: 'blocks'\n  validate?: BlocksFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type BlocksFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<BlocksField['admin'], 'initCollapsed' | 'isSortable'>\n  /**\n   * Like `blocks`, but allows you to also pass strings that are slugs of blocks defined in `config.blocks`.\n   *\n   * @todo `blockReferences` will be merged with `blocks` in 4.0\n   */\n  blockReferences?: (ClientBlock | string)[]\n  blocks: ClientBlock[]\n  labels?: LabelsClient\n} & FieldBaseClient &\n  Pick<BlocksField, 'maxRows' | 'minRows' | 'type'>\n\nexport type PointField = {\n  admin?: {\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<PointFieldErrorClientComponent | PointFieldErrorServerComponent>\n      Label?: CustomComponent<PointFieldLabelClientComponent | PointFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    placeholder?: Record<string, string> | string\n    step?: number\n  } & FieldAdmin\n  type: 'point'\n  validate?: PointFieldValidation\n} & Omit<FieldBase, 'validate'>\n\nexport type PointFieldClient = {\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  admin?: AdminClient & Pick<PointField['admin'], 'placeholder' | 'step'>\n} & FieldBaseClient &\n  Pick<PointField, 'type'>\n\n/**\n * A virtual field that loads in related collections by querying a relationship or upload field.\n */\nexport type JoinField = {\n  access?: {\n    create?: never\n    read?: FieldAccess\n    update?: never\n  }\n  admin?: {\n    allowCreate?: boolean\n    components?: {\n      afterInput?: CustomComponent[]\n      beforeInput?: CustomComponent[]\n      Error?: CustomComponent<JoinFieldErrorClientComponent | JoinFieldErrorServerComponent>\n      Label?: CustomComponent<JoinFieldLabelClientComponent | JoinFieldLabelServerComponent>\n    } & FieldAdmin['components']\n    defaultColumns?: string[]\n    disableBulkEdit?: never\n    disableRowTypes?: boolean\n    readOnly?: never\n  } & FieldAdmin\n  /**\n   * The slug of the collection to relate with.\n   */\n  collection: CollectionSlug | CollectionSlug[]\n  defaultLimit?: number\n  defaultSort?: Sort\n  defaultValue?: never\n  /**\n   * This does not need to be set and will be overridden by the relationship field's hasMany property.\n   */\n  hasMany?: boolean\n  hidden?: false\n  index?: never\n  /**\n   * This does not need to be set and will be overridden by the relationship field's localized property.\n   */\n  localized?: boolean\n  /**\n   * The maximum allowed depth to be permitted application-wide. This setting helps prevent against malicious queries.\n   *\n   * @see https://payloadcms.com/docs/getting-started/concepts#depth\n   *\n   * @default 1\n   */\n  maxDepth?: number\n  /**\n   * A string for the field in the collection being joined to.\n   */\n  on: string\n  /**\n   * If true, enables custom ordering for the collection with the relationship, and joined documents can be reordered via drag and drop.\n   * New documents are inserted at the end of the list according to this parameter.\n   *\n   * Under the hood, a field with {@link https://observablehq.com/@dgreensp/implementing-fractional-indexing|fractional indexing} is used to optimize inserts and reorderings.\n   *\n   * @default false\n   *\n   * @experimental There may be frequent breaking changes to this API\n   */\n  orderable?: boolean\n  sanitizedMany?: JoinField[]\n  type: 'join'\n  validate?: never\n  where?: Where\n} & FieldBase &\n  FieldGraphQLType\n\nexport type JoinFieldClient = {\n  admin?: AdminClient &\n    Pick<\n      JoinField['admin'],\n      // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n      'allowCreate' | 'defaultColumns' | 'disableBulkEdit' | 'disableRowTypes' | 'readOnly'\n    >\n} & { targetField: Pick<RelationshipFieldClient, 'relationTo'> } & FieldBaseClient &\n  Pick<\n    JoinField,\n    | 'collection'\n    | 'defaultLimit'\n    | 'defaultSort'\n    | 'index'\n    | 'maxDepth'\n    | 'on'\n    | 'orderable'\n    | 'type'\n    | 'where'\n  >\n\nexport type FlattenedBlock = {\n  flattenedFields: FlattenedField[]\n} & Block\n\nexport type FlattenedBlocksField = {\n  /**\n   * Like `blocks`, but allows you to also pass strings that are slugs of blocks defined in `config.blocks`.\n   *\n   * @todo `blockReferences` will be merged with `blocks` in 4.0\n   */\n  blockReferences?: (FlattenedBlock | string)[]\n  blocks: FlattenedBlock[]\n} & Omit<BlocksField, 'blockReferences' | 'blocks'>\n\nexport type FlattenedGroupField = {\n  flattenedFields: FlattenedField[]\n  name: string\n} & GroupField\n\nexport type FlattenedArrayField = {\n  flattenedFields: FlattenedField[]\n} & ArrayField\n\nexport type FlattenedTabAsField = {\n  flattenedFields: FlattenedField[]\n} & MarkRequired<TabAsField, 'name'>\n\nexport type FlattenedJoinField = {\n  targetField: RelationshipField | UploadField\n} & JoinField\n\nexport type FlattenedField =\n  | CheckboxField\n  | CodeField\n  | DateField\n  | EmailField\n  | FlattenedArrayField\n  | FlattenedBlocksField\n  | FlattenedGroupField\n  | FlattenedJoinField\n  | FlattenedTabAsField\n  | JSONField\n  | NumberField\n  | PointField\n  | RadioField\n  | RelationshipField\n  | RichTextField\n  | SelectField\n  | TextareaField\n  | TextField\n  | UploadField\nexport type Field =\n  | ArrayField\n  | BlocksField\n  | CheckboxField\n  | CodeField\n  | CollapsibleField\n  | DateField\n  | EmailField\n  | GroupField\n  | JoinField\n  | JSONField\n  | NumberField\n  | PointField\n  | RadioField\n  | RelationshipField\n  | RichTextField\n  | RowField\n  | SelectField\n  | TabsField\n  | TextareaField\n  | TextField\n  | UIField\n  | UploadField\n\nexport type ClientField =\n  | ArrayFieldClient\n  | BlocksFieldClient\n  | CheckboxFieldClient\n  | CodeFieldClient\n  | CollapsibleFieldClient\n  | DateFieldClient\n  | EmailFieldClient\n  | GroupFieldClient\n  | JoinFieldClient\n  | JSONFieldClient\n  | NumberFieldClient\n  | PointFieldClient\n  | RadioFieldClient\n  | RelationshipFieldClient\n  | RichTextFieldClient\n  | RowFieldClient\n  | SelectFieldClient\n  | TabsFieldClient\n  | TextareaFieldClient\n  | TextFieldClient\n  | UIFieldClient\n  | UploadFieldClient\n\nexport type ClientFieldProps =\n  | ArrayFieldClientProps\n  | BlocksFieldClientProps\n  | CheckboxFieldClientProps\n  | CodeFieldClientProps\n  | CollapsibleFieldClientProps\n  | DateFieldClientProps\n  | EmailFieldClientProps\n  | GroupFieldClientProps\n  | HiddenFieldProps\n  | JoinFieldClientProps\n  | JSONFieldClientProps\n  | NumberFieldClientProps\n  | PointFieldClientProps\n  | RadioFieldClientProps\n  | RelationshipFieldClientProps\n  | RichTextFieldClientProps\n  | RowFieldClientProps\n  | SelectFieldClientProps\n  | TabsFieldClientProps\n  | TextareaFieldClientProps\n  | TextFieldClientProps\n  | UploadFieldClientProps\n\ntype ExtractFieldTypes<T> = T extends { type: infer U } ? U : never\n\nexport type FieldTypes = ExtractFieldTypes<Field>\n\nexport type FieldAffectingData =\n  | ArrayField\n  | BlocksField\n  | CheckboxField\n  | CodeField\n  | DateField\n  | EmailField\n  | JoinField\n  | JSONField\n  | NamedGroupField\n  | NumberField\n  | PointField\n  | RadioField\n  | RelationshipField\n  | RichTextField\n  | SelectField\n  | TabAsField\n  | TextareaField\n  | TextField\n  | UploadField\n\nexport type FieldAffectingDataClient =\n  | ArrayFieldClient\n  | BlocksFieldClient\n  | CheckboxFieldClient\n  | CodeFieldClient\n  | DateFieldClient\n  | EmailFieldClient\n  | JoinFieldClient\n  | JSONFieldClient\n  | NamedGroupFieldClient\n  | NumberFieldClient\n  | PointFieldClient\n  | RadioFieldClient\n  | RelationshipFieldClient\n  | RichTextFieldClient\n  | SelectFieldClient\n  | TabAsFieldClient\n  | TextareaFieldClient\n  | TextFieldClient\n  | UploadFieldClient\n\nexport type NonPresentationalField =\n  | ArrayField\n  | BlocksField\n  | CheckboxField\n  | CodeField\n  | CollapsibleField\n  | DateField\n  | EmailField\n  | JSONField\n  | NamedGroupField\n  | NumberField\n  | PointField\n  | RadioField\n  | RelationshipField\n  | RichTextField\n  | RowField\n  | SelectField\n  | TabsField\n  | TextareaField\n  | TextField\n  | UploadField\n\nexport type NonPresentationalFieldClient =\n  | ArrayFieldClient\n  | BlocksFieldClient\n  | CheckboxFieldClient\n  | CodeFieldClient\n  | CollapsibleFieldClient\n  | DateFieldClient\n  | EmailFieldClient\n  | JSONFieldClient\n  | NamedGroupFieldClient\n  | NumberFieldClient\n  | PointFieldClient\n  | RadioFieldClient\n  | RelationshipFieldClient\n  | RichTextFieldClient\n  | RowFieldClient\n  | SelectFieldClient\n  | TabsFieldClient\n  | TextareaFieldClient\n  | TextFieldClient\n  | UploadFieldClient\n\nexport type FieldWithPath = {\n  path?: string\n} & Field\n\nexport type FieldWithPathClient = {\n  path?: string\n} & ClientField\n\nexport type FieldWithSubFields = ArrayField | CollapsibleField | GroupField | RowField\n\nexport type FieldWithSubFieldsClient =\n  | ArrayFieldClient\n  | CollapsibleFieldClient\n  | GroupFieldClient\n  | RowFieldClient\n\nexport type FieldPresentationalOnly = UIField\nexport type FieldPresentationalOnlyClient = UIFieldClient\n\nexport type FieldWithMany = RelationshipField | SelectField\nexport type FieldWithManyClient = RelationshipFieldClient | SelectFieldClient\n\nexport type FieldWithMaxDepth = RelationshipField | UploadField\nexport type FieldWithMaxDepthClient = JoinFieldClient | RelationshipFieldClient | UploadFieldClient\n\nexport function fieldHasSubFields<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? FieldWithSubFieldsClient : FieldWithSubFields) {\n  return (\n    field.type === 'group' ||\n    field.type === 'array' ||\n    field.type === 'row' ||\n    field.type === 'collapsible'\n  )\n}\n\nexport function fieldIsArrayType<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? ArrayFieldClient : ArrayField) {\n  return field.type === 'array'\n}\n\nexport function fieldIsBlockType<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? BlocksFieldClient : BlocksField) {\n  return field.type === 'blocks'\n}\n\nexport function fieldIsGroupType<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? GroupFieldClient : GroupField) {\n  return field.type === 'group'\n}\n\nexport function optionIsObject(option: Option): option is OptionObject {\n  return typeof option === 'object'\n}\n\nexport function optionsAreObjects(options: Option[]): options is OptionObject[] {\n  return Array.isArray(options) && typeof options?.[0] === 'object'\n}\n\nexport function optionIsValue(option: Option): option is string {\n  return typeof option === 'string'\n}\n\nexport function fieldSupportsMany<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? FieldWithManyClient : FieldWithMany) {\n  return field.type === 'select' || field.type === 'relationship' || field.type === 'upload'\n}\n\nexport function fieldHasMaxDepth<TField extends ClientField | Field>(\n  field: TField,\n): field is TField & (TField extends ClientField ? FieldWithMaxDepthClient : FieldWithMaxDepth) {\n  return (\n    (field.type === 'upload' || field.type === 'relationship' || field.type === 'join') &&\n    typeof field.maxDepth === 'number'\n  )\n}\n\nexport function fieldIsPresentationalOnly<\n  TField extends ClientField | Field | TabAsField | TabAsFieldClient,\n>(\n  field: TField,\n): field is TField & (TField extends ClientField | TabAsFieldClient ? UIFieldClient : UIField) {\n  return field.type === 'ui'\n}\n\nexport function fieldIsSidebar<TField extends ClientField | Field | TabAsField | TabAsFieldClient>(\n  field: TField,\n): field is { admin: { position: 'sidebar' } } & TField {\n  return 'admin' in field && 'position' in field.admin! && field.admin.position === 'sidebar'\n}\n\nexport function fieldIsID<TField extends ClientField | Field>(\n  field: TField,\n): field is { name: 'id' } & TField {\n  return 'name' in field && field.name === 'id'\n}\n\nexport function fieldIsHiddenOrDisabled<\n  TField extends ClientField | Field | TabAsField | TabAsFieldClient,\n>(field: TField): field is { admin: { hidden: true } } & TField {\n  return (\n    ('hidden' in field && field.hidden) ||\n    ('admin' in field && 'disabled' in field.admin! && field.admin.disabled!)\n  )\n}\n\nexport function fieldAffectsData<\n  TField extends ClientField | Field | TabAsField | TabAsFieldClient,\n>(\n  field: TField,\n): field is TField &\n  (TField extends ClientField | TabAsFieldClient ? FieldAffectingDataClient : FieldAffectingData) {\n  return 'name' in field && !fieldIsPresentationalOnly(field)\n}\n\nexport function tabHasName<TField extends ClientTab | Tab>(tab: TField): tab is NamedTab & TField {\n  return 'name' in tab\n}\n\nexport function groupHasName(\n  group: Partial<NamedGroupFieldClient>,\n): group is NamedGroupFieldClient {\n  return 'name' in group\n}\n\n/**\n * Check if a field has localized: true set. This does not check if a field *should*\n * be localized. To check if a field should be localized, use `fieldShouldBeLocalized`.\n *\n * @deprecated this will be removed or modified in v4.0, as `fieldIsLocalized` can easily lead to bugs due to\n * parent field localization not being taken into account.\n */\nexport function fieldIsLocalized(field: Field | Tab): boolean {\n  return 'localized' in field && field.localized!\n}\n\n/**\n * Similar to `fieldIsLocalized`, but returns `false` if any parent field is localized.\n */\nexport function fieldShouldBeLocalized({\n  field,\n  parentIsLocalized,\n}: {\n  field: ClientField | ClientTab | Field | Tab\n  parentIsLocalized: boolean\n}): boolean {\n  return (\n    'localized' in field &&\n    field.localized! &&\n    (!parentIsLocalized ||\n      process.env.NEXT_PUBLIC_PAYLOAD_COMPATIBILITY_allowLocalizedWithinLocalized === 'true')\n  )\n}\n\nexport function fieldIsVirtual(field: Field | Tab): boolean {\n  return 'virtual' in field && Boolean(field.virtual)\n}\n\nexport type HookName =\n  | 'afterChange'\n  | 'afterRead'\n  | 'beforeChange'\n  | 'beforeRead'\n  | 'beforeValidate'\n"], "names": ["valueIsValueWithRelation", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "field", "type", "fieldIsArrayType", "fieldIsBlockType", "fieldIsGroupType", "optionIsObject", "option", "optionsAreObjects", "options", "Array", "isArray", "optionIsValue", "fieldSupportsMany", "fieldHasMaxDepth", "max<PERSON><PERSON><PERSON>", "fieldIsPresentationalOnly", "fieldIsSidebar", "admin", "position", "fieldIsID", "name", "fieldIsHiddenOrDisabled", "hidden", "disabled", "fieldAffectsData", "tabHasName", "tab", "groupHasName", "group", "fieldIsLocalized", "localized", "fieldShouldBeLocalized", "parentIsLocalized", "process", "env", "NEXT_PUBLIC_PAYLOAD_COMPATIBILITY_allowLocalizedWithinLocalized", "fieldIsVirtual", "Boolean", "virtual"], "mappings": "AAAA,qDAAqD,GAuvCrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,SAASA,yBAAyBC,KAAc;IACrD,OAAOA,UAAU,QAAQ,OAAOA,UAAU,YAAY,gBAAgBA,SAAS,WAAWA;AAC5F;AA0pBO,SAASC,kBACdC,KAAa;IAEb,OACEA,MAAMC,IAAI,KAAK,WACfD,MAAMC,IAAI,KAAK,WACfD,MAAMC,IAAI,KAAK,SACfD,MAAMC,IAAI,KAAK;AAEnB;AAEO,SAASC,iBACdF,KAAa;IAEb,OAAOA,MAAMC,IAAI,KAAK;AACxB;AAEO,SAASE,iBACdH,KAAa;IAEb,OAAOA,MAAMC,IAAI,KAAK;AACxB;AAEO,SAASG,iBACdJ,KAAa;IAEb,OAAOA,MAAMC,IAAI,KAAK;AACxB;AAEO,SAASI,eAAeC,MAAc;IAC3C,OAAO,OAAOA,WAAW;AAC3B;AAEO,SAASC,kBAAkBC,OAAiB;IACjD,OAAOC,MAAMC,OAAO,CAACF,YAAY,OAAOA,SAAS,CAAC,EAAE,KAAK;AAC3D;AAEO,SAASG,cAAcL,MAAc;IAC1C,OAAO,OAAOA,WAAW;AAC3B;AAEO,SAASM,kBACdZ,KAAa;IAEb,OAAOA,MAAMC,IAAI,KAAK,YAAYD,MAAMC,IAAI,KAAK,kBAAkBD,MAAMC,IAAI,KAAK;AACpF;AAEO,SAASY,iBACdb,KAAa;IAEb,OACGA,CAAAA,MAAMC,IAAI,KAAK,YAAYD,MAAMC,IAAI,KAAK,kBAAkBD,MAAMC,IAAI,KAAK,MAAK,KACjF,OAAOD,MAAMc,QAAQ,KAAK;AAE9B;AAEO,SAASC,0BAGdf,KAAa;IAEb,OAAOA,MAAMC,IAAI,KAAK;AACxB;AAEO,SAASe,eACdhB,KAAa;IAEb,OAAO,WAAWA,SAAS,cAAcA,MAAMiB,KAAK,IAAKjB,MAAMiB,KAAK,CAACC,QAAQ,KAAK;AACpF;AAEO,SAASC,UACdnB,KAAa;IAEb,OAAO,UAAUA,SAASA,MAAMoB,IAAI,KAAK;AAC3C;AAEO,SAASC,wBAEdrB,KAAa;IACb,OACG,YAAYA,SAASA,MAAMsB,MAAM,IACjC,WAAWtB,SAAS,cAAcA,MAAMiB,KAAK,IAAKjB,MAAMiB,KAAK,CAACM,QAAQ;AAE3E;AAEO,SAASC,iBAGdxB,KAAa;IAGb,OAAO,UAAUA,SAAS,CAACe,0BAA0Bf;AACvD;AAEO,SAASyB,WAA2CC,GAAW;IACpE,OAAO,UAAUA;AACnB;AAEO,SAASC,aACdC,KAAqC;IAErC,OAAO,UAAUA;AACnB;AASO,SAASC,iBAAiB7B,KAAkB;IACjD,OAAO,eAAeA,SAASA,MAAM8B,SAAS;AAChD;AAKO,SAASC,uBAAuB,EACrC/B,KAAK,EACLgC,iBAAiB,EAIlB;IACC,OACE,eAAehC,SACfA,MAAM8B,SAAS,IACd,CAAA,CAACE,qBACAC,QAAQC,GAAG,CAACC,+DAA+D,KAAK,MAAK;AAE3F;AAEO,SAASC,eAAepC,KAAkB;IAC/C,OAAO,aAAaA,SAASqC,QAAQrC,MAAMsC,OAAO;AACpD", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/config/client.ts"], "sourcesContent": ["// @ts-strict-ignore\n/* eslint-disable perfectionist/sort-switch-case */\n// Keep perfectionist/sort-switch-case disabled - it incorrectly messes up the ordering of the switch cases, causing it to break\nimport type { I18nClient, TFunction } from '@payloadcms/translations'\n\nimport type {\n  AdminClient,\n  ArrayFieldClient,\n  Block,\n  BlockJSX,\n  BlocksFieldClient,\n  ClientBlock,\n  ClientField,\n  Field,\n  FieldBase,\n  JoinFieldClient,\n  LabelsClient,\n  RadioFieldClient,\n  RowFieldClient,\n  SelectFieldClient,\n  TabsFieldClient,\n} from '../../fields/config/types.js'\nimport type { Payload } from '../../types/index.js'\n\nimport { getFromImportMap } from '../../bin/generateImportMap/utilities/getFromImportMap.js'\nimport { MissingEditorProp } from '../../errors/MissingEditorProp.js'\nimport { fieldAffectsData } from '../../fields/config/types.js'\nimport { flattenTopLevelFields, type ImportMap } from '../../index.js'\n\n// Should not be used - <PERSON>lient<PERSON>ield should be used instead. This is why we don't export ClientField, we don't want people\n// to accidentally use it instead of ClientField and get confused\n\nexport { ClientField }\n\nexport type ServerOnlyFieldProperties =\n  | 'dbName' // can be a function\n  | 'editor' // This is a `richText` only property\n  | 'enumName' // can be a function\n  | 'filterOptions' // This is a `relationship`, `upload`, and `select` only property\n  | 'graphQL'\n  | 'label'\n  | 'typescriptSchema'\n  | 'validate'\n  | keyof Pick<FieldBase, 'access' | 'custom' | 'defaultValue' | 'hooks'>\n\nexport type ServerOnlyFieldAdminProperties = keyof Pick<\n  FieldBase['admin'],\n  // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n  'components' | 'condition'\n>\n\nconst serverOnlyFieldProperties: Partial<ServerOnlyFieldProperties>[] = [\n  'hooks',\n  'access',\n  'validate',\n  'defaultValue',\n  'filterOptions', // This is a `relationship`, `upload`, and `select` only property\n  'editor', // This is a `richText` only property\n  'custom',\n  'typescriptSchema',\n  'dbName', // can be a function\n  'enumName', // can be a function\n  'graphQL', // client does not need graphQL\n  // the following props are handled separately (see below):\n  // `label`\n  // `fields`\n  // `blocks`\n  // `tabs`\n  // `admin`\n]\n\nconst serverOnlyFieldAdminProperties: Partial<ServerOnlyFieldAdminProperties>[] = [\n  'condition',\n  'components',\n]\n\ntype FieldWithDescription = {\n  admin: AdminClient\n} & ClientField\n\nexport const createClientBlocks = ({\n  blocks,\n  defaultIDType,\n  i18n,\n  importMap,\n}: {\n  blocks: (Block | string)[]\n  defaultIDType: Payload['config']['db']['defaultIDType']\n  i18n: I18nClient\n  importMap: ImportMap\n}): (ClientBlock | string)[] | ClientBlock[] => {\n  const clientBlocks: (ClientBlock | string)[] = []\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i]!\n\n    if (typeof block === 'string') {\n      // Do not process blocks that are just strings - they are processed once in the client config\n      clientBlocks.push(block)\n      continue\n    }\n\n    const clientBlock: ClientBlock = {\n      slug: block.slug,\n      fields: [],\n    }\n    if (block.imageAltText) {\n      clientBlock.imageAltText = block.imageAltText\n    }\n    if (block.imageURL) {\n      clientBlock.imageURL = block.imageURL\n    }\n\n    if (block.admin?.custom || block.admin?.group) {\n      // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n      clientBlock.admin = {}\n      if (block.admin.custom) {\n        clientBlock.admin!.custom = block.admin.custom\n      }\n      if (block.admin.group) {\n        clientBlock.admin!.group = block.admin.group\n      }\n    }\n\n    if (block?.admin?.jsx) {\n      const jsxResolved = getFromImportMap<BlockJSX>({\n        importMap,\n        PayloadComponent: block.admin.jsx,\n        schemaPath: '',\n      })\n      clientBlock.jsx = jsxResolved\n    }\n\n    if (block?.admin?.disableBlockName) {\n      // Check for existing admin object, this way we don't have to spread it in\n      if (clientBlock.admin) {\n        clientBlock.admin.disableBlockName = block.admin.disableBlockName\n      } else {\n        // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n        clientBlock.admin = { disableBlockName: block.admin.disableBlockName }\n      }\n    }\n\n    if (block.labels) {\n      clientBlock.labels = {} as unknown as LabelsClient\n\n      if (block.labels.singular) {\n        if (typeof block.labels.singular === 'function') {\n          clientBlock.labels.singular = block.labels.singular({ i18n, t: i18n.t as TFunction })\n        } else {\n          clientBlock.labels.singular = block.labels.singular\n        }\n        if (typeof block.labels.plural === 'function') {\n          clientBlock.labels.plural = block.labels.plural({ i18n, t: i18n.t as TFunction })\n        } else {\n          clientBlock.labels.plural = block.labels.plural\n        }\n      }\n    }\n\n    clientBlock.fields = createClientFields({\n      defaultIDType,\n      fields: block.fields,\n      i18n,\n      importMap,\n    })\n\n    clientBlocks.push(clientBlock)\n  }\n\n  return clientBlocks\n}\n\nexport const createClientField = ({\n  defaultIDType,\n  field: incomingField,\n  i18n,\n  importMap,\n}: {\n  defaultIDType: Payload['config']['db']['defaultIDType']\n  field: Field\n  i18n: I18nClient\n  importMap: ImportMap\n}): ClientField => {\n  const clientField: ClientField = {} as ClientField\n\n  for (const key in incomingField) {\n    if (serverOnlyFieldProperties.includes(key as any)) {\n      continue\n    }\n\n    switch (key) {\n      case 'admin':\n        if (!incomingField.admin) {\n          break\n        }\n\n        clientField.admin = {} as AdminClient\n\n        for (const adminKey in incomingField.admin) {\n          if (serverOnlyFieldAdminProperties.includes(adminKey as any)) {\n            continue\n          }\n\n          switch (adminKey) {\n            case 'description':\n              if ('description' in incomingField.admin) {\n                if (typeof incomingField.admin?.description !== 'function') {\n                  ;(clientField as FieldWithDescription).admin.description =\n                    incomingField.admin.description\n                }\n              }\n\n              break\n\n            default:\n              ;(clientField.admin as any)[adminKey] =\n                incomingField.admin[adminKey as keyof typeof incomingField.admin]\n          }\n        }\n\n        break\n\n      case 'blocks':\n      case 'fields':\n      case 'tabs':\n        // Skip - we handle sub-fields in the switch below\n        break\n\n      case 'label':\n        //@ts-expect-error - would need to type narrow\n        if (typeof incomingField.label === 'function') {\n          //@ts-expect-error - would need to type narrow\n          clientField.label = incomingField.label({ i18n, t: i18n.t })\n        } else {\n          //@ts-expect-error - would need to type narrow\n          clientField.label = incomingField.label\n        }\n\n        break\n\n      default:\n        ;(clientField as any)[key] = incomingField[key as keyof Field]\n    }\n  }\n\n  switch (incomingField.type) {\n    case 'array': {\n      if (incomingField.labels) {\n        const field = clientField as unknown as ArrayFieldClient\n\n        field.labels = {} as unknown as LabelsClient\n\n        if (incomingField.labels.singular) {\n          if (typeof incomingField.labels.singular === 'function') {\n            field.labels.singular = incomingField.labels.singular({ i18n, t: i18n.t as TFunction })\n          } else {\n            field.labels.singular = incomingField.labels.singular\n          }\n          if (typeof incomingField.labels.plural === 'function') {\n            field.labels.plural = incomingField.labels.plural({ i18n, t: i18n.t as TFunction })\n          } else {\n            field.labels.plural = incomingField.labels.plural\n          }\n        }\n      }\n    }\n    // falls through\n    case 'collapsible':\n    case 'group':\n    case 'row': {\n      const field = clientField as unknown as RowFieldClient\n\n      if (!field.fields) {\n        field.fields = []\n      }\n\n      field.fields = createClientFields({\n        defaultIDType,\n        disableAddingID: incomingField.type !== 'array',\n        fields: incomingField.fields,\n        i18n,\n        importMap,\n      })\n\n      break\n    }\n\n    case 'blocks': {\n      const field = clientField as unknown as BlocksFieldClient\n\n      if (incomingField.labels) {\n        field.labels = {} as unknown as LabelsClient\n\n        if (incomingField.labels.singular) {\n          if (typeof incomingField.labels.singular === 'function') {\n            field.labels.singular = incomingField.labels.singular({ i18n, t: i18n.t as TFunction })\n          } else {\n            field.labels.singular = incomingField.labels.singular\n          }\n          if (typeof incomingField.labels.plural === 'function') {\n            field.labels.plural = incomingField.labels.plural({ i18n, t: i18n.t as TFunction })\n          } else {\n            field.labels.plural = incomingField.labels.plural\n          }\n        }\n      }\n\n      if (incomingField.blockReferences?.length) {\n        field.blockReferences = createClientBlocks({\n          blocks: incomingField.blockReferences,\n          defaultIDType,\n          i18n,\n          importMap,\n        })\n      }\n\n      if (incomingField.blocks?.length) {\n        field.blocks = createClientBlocks({\n          blocks: incomingField.blocks,\n          defaultIDType,\n          i18n,\n          importMap,\n        }) as ClientBlock[]\n      }\n\n      break\n    }\n\n    case 'join': {\n      const field = clientField as JoinFieldClient\n\n      field.targetField = {\n        relationTo: field.targetField?.relationTo,\n      }\n\n      break\n    }\n\n    case 'radio':\n    // falls through\n    case 'select': {\n      const field = clientField as RadioFieldClient | SelectFieldClient\n\n      if (incomingField.options?.length) {\n        for (let i = 0; i < incomingField.options.length; i++) {\n          const option = incomingField.options[i]\n\n          if (typeof option === 'object' && typeof option.label === 'function') {\n            if (!field.options) {\n              field.options = []\n            }\n\n            field.options[i] = {\n              label: option.label({ i18n, t: i18n.t as TFunction }),\n              value: option.value,\n            }\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'richText': {\n      if (!incomingField?.editor) {\n        throw new MissingEditorProp(incomingField) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n\n      if (typeof incomingField?.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      break\n    }\n\n    case 'tabs': {\n      const field = clientField as unknown as TabsFieldClient\n\n      if (incomingField.tabs?.length) {\n        field.tabs = []\n\n        for (let i = 0; i < incomingField.tabs.length; i++) {\n          const tab = incomingField.tabs[i]\n          const clientTab = {} as unknown as TabsFieldClient['tabs'][0]\n\n          for (const key in tab) {\n            if (serverOnlyFieldProperties.includes(key as any)) {\n              continue\n            }\n\n            const tabProp = tab[key as keyof typeof tab]\n\n            if (key === 'fields') {\n              clientTab.fields = createClientFields({\n                defaultIDType,\n                disableAddingID: true,\n                fields: tab.fields,\n                i18n,\n                importMap,\n              })\n            } else if (\n              (key === 'label' || key === 'description') &&\n              typeof tabProp === 'function'\n            ) {\n              // @ts-expect-error - vestiges of when tsconfig was not strict. Feel free to improve\n              clientTab[key] = tabProp({ t: i18n.t })\n            } else if (key === 'admin') {\n              clientTab.admin = {} as AdminClient\n\n              for (const adminKey in tab.admin) {\n                if (serverOnlyFieldAdminProperties.includes(adminKey as any)) {\n                  continue\n                }\n\n                switch (adminKey) {\n                  case 'description':\n                    if ('description' in tab.admin) {\n                      if (typeof tab.admin?.description === 'function') {\n                        clientTab.admin.description = tab.admin.description({\n                          i18n,\n                          t: i18n.t as TFunction,\n                        })\n                      } else {\n                        clientTab.admin.description = tab.admin.description\n                      }\n                    }\n\n                    break\n\n                  default:\n                    ;(clientTab.admin as any)[adminKey] =\n                      tab.admin[adminKey as keyof typeof tab.admin]\n                }\n              }\n            } else {\n              ;(clientTab as any)[key] = tabProp\n            }\n          }\n          field.tabs[i] = clientTab\n        }\n      }\n\n      break\n    }\n\n    default:\n      break\n  }\n\n  return clientField\n}\n\nexport const createClientFields = ({\n  defaultIDType,\n  disableAddingID,\n  fields,\n  i18n,\n  importMap,\n}: {\n  defaultIDType: Payload['config']['db']['defaultIDType']\n  disableAddingID?: boolean\n  fields: Field[]\n  i18n: I18nClient\n  importMap: ImportMap\n}): ClientField[] => {\n  const clientFields: ClientField[] = []\n\n  for (let i = 0; i < fields.length; i++) {\n    const field = fields[i]!\n\n    const clientField = createClientField({\n      defaultIDType,\n      field,\n      i18n,\n      importMap,\n    })\n\n    clientFields.push(clientField)\n  }\n\n  const hasID = flattenTopLevelFields(fields).some((f) => fieldAffectsData(f) && f.name === 'id')\n\n  if (!disableAddingID && !hasID) {\n    clientFields.push({\n      name: 'id',\n      type: defaultIDType,\n      admin: {\n        description: 'The unique identifier for this document',\n        disableBulkEdit: true,\n        disabled: true,\n        hidden: true,\n      },\n      hidden: true,\n      label: 'ID',\n    } as ClientField)\n  }\n\n  return clientFields\n}\n"], "names": ["getFromImportMap", "MissingEditorProp", "fieldAffectsData", "flattenTopLevelFields", "serverOnlyFieldProperties", "serverOnlyFieldAdminProperties", "createClientBlocks", "blocks", "defaultIDType", "i18n", "importMap", "clientBlocks", "i", "length", "block", "push", "clientBlock", "slug", "fields", "imageAltText", "imageURL", "admin", "custom", "group", "jsx", "jsxResolved", "PayloadComponent", "schemaPath", "disable<PERSON><PERSON><PERSON>ame", "labels", "singular", "t", "plural", "createClientFields", "createClientField", "field", "incomingField", "clientField", "key", "includes", "admin<PERSON><PERSON>", "description", "label", "type", "disableAddingID", "blockReferences", "targetField", "relationTo", "options", "option", "value", "editor", "Error", "tabs", "tab", "clientTab", "tabProp", "clientFields", "hasID", "some", "f", "name", "disableBulkEdit", "disabled", "hidden"], "mappings": "AAAA,oBAAoB;AACpB,iDAAiD,GACjD,gIAAgI;;;;;;;;;AAsBhI,SAASA,gBAAgB,QAAQ,4DAA2D;AAC5F,SAASC,iBAAiB,QAAQ,oCAAmC;AACrE,SAASC,gBAAgB,QAAQ,+BAA8B;AAC/D,SAASC,qBAAqB,QAAwB,iBAAgB;;;;;AAwBtE,MAAMC,4BAAkE;IACtE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CAOD;AAED,MAAMC,iCAA4E;IAChF;IACA;CACD;AAMM,MAAMC,qBAAqB,CAAC,EACjCC,MAAM,EACNC,aAAa,EACbC,IAAI,EACJC,SAAS,EAMV;IACC,MAAMC,eAAyC,EAAE;IACjD,IAAK,IAAIC,IAAI,GAAGA,IAAIL,OAAOM,MAAM,EAAED,IAAK;QACtC,MAAME,QAAQP,MAAM,CAACK,EAAE;QAEvB,IAAI,OAAOE,UAAU,UAAU;YAC7B,6FAA6F;YAC7FH,aAAaI,IAAI,CAACD;YAClB;QACF;QAEA,MAAME,cAA2B;YAC/BC,MAAMH,MAAMG,IAAI;YAChBC,QAAQ,EAAE;QACZ;QACA,IAAIJ,MAAMK,YAAY,EAAE;YACtBH,YAAYG,YAAY,GAAGL,MAAMK,YAAY;QAC/C;QACA,IAAIL,MAAMM,QAAQ,EAAE;YAClBJ,YAAYI,QAAQ,GAAGN,MAAMM,QAAQ;QACvC;QAEA,IAAIN,MAAMO,KAAK,EAAEC,UAAUR,MAAMO,KAAK,EAAEE,OAAO;YAC7C,oFAAoF;YACpFP,YAAYK,KAAK,GAAG,CAAC;YACrB,IAAIP,MAAMO,KAAK,CAACC,MAAM,EAAE;gBACtBN,YAAYK,KAAK,CAAEC,MAAM,GAAGR,MAAMO,KAAK,CAACC,MAAM;YAChD;YACA,IAAIR,MAAMO,KAAK,CAACE,KAAK,EAAE;gBACrBP,YAAYK,KAAK,CAAEE,KAAK,GAAGT,MAAMO,KAAK,CAACE,KAAK;YAC9C;QACF;QAEA,IAAIT,OAAOO,OAAOG,KAAK;YACrB,MAAMC,kBAAczB,0OAAAA,EAA2B;gBAC7CU;gBACAgB,kBAAkBZ,MAAMO,KAAK,CAACG,GAAG;gBACjCG,YAAY;YACd;YACAX,YAAYQ,GAAG,GAAGC;QACpB;QAEA,IAAIX,OAAOO,OAAOO,kBAAkB;YAClC,0EAA0E;YAC1E,IAAIZ,YAAYK,KAAK,EAAE;gBACrBL,YAAYK,KAAK,CAACO,gBAAgB,GAAGd,MAAMO,KAAK,CAACO,gBAAgB;YACnE,OAAO;gBACL,oFAAoF;gBACpFZ,YAAYK,KAAK,GAAG;oBAAEO,kBAAkBd,MAAMO,KAAK,CAACO,gBAAgB;gBAAC;YACvE;QACF;QAEA,IAAId,MAAMe,MAAM,EAAE;YAChBb,YAAYa,MAAM,GAAG,CAAC;YAEtB,IAAIf,MAAMe,MAAM,CAACC,QAAQ,EAAE;gBACzB,IAAI,OAAOhB,MAAMe,MAAM,CAACC,QAAQ,KAAK,YAAY;oBAC/Cd,YAAYa,MAAM,CAACC,QAAQ,GAAGhB,MAAMe,MAAM,CAACC,QAAQ,CAAC;wBAAErB;wBAAMsB,GAAGtB,KAAKsB,CAAC;oBAAc;gBACrF,OAAO;oBACLf,YAAYa,MAAM,CAACC,QAAQ,GAAGhB,MAAMe,MAAM,CAACC,QAAQ;gBACrD;gBACA,IAAI,OAAOhB,MAAMe,MAAM,CAACG,MAAM,KAAK,YAAY;oBAC7ChB,YAAYa,MAAM,CAACG,MAAM,GAAGlB,MAAMe,MAAM,CAACG,MAAM,CAAC;wBAAEvB;wBAAMsB,GAAGtB,KAAKsB,CAAC;oBAAc;gBACjF,OAAO;oBACLf,YAAYa,MAAM,CAACG,MAAM,GAAGlB,MAAMe,MAAM,CAACG,MAAM;gBACjD;YACF;QACF;QAEAhB,YAAYE,MAAM,GAAGe,mBAAmB;YACtCzB;YACAU,QAAQJ,MAAMI,MAAM;YACpBT;YACAC;QACF;QAEAC,aAAaI,IAAI,CAACC;IACpB;IAEA,OAAOL;AACT,EAAC;AAEM,MAAMuB,oBAAoB,CAAC,EAChC1B,aAAa,EACb2B,OAAOC,aAAa,EACpB3B,IAAI,EACJC,SAAS,EAMV;IACC,MAAM2B,cAA2B,CAAC;IAElC,IAAK,MAAMC,OAAOF,cAAe;QAC/B,IAAIhC,0BAA0BmC,QAAQ,CAACD,MAAa;YAClD;QACF;QAEA,OAAQA;YACN,KAAK;gBACH,IAAI,CAACF,cAAcf,KAAK,EAAE;oBACxB;gBACF;gBAEAgB,YAAYhB,KAAK,GAAG,CAAC;gBAErB,IAAK,MAAMmB,YAAYJ,cAAcf,KAAK,CAAE;oBAC1C,IAAIhB,+BAA+BkC,QAAQ,CAACC,WAAkB;wBAC5D;oBACF;oBAEA,OAAQA;wBACN,KAAK;4BACH,IAAI,iBAAiBJ,cAAcf,KAAK,EAAE;gCACxC,IAAI,OAAOe,cAAcf,KAAK,EAAEoB,gBAAgB,YAAY;;oCACxDJ,YAAqChB,KAAK,CAACoB,WAAW,GACtDL,cAAcf,KAAK,CAACoB,WAAW;gCACnC;4BACF;4BAEA;wBAEF;;4BACIJ,YAAYhB,KAAa,CAACmB,SAAS,GACnCJ,cAAcf,KAAK,CAACmB,SAA6C;oBACvE;gBACF;gBAEA;YAEF,KAAK;YACL,KAAK;YACL,KAAK;gBAEH;YAEF,KAAK;gBACH,8CAA8C;gBAC9C,IAAI,OAAOJ,cAAcM,KAAK,KAAK,YAAY;oBAC7C,8CAA8C;oBAC9CL,YAAYK,KAAK,GAAGN,cAAcM,KAAK,CAAC;wBAAEjC;wBAAMsB,GAAGtB,KAAKsB,CAAC;oBAAC;gBAC5D,OAAO;oBACL,8CAA8C;oBAC9CM,YAAYK,KAAK,GAAGN,cAAcM,KAAK;gBACzC;gBAEA;YAEF;;gBACIL,WAAmB,CAACC,IAAI,GAAGF,aAAa,CAACE,IAAmB;QAClE;IACF;IAEA,OAAQF,cAAcO,IAAI;QACxB,KAAK;YAAS;gBACZ,IAAIP,cAAcP,MAAM,EAAE;oBACxB,MAAMM,QAAQE;oBAEdF,MAAMN,MAAM,GAAG,CAAC;oBAEhB,IAAIO,cAAcP,MAAM,CAACC,QAAQ,EAAE;wBACjC,IAAI,OAAOM,cAAcP,MAAM,CAACC,QAAQ,KAAK,YAAY;4BACvDK,MAAMN,MAAM,CAACC,QAAQ,GAAGM,cAAcP,MAAM,CAACC,QAAQ,CAAC;gCAAErB;gCAAMsB,GAAGtB,KAAKsB,CAAC;4BAAc;wBACvF,OAAO;4BACLI,MAAMN,MAAM,CAACC,QAAQ,GAAGM,cAAcP,MAAM,CAACC,QAAQ;wBACvD;wBACA,IAAI,OAAOM,cAAcP,MAAM,CAACG,MAAM,KAAK,YAAY;4BACrDG,MAAMN,MAAM,CAACG,MAAM,GAAGI,cAAcP,MAAM,CAACG,MAAM,CAAC;gCAAEvB;gCAAMsB,GAAGtB,KAAKsB,CAAC;4BAAc;wBACnF,OAAO;4BACLI,MAAMN,MAAM,CAACG,MAAM,GAAGI,cAAcP,MAAM,CAACG,MAAM;wBACnD;oBACF;gBACF;YACF;QACA,gBAAgB;QAChB,KAAK;QACL,KAAK;QACL,KAAK;YAAO;gBACV,MAAMG,QAAQE;gBAEd,IAAI,CAACF,MAAMjB,MAAM,EAAE;oBACjBiB,MAAMjB,MAAM,GAAG,EAAE;gBACnB;gBAEAiB,MAAMjB,MAAM,GAAGe,mBAAmB;oBAChCzB;oBACAoC,iBAAiBR,cAAcO,IAAI,KAAK;oBACxCzB,QAAQkB,cAAclB,MAAM;oBAC5BT;oBACAC;gBACF;gBAEA;YACF;QAEA,KAAK;YAAU;gBACb,MAAMyB,QAAQE;gBAEd,IAAID,cAAcP,MAAM,EAAE;oBACxBM,MAAMN,MAAM,GAAG,CAAC;oBAEhB,IAAIO,cAAcP,MAAM,CAACC,QAAQ,EAAE;wBACjC,IAAI,OAAOM,cAAcP,MAAM,CAACC,QAAQ,KAAK,YAAY;4BACvDK,MAAMN,MAAM,CAACC,QAAQ,GAAGM,cAAcP,MAAM,CAACC,QAAQ,CAAC;gCAAErB;gCAAMsB,GAAGtB,KAAKsB,CAAC;4BAAc;wBACvF,OAAO;4BACLI,MAAMN,MAAM,CAACC,QAAQ,GAAGM,cAAcP,MAAM,CAACC,QAAQ;wBACvD;wBACA,IAAI,OAAOM,cAAcP,MAAM,CAACG,MAAM,KAAK,YAAY;4BACrDG,MAAMN,MAAM,CAACG,MAAM,GAAGI,cAAcP,MAAM,CAACG,MAAM,CAAC;gCAAEvB;gCAAMsB,GAAGtB,KAAKsB,CAAC;4BAAc;wBACnF,OAAO;4BACLI,MAAMN,MAAM,CAACG,MAAM,GAAGI,cAAcP,MAAM,CAACG,MAAM;wBACnD;oBACF;gBACF;gBAEA,IAAII,cAAcS,eAAe,EAAEhC,QAAQ;oBACzCsB,MAAMU,eAAe,GAAGvC,mBAAmB;wBACzCC,QAAQ6B,cAAcS,eAAe;wBACrCrC;wBACAC;wBACAC;oBACF;gBACF;gBAEA,IAAI0B,cAAc7B,MAAM,EAAEM,QAAQ;oBAChCsB,MAAM5B,MAAM,GAAGD,mBAAmB;wBAChCC,QAAQ6B,cAAc7B,MAAM;wBAC5BC;wBACAC;wBACAC;oBACF;gBACF;gBAEA;YACF;QAEA,KAAK;YAAQ;gBACX,MAAMyB,QAAQE;gBAEdF,MAAMW,WAAW,GAAG;oBAClBC,YAAYZ,MAAMW,WAAW,EAAEC;gBACjC;gBAEA;YACF;QAEA,KAAK;QACL,gBAAgB;QAChB,KAAK;YAAU;gBACb,MAAMZ,QAAQE;gBAEd,IAAID,cAAcY,OAAO,EAAEnC,QAAQ;oBACjC,IAAK,IAAID,IAAI,GAAGA,IAAIwB,cAAcY,OAAO,CAACnC,MAAM,EAAED,IAAK;wBACrD,MAAMqC,SAASb,cAAcY,OAAO,CAACpC,EAAE;wBAEvC,IAAI,OAAOqC,WAAW,YAAY,OAAOA,OAAOP,KAAK,KAAK,YAAY;4BACpE,IAAI,CAACP,MAAMa,OAAO,EAAE;gCAClBb,MAAMa,OAAO,GAAG,EAAE;4BACpB;4BAEAb,MAAMa,OAAO,CAACpC,EAAE,GAAG;gCACjB8B,OAAOO,OAAOP,KAAK,CAAC;oCAAEjC;oCAAMsB,GAAGtB,KAAKsB,CAAC;gCAAc;gCACnDmB,OAAOD,OAAOC,KAAK;4BACrB;wBACF;oBACF;gBACF;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAACd,eAAee,QAAQ;oBAC1B,MAAM,IAAIlD,6MAAAA,CAAkBmC,eAAe,8HAA8H;;gBAC3K;gBAEA,IAAI,OAAOA,eAAee,WAAW,YAAY;oBAC/C,MAAM,IAAIC,MAAM;gBAClB;gBAEA;YACF;QAEA,KAAK;YAAQ;gBACX,MAAMjB,QAAQE;gBAEd,IAAID,cAAciB,IAAI,EAAExC,QAAQ;oBAC9BsB,MAAMkB,IAAI,GAAG,EAAE;oBAEf,IAAK,IAAIzC,IAAI,GAAGA,IAAIwB,cAAciB,IAAI,CAACxC,MAAM,EAAED,IAAK;wBAClD,MAAM0C,MAAMlB,cAAciB,IAAI,CAACzC,EAAE;wBACjC,MAAM2C,YAAY,CAAC;wBAEnB,IAAK,MAAMjB,OAAOgB,IAAK;4BACrB,IAAIlD,0BAA0BmC,QAAQ,CAACD,MAAa;gCAClD;4BACF;4BAEA,MAAMkB,UAAUF,GAAG,CAAChB,IAAwB;4BAE5C,IAAIA,QAAQ,UAAU;gCACpBiB,UAAUrC,MAAM,GAAGe,mBAAmB;oCACpCzB;oCACAoC,iBAAiB;oCACjB1B,QAAQoC,IAAIpC,MAAM;oCAClBT;oCACAC;gCACF;4BACF,OAAO,IACJ4B,CAAAA,QAAQ,WAAWA,QAAQ,aAAY,KACxC,OAAOkB,YAAY,YACnB;gCACA,oFAAoF;gCACpFD,SAAS,CAACjB,IAAI,GAAGkB,QAAQ;oCAAEzB,GAAGtB,KAAKsB,CAAC;gCAAC;4BACvC,OAAO,IAAIO,QAAQ,SAAS;gCAC1BiB,UAAUlC,KAAK,GAAG,CAAC;gCAEnB,IAAK,MAAMmB,YAAYc,IAAIjC,KAAK,CAAE;oCAChC,IAAIhB,+BAA+BkC,QAAQ,CAACC,WAAkB;wCAC5D;oCACF;oCAEA,OAAQA;wCACN,KAAK;4CACH,IAAI,iBAAiBc,IAAIjC,KAAK,EAAE;gDAC9B,IAAI,OAAOiC,IAAIjC,KAAK,EAAEoB,gBAAgB,YAAY;oDAChDc,UAAUlC,KAAK,CAACoB,WAAW,GAAGa,IAAIjC,KAAK,CAACoB,WAAW,CAAC;wDAClDhC;wDACAsB,GAAGtB,KAAKsB,CAAC;oDACX;gDACF,OAAO;oDACLwB,UAAUlC,KAAK,CAACoB,WAAW,GAAGa,IAAIjC,KAAK,CAACoB,WAAW;gDACrD;4CACF;4CAEA;wCAEF;;4CACIc,UAAUlC,KAAa,CAACmB,SAAS,GACjCc,IAAIjC,KAAK,CAACmB,SAAmC;oCACnD;gCACF;4BACF,OAAO;;gCACHe,SAAiB,CAACjB,IAAI,GAAGkB;4BAC7B;wBACF;wBACArB,MAAMkB,IAAI,CAACzC,EAAE,GAAG2C;oBAClB;gBACF;gBAEA;YACF;QAEA;YACE;IACJ;IAEA,OAAOlB;AACT,EAAC;AAEM,MAAMJ,qBAAqB,CAAC,EACjCzB,aAAa,EACboC,eAAe,EACf1B,MAAM,EACNT,IAAI,EACJC,SAAS,EAOV;IACC,MAAM+C,eAA8B,EAAE;IAEtC,IAAK,IAAI7C,IAAI,GAAGA,IAAIM,OAAOL,MAAM,EAAED,IAAK;QACtC,MAAMuB,QAAQjB,MAAM,CAACN,EAAE;QAEvB,MAAMyB,cAAcH,kBAAkB;YACpC1B;YACA2B;YACA1B;YACAC;QACF;QAEA+C,aAAa1C,IAAI,CAACsB;IACpB;IAEA,MAAMqB,YAAQvD,wNAAAA,EAAsBe,QAAQyC,IAAI,CAAC,CAACC,QAAM1D,0MAAAA,EAAiB0D,MAAMA,EAAEC,IAAI,KAAK;IAE1F,IAAI,CAACjB,mBAAmB,CAACc,OAAO;QAC9BD,aAAa1C,IAAI,CAAC;YAChB8C,MAAM;YACNlB,MAAMnC;YACNa,OAAO;gBACLoB,aAAa;gBACbqB,iBAAiB;gBACjBC,UAAU;gBACVC,QAAQ;YACV;YACAA,QAAQ;YACRtB,OAAO;QACT;IACF;IAEA,OAAOe;AACT,EAAC", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/getDefaultValue.ts"], "sourcesContent": ["import type { DefaultValue, JsonValue, PayloadRequest } from '../types/index.js'\n\nimport { deepCopyObjectSimple } from '../utilities/deepCopyObject.js'\n\ntype Args = {\n  defaultValue: DefaultValue\n  locale: string | undefined\n  req: PayloadRequest\n  user: PayloadRequest['user']\n  value?: JsonValue\n}\n\nexport const getDefaultValue = async ({\n  defaultValue,\n  locale,\n  req,\n  user,\n  value,\n}: Args): Promise<JsonValue> => {\n  if (typeof value !== 'undefined') {\n    return value\n  }\n\n  if (defaultValue && typeof defaultValue === 'function') {\n    return await defaultValue({ locale, req, user })\n  }\n\n  if (typeof defaultValue === 'object') {\n    return deepCopyObjectSimple(defaultValue)\n  }\n\n  return defaultValue\n}\n"], "names": ["deepCopyObjectSimple", "getDefaultValue", "defaultValue", "locale", "req", "user", "value"], "mappings": ";;;;AAEA,SAASA,oBAAoB,QAAQ,iCAAgC;;AAU9D,MAAMC,kBAAkB,OAAO,EACpCC,YAAY,EACZC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACA;IACL,IAAI,OAAOA,UAAU,aAAa;QAChC,OAAOA;IACT;IAEA,IAAIJ,gBAAgB,OAAOA,iBAAiB,YAAY;QACtD,OAAO,MAAMA,aAAa;YAAEC;YAAQC;YAAKC;QAAK;IAChD;IAEA,IAAI,OAAOH,iBAAiB,UAAU;QACpC,WAAOF,gNAAAA,EAAqBE;IAC9B;IAEA,OAAOA;AACT,EAAC", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/getFieldPaths.ts"], "sourcesContent": ["import type { ClientField, Field, Tab, TabAsFieldClient } from './config/types.js'\n\ntype Args = {\n  field: ClientField | Field | Tab | TabAsFieldClient\n  index: number\n  parentIndexPath: string\n  parentPath: string\n  parentSchemaPath: string\n}\n\ntype FieldPaths = {\n  /**\n   * A string of '-' separated indexes representing where\n   * to find this field in a given field schema array.\n   * It will always be complete and accurate.\n   */\n  indexPath: string\n  /**\n   * Path for this field relative to its position in the data.\n   */\n  path: string\n  /**\n   * Path for this field relative to its position in the schema.\n   */\n  schemaPath: string\n}\n\nexport function getFieldPaths({\n  field,\n  index,\n  parentIndexPath,\n  parentPath,\n  parentSchemaPath,\n}: Args): FieldPaths {\n  if ('name' in field) {\n    return {\n      indexPath: `${parentIndexPath ? parentIndexPath + '-' : ''}${index}`,\n      path: `${parentPath ? parentPath + '.' : ''}${field.name}`,\n      schemaPath: `${parentSchemaPath ? parentSchemaPath + '.' : ''}${field.name}`,\n    }\n  }\n\n  const indexSuffix = `_index-${`${parentIndexPath ? parentIndexPath + '-' : ''}${index}`}`\n\n  return {\n    indexPath: `${parentIndexPath ? parentIndexPath + '-' : ''}${index}`,\n    path: `${parentPath ? parentPath + '.' : ''}${indexSuffix}`,\n    schemaPath: `${parentSchemaPath ? parentSchemaPath + '.' : ''}${indexSuffix}`,\n  }\n}\n\n/**\n * @deprecated - will be removed in 4.0. Use `getFieldPaths` instead.\n */\nexport function getFieldPathsModified({\n  field,\n  index,\n  parentIndexPath,\n  parentPath,\n  parentSchemaPath,\n}: Args): FieldPaths {\n  const parentPathSegments = parentPath.split('.')\n\n  const parentIsUnnamed = parentPathSegments[parentPathSegments.length - 1]!.startsWith('_index-')\n\n  const parentWithoutIndex = parentIsUnnamed\n    ? parentPathSegments.slice(0, -1).join('.')\n    : parentPath\n\n  const parentPathToUse = parentIsUnnamed ? parentWithoutIndex : parentPath\n\n  if ('name' in field) {\n    return {\n      indexPath: '',\n      path: `${parentPathToUse ? parentPathToUse + '.' : ''}${field.name}`,\n      schemaPath: `${parentSchemaPath ? parentSchemaPath + '.' : ''}${field.name}`,\n    }\n  }\n\n  const indexSuffix = `_index-${`${parentIndexPath ? parentIndexPath + '-' : ''}${index}`}`\n\n  return {\n    indexPath: `${parentIndexPath ? parentIndexPath + '-' : ''}${index}`,\n    path: `${parentPathToUse ? parentPathToUse + '.' : ''}${indexSuffix}`,\n    schemaPath: `${!parentIsUnnamed && parentSchemaPath ? parentSchemaPath + '.' : ''}${indexSuffix}`,\n  }\n}\n"], "names": ["getFieldPaths", "field", "index", "parentIndexPath", "parentPath", "parentSchemaPath", "indexPath", "path", "name", "schemaPath", "indexSuffix", "getFieldPathsModified", "parentPathSegments", "split", "parentIsUnnamed", "length", "startsWith", "parentWithoutIndex", "slice", "join", "parentPathToUse"], "mappings": ";;;;;;AA2BO,SAASA,cAAc,EAC5BC,KAAK,EACLC,KAAK,EACLC,eAAe,EACfC,UAAU,EACVC,gBAAgB,EACX;IACL,IAAI,UAAUJ,OAAO;QACnB,OAAO;YACLK,WAAW,GAAGH,kBAAkBA,kBAAkB,MAAM,KAAKD,OAAO;YACpEK,MAAM,GAAGH,aAAaA,aAAa,MAAM,KAAKH,MAAMO,IAAI,EAAE;YAC1DC,YAAY,GAAGJ,mBAAmBA,mBAAmB,MAAM,KAAKJ,MAAMO,IAAI,EAAE;QAC9E;IACF;IAEA,MAAME,cAAc,CAAC,OAAO,EAAE,GAAGP,kBAAkBA,kBAAkB,MAAM,KAAKD,OAAO,EAAE;IAEzF,OAAO;QACLI,WAAW,GAAGH,kBAAkBA,kBAAkB,MAAM,KAAKD,OAAO;QACpEK,MAAM,GAAGH,aAAaA,aAAa,MAAM,KAAKM,aAAa;QAC3DD,YAAY,GAAGJ,mBAAmBA,mBAAmB,MAAM,KAAKK,aAAa;IAC/E;AACF;AAKO,SAASC,sBAAsB,EACpCV,KAAK,EACLC,KAAK,EACLC,eAAe,EACfC,UAAU,EACVC,gBAAgB,EACX;IACL,MAAMO,qBAAqBR,WAAWS,KAAK,CAAC;IAE5C,MAAMC,kBAAkBF,kBAAkB,CAACA,mBAAmBG,MAAM,GAAG,EAAE,CAAEC,UAAU,CAAC;IAEtF,MAAMC,qBAAqBH,kBACvBF,mBAAmBM,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC,OACrCf;IAEJ,MAAMgB,kBAAkBN,kBAAkBG,qBAAqBb;IAE/D,IAAI,UAAUH,OAAO;QACnB,OAAO;YACLK,WAAW;YACXC,MAAM,GAAGa,kBAAkBA,kBAAkB,MAAM,KAAKnB,MAAMO,IAAI,EAAE;YACpEC,YAAY,GAAGJ,mBAAmBA,mBAAmB,MAAM,KAAKJ,MAAMO,IAAI,EAAE;QAC9E;IACF;IAEA,MAAME,cAAc,CAAC,OAAO,EAAE,GAAGP,kBAAkBA,kBAAkB,MAAM,KAAKD,OAAO,EAAE;IAEzF,OAAO;QACLI,WAAW,GAAGH,kBAAkBA,kBAAkB,MAAM,KAAKD,OAAO;QACpEK,MAAM,GAAGa,kBAAkBA,kBAAkB,MAAM,KAAKV,aAAa;QACrED,YAAY,GAAG,CAACK,mBAAmBT,mBAAmBA,mBAAmB,MAAM,KAAKK,aAAa;IACnG;AACF", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterRead/relationshipPopulationPromise.ts"], "sourcesContent": ["import type { PayloadRequest, PopulateType } from '../../../types/index.js'\nimport type { <PERSON><PERSON><PERSON><PERSON>, RelationshipField, UploadField } from '../../config/types.js'\n\nimport { createDataloader<PERSON>ache<PERSON>ey } from '../../../collections/dataloader.js'\nimport { fieldHasMaxDepth, fieldShouldBeLocalized, fieldSupportsMany } from '../../config/types.js'\n\ntype PopulateArgs = {\n  currentDepth: number\n  data: Record<string, unknown>\n  dataReference: Record<string, any>\n  depth: number\n  draft: boolean\n  fallbackLocale: null | string\n  field: JoinField | RelationshipField | UploadField\n  index?: number\n  key?: string\n  locale: null | string\n  overrideAccess: boolean\n  populateArg?: PopulateType\n  req: PayloadRequest\n  showHiddenFields: boolean\n}\n\n// TODO: this function is mess, refactor logic\nconst populate = async ({\n  currentDepth,\n  data,\n  dataReference,\n  depth,\n  draft,\n  fallbackLocale,\n  field,\n  index,\n  key,\n  locale,\n  overrideAccess,\n  populateArg,\n  req,\n  showHiddenFields,\n}: PopulateArgs) => {\n  const dataToUpdate = dataReference\n  let relation\n  if (field.type === 'join') {\n    relation = Array.isArray(field.collection) ? data.relationTo : field.collection\n  } else {\n    relation = Array.isArray(field.relationTo) ? (data.relationTo as string) : field.relationTo\n  }\n\n  const relatedCollection =\n    req.payload.collections[relation as keyof typeof req.payload.collections]\n\n  if (relatedCollection) {\n    let id: unknown\n\n    if (field.type === 'join' && Array.isArray(field.collection)) {\n      id = data.value\n    } else if (field.type !== 'join' && Array.isArray(field.relationTo)) {\n      id = data.value\n    } else {\n      id = data\n    }\n\n    let relationshipValue\n    const shouldPopulate = depth && currentDepth <= depth\n\n    if (\n      typeof id !== 'string' &&\n      typeof id !== 'number' &&\n      typeof id?.toString === 'function' &&\n      typeof id !== 'object'\n    ) {\n      id = id.toString()\n    }\n\n    if (shouldPopulate) {\n      relationshipValue = await req.payloadDataLoader.load(\n        createDataloaderCacheKey({\n          collectionSlug: relatedCollection.config.slug,\n          currentDepth: currentDepth + 1,\n          depth,\n          docID: id as string,\n          draft,\n          fallbackLocale: fallbackLocale!,\n          locale: locale!,\n          overrideAccess,\n          populate: populateArg,\n          select:\n            populateArg?.[relatedCollection.config.slug] ??\n            relatedCollection.config.defaultPopulate,\n          showHiddenFields,\n          transactionID: req.transactionID!,\n        }),\n      )\n    }\n\n    if (!relationshipValue) {\n      // ids are visible regardless of access controls\n      relationshipValue = id\n    }\n    if (typeof index === 'number' && typeof key === 'string') {\n      if (field.type !== 'join' && Array.isArray(field.relationTo)) {\n        dataToUpdate[field.name][key][index].value = relationshipValue\n      } else {\n        if (field.type === 'join' && Array.isArray(field.collection)) {\n          dataToUpdate[field.name][key][index].value = relationshipValue\n        } else {\n          dataToUpdate[field.name][key][index] = relationshipValue\n        }\n      }\n    } else if (typeof index === 'number' || typeof key === 'string') {\n      if (field.type === 'join') {\n        if (!Array.isArray(field.collection)) {\n          dataToUpdate[field.name].docs[index ?? key!] = relationshipValue\n        } else {\n          dataToUpdate[field.name].docs[index ?? key!].value = relationshipValue\n        }\n      } else if (Array.isArray(field.relationTo)) {\n        dataToUpdate[field.name][index ?? key!].value = relationshipValue\n      } else {\n        dataToUpdate[field.name][index ?? key!] = relationshipValue\n      }\n    } else if (field.type !== 'join' && Array.isArray(field.relationTo)) {\n      dataToUpdate[field.name].value = relationshipValue\n    } else {\n      if (field.type === 'join' && Array.isArray(field.collection)) {\n        dataToUpdate[field.name].value = relationshipValue\n      } else {\n        dataToUpdate[field.name] = relationshipValue\n      }\n    }\n  }\n}\n\ntype PromiseArgs = {\n  currentDepth: number\n  depth: number\n  draft: boolean\n  fallbackLocale: null | string\n  field: JoinField | RelationshipField | UploadField\n  locale: null | string\n  overrideAccess: boolean\n  parentIsLocalized: boolean\n  populate?: PopulateType\n  req: PayloadRequest\n  showHiddenFields: boolean\n  siblingDoc: Record<string, any>\n}\n\nexport const relationshipPopulationPromise = async ({\n  currentDepth,\n  depth,\n  draft,\n  fallbackLocale,\n  field,\n  locale,\n  overrideAccess,\n  parentIsLocalized,\n  populate: populateArg,\n  req,\n  showHiddenFields,\n  siblingDoc,\n}: PromiseArgs): Promise<void> => {\n  const resultingDoc = siblingDoc\n  const populateDepth = fieldHasMaxDepth(field) && field.maxDepth! < depth ? field.maxDepth : depth\n  const rowPromises: Promise<void>[] = []\n\n  if (field.type === 'join' || (fieldSupportsMany(field) && field.hasMany)) {\n    if (\n      fieldShouldBeLocalized({ field, parentIsLocalized }) &&\n      locale === 'all' &&\n      typeof siblingDoc[field.name] === 'object' &&\n      siblingDoc[field.name] !== null\n    ) {\n      Object.keys(siblingDoc[field.name]).forEach((localeKey) => {\n        if (Array.isArray(siblingDoc[field.name][localeKey])) {\n          siblingDoc[field.name][localeKey].forEach((_relatedDoc: any, index: number) => {\n            const rowPromise = async () => {\n              await populate({\n                currentDepth,\n                data: siblingDoc[field.name][localeKey][index],\n                dataReference: resultingDoc,\n                depth: populateDepth!,\n                draft,\n                fallbackLocale,\n                field,\n                index,\n                key: localeKey,\n                locale,\n                overrideAccess,\n                populateArg,\n                req,\n                showHiddenFields,\n              })\n            }\n            rowPromises.push(rowPromise())\n          })\n        }\n      })\n    } else if (\n      Array.isArray(siblingDoc[field.name]) ||\n      Array.isArray(siblingDoc[field.name]?.docs)\n    ) {\n      ;(Array.isArray(siblingDoc[field.name])\n        ? siblingDoc[field.name]\n        : siblingDoc[field.name].docs\n      ).forEach((relatedDoc: any, index: number) => {\n        const rowPromise = async () => {\n          if (relatedDoc) {\n            await populate({\n              currentDepth,\n              data:\n                !(field.type === 'join' && Array.isArray(field.collection)) && relatedDoc?.id\n                  ? relatedDoc.id\n                  : relatedDoc,\n              dataReference: resultingDoc,\n              depth: populateDepth!,\n              draft,\n              fallbackLocale,\n              field,\n              index,\n              locale,\n              overrideAccess,\n              populateArg,\n              req,\n              showHiddenFields,\n            })\n          }\n        }\n\n        rowPromises.push(rowPromise())\n      })\n    }\n  } else if (\n    field.localized &&\n    locale === 'all' &&\n    typeof siblingDoc[field.name] === 'object' &&\n    siblingDoc[field.name] !== null\n  ) {\n    Object.keys(siblingDoc[field.name]).forEach((localeKey) => {\n      const rowPromise = async () => {\n        await populate({\n          currentDepth,\n          data: siblingDoc[field.name][localeKey],\n          dataReference: resultingDoc,\n          depth: populateDepth!,\n          draft,\n          fallbackLocale,\n          field,\n          key: localeKey,\n          locale,\n          overrideAccess,\n          populateArg,\n          req,\n          showHiddenFields,\n        })\n      }\n      rowPromises.push(rowPromise())\n    })\n\n    await Promise.all(rowPromises)\n  } else if (siblingDoc[field.name]) {\n    await populate({\n      currentDepth,\n      data: siblingDoc[field.name],\n      dataReference: resultingDoc,\n      depth: populateDepth!,\n      draft,\n      fallbackLocale,\n      field,\n      locale,\n      overrideAccess,\n      populateArg,\n      req,\n      showHiddenFields,\n    })\n  }\n  await Promise.all(rowPromises)\n}\n"], "names": ["createDataloaderCache<PERSON>ey", "fieldHasMaxDepth", "fieldShouldBeLocalized", "fieldSupportsMany", "populate", "<PERSON><PERSON><PERSON><PERSON>", "data", "dataReference", "depth", "draft", "fallback<PERSON><PERSON><PERSON>", "field", "index", "key", "locale", "overrideAccess", "populateArg", "req", "show<PERSON><PERSON>den<PERSON><PERSON>s", "dataToUpdate", "relation", "type", "Array", "isArray", "collection", "relationTo", "relatedCollection", "payload", "collections", "id", "value", "relationshipValue", "shouldPopulate", "toString", "payloadDataLoader", "load", "collectionSlug", "config", "slug", "docID", "select", "defaultPopulate", "transactionID", "name", "docs", "relationshipPopulationPromise", "parentIsLocalized", "siblingDoc", "resultingDoc", "populateDepth", "max<PERSON><PERSON><PERSON>", "rowPromises", "hasMany", "Object", "keys", "for<PERSON>ach", "localeKey", "_relatedDoc", "row<PERSON>romise", "push", "relatedDoc", "localized", "Promise", "all"], "mappings": ";;;;AAGA,SAASA,wBAAwB,QAAQ,qCAAoC;AAC7E,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,iBAAiB,QAAQ,wBAAuB;;;AAmBnG,8CAA8C;AAC9C,MAAMC,WAAW,OAAO,EACtBC,YAAY,EACZC,IAAI,EACJC,aAAa,EACbC,KAAK,EACLC,KAAK,EACLC,cAAc,EACdC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,cAAc,EACdC,WAAW,EACXC,GAAG,EACHC,gBAAgB,EACH;IACb,MAAMC,eAAeZ;IACrB,IAAIa;IACJ,IAAIT,MAAMU,IAAI,KAAK,QAAQ;QACzBD,WAAWE,MAAMC,OAAO,CAACZ,MAAMa,UAAU,IAAIlB,KAAKmB,UAAU,GAAGd,MAAMa,UAAU;IACjF,OAAO;QACLJ,WAAWE,MAAMC,OAAO,CAACZ,MAAMc,UAAU,IAAKnB,KAAKmB,UAAU,GAAcd,MAAMc,UAAU;IAC7F;IAEA,MAAMC,oBACJT,IAAIU,OAAO,CAACC,WAAW,CAACR,SAAiD;IAE3E,IAAIM,mBAAmB;QACrB,IAAIG;QAEJ,IAAIlB,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMa,UAAU,GAAG;YAC5DK,KAAKvB,KAAKwB,KAAK;QACjB,OAAO,IAAInB,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMc,UAAU,GAAG;YACnEI,KAAKvB,KAAKwB,KAAK;QACjB,OAAO;YACLD,KAAKvB;QACP;QAEA,IAAIyB;QACJ,MAAMC,iBAAiBxB,SAASH,gBAAgBG;QAEhD,IACE,OAAOqB,OAAO,YACd,OAAOA,OAAO,YACd,OAAOA,IAAII,aAAa,cACxB,OAAOJ,OAAO,UACd;YACAA,KAAKA,GAAGI,QAAQ;QAClB;QAEA,IAAID,gBAAgB;YAClBD,oBAAoB,MAAMd,IAAIiB,iBAAiB,CAACC,IAAI,KAClDnC,kNAAAA,EAAyB;gBACvBoC,gBAAgBV,kBAAkBW,MAAM,CAACC,IAAI;gBAC7CjC,cAAcA,eAAe;gBAC7BG;gBACA+B,OAAOV;gBACPpB;gBACAC,gBAAgBA;gBAChBI,QAAQA;gBACRC;gBACAX,UAAUY;gBACVwB,QACExB,aAAa,CAACU,kBAAkBW,MAAM,CAACC,IAAI,CAAC,IAC5CZ,kBAAkBW,MAAM,CAACI,eAAe;gBAC1CvB;gBACAwB,eAAezB,IAAIyB,aAAa;YAClC;QAEJ;QAEA,IAAI,CAACX,mBAAmB;YACtB,gDAAgD;YAChDA,oBAAoBF;QACtB;QACA,IAAI,OAAOjB,UAAU,YAAY,OAAOC,QAAQ,UAAU;YACxD,IAAIF,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMc,UAAU,GAAG;gBAC5DN,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAAC9B,IAAI,CAACD,MAAM,CAACkB,KAAK,GAAGC;YAC/C,OAAO;gBACL,IAAIpB,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMa,UAAU,GAAG;oBAC5DL,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAAC9B,IAAI,CAACD,MAAM,CAACkB,KAAK,GAAGC;gBAC/C,OAAO;oBACLZ,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAAC9B,IAAI,CAACD,MAAM,GAAGmB;gBACzC;YACF;QACF,OAAO,IAAI,OAAOnB,UAAU,YAAY,OAAOC,QAAQ,UAAU;YAC/D,IAAIF,MAAMU,IAAI,KAAK,QAAQ;gBACzB,IAAI,CAACC,MAAMC,OAAO,CAACZ,MAAMa,UAAU,GAAG;oBACpCL,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAACC,IAAI,CAAChC,SAASC,IAAK,GAAGkB;gBACjD,OAAO;oBACLZ,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAACC,IAAI,CAAChC,SAASC,IAAK,CAACiB,KAAK,GAAGC;gBACvD;YACF,OAAO,IAAIT,MAAMC,OAAO,CAACZ,MAAMc,UAAU,GAAG;gBAC1CN,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAAC/B,SAASC,IAAK,CAACiB,KAAK,GAAGC;YAClD,OAAO;gBACLZ,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAAC/B,SAASC,IAAK,GAAGkB;YAC5C;QACF,OAAO,IAAIpB,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMc,UAAU,GAAG;YACnEN,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAACb,KAAK,GAAGC;QACnC,OAAO;YACL,IAAIpB,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMa,UAAU,GAAG;gBAC5DL,YAAY,CAACR,MAAMgC,IAAI,CAAC,CAACb,KAAK,GAAGC;YACnC,OAAO;gBACLZ,YAAY,CAACR,MAAMgC,IAAI,CAAC,GAAGZ;YAC7B;QACF;IACF;AACF;AAiBO,MAAMc,gCAAgC,OAAO,EAClDxC,YAAY,EACZG,KAAK,EACLC,KAAK,EACLC,cAAc,EACdC,KAAK,EACLG,MAAM,EACNC,cAAc,EACd+B,iBAAiB,EACjB1C,UAAUY,WAAW,EACrBC,GAAG,EACHC,gBAAgB,EAChB6B,UAAU,EACE;IACZ,MAAMC,eAAeD;IACrB,MAAME,oBAAgBhD,0MAAAA,EAAiBU,UAAUA,MAAMuC,QAAQ,GAAI1C,QAAQG,MAAMuC,QAAQ,GAAG1C;IAC5F,MAAM2C,cAA+B,EAAE;IAEvC,IAAIxC,MAAMU,IAAI,KAAK,cAAWlB,2MAAAA,EAAkBQ,UAAUA,MAAMyC,OAAO,EAAG;QACxE,QACElD,gNAAAA,EAAuB;YAAES;YAAOmC;QAAkB,MAClDhC,WAAW,SACX,OAAOiC,UAAU,CAACpC,MAAMgC,IAAI,CAAC,KAAK,YAClCI,UAAU,CAACpC,MAAMgC,IAAI,CAAC,KAAK,MAC3B;YACAU,OAAOC,IAAI,CAACP,UAAU,CAACpC,MAAMgC,IAAI,CAAC,EAAEY,OAAO,CAAC,CAACC;gBAC3C,IAAIlC,MAAMC,OAAO,CAACwB,UAAU,CAACpC,MAAMgC,IAAI,CAAC,CAACa,UAAU,GAAG;oBACpDT,UAAU,CAACpC,MAAMgC,IAAI,CAAC,CAACa,UAAU,CAACD,OAAO,CAAC,CAACE,aAAkB7C;wBAC3D,MAAM8C,aAAa;4BACjB,MAAMtD,SAAS;gCACbC;gCACAC,MAAMyC,UAAU,CAACpC,MAAMgC,IAAI,CAAC,CAACa,UAAU,CAAC5C,MAAM;gCAC9CL,eAAeyC;gCACfxC,OAAOyC;gCACPxC;gCACAC;gCACAC;gCACAC;gCACAC,KAAK2C;gCACL1C;gCACAC;gCACAC;gCACAC;gCACAC;4BACF;wBACF;wBACAiC,YAAYQ,IAAI,CAACD;oBACnB;gBACF;YACF;QACF,OAAO,IACLpC,MAAMC,OAAO,CAACwB,UAAU,CAACpC,MAAMgC,IAAI,CAAC,KACpCrB,MAAMC,OAAO,CAACwB,UAAU,CAACpC,MAAMgC,IAAI,CAAC,EAAEC,OACtC;;YACEtB,CAAAA,MAAMC,OAAO,CAACwB,UAAU,CAACpC,MAAMgC,IAAI,CAAC,IAClCI,UAAU,CAACpC,MAAMgC,IAAI,CAAC,GACtBI,UAAU,CAACpC,MAAMgC,IAAI,CAAC,CAACC,IAAG,EAC5BW,OAAO,CAAC,CAACK,YAAiBhD;gBAC1B,MAAM8C,aAAa;oBACjB,IAAIE,YAAY;wBACd,MAAMxD,SAAS;4BACbC;4BACAC,MACE,CAAEK,CAAAA,MAAMU,IAAI,KAAK,UAAUC,MAAMC,OAAO,CAACZ,MAAMa,UAAU,CAAA,KAAMoC,YAAY/B,KACvE+B,WAAW/B,EAAE,GACb+B;4BACNrD,eAAeyC;4BACfxC,OAAOyC;4BACPxC;4BACAC;4BACAC;4BACAC;4BACAE;4BACAC;4BACAC;4BACAC;4BACAC;wBACF;oBACF;gBACF;gBAEAiC,YAAYQ,IAAI,CAACD;YACnB;QACF;IACF,OAAO,IACL/C,MAAMkD,SAAS,IACf/C,WAAW,SACX,OAAOiC,UAAU,CAACpC,MAAMgC,IAAI,CAAC,KAAK,YAClCI,UAAU,CAACpC,MAAMgC,IAAI,CAAC,KAAK,MAC3B;QACAU,OAAOC,IAAI,CAACP,UAAU,CAACpC,MAAMgC,IAAI,CAAC,EAAEY,OAAO,CAAC,CAACC;YAC3C,MAAME,aAAa;gBACjB,MAAMtD,SAAS;oBACbC;oBACAC,MAAMyC,UAAU,CAACpC,MAAMgC,IAAI,CAAC,CAACa,UAAU;oBACvCjD,eAAeyC;oBACfxC,OAAOyC;oBACPxC;oBACAC;oBACAC;oBACAE,KAAK2C;oBACL1C;oBACAC;oBACAC;oBACAC;oBACAC;gBACF;YACF;YACAiC,YAAYQ,IAAI,CAACD;QACnB;QAEA,MAAMI,QAAQC,GAAG,CAACZ;IACpB,OAAO,IAAIJ,UAAU,CAACpC,MAAMgC,IAAI,CAAC,EAAE;QACjC,MAAMvC,SAAS;YACbC;YACAC,MAAMyC,UAAU,CAACpC,MAAMgC,IAAI,CAAC;YAC5BpC,eAAeyC;YACfxC,OAAOyC;YACPxC;YACAC;YACAC;YACAG;YACAC;YACAC;YACAC;YACAC;QACF;IACF;IACA,MAAM4C,QAAQC,GAAG,CAACZ;AACpB,EAAC", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterRead/virtualFieldPopulationPromise.ts"], "sourcesContent": ["import type { PayloadRequest } from '../../../types/index.js'\nimport type { FlattenedField } from '../../config/types.js'\n\nimport { createDataloaderCacheKey } from '../../../collections/dataloader.js'\n\nexport const virtualFieldPopulationPromise = async ({\n  name,\n  draft,\n  fallbackLocale,\n  fields,\n  hasMany,\n  locale,\n  overrideAccess,\n  ref,\n  req,\n  segments,\n  showHiddenFields,\n  siblingDoc,\n}: {\n  draft: boolean\n  fallbackLocale: string\n  fields: FlattenedField[]\n  hasMany?: boolean\n  locale: string\n  name: string\n  overrideAccess: boolean\n  ref: any\n  req: PayloadRequest\n  segments: string[]\n  shift?: boolean\n  showHiddenFields: boolean\n  siblingDoc: Record<string, unknown>\n}): Promise<void> => {\n  const currentSegment = segments.shift()\n\n  if (!currentSegment) {\n    return\n  }\n\n  const currentValue = ref[currentSegment]\n\n  if (typeof currentValue === 'undefined') {\n    return\n  }\n\n  // Final step\n  if (segments.length === 0) {\n    if (hasMany) {\n      if (!Array.isArray(siblingDoc[name])) {\n        siblingDoc[name] = []\n      }\n      ;(siblingDoc[name] as any[]).push(currentValue)\n    } else {\n      siblingDoc[name] = currentValue\n    }\n    return\n  }\n\n  const currentField = fields.find((each) => each.name === currentSegment)\n\n  if (!currentField) {\n    return\n  }\n\n  if (currentField.type === 'group' || currentField.type === 'tab') {\n    if (!currentValue || typeof currentValue !== 'object') {\n      return\n    }\n\n    return virtualFieldPopulationPromise({\n      name,\n      draft,\n      fallbackLocale,\n      fields: currentField.flattenedFields,\n      locale,\n      overrideAccess,\n      ref: currentValue,\n      req,\n      segments,\n      showHiddenFields,\n      siblingDoc,\n    })\n  }\n\n  if (\n    (currentField.type === 'relationship' || currentField.type === 'upload') &&\n    typeof currentField.relationTo === 'string'\n  ) {\n    const select = {}\n    let currentSelectRef: any = select\n    const currentFields = req.payload.collections[currentField.relationTo]?.config.flattenedFields\n\n    for (let i = 0; i < segments.length; i++) {\n      const field = currentFields?.find((each) => each.name === segments[i])\n\n      const shouldBreak =\n        i === segments.length - 1 || field?.type === 'relationship' || field?.type === 'upload'\n\n      currentSelectRef[segments[i]!] = shouldBreak ? true : {}\n      currentSelectRef = currentSelectRef[segments[i]!]\n\n      if (shouldBreak) {\n        break\n      }\n    }\n\n    if (currentField.hasMany) {\n      if (!Array.isArray(currentValue)) {\n        return\n      }\n\n      const docIDs = currentValue\n        .map((e) => {\n          if (!e) {\n            return null\n          }\n          if (typeof e === 'object') {\n            return e.id\n          }\n          return e\n        })\n        .filter((e) => typeof e === 'string' || typeof e === 'number')\n\n      if (segments[0] === 'id' && segments.length === 0) {\n        siblingDoc[name] = docIDs\n        return\n      }\n\n      const collectionSlug = currentField.relationTo\n\n      const populatedDocs = await Promise.all(\n        docIDs.map((docID) => {\n          return req.payloadDataLoader.load(\n            createDataloaderCacheKey({\n              collectionSlug,\n              currentDepth: 0,\n              depth: 0,\n              docID,\n              draft,\n              fallbackLocale,\n              locale,\n              overrideAccess,\n              select,\n              showHiddenFields,\n              transactionID: req.transactionID as number,\n            }),\n          )\n        }),\n      )\n\n      for (const doc of populatedDocs) {\n        if (!doc) {\n          continue\n        }\n\n        await virtualFieldPopulationPromise({\n          name,\n          draft,\n          fallbackLocale,\n          fields: req.payload.collections[currentField.relationTo]!.config.flattenedFields,\n          hasMany: true,\n          locale,\n          overrideAccess,\n          ref: doc,\n          req,\n          segments: [...segments],\n          showHiddenFields,\n          siblingDoc,\n        })\n      }\n\n      return\n    }\n\n    let docID: number | string\n\n    if (typeof currentValue === 'object' && currentValue) {\n      docID = currentValue.id\n    } else {\n      docID = currentValue\n    }\n\n    if (segments[0] === 'id' && segments.length === 0) {\n      siblingDoc[name] = docID\n      return\n    }\n\n    if (typeof docID !== 'string' && typeof docID !== 'number') {\n      return\n    }\n\n    const populatedDoc = await req.payloadDataLoader.load(\n      createDataloaderCacheKey({\n        collectionSlug: currentField.relationTo,\n        currentDepth: 0,\n        depth: 0,\n        docID,\n        draft,\n        fallbackLocale,\n        locale,\n        overrideAccess,\n        select,\n        showHiddenFields,\n        transactionID: req.transactionID as number,\n      }),\n    )\n\n    if (!populatedDoc) {\n      return\n    }\n\n    return virtualFieldPopulationPromise({\n      name,\n      draft,\n      fallbackLocale,\n      fields: req.payload.collections[currentField.relationTo]!.config.flattenedFields,\n      hasMany,\n      locale,\n      overrideAccess,\n      ref: populatedDoc,\n      req,\n      segments,\n      showHiddenFields,\n      siblingDoc,\n    })\n  }\n}\n"], "names": ["createDataloaderCache<PERSON>ey", "virtualFieldPopulationPromise", "name", "draft", "fallback<PERSON><PERSON><PERSON>", "fields", "hasMany", "locale", "overrideAccess", "ref", "req", "segments", "show<PERSON><PERSON>den<PERSON><PERSON>s", "siblingDoc", "currentSegment", "shift", "currentValue", "length", "Array", "isArray", "push", "current<PERSON><PERSON>", "find", "each", "type", "flattenedFields", "relationTo", "select", "currentSelectRef", "<PERSON><PERSON><PERSON>s", "payload", "collections", "config", "i", "field", "shouldBreak", "docIDs", "map", "e", "id", "filter", "collectionSlug", "populatedDocs", "Promise", "all", "docID", "payloadDataLoader", "load", "<PERSON><PERSON><PERSON><PERSON>", "depth", "transactionID", "doc", "populatedDoc"], "mappings": ";;;;AAGA,SAASA,wBAAwB,QAAQ,qCAAoC;;AAEtE,MAAMC,gCAAgC,OAAO,EAClDC,IAAI,EACJC,KAAK,EACLC,cAAc,EACdC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,cAAc,EACdC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EAeX;IACC,MAAMC,iBAAiBH,SAASI,KAAK;IAErC,IAAI,CAACD,gBAAgB;QACnB;IACF;IAEA,MAAME,eAAeP,GAAG,CAACK,eAAe;IAExC,IAAI,OAAOE,iBAAiB,aAAa;QACvC;IACF;IAEA,aAAa;IACb,IAAIL,SAASM,MAAM,KAAK,GAAG;QACzB,IAAIX,SAAS;YACX,IAAI,CAACY,MAAMC,OAAO,CAACN,UAAU,CAACX,KAAK,GAAG;gBACpCW,UAAU,CAACX,KAAK,GAAG,EAAE;YACvB;;YACEW,UAAU,CAACX,KAAK,CAAWkB,IAAI,CAACJ;QACpC,OAAO;YACLH,UAAU,CAACX,KAAK,GAAGc;QACrB;QACA;IACF;IAEA,MAAMK,eAAehB,OAAOiB,IAAI,CAAC,CAACC,OAASA,KAAKrB,IAAI,KAAKY;IAEzD,IAAI,CAACO,cAAc;QACjB;IACF;IAEA,IAAIA,aAAaG,IAAI,KAAK,WAAWH,aAAaG,IAAI,KAAK,OAAO;QAChE,IAAI,CAACR,gBAAgB,OAAOA,iBAAiB,UAAU;YACrD;QACF;QAEA,OAAOf,8BAA8B;YACnCC;YACAC;YACAC;YACAC,QAAQgB,aAAaI,eAAe;YACpClB;YACAC;YACAC,KAAKO;YACLN;YACAC;YACAC;YACAC;QACF;IACF;IAEA,IACGQ,CAAAA,aAAaG,IAAI,KAAK,kBAAkBH,aAAaG,IAAI,KAAK,QAAO,KACtE,OAAOH,aAAaK,UAAU,KAAK,UACnC;QACA,MAAMC,SAAS,CAAC;QAChB,IAAIC,mBAAwBD;QAC5B,MAAME,gBAAgBnB,IAAIoB,OAAO,CAACC,WAAW,CAACV,aAAaK,UAAU,CAAC,EAAEM,OAAOP;QAE/E,IAAK,IAAIQ,IAAI,GAAGA,IAAItB,SAASM,MAAM,EAAEgB,IAAK;YACxC,MAAMC,QAAQL,eAAeP,KAAK,CAACC,OAASA,KAAKrB,IAAI,KAAKS,QAAQ,CAACsB,EAAE;YAErE,MAAME,cACJF,MAAMtB,SAASM,MAAM,GAAG,KAAKiB,OAAOV,SAAS,kBAAkBU,OAAOV,SAAS;YAEjFI,gBAAgB,CAACjB,QAAQ,CAACsB,EAAE,CAAE,GAAGE,cAAc,OAAO,CAAC;YACvDP,mBAAmBA,gBAAgB,CAACjB,QAAQ,CAACsB,EAAE,CAAE;YAEjD,IAAIE,aAAa;gBACf;YACF;QACF;QAEA,IAAId,aAAaf,OAAO,EAAE;YACxB,IAAI,CAACY,MAAMC,OAAO,CAACH,eAAe;gBAChC;YACF;YAEA,MAAMoB,SAASpB,aACZqB,GAAG,CAAC,CAACC;gBACJ,IAAI,CAACA,GAAG;oBACN,OAAO;gBACT;gBACA,IAAI,OAAOA,MAAM,UAAU;oBACzB,OAAOA,EAAEC,EAAE;gBACb;gBACA,OAAOD;YACT,GACCE,MAAM,CAAC,CAACF,IAAM,OAAOA,MAAM,YAAY,OAAOA,MAAM;YAEvD,IAAI3B,QAAQ,CAAC,EAAE,KAAK,QAAQA,SAASM,MAAM,KAAK,GAAG;gBACjDJ,UAAU,CAACX,KAAK,GAAGkC;gBACnB;YACF;YAEA,MAAMK,iBAAiBpB,aAAaK,UAAU;YAE9C,MAAMgB,gBAAgB,MAAMC,QAAQC,GAAG,CACrCR,OAAOC,GAAG,CAAC,CAACQ;gBACV,OAAOnC,IAAIoC,iBAAiB,CAACC,IAAI,KAC/B/C,kNAAAA,EAAyB;oBACvByC;oBACAO,cAAc;oBACdC,OAAO;oBACPJ;oBACA1C;oBACAC;oBACAG;oBACAC;oBACAmB;oBACAf;oBACAsC,eAAexC,IAAIwC,aAAa;gBAClC;YAEJ;YAGF,KAAK,MAAMC,OAAOT,cAAe;gBAC/B,IAAI,CAACS,KAAK;oBACR;gBACF;gBAEA,MAAMlD,8BAA8B;oBAClCC;oBACAC;oBACAC;oBACAC,QAAQK,IAAIoB,OAAO,CAACC,WAAW,CAACV,aAAaK,UAAU,CAAC,CAAEM,MAAM,CAACP,eAAe;oBAChFnB,SAAS;oBACTC;oBACAC;oBACAC,KAAK0C;oBACLzC;oBACAC,UAAU;2BAAIA;qBAAS;oBACvBC;oBACAC;gBACF;YACF;YAEA;QACF;QAEA,IAAIgC;QAEJ,IAAI,OAAO7B,iBAAiB,YAAYA,cAAc;YACpD6B,QAAQ7B,aAAauB,EAAE;QACzB,OAAO;YACLM,QAAQ7B;QACV;QAEA,IAAIL,QAAQ,CAAC,EAAE,KAAK,QAAQA,SAASM,MAAM,KAAK,GAAG;YACjDJ,UAAU,CAACX,KAAK,GAAG2C;YACnB;QACF;QAEA,IAAI,OAAOA,UAAU,YAAY,OAAOA,UAAU,UAAU;YAC1D;QACF;QAEA,MAAMO,eAAe,MAAM1C,IAAIoC,iBAAiB,CAACC,IAAI,KACnD/C,kNAAAA,EAAyB;YACvByC,gBAAgBpB,aAAaK,UAAU;YACvCsB,cAAc;YACdC,OAAO;YACPJ;YACA1C;YACAC;YACAG;YACAC;YACAmB;YACAf;YACAsC,eAAexC,IAAIwC,aAAa;QAClC;QAGF,IAAI,CAACE,cAAc;YACjB;QACF;QAEA,OAAOnD,8BAA8B;YACnCC;YACAC;YACAC;YACAC,QAAQK,IAAIoB,OAAO,CAACC,WAAW,CAACV,aAAaK,UAAU,CAAC,CAAEM,MAAM,CAACP,eAAe;YAChFnB;YACAC;YACAC;YACAC,KAAK2C;YACL1C;YACAC;YACAC;YACAC;QACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterRead/promise.ts"], "sourcesContent": ["import type { RichTextAdapter } from '../../../admin/RichText.js'\nimport type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type {\n  JsonObject,\n  PayloadRequest,\n  PopulateType,\n  SelectMode,\n  SelectType,\n} from '../../../types/index.js'\nimport type { Block, Field, TabAsField } from '../../config/types.js'\nimport type { AfterReadArgs } from './index.js'\n\nimport { MissingEditorProp } from '../../../errors/index.js'\nimport { type RequestContext } from '../../../index.js'\nimport { getBlockSelect } from '../../../utilities/getBlockSelect.js'\nimport { stripUnselectedFields } from '../../../utilities/stripUnselectedFields.js'\nimport { fieldAffectsData, fieldShouldBeLocalized, tabHasName } from '../../config/types.js'\nimport { getDefaultValue } from '../../getDefaultValue.js'\nimport { getFieldPathsModified as getFieldPaths } from '../../getFieldPaths.js'\nimport { relationshipPopulationPromise } from './relationshipPopulationPromise.js'\nimport { traverseFields } from './traverseFields.js'\nimport { virtualFieldPopulationPromise } from './virtualFieldPopulationPromise.js'\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  currentDepth: number\n  depth: number\n  doc: JsonObject\n  draft: boolean\n  fallbackLocale: null | string\n  field: Field | TabAsField\n  fieldIndex: number\n  /**\n   * fieldPromises are used for things like field hooks. They should be awaited before awaiting populationPromises\n   */\n  fieldPromises: Promise<void>[]\n  findMany: boolean\n  global: null | SanitizedGlobalConfig\n  locale: null | string\n  overrideAccess: boolean\n  parentIndexPath: string\n  /**\n   * @todo make required in v4.0\n   */\n  parentIsLocalized?: boolean\n  parentPath: string\n  parentSchemaPath: string\n  populate?: PopulateType\n  populationPromises: Promise<void>[]\n  req: PayloadRequest\n  select?: SelectType\n  selectMode?: SelectMode\n  showHiddenFields: boolean\n  siblingDoc: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n  triggerAccessControl?: boolean\n  triggerHooks?: boolean\n} & Required<Pick<AfterReadArgs<JsonObject>, 'flattenLocales'>>\n\n// This function is responsible for the following actions, in order:\n// - Remove hidden fields from response\n// - Flatten locales into requested locale\n// - Sanitize outgoing data (point field, etc.)\n// - Execute field hooks\n// - Execute read access control\n// - Populate relationships\n\nexport const promise = async ({\n  blockData,\n  collection,\n  context,\n  currentDepth,\n  depth,\n  doc,\n  draft,\n  fallbackLocale,\n  field,\n  fieldIndex,\n  fieldPromises,\n  findMany,\n  flattenLocales,\n  global,\n  locale,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  populate,\n  populationPromises,\n  req,\n  select,\n  selectMode,\n  showHiddenFields,\n  siblingDoc,\n  siblingFields,\n  triggerAccessControl = true,\n  triggerHooks = true,\n}: Args): Promise<void> => {\n  const { indexPath, path, schemaPath } = getFieldPaths({\n    field,\n    index: fieldIndex,\n    parentIndexPath,\n    parentPath,\n    parentSchemaPath,\n  })\n\n  const fieldAffectsDataResult = fieldAffectsData(field)\n  const pathSegments = path ? path.split('.') : []\n  const schemaPathSegments = schemaPath ? schemaPath.split('.') : []\n  const indexPathSegments = indexPath ? indexPath.split('-').filter(Boolean)?.map(Number) : []\n  let removedFieldValue = false\n\n  if (\n    fieldAffectsDataResult &&\n    field.hidden &&\n    typeof siblingDoc[field.name!] !== 'undefined' &&\n    !showHiddenFields\n  ) {\n    removedFieldValue = true\n    delete siblingDoc[field.name!]\n  }\n\n  if (path !== 'id') {\n    const shouldContinue = stripUnselectedFields({\n      field,\n      select: select!,\n      selectMode: selectMode!,\n      siblingDoc,\n    })\n\n    if (!shouldContinue) {\n      return\n    }\n  }\n\n  const shouldHoistLocalizedValue: boolean = Boolean(\n    flattenLocales &&\n      fieldAffectsDataResult &&\n      typeof siblingDoc[field.name!] === 'object' &&\n      siblingDoc[field.name!] !== null &&\n      fieldShouldBeLocalized({ field, parentIsLocalized: parentIsLocalized! }) &&\n      locale !== 'all' &&\n      req.payload.config.localization,\n  )\n\n  if (fieldAffectsDataResult && shouldHoistLocalizedValue) {\n    // replace actual value with localized value before sanitizing\n    // { [locale]: fields } -> fields\n    const value = siblingDoc[field.name!][locale!]\n\n    let hoistedValue = value\n\n    if (fallbackLocale && fallbackLocale !== locale) {\n      const fallbackValue = siblingDoc[field.name!][fallbackLocale]\n      const isNullOrUndefined = typeof value === 'undefined' || value === null\n\n      if (fallbackValue) {\n        switch (field.type) {\n          case 'text':\n          case 'textarea': {\n            if (value === '' || isNullOrUndefined) {\n              hoistedValue = fallbackValue\n            }\n            break\n          }\n\n          default: {\n            if (isNullOrUndefined) {\n              hoistedValue = fallbackValue\n            }\n            break\n          }\n        }\n      }\n    }\n\n    siblingDoc[field.name!] = hoistedValue\n  }\n\n  // Sanitize outgoing field value\n  switch (field.type) {\n    case 'group': {\n      // Fill groups with empty objects so fields with hooks within groups can populate\n      // themselves virtually as necessary\n      if (fieldAffectsDataResult && typeof siblingDoc[field.name] === 'undefined') {\n        siblingDoc[field.name] = {}\n      }\n\n      break\n    }\n    case 'point': {\n      const pointDoc = siblingDoc[field.name] as Record<string, unknown>\n      if (Array.isArray(pointDoc?.coordinates) && pointDoc.coordinates.length === 2) {\n        siblingDoc[field.name] = pointDoc.coordinates\n      } else {\n        siblingDoc[field.name] = undefined\n      }\n\n      break\n    }\n\n    case 'richText': {\n      if (!field?.editor) {\n        throw new MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n      if (typeof field?.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      // Rich Text fields should use afterRead hooks to do population. The previous editor.populationPromises have been renamed to editor.graphQLPopulationPromises\n      break\n    }\n\n    case 'tabs': {\n      field.tabs.forEach((tab) => {\n        if (\n          tabHasName(tab) &&\n          (typeof siblingDoc[tab.name] === 'undefined' || siblingDoc[tab.name] === null)\n        ) {\n          siblingDoc[tab.name] = {}\n        }\n      })\n\n      break\n    }\n\n    default: {\n      break\n    }\n  }\n\n  // If locale is `all`, siblingDoc[field.name] will be an object mapping locales to values - locales won't be flattened.\n  // In this case, run the hook for each locale and value pair\n  const shouldRunHookOnAllLocales =\n    locale === 'all' &&\n    'name' in field &&\n    typeof field.name === 'string' &&\n    // If localized values were hoisted, siblingDoc[field.name] will not be an object mapping locales to values\n    // => Object.entries(siblingDoc[field.name]) will be the value of a single locale, not all locales\n    // => do not run the hook for each locale\n    !shouldHoistLocalizedValue &&\n    fieldShouldBeLocalized({ field, parentIsLocalized: parentIsLocalized! }) &&\n    typeof siblingDoc[field.name] === 'object'\n\n  if (fieldAffectsDataResult) {\n    // Execute hooks\n    if (triggerHooks && 'hooks' in field && field.hooks?.afterRead) {\n      for (const hook of field.hooks.afterRead) {\n        if (shouldRunHookOnAllLocales) {\n          const localesAndValues = Object.entries(siblingDoc[field.name])\n          await Promise.all(\n            localesAndValues.map(async ([localeKey, value]) => {\n              const hookedValue = await hook({\n                blockData,\n                collection,\n                context,\n                currentDepth,\n                data: doc,\n                depth,\n                draft,\n                field,\n                findMany,\n                global,\n                indexPath: indexPathSegments,\n                operation: 'read',\n                originalDoc: doc,\n                overrideAccess,\n                path: pathSegments,\n                req,\n                schemaPath: schemaPathSegments,\n                showHiddenFields,\n                siblingData: siblingDoc,\n                siblingFields: siblingFields!,\n                value,\n              })\n\n              if (hookedValue !== undefined) {\n                siblingDoc[field.name][localeKey] = hookedValue\n              }\n            }),\n          )\n        } else {\n          const hookedValue = await hook({\n            blockData,\n            collection,\n            context,\n            currentDepth,\n            data: doc,\n            depth,\n            draft,\n            field,\n            findMany,\n            global,\n            indexPath: indexPathSegments,\n            operation: 'read',\n            originalDoc: doc,\n            overrideAccess,\n            path: pathSegments,\n            req,\n            schemaPath: schemaPathSegments,\n            showHiddenFields,\n            siblingData: siblingDoc,\n            siblingFields: siblingFields!,\n            value: siblingDoc[field.name],\n          })\n\n          if (hookedValue !== undefined) {\n            siblingDoc[field.name] = hookedValue\n          }\n        }\n      }\n    }\n\n    if (\n      'virtual' in field &&\n      typeof field.virtual === 'string' &&\n      (!field.hidden || showHiddenFields)\n    ) {\n      populationPromises.push(\n        virtualFieldPopulationPromise({\n          name: field.name,\n          draft,\n          fallbackLocale: fallbackLocale!,\n          fields: (collection || global)!.flattenedFields,\n          locale: locale!,\n          overrideAccess,\n          ref: doc,\n          req,\n          segments: field.virtual.split('.'),\n          showHiddenFields,\n          siblingDoc,\n        }),\n      )\n    }\n\n    // Execute access control\n    let allowDefaultValue = true\n    if (triggerAccessControl && field.access && field.access.read) {\n      const canReadField = overrideAccess\n        ? true\n        : await field.access.read({\n            id: doc.id as number | string,\n            blockData,\n            data: doc,\n            doc,\n            req,\n            siblingData: siblingDoc,\n          })\n\n      if (!canReadField) {\n        allowDefaultValue = false\n        delete siblingDoc[field.name!]\n      }\n    }\n\n    // Set defaultValue on the field for globals being returned without being first created\n    // or collection documents created prior to having a default\n    if (\n      !removedFieldValue &&\n      allowDefaultValue &&\n      typeof siblingDoc[field.name!] === 'undefined' &&\n      typeof field.defaultValue !== 'undefined'\n    ) {\n      siblingDoc[field.name!] = await getDefaultValue({\n        defaultValue: field.defaultValue,\n        locale: locale!,\n        req,\n        user: req.user,\n        value: siblingDoc[field.name!],\n      })\n    }\n\n    if (field.type === 'relationship' || field.type === 'upload' || field.type === 'join') {\n      populationPromises.push(\n        relationshipPopulationPromise({\n          currentDepth,\n          depth,\n          draft,\n          fallbackLocale,\n          field,\n          locale,\n          overrideAccess,\n          parentIsLocalized: parentIsLocalized!,\n          populate,\n          req,\n          showHiddenFields,\n          siblingDoc,\n        }),\n      )\n    }\n  }\n\n  switch (field.type) {\n    case 'array': {\n      const rows = siblingDoc[field.name] as JsonObject\n\n      let arraySelect = select?.[field.name]\n\n      if (selectMode === 'include' && typeof arraySelect === 'object') {\n        arraySelect = {\n          ...arraySelect,\n          id: true,\n        }\n      }\n\n      if (Array.isArray(rows) && rows.length > 0) {\n        rows.forEach((row, rowIndex) => {\n          traverseFields({\n            blockData,\n            collection,\n            context,\n            currentDepth,\n            depth,\n            doc,\n            draft,\n            fallbackLocale,\n            fieldPromises,\n            fields: field.fields,\n            findMany,\n            flattenLocales,\n            global,\n            locale,\n            overrideAccess,\n            parentIndexPath: '',\n            parentIsLocalized: parentIsLocalized || field.localized,\n            parentPath: path + '.' + rowIndex,\n            parentSchemaPath: schemaPath,\n            populate,\n            populationPromises,\n            req,\n            select: typeof arraySelect === 'object' ? arraySelect : undefined,\n            selectMode,\n            showHiddenFields,\n            siblingDoc: row || {},\n            triggerAccessControl,\n            triggerHooks,\n          })\n        })\n      } else if (!shouldHoistLocalizedValue && typeof rows === 'object' && rows !== null) {\n        Object.values(rows).forEach((localeRows) => {\n          if (Array.isArray(localeRows)) {\n            localeRows.forEach((row, rowIndex) => {\n              traverseFields({\n                blockData,\n                collection,\n                context,\n                currentDepth,\n                depth,\n                doc,\n                draft,\n                fallbackLocale,\n                fieldPromises,\n                fields: field.fields,\n                findMany,\n                flattenLocales,\n                global,\n                locale,\n                overrideAccess,\n                parentIndexPath: '',\n                parentIsLocalized: parentIsLocalized || field.localized,\n                parentPath: path + '.' + rowIndex,\n                parentSchemaPath: schemaPath,\n                populate,\n                populationPromises,\n                req,\n                showHiddenFields,\n                siblingDoc: (row as JsonObject) || {},\n                triggerAccessControl,\n                triggerHooks,\n              })\n            })\n          }\n        })\n      } else if (shouldHoistLocalizedValue && (!rows || rows.length === 0)) {\n        siblingDoc[field.name] = null\n      } else if (field.hidden !== true || showHiddenFields === true) {\n        siblingDoc[field.name] = []\n      }\n      break\n    }\n\n    case 'blocks': {\n      const rows = siblingDoc[field.name]\n\n      if (Array.isArray(rows) && rows.length > 0) {\n        rows.forEach((row, rowIndex) => {\n          const blockTypeToMatch = (row as JsonObject).blockType\n\n          const block: Block | undefined =\n            req.payload.blocks[blockTypeToMatch] ??\n            ((field.blockReferences ?? field.blocks).find(\n              (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n            ) as Block | undefined)\n\n          const { blockSelect, blockSelectMode } = getBlockSelect({\n            block: block!,\n            // TODO: fix this\n            // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain\n            select: select?.[field.name]!,\n            selectMode: selectMode!,\n          })\n\n          if (block) {\n            traverseFields({\n              blockData: row,\n              collection,\n              context,\n              currentDepth,\n              depth,\n              doc,\n              draft,\n              fallbackLocale,\n              fieldPromises,\n              fields: block.fields,\n              findMany,\n              flattenLocales,\n              global,\n              locale,\n              overrideAccess,\n              parentIndexPath: '',\n              parentIsLocalized: parentIsLocalized || field.localized,\n              parentPath: path + '.' + rowIndex,\n              parentSchemaPath: schemaPath + '.' + block.slug,\n              populate,\n              populationPromises,\n              req,\n              select: typeof blockSelect === 'object' ? blockSelect : undefined,\n              selectMode: blockSelectMode,\n              showHiddenFields,\n              siblingDoc: (row as JsonObject) || {},\n              triggerAccessControl,\n              triggerHooks,\n            })\n          }\n        })\n      } else if (!shouldHoistLocalizedValue && typeof rows === 'object' && rows !== null) {\n        Object.values(rows).forEach((localeRows) => {\n          if (Array.isArray(localeRows)) {\n            localeRows.forEach((row, rowIndex) => {\n              const blockTypeToMatch = row.blockType\n\n              const block: Block | undefined =\n                req.payload.blocks[blockTypeToMatch] ??\n                ((field.blockReferences ?? field.blocks).find(\n                  (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n                ) as Block | undefined)\n\n              if (block) {\n                traverseFields({\n                  blockData: row,\n                  collection,\n                  context,\n                  currentDepth,\n                  depth,\n                  doc,\n                  draft,\n                  fallbackLocale,\n                  fieldPromises,\n                  fields: block.fields,\n                  findMany,\n                  flattenLocales,\n                  global,\n                  locale,\n                  overrideAccess,\n                  parentIndexPath: '',\n                  parentIsLocalized: parentIsLocalized || field.localized,\n                  parentPath: path + '.' + rowIndex,\n                  parentSchemaPath: schemaPath + '.' + block.slug,\n                  populate,\n                  populationPromises,\n                  req,\n                  showHiddenFields,\n                  siblingDoc: (row as JsonObject) || {},\n                  triggerAccessControl,\n                  triggerHooks,\n                })\n              }\n            })\n          }\n        })\n      } else if (shouldHoistLocalizedValue && (!rows || rows.length === 0)) {\n        siblingDoc[field.name] = null\n      } else if (field.hidden !== true || showHiddenFields === true) {\n        siblingDoc[field.name] = []\n      }\n\n      break\n    }\n\n    case 'collapsible':\n    case 'row': {\n      traverseFields({\n        blockData,\n        collection,\n        context,\n        currentDepth,\n        depth,\n        doc,\n        draft,\n        fallbackLocale,\n        fieldPromises,\n        fields: field.fields,\n        findMany,\n        flattenLocales,\n        global,\n        locale,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath: schemaPath,\n        populate,\n        populationPromises,\n        req,\n        select,\n        selectMode,\n        showHiddenFields,\n        siblingDoc,\n        triggerAccessControl,\n        triggerHooks,\n      })\n\n      break\n    }\n\n    case 'group': {\n      if (fieldAffectsDataResult) {\n        let groupDoc = siblingDoc[field.name] as JsonObject\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          groupDoc = {}\n        }\n\n        const groupSelect = select?.[field.name]\n\n        traverseFields({\n          blockData,\n          collection,\n          context,\n          currentDepth,\n          depth,\n          doc,\n          draft,\n          fallbackLocale,\n          fieldPromises,\n          fields: field.fields,\n          findMany,\n          flattenLocales,\n          global,\n          locale,\n          overrideAccess,\n          parentIndexPath: '',\n          parentIsLocalized: parentIsLocalized || field.localized,\n          parentPath: path,\n          parentSchemaPath: schemaPath,\n          populate,\n          populationPromises,\n          req,\n          select: typeof groupSelect === 'object' ? groupSelect : undefined,\n          selectMode,\n          showHiddenFields,\n          siblingDoc: groupDoc,\n          triggerAccessControl,\n          triggerHooks,\n        })\n      } else {\n        traverseFields({\n          blockData,\n          collection,\n          context,\n          currentDepth,\n          depth,\n          doc,\n          draft,\n          fallbackLocale,\n          fieldPromises,\n          fields: field.fields,\n          findMany,\n          flattenLocales,\n          global,\n          locale,\n          overrideAccess,\n          parentIndexPath: indexPath,\n          parentIsLocalized,\n          parentPath,\n          parentSchemaPath: schemaPath,\n          populate,\n          populationPromises,\n          req,\n          select,\n          selectMode,\n          showHiddenFields,\n          siblingDoc,\n          triggerAccessControl,\n          triggerHooks,\n        })\n      }\n\n      break\n    }\n\n    case 'richText': {\n      if (!field?.editor) {\n        throw new MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n\n      if (typeof field?.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      const editor: RichTextAdapter = field?.editor\n\n      if (editor?.hooks?.afterRead?.length) {\n        for (const hook of editor.hooks.afterRead) {\n          if (shouldRunHookOnAllLocales) {\n            const localesAndValues = Object.entries(siblingDoc[field.name])\n\n            await Promise.all(\n              localesAndValues.map(async ([locale, value]) => {\n                const hookedValue = await hook({\n                  collection,\n                  context,\n                  currentDepth,\n                  data: doc,\n                  depth,\n                  draft,\n                  fallbackLocale: fallbackLocale!,\n                  field,\n                  fieldPromises,\n                  findMany,\n                  flattenLocales,\n                  global,\n                  indexPath: indexPathSegments,\n                  locale,\n                  operation: 'read',\n                  originalDoc: doc,\n                  overrideAccess,\n                  parentIsLocalized: parentIsLocalized!,\n                  path: pathSegments,\n                  populate,\n                  populationPromises,\n                  req,\n                  schemaPath: schemaPathSegments,\n                  showHiddenFields,\n                  siblingData: siblingDoc,\n                  triggerAccessControl,\n                  triggerHooks,\n                  value,\n                })\n\n                if (hookedValue !== undefined) {\n                  siblingDoc[field.name][locale] = hookedValue\n                }\n              }),\n            )\n          } else {\n            const hookedValue = await hook({\n              collection,\n              context,\n              currentDepth,\n              data: doc,\n              depth,\n              draft,\n              fallbackLocale: fallbackLocale!,\n              field,\n              fieldPromises,\n              findMany,\n              flattenLocales,\n              global,\n              indexPath: indexPathSegments,\n              locale: locale!,\n              operation: 'read',\n              originalDoc: doc,\n              overrideAccess,\n              parentIsLocalized: parentIsLocalized!,\n              path: pathSegments,\n              populate,\n              populationPromises,\n              req,\n              schemaPath: schemaPathSegments,\n              showHiddenFields,\n              siblingData: siblingDoc,\n              triggerAccessControl,\n              triggerHooks,\n              value: siblingDoc[field.name],\n            })\n\n            if (hookedValue !== undefined) {\n              siblingDoc[field.name] = hookedValue\n            }\n          }\n        }\n      }\n      break\n    }\n\n    case 'tab': {\n      let tabDoc = siblingDoc\n      let tabSelect: SelectType | undefined\n\n      const isNamedTab = tabHasName(field)\n\n      if (isNamedTab) {\n        tabDoc = siblingDoc[field.name] as JsonObject\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          tabDoc = {}\n        }\n\n        if (typeof select?.[field.name] === 'object') {\n          tabSelect = select?.[field.name] as SelectType\n        }\n      } else {\n        tabSelect = select\n      }\n\n      traverseFields({\n        blockData,\n        collection,\n        context,\n        currentDepth,\n        depth,\n        doc,\n        draft,\n        fallbackLocale,\n        fieldPromises,\n        fields: field.fields,\n        findMany,\n        flattenLocales,\n        global,\n        locale,\n        overrideAccess,\n        parentIndexPath: isNamedTab ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedTab ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        populate,\n        populationPromises,\n        req,\n        select: tabSelect,\n        selectMode,\n        showHiddenFields,\n        siblingDoc: tabDoc,\n        triggerAccessControl,\n        triggerHooks,\n      })\n\n      break\n    }\n\n    case 'tabs': {\n      traverseFields({\n        blockData,\n        collection,\n        context,\n        currentDepth,\n        depth,\n        doc,\n        draft,\n        fallbackLocale,\n        fieldPromises,\n        fields: field.tabs.map((tab) => ({ ...tab, type: 'tab' })),\n        findMany,\n        flattenLocales,\n        global,\n        locale,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath: path,\n        parentSchemaPath: schemaPath,\n        populate,\n        populationPromises,\n        req,\n        select,\n        selectMode,\n        showHiddenFields,\n        siblingDoc,\n        triggerAccessControl,\n        triggerHooks,\n      })\n\n      break\n    }\n    default: {\n      break\n    }\n  }\n}\n"], "names": ["MissingEditorProp", "getBlockSelect", "stripUnselected<PERSON><PERSON>s", "fieldAffectsData", "fieldShouldBeLocalized", "tabHasName", "getDefaultValue", "getFieldPathsModified", "getFieldPaths", "relationshipPopulationPromise", "traverseFields", "virtualFieldPopulationPromise", "promise", "blockData", "collection", "context", "<PERSON><PERSON><PERSON><PERSON>", "depth", "doc", "draft", "fallback<PERSON><PERSON><PERSON>", "field", "fieldIndex", "fieldPromises", "find<PERSON>any", "flattenLocales", "global", "locale", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "populate", "populationPromises", "req", "select", "selectMode", "show<PERSON><PERSON>den<PERSON><PERSON>s", "siblingDoc", "siblingFields", "triggerAccessControl", "triggerHooks", "indexPath", "path", "schemaPath", "index", "fieldAffectsDataResult", "pathSegments", "split", "schemaPathSegments", "indexPathSegments", "filter", "Boolean", "map", "Number", "removedFieldValue", "hidden", "name", "shouldC<PERSON><PERSON>ue", "shouldHoistLocalizedValue", "payload", "config", "localization", "value", "hoistedValue", "fallback<PERSON><PERSON><PERSON>", "isNullOrUndefined", "type", "pointDoc", "Array", "isArray", "coordinates", "length", "undefined", "editor", "Error", "tabs", "for<PERSON>ach", "tab", "shouldRunHookOnAllLocales", "hooks", "afterRead", "hook", "localesAndValues", "Object", "entries", "Promise", "all", "localeKey", "hookedValue", "data", "operation", "originalDoc", "siblingData", "virtual", "push", "fields", "flattenedFields", "ref", "segments", "allowDefaultValue", "access", "read", "canReadField", "id", "defaultValue", "user", "rows", "arraySelect", "row", "rowIndex", "localized", "values", "localeRows", "blockTypeToMatch", "blockType", "block", "blocks", "blockReferences", "find", "curBlock", "slug", "blockSelect", "blockSelectMode", "groupDoc", "groupSelect", "tabDoc", "tabSelect", "isNamedTab"], "mappings": ";;;;AAaA,SAASA,iBAAiB,QAAQ,2BAA0B;AAE5D,SAASC,cAAc,QAAQ,uCAAsC;AACrE,SAASC,qBAAqB,QAAQ,8CAA6C;AACnF,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,UAAU,QAAQ,wBAAuB;AAC5F,SAASC,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,yBAAyBC,aAAa,QAAQ,yBAAwB;AAC/E,SAASC,6BAA6B,QAAQ,qCAAoC;AAClF,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,6BAA6B,QAAQ,qCAAoC;;;;;;;;;;AAmD3E,MAAMC,UAAU,OAAO,EAC5BC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,aAAa,EACbC,QAAQ,EACRC,cAAc,EACdC,MAAM,EACNC,MAAM,EACNC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,kBAAkB,EAClBC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,aAAa,EACbC,uBAAuB,IAAI,EAC3BC,eAAe,IAAI,EACd;IACL,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAE,OAAGrC,6MAAAA,EAAc;QACpDa;QACAyB,OAAOxB;QACPO;QACAE;QACAC;IACF;IAEA,MAAMe,6BAAyB5C,0MAAAA,EAAiBkB;IAChD,MAAM2B,eAAeJ,OAAOA,KAAKK,KAAK,CAAC,OAAO,EAAE;IAChD,MAAMC,qBAAqBL,aAAaA,WAAWI,KAAK,CAAC,OAAO,EAAE;IAClE,MAAME,oBAAoBR,YAAYA,UAAUM,KAAK,CAAC,KAAKG,MAAM,CAACC,UAAUC,IAAIC,UAAU,EAAE;IAC5F,IAAIC,oBAAoB;IAExB,IACET,0BACA1B,MAAMoC,MAAM,IACZ,OAAOlB,UAAU,CAAClB,MAAMqC,IAAI,CAAE,KAAK,eACnC,CAACpB,kBACD;QACAkB,oBAAoB;QACpB,OAAOjB,UAAU,CAAClB,MAAMqC,IAAI,CAAE;IAChC;IAEA,IAAId,SAAS,MAAM;QACjB,MAAMe,qBAAiBzD,wNAAAA,EAAsB;YAC3CmB;YACAe,QAAQA;YACRC,YAAYA;YACZE;QACF;QAEA,IAAI,CAACoB,gBAAgB;YACnB;QACF;IACF;IAEA,MAAMC,4BAAqCP,QACzC5B,kBACEsB,0BACA,OAAOR,UAAU,CAAClB,MAAMqC,IAAI,CAAE,KAAK,YACnCnB,UAAU,CAAClB,MAAMqC,IAAI,CAAE,KAAK,YAC5BtD,gNAAAA,EAAuB;QAAEiB;QAAOS,mBAAmBA;IAAmB,MACtEH,WAAW,SACXQ,IAAI0B,OAAO,CAACC,MAAM,CAACC,YAAY;IAGnC,IAAIhB,0BAA0Ba,2BAA2B;QACvD,8DAA8D;QAC9D,iCAAiC;QACjC,MAAMI,QAAQzB,UAAU,CAAClB,MAAMqC,IAAI,CAAE,CAAC/B,OAAQ;QAE9C,IAAIsC,eAAeD;QAEnB,IAAI5C,kBAAkBA,mBAAmBO,QAAQ;YAC/C,MAAMuC,gBAAgB3B,UAAU,CAAClB,MAAMqC,IAAI,CAAE,CAACtC,eAAe;YAC7D,MAAM+C,oBAAoB,OAAOH,UAAU,eAAeA,UAAU;YAEpE,IAAIE,eAAe;gBACjB,OAAQ7C,MAAM+C,IAAI;oBAChB,KAAK;oBACL,KAAK;wBAAY;4BACf,IAAIJ,UAAU,MAAMG,mBAAmB;gCACrCF,eAAeC;4BACjB;4BACA;wBACF;oBAEA;wBAAS;4BACP,IAAIC,mBAAmB;gCACrBF,eAAeC;4BACjB;4BACA;wBACF;gBACF;YACF;QACF;QAEA3B,UAAU,CAAClB,MAAMqC,IAAI,CAAE,GAAGO;IAC5B;IAEA,gCAAgC;IAChC,OAAQ5C,MAAM+C,IAAI;QAChB,KAAK;YAAS;gBACZ,iFAAiF;gBACjF,oCAAoC;gBACpC,IAAIrB,0BAA0B,OAAOR,UAAU,CAAClB,MAAMqC,IAAI,CAAC,KAAK,aAAa;oBAC3EnB,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAG,CAAC;gBAC5B;gBAEA;YACF;QACA,KAAK;YAAS;gBACZ,MAAMW,WAAW9B,UAAU,CAAClB,MAAMqC,IAAI,CAAC;gBACvC,IAAIY,MAAMC,OAAO,CAACF,UAAUG,gBAAgBH,SAASG,WAAW,CAACC,MAAM,KAAK,GAAG;oBAC7ElC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAGW,SAASG,WAAW;gBAC/C,OAAO;oBACLjC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAGgB;gBAC3B;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAACrD,OAAOsD,QAAQ;oBAClB,MAAM,IAAI3E,6MAAAA,CAAkBqB,OAAO,8HAA8H;;gBACnK;gBACA,IAAI,OAAOA,OAAOsD,WAAW,YAAY;oBACvC,MAAM,IAAIC,MAAM;gBAClB;gBAGA;YACF;QAEA,KAAK;YAAQ;gBACXvD,MAAMwD,IAAI,CAACC,OAAO,CAAC,CAACC;oBAClB,QACE1E,oMAAAA,EAAW0E,QACV,CAAA,OAAOxC,UAAU,CAACwC,IAAIrB,IAAI,CAAC,KAAK,eAAenB,UAAU,CAACwC,IAAIrB,IAAI,CAAC,KAAK,IAAG,GAC5E;wBACAnB,UAAU,CAACwC,IAAIrB,IAAI,CAAC,GAAG,CAAC;oBAC1B;gBACF;gBAEA;YACF;QAEA;YAAS;gBACP;YACF;IACF;IAEA,uHAAuH;IACvH,4DAA4D;IAC5D,MAAMsB,4BACJrD,WAAW,SACX,UAAUN,SACV,OAAOA,MAAMqC,IAAI,KAAK,YACtB,2GAA2G;IAC3G,kGAAkG;IAClG,yCAAyC;IACzC,CAACE,iCACDxD,gNAAAA,EAAuB;QAAEiB;QAAOS,mBAAmBA;IAAmB,MACtE,OAAOS,UAAU,CAAClB,MAAMqC,IAAI,CAAC,KAAK;IAEpC,IAAIX,wBAAwB;QAC1B,gBAAgB;QAChB,IAAIL,gBAAgB,WAAWrB,SAASA,MAAM4D,KAAK,EAAEC,WAAW;YAC9D,KAAK,MAAMC,QAAQ9D,MAAM4D,KAAK,CAACC,SAAS,CAAE;gBACxC,IAAIF,2BAA2B;oBAC7B,MAAMI,mBAAmBC,OAAOC,OAAO,CAAC/C,UAAU,CAAClB,MAAMqC,IAAI,CAAC;oBAC9D,MAAM6B,QAAQC,GAAG,CACfJ,iBAAiB9B,GAAG,CAAC,OAAO,CAACmC,WAAWzB,MAAM;wBAC5C,MAAM0B,cAAc,MAAMP,KAAK;4BAC7BtE;4BACAC;4BACAC;4BACAC;4BACA2E,MAAMzE;4BACND;4BACAE;4BACAE;4BACAG;4BACAE;4BACAiB,WAAWQ;4BACXyC,WAAW;4BACXC,aAAa3E;4BACbU;4BACAgB,MAAMI;4BACNb;4BACAU,YAAYK;4BACZZ;4BACAwD,aAAavD;4BACbC,eAAeA;4BACfwB;wBACF;wBAEA,IAAI0B,gBAAgBhB,WAAW;4BAC7BnC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,CAAC+B,UAAU,GAAGC;wBACtC;oBACF;gBAEJ,OAAO;oBACL,MAAMA,cAAc,MAAMP,KAAK;wBAC7BtE;wBACAC;wBACAC;wBACAC;wBACA2E,MAAMzE;wBACND;wBACAE;wBACAE;wBACAG;wBACAE;wBACAiB,WAAWQ;wBACXyC,WAAW;wBACXC,aAAa3E;wBACbU;wBACAgB,MAAMI;wBACNb;wBACAU,YAAYK;wBACZZ;wBACAwD,aAAavD;wBACbC,eAAeA;wBACfwB,OAAOzB,UAAU,CAAClB,MAAMqC,IAAI,CAAC;oBAC/B;oBAEA,IAAIgC,gBAAgBhB,WAAW;wBAC7BnC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAGgC;oBAC3B;gBACF;YACF;QACF;QAEA,IACE,aAAarE,SACb,OAAOA,MAAM0E,OAAO,KAAK,YACxB,CAAA,CAAC1E,MAAMoC,MAAM,IAAInB,gBAAe,GACjC;YACAJ,mBAAmB8D,IAAI,KACrBrF,2PAAAA,EAA8B;gBAC5B+C,MAAMrC,MAAMqC,IAAI;gBAChBvC;gBACAC,gBAAgBA;gBAChB6E,QAASnF,CAAAA,cAAcY,MAAK,EAAIwE,eAAe;gBAC/CvE,QAAQA;gBACRC;gBACAuE,KAAKjF;gBACLiB;gBACAiE,UAAU/E,MAAM0E,OAAO,CAAC9C,KAAK,CAAC;gBAC9BX;gBACAC;YACF;QAEJ;QAEA,yBAAyB;QACzB,IAAI8D,oBAAoB;QACxB,IAAI5D,wBAAwBpB,MAAMiF,MAAM,IAAIjF,MAAMiF,MAAM,CAACC,IAAI,EAAE;YAC7D,MAAMC,eAAe5E,iBACjB,OACA,MAAMP,MAAMiF,MAAM,CAACC,IAAI,CAAC;gBACtBE,IAAIvF,IAAIuF,EAAE;gBACV5F;gBACA8E,MAAMzE;gBACNA;gBACAiB;gBACA2D,aAAavD;YACf;YAEJ,IAAI,CAACiE,cAAc;gBACjBH,oBAAoB;gBACpB,OAAO9D,UAAU,CAAClB,MAAMqC,IAAI,CAAE;YAChC;QACF;QAEA,uFAAuF;QACvF,4DAA4D;QAC5D,IACE,CAACF,qBACD6C,qBACA,OAAO9D,UAAU,CAAClB,MAAMqC,IAAI,CAAE,KAAK,eACnC,OAAOrC,MAAMqF,YAAY,KAAK,aAC9B;YACAnE,UAAU,CAAClB,MAAMqC,IAAI,CAAE,GAAG,UAAMpD,yMAAAA,EAAgB;gBAC9CoG,cAAcrF,MAAMqF,YAAY;gBAChC/E,QAAQA;gBACRQ;gBACAwE,MAAMxE,IAAIwE,IAAI;gBACd3C,OAAOzB,UAAU,CAAClB,MAAMqC,IAAI,CAAE;YAChC;QACF;QAEA,IAAIrC,MAAM+C,IAAI,KAAK,kBAAkB/C,MAAM+C,IAAI,KAAK,YAAY/C,MAAM+C,IAAI,KAAK,QAAQ;YACrFlC,mBAAmB8D,IAAI,KACrBvF,2PAAAA,EAA8B;gBAC5BO;gBACAC;gBACAE;gBACAC;gBACAC;gBACAM;gBACAC;gBACAE,mBAAmBA;gBACnBG;gBACAE;gBACAG;gBACAC;YACF;QAEJ;IACF;IAEA,OAAQlB,MAAM+C,IAAI;QAChB,KAAK;YAAS;gBACZ,MAAMwC,OAAOrE,UAAU,CAAClB,MAAMqC,IAAI,CAAC;gBAEnC,IAAImD,cAAczE,QAAQ,CAACf,MAAMqC,IAAI,CAAC;gBAEtC,IAAIrB,eAAe,aAAa,OAAOwE,gBAAgB,UAAU;oBAC/DA,cAAc;wBACZ,GAAGA,WAAW;wBACdJ,IAAI;oBACN;gBACF;gBAEA,IAAInC,MAAMC,OAAO,CAACqC,SAASA,KAAKnC,MAAM,GAAG,GAAG;oBAC1CmC,KAAK9B,OAAO,CAAC,CAACgC,KAAKC;4BACjBrG,6NAAAA,EAAe;4BACbG;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAG;4BACA0E,QAAQ5E,MAAM4E,MAAM;4BACpBzE;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBT,MAAM2F,SAAS;4BACvDjF,YAAYa,OAAO,MAAMmE;4BACzB/E,kBAAkBa;4BAClBZ;4BACAC;4BACAC;4BACAC,QAAQ,OAAOyE,gBAAgB,WAAWA,cAAcnC;4BACxDrC;4BACAC;4BACAC,YAAYuE,OAAO,CAAC;4BACpBrE;4BACAC;wBACF;oBACF;gBACF,OAAO,IAAI,CAACkB,6BAA6B,OAAOgD,SAAS,YAAYA,SAAS,MAAM;oBAClFvB,OAAO4B,MAAM,CAACL,MAAM9B,OAAO,CAAC,CAACoC;wBAC3B,IAAI5C,MAAMC,OAAO,CAAC2C,aAAa;4BAC7BA,WAAWpC,OAAO,CAAC,CAACgC,KAAKC;oCACvBrG,6NAAAA,EAAe;oCACbG;oCACAC;oCACAC;oCACAC;oCACAC;oCACAC;oCACAC;oCACAC;oCACAG;oCACA0E,QAAQ5E,MAAM4E,MAAM;oCACpBzE;oCACAC;oCACAC;oCACAC;oCACAC;oCACAC,iBAAiB;oCACjBC,mBAAmBA,qBAAqBT,MAAM2F,SAAS;oCACvDjF,YAAYa,OAAO,MAAMmE;oCACzB/E,kBAAkBa;oCAClBZ;oCACAC;oCACAC;oCACAG;oCACAC,YAAauE,OAAsB,CAAC;oCACpCrE;oCACAC;gCACF;4BACF;wBACF;oBACF;gBACF,OAAO,IAAIkB,6BAA8B,CAAA,CAACgD,QAAQA,KAAKnC,MAAM,KAAK,CAAA,GAAI;oBACpElC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAG;gBAC3B,OAAO,IAAIrC,MAAMoC,MAAM,KAAK,QAAQnB,qBAAqB,MAAM;oBAC7DC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAG,EAAE;gBAC7B;gBACA;YACF;QAEA,KAAK;YAAU;gBACb,MAAMkD,OAAOrE,UAAU,CAAClB,MAAMqC,IAAI,CAAC;gBAEnC,IAAIY,MAAMC,OAAO,CAACqC,SAASA,KAAKnC,MAAM,GAAG,GAAG;oBAC1CmC,KAAK9B,OAAO,CAAC,CAACgC,KAAKC;wBACjB,MAAMI,mBAAoBL,IAAmBM,SAAS;wBAEtD,MAAMC,QACJlF,IAAI0B,OAAO,CAACyD,MAAM,CAACH,iBAAiB,IAClC9F,CAAAA,MAAMkG,eAAe,IAAIlG,MAAMiG,MAAK,EAAGE,IAAI,CAC3C,CAACC,WAAa,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKP;wBAGpE,MAAM,EAAEQ,WAAW,EAAEC,eAAe,EAAE,OAAG3H,0MAAAA,EAAe;4BACtDoH,OAAOA;4BACP,iBAAiB;4BACjB,kFAAkF;4BAClFjF,QAAQA,QAAQ,CAACf,MAAMqC,IAAI,CAAC;4BAC5BrB,YAAYA;wBACd;wBAEA,IAAIgF,OAAO;gCACT3G,6NAAAA,EAAe;gCACbG,WAAWiG;gCACXhG;gCACAC;gCACAC;gCACAC;gCACAC;gCACAC;gCACAC;gCACAG;gCACA0E,QAAQoB,MAAMpB,MAAM;gCACpBzE;gCACAC;gCACAC;gCACAC;gCACAC;gCACAC,iBAAiB;gCACjBC,mBAAmBA,qBAAqBT,MAAM2F,SAAS;gCACvDjF,YAAYa,OAAO,MAAMmE;gCACzB/E,kBAAkBa,aAAa,MAAMwE,MAAMK,IAAI;gCAC/CzF;gCACAC;gCACAC;gCACAC,QAAQ,OAAOuF,gBAAgB,WAAWA,cAAcjD;gCACxDrC,YAAYuF;gCACZtF;gCACAC,YAAauE,OAAsB,CAAC;gCACpCrE;gCACAC;4BACF;wBACF;oBACF;gBACF,OAAO,IAAI,CAACkB,6BAA6B,OAAOgD,SAAS,YAAYA,SAAS,MAAM;oBAClFvB,OAAO4B,MAAM,CAACL,MAAM9B,OAAO,CAAC,CAACoC;wBAC3B,IAAI5C,MAAMC,OAAO,CAAC2C,aAAa;4BAC7BA,WAAWpC,OAAO,CAAC,CAACgC,KAAKC;gCACvB,MAAMI,mBAAmBL,IAAIM,SAAS;gCAEtC,MAAMC,QACJlF,IAAI0B,OAAO,CAACyD,MAAM,CAACH,iBAAiB,IAClC9F,CAAAA,MAAMkG,eAAe,IAAIlG,MAAMiG,MAAK,EAAGE,IAAI,CAC3C,CAACC,WAAa,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKP;gCAGpE,IAAIE,OAAO;wCACT3G,6NAAAA,EAAe;wCACbG,WAAWiG;wCACXhG;wCACAC;wCACAC;wCACAC;wCACAC;wCACAC;wCACAC;wCACAG;wCACA0E,QAAQoB,MAAMpB,MAAM;wCACpBzE;wCACAC;wCACAC;wCACAC;wCACAC;wCACAC,iBAAiB;wCACjBC,mBAAmBA,qBAAqBT,MAAM2F,SAAS;wCACvDjF,YAAYa,OAAO,MAAMmE;wCACzB/E,kBAAkBa,aAAa,MAAMwE,MAAMK,IAAI;wCAC/CzF;wCACAC;wCACAC;wCACAG;wCACAC,YAAauE,OAAsB,CAAC;wCACpCrE;wCACAC;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF,OAAO,IAAIkB,6BAA8B,CAAA,CAACgD,QAAQA,KAAKnC,MAAM,KAAK,CAAA,GAAI;oBACpElC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAG;gBAC3B,OAAO,IAAIrC,MAAMoC,MAAM,KAAK,QAAQnB,qBAAqB,MAAM;oBAC7DC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAG,EAAE;gBAC7B;gBAEA;YACF;QAEA,KAAK;QACL,KAAK;YAAO;oBACVhD,6NAAAA,EAAe;oBACbG;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG;oBACA0E,QAAQ5E,MAAM4E,MAAM;oBACpBzE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBc;oBACjBb;oBACAC;oBACAC,kBAAkBa;oBAClBZ;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAE;oBACAC;gBACF;gBAEA;YACF;QAEA,KAAK;YAAS;gBACZ,IAAIK,wBAAwB;oBAC1B,IAAI8E,WAAWtF,UAAU,CAAClB,MAAMqC,IAAI,CAAC;oBAErC,IAAI,OAAOnB,UAAU,CAAClB,MAAMqC,IAAI,CAAC,KAAK,UAAU;wBAC9CmE,WAAW,CAAC;oBACd;oBAEA,MAAMC,cAAc1F,QAAQ,CAACf,MAAMqC,IAAI,CAAC;wBAExChD,6NAAAA,EAAe;wBACbG;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAG;wBACA0E,QAAQ5E,MAAM4E,MAAM;wBACpBzE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC,iBAAiB;wBACjBC,mBAAmBA,qBAAqBT,MAAM2F,SAAS;wBACvDjF,YAAYa;wBACZZ,kBAAkBa;wBAClBZ;wBACAC;wBACAC;wBACAC,QAAQ,OAAO0F,gBAAgB,WAAWA,cAAcpD;wBACxDrC;wBACAC;wBACAC,YAAYsF;wBACZpF;wBACAC;oBACF;gBACF,OAAO;wBACLhC,6NAAAA,EAAe;wBACbG;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAG;wBACA0E,QAAQ5E,MAAM4E,MAAM;wBACpBzE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC,iBAAiBc;wBACjBb;wBACAC;wBACAC,kBAAkBa;wBAClBZ;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;wBACAE;wBACAC;oBACF;gBACF;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAACrB,OAAOsD,QAAQ;oBAClB,MAAM,IAAI3E,6MAAAA,CAAkBqB,OAAO,8HAA8H;;gBACnK;gBAEA,IAAI,OAAOA,OAAOsD,WAAW,YAAY;oBACvC,MAAM,IAAIC,MAAM;gBAClB;gBAEA,MAAMD,SAA0BtD,OAAOsD;gBAEvC,IAAIA,QAAQM,OAAOC,WAAWT,QAAQ;oBACpC,KAAK,MAAMU,QAAQR,OAAOM,KAAK,CAACC,SAAS,CAAE;wBACzC,IAAIF,2BAA2B;4BAC7B,MAAMI,mBAAmBC,OAAOC,OAAO,CAAC/C,UAAU,CAAClB,MAAMqC,IAAI,CAAC;4BAE9D,MAAM6B,QAAQC,GAAG,CACfJ,iBAAiB9B,GAAG,CAAC,OAAO,CAAC3B,QAAQqC,MAAM;gCACzC,MAAM0B,cAAc,MAAMP,KAAK;oCAC7BrE;oCACAC;oCACAC;oCACA2E,MAAMzE;oCACND;oCACAE;oCACAC,gBAAgBA;oCAChBC;oCACAE;oCACAC;oCACAC;oCACAC;oCACAiB,WAAWQ;oCACXxB;oCACAiE,WAAW;oCACXC,aAAa3E;oCACbU;oCACAE,mBAAmBA;oCACnBc,MAAMI;oCACNf;oCACAC;oCACAC;oCACAU,YAAYK;oCACZZ;oCACAwD,aAAavD;oCACbE;oCACAC;oCACAsB;gCACF;gCAEA,IAAI0B,gBAAgBhB,WAAW;oCAC7BnC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,CAAC/B,OAAO,GAAG+D;gCACnC;4BACF;wBAEJ,OAAO;4BACL,MAAMA,cAAc,MAAMP,KAAK;gCAC7BrE;gCACAC;gCACAC;gCACA2E,MAAMzE;gCACND;gCACAE;gCACAC,gBAAgBA;gCAChBC;gCACAE;gCACAC;gCACAC;gCACAC;gCACAiB,WAAWQ;gCACXxB,QAAQA;gCACRiE,WAAW;gCACXC,aAAa3E;gCACbU;gCACAE,mBAAmBA;gCACnBc,MAAMI;gCACNf;gCACAC;gCACAC;gCACAU,YAAYK;gCACZZ;gCACAwD,aAAavD;gCACbE;gCACAC;gCACAsB,OAAOzB,UAAU,CAAClB,MAAMqC,IAAI,CAAC;4BAC/B;4BAEA,IAAIgC,gBAAgBhB,WAAW;gCAC7BnC,UAAU,CAAClB,MAAMqC,IAAI,CAAC,GAAGgC;4BAC3B;wBACF;oBACF;gBACF;gBACA;YACF;QAEA,KAAK;YAAO;gBACV,IAAIqC,SAASxF;gBACb,IAAIyF;gBAEJ,MAAMC,iBAAa5H,oMAAAA,EAAWgB;gBAE9B,IAAI4G,YAAY;oBACdF,SAASxF,UAAU,CAAClB,MAAMqC,IAAI,CAAC;oBAE/B,IAAI,OAAOnB,UAAU,CAAClB,MAAMqC,IAAI,CAAC,KAAK,UAAU;wBAC9CqE,SAAS,CAAC;oBACZ;oBAEA,IAAI,OAAO3F,QAAQ,CAACf,MAAMqC,IAAI,CAAC,KAAK,UAAU;wBAC5CsE,YAAY5F,QAAQ,CAACf,MAAMqC,IAAI,CAAC;oBAClC;gBACF,OAAO;oBACLsE,YAAY5F;gBACd;oBAEA1B,6NAAAA,EAAe;oBACbG;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG;oBACA0E,QAAQ5E,MAAM4E,MAAM;oBACpBzE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBoG,aAAa,KAAKtF;oBACnCb,mBAAmBA,qBAAqBT,MAAM2F,SAAS;oBACvDjF,YAAYkG,aAAarF,OAAOb;oBAChCC,kBAAkBa;oBAClBZ;oBACAC;oBACAC;oBACAC,QAAQ4F;oBACR3F;oBACAC;oBACAC,YAAYwF;oBACZtF;oBACAC;gBACF;gBAEA;YACF;QAEA,KAAK;YAAQ;oBACXhC,6NAAAA,EAAe;oBACbG;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG;oBACA0E,QAAQ5E,MAAMwD,IAAI,CAACvB,GAAG,CAAC,CAACyB,MAAS,CAAA;4BAAE,GAAGA,GAAG;4BAAEX,MAAM;wBAAM,CAAA;oBACvD5C;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBc;oBACjBb;oBACAC,YAAYa;oBACZZ,kBAAkBa;oBAClBZ;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAE;oBACAC;gBACF;gBAEA;YACF;QACA;YAAS;gBACP;YACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterRead/traverseFields.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type {\n  JsonObject,\n  PayloadRequest,\n  PopulateType,\n  SelectMode,\n  SelectType,\n} from '../../../types/index.js'\nimport type { Field, TabAsField } from '../../config/types.js'\n\nimport { promise } from './promise.js'\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  currentDepth: number\n  depth: number\n  doc: JsonObject\n  draft: boolean\n  fallbackLocale: null | string\n  /**\n   * fieldPromises are used for things like field hooks. They should be awaited before awaiting populationPromises\n   */\n  fieldPromises: Promise<void>[]\n  fields: (Field | TabAsField)[]\n  findMany: boolean\n  flattenLocales: boolean\n  global: null | SanitizedGlobalConfig\n  locale: null | string\n  overrideAccess: boolean\n  parentIndexPath: string\n  /**\n   * @todo make required in v4.0\n   */\n  parentIsLocalized?: boolean\n  parentPath: string\n  parentSchemaPath: string\n  populate?: PopulateType\n  populationPromises: Promise<void>[]\n  req: PayloadRequest\n  select?: SelectType\n  selectMode?: SelectMode\n  showHiddenFields: boolean\n  siblingDoc: JsonObject\n  triggerAccessControl?: boolean\n  triggerHooks?: boolean\n}\n\nexport const traverseFields = ({\n  blockData,\n  collection,\n  context,\n  currentDepth,\n  depth,\n  doc,\n  draft,\n  fallbackLocale,\n  fieldPromises,\n  fields,\n  findMany,\n  flattenLocales,\n  global,\n  locale,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  populate,\n  populationPromises,\n  req,\n  select,\n  selectMode,\n  showHiddenFields,\n  siblingDoc,\n  triggerAccessControl = true,\n  triggerHooks = true,\n}: Args): void => {\n  fields.forEach((field, fieldIndex) => {\n    fieldPromises.push(\n      promise({\n        blockData,\n        collection,\n        context,\n        currentDepth,\n        depth,\n        doc,\n        draft,\n        fallbackLocale,\n        field,\n        fieldIndex,\n        fieldPromises,\n        findMany,\n        flattenLocales,\n        global,\n        locale,\n        overrideAccess,\n        parentIndexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath,\n        populate,\n        populationPromises,\n        req,\n        select,\n        selectMode,\n        showHiddenFields,\n        siblingDoc,\n        siblingFields: fields,\n        triggerAccessControl,\n        triggerHooks,\n      }),\n    )\n  })\n}\n"], "names": ["promise", "traverseFields", "blockData", "collection", "context", "<PERSON><PERSON><PERSON><PERSON>", "depth", "doc", "draft", "fallback<PERSON><PERSON><PERSON>", "fieldPromises", "fields", "find<PERSON>any", "flattenLocales", "global", "locale", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "populate", "populationPromises", "req", "select", "selectMode", "show<PERSON><PERSON>den<PERSON><PERSON>s", "siblingDoc", "triggerAccessControl", "triggerHooks", "for<PERSON>ach", "field", "fieldIndex", "push", "siblingFields"], "mappings": ";;;;AAYA,SAASA,OAAO,QAAQ,eAAc;;AA0C/B,MAAMC,iBAAiB,CAAC,EAC7BC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,cAAc,EACdC,aAAa,EACbC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,MAAM,EACNC,MAAM,EACNC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,QAAQ,EACRC,kBAAkB,EAClBC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,uBAAuB,IAAI,EAC3BC,eAAe,IAAI,EACd;IACLlB,OAAOmB,OAAO,CAAC,CAACC,OAAOC;QACrBtB,cAAcuB,IAAI,KAChBjC,+MAAAA,EAAQ;YACNE;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAsB;YACAC;YACAtB;YACAE;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAO,eAAevB;YACfiB;YACAC;QACF;IAEJ;AACF,EAAC", "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterRead/index.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest, PopulateType, SelectType } from '../../../types/index.js'\n\nimport { getSelectMode } from '../../../utilities/getSelectMode.js'\nimport { traverseFields } from './traverseFields.js'\n\nexport type AfterReadArgs<T extends JsonObject> = {\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  currentDepth?: number\n  depth: number\n  doc: T\n  draft: boolean\n  fallbackLocale: null | string\n  findMany?: boolean\n  /**\n   * Controls whether locales should be flattened into the requested locale.\n   * E.g.: { [locale]: fields } -> fields\n   *\n   * @default true\n   */\n  flattenLocales?: boolean\n  global: null | SanitizedGlobalConfig\n  locale: string\n  overrideAccess: boolean\n  populate?: PopulateType\n  req: PayloadRequest\n  select?: SelectType\n  showHiddenFields: boolean\n}\n\n/**\n * This function is responsible for the following actions, in order:\n * - Remove hidden fields from response\n * - Flatten locales into requested locale. If the input doc contains all locales, the output doc after this function will only contain the requested locale.\n * - Sanitize outgoing data (point field, etc.)\n * - Execute field hooks\n * - Execute read access control\n * - Populate relationships\n */\n\nexport async function afterRead<T extends JsonObject>(args: AfterReadArgs<T>): Promise<T> {\n  const {\n    collection,\n    context,\n    currentDepth: incomingCurrentDepth,\n    depth: incomingDepth,\n    doc: incomingDoc,\n    draft,\n    fallbackLocale,\n    findMany,\n    flattenLocales = true,\n    global,\n    locale,\n    overrideAccess,\n    populate,\n    req,\n    select,\n    showHiddenFields,\n  } = args\n\n  const fieldPromises: Promise<void>[] = []\n  const populationPromises: Promise<void>[] = []\n\n  let depth =\n    incomingDepth || incomingDepth === 0\n      ? parseInt(String(incomingDepth), 10)\n      : req.payload.config.defaultDepth\n  if (depth > req.payload.config.maxDepth) {\n    depth = req.payload.config.maxDepth\n  }\n\n  const currentDepth = incomingCurrentDepth || 1\n\n  traverseFields({\n    collection,\n    context,\n    currentDepth,\n    depth,\n    doc: incomingDoc,\n    draft,\n    fallbackLocale,\n    fieldPromises,\n    fields: (collection?.fields || global?.fields)!,\n    findMany: findMany!,\n    flattenLocales,\n    global,\n    locale,\n    overrideAccess,\n    parentIndexPath: '',\n    parentIsLocalized: false,\n    parentPath: '',\n    parentSchemaPath: '',\n    populate,\n    populationPromises,\n    req,\n    select,\n    selectMode: select ? getSelectMode(select) : undefined,\n    showHiddenFields,\n    siblingDoc: incomingDoc,\n  })\n\n  /**\n   * Await all field and population promises in parallel.\n   * A field promise is able to add more field promises to the fieldPromises array, which will not be\n   * awaited in the first run.\n   * This is why we need to loop again to process the new field promises, until there are no more field promises left.\n   */\n  let iterations = 0\n  while (fieldPromises.length > 0 || populationPromises.length > 0) {\n    const currentFieldPromises = fieldPromises.splice(0, fieldPromises.length)\n    const currentPopulationPromises = populationPromises.splice(0, populationPromises.length)\n\n    await Promise.all(currentFieldPromises)\n    await Promise.all(currentPopulationPromises)\n\n    iterations++\n    if (iterations >= 100) {\n      throw new Error(\n        'Infinite afterRead promise loop detected. A hook is likely adding field promises in an infinitely recursive way.',\n      )\n    }\n  }\n  return incomingDoc\n}\n"], "names": ["getSelectMode", "traverseFields", "afterRead", "args", "collection", "context", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depth", "<PERSON><PERSON><PERSON><PERSON>", "doc", "incomingDoc", "draft", "fallback<PERSON><PERSON><PERSON>", "find<PERSON>any", "flattenLocales", "global", "locale", "overrideAccess", "populate", "req", "select", "show<PERSON><PERSON>den<PERSON><PERSON>s", "fieldPromises", "populationPromises", "parseInt", "String", "payload", "config", "defaultDepth", "max<PERSON><PERSON><PERSON>", "fields", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "selectMode", "undefined", "siblingDoc", "iterations", "length", "currentFieldPromises", "splice", "currentPopulationPromises", "Promise", "all", "Error"], "mappings": ";;;;AAKA,SAASA,aAAa,QAAQ,sCAAqC;AACnE,SAASC,cAAc,QAAQ,sBAAqB;;;AAqC7C,eAAeC,UAAgCC,IAAsB;IAC1E,MAAM,EACJC,UAAU,EACVC,OAAO,EACPC,cAAcC,oBAAoB,EAClCC,OAAOC,aAAa,EACpBC,KAAKC,WAAW,EAChBC,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRC,iBAAiB,IAAI,EACrBC,MAAM,EACNC,MAAM,EACNC,cAAc,EACdC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,gBAAgB,EACjB,GAAGnB;IAEJ,MAAMoB,gBAAiC,EAAE;IACzC,MAAMC,qBAAsC,EAAE;IAE9C,IAAIhB,QACFC,iBAAiBA,kBAAkB,IAC/BgB,SAASC,OAAOjB,gBAAgB,MAChCW,IAAIO,OAAO,CAACC,MAAM,CAACC,YAAY;IACrC,IAAIrB,QAAQY,IAAIO,OAAO,CAACC,MAAM,CAACE,QAAQ,EAAE;QACvCtB,QAAQY,IAAIO,OAAO,CAACC,MAAM,CAACE,QAAQ;IACrC;IAEA,MAAMxB,eAAeC,wBAAwB;QAE7CN,6NAAAA,EAAe;QACbG;QACAC;QACAC;QACAE;QACAE,KAAKC;QACLC;QACAC;QACAU;QACAQ,QAAS3B,YAAY2B,UAAUf,QAAQe;QACvCjB,UAAUA;QACVC;QACAC;QACAC;QACAC;QACAc,iBAAiB;QACjBC,mBAAmB;QACnBC,YAAY;QACZC,kBAAkB;QAClBhB;QACAK;QACAJ;QACAC;QACAe,YAAYf,aAASrB,wMAAAA,EAAcqB,UAAUgB;QAC7Cf;QACAgB,YAAY3B;IACd;IAEA;;;;;GAKC,GACD,IAAI4B,aAAa;IACjB,MAAOhB,cAAciB,MAAM,GAAG,KAAKhB,mBAAmBgB,MAAM,GAAG,EAAG;QAChE,MAAMC,uBAAuBlB,cAAcmB,MAAM,CAAC,GAAGnB,cAAciB,MAAM;QACzE,MAAMG,4BAA4BnB,mBAAmBkB,MAAM,CAAC,GAAGlB,mBAAmBgB,MAAM;QAExF,MAAMI,QAAQC,GAAG,CAACJ;QAClB,MAAMG,QAAQC,GAAG,CAACF;QAElBJ;QACA,IAAIA,cAAc,KAAK;YACrB,MAAM,IAAIO,MACR;QAEJ;IACF;IACA,OAAOnC;AACT", "debugId": null}}, {"offset": {"line": 1773, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/validations.ts"], "sourcesContent": ["import Ajv from 'ajv'\nimport ObjectIdImport from 'bson-objectid'\n\nconst ObjectId = 'default' in ObjectIdImport ? ObjectIdImport.default : ObjectIdImport\n\nimport type { TFunction } from '@payloadcms/translations'\nimport type { JSONSchema4 } from 'json-schema'\n\nimport type { RichTextAdapter } from '../admin/types.js'\nimport type { CollectionSlug } from '../index.js'\nimport type { Where } from '../types/index.js'\nimport type {\n  ArrayField,\n  BlocksField,\n  CheckboxField,\n  CodeField,\n  DateField,\n  EmailField,\n  JSONField,\n  NumberField,\n  PointField,\n  RadioField,\n  RelationshipField,\n  RelationshipValue,\n  RelationshipValueMany,\n  RelationshipValueSingle,\n  RichTextField,\n  SelectField,\n  TextareaField,\n  TextField,\n  UploadField,\n  Validate,\n  ValueWithRelation,\n} from './config/types.js'\n\nimport { isNumber } from '../utilities/isNumber.js'\nimport { isValidID } from '../utilities/isValidID.js'\n\nexport type TextFieldValidation = Validate<string, unknown, unknown, TextField>\n\nexport type TextFieldManyValidation = Validate<string[], unknown, unknown, TextField>\n\nexport type TextFieldSingleValidation = Validate<string, unknown, unknown, TextField>\n\nexport const text: TextFieldValidation = (\n  value,\n  {\n    hasMany,\n    maxLength: fieldMaxLength,\n    maxRows,\n    minLength,\n    minRows,\n    req: {\n      payload: { config },\n      t,\n    },\n    required,\n  },\n) => {\n  let maxLength!: number\n\n  if (!required) {\n    if (value === undefined || value === null) {\n      return true\n    }\n  }\n\n  if (hasMany === true) {\n    const lengthValidationResult = validateArrayLength(value, { maxRows, minRows, required, t })\n    if (typeof lengthValidationResult === 'string') {\n      return lengthValidationResult\n    }\n  }\n\n  if (typeof config?.defaultMaxTextLength === 'number') {\n    maxLength = config.defaultMaxTextLength\n  }\n  if (typeof fieldMaxLength === 'number') {\n    maxLength = fieldMaxLength\n  }\n\n  const stringsToValidate: string[] = Array.isArray(value) ? value : [value!]\n\n  for (const stringValue of stringsToValidate) {\n    const length = stringValue?.length || 0\n\n    if (typeof maxLength === 'number' && length > maxLength) {\n      return t('validation:shorterThanMax', { label: t('general:value'), maxLength, stringValue })\n    }\n\n    if (typeof minLength === 'number' && length < minLength) {\n      return t('validation:longerThanMin', { label: t('general:value'), minLength, stringValue })\n    }\n  }\n\n  if (required) {\n    if (!(typeof value === 'string' || Array.isArray(value)) || value?.length === 0) {\n      return t('validation:required')\n    }\n  }\n\n  return true\n}\n\nexport type PasswordFieldValidation = Validate<string, unknown, unknown, TextField>\n\nexport const password: PasswordFieldValidation = (\n  value,\n  {\n    maxLength: fieldMaxLength,\n    minLength = 3,\n    req: {\n      payload: { config },\n      t,\n    },\n    required,\n  },\n) => {\n  let maxLength!: number\n\n  if (typeof config?.defaultMaxTextLength === 'number') {\n    maxLength = config.defaultMaxTextLength\n  }\n  if (typeof fieldMaxLength === 'number') {\n    maxLength = fieldMaxLength\n  }\n\n  if (value && maxLength && value.length > maxLength) {\n    return t('validation:shorterThanMax', { maxLength })\n  }\n\n  if (value && minLength && value.length < minLength) {\n    return t('validation:longerThanMin', { minLength })\n  }\n\n  if (required && !value) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type ConfirmPasswordFieldValidation = Validate<\n  string,\n  unknown,\n  { password: string },\n  TextField\n>\n\nexport const confirmPassword: ConfirmPasswordFieldValidation = (\n  value,\n  { req: { t }, required, siblingData },\n) => {\n  if (required && !value) {\n    return t('validation:required')\n  }\n\n  if (value && value !== siblingData.password) {\n    return t('fields:passwordsDoNotMatch')\n  }\n\n  return true\n}\n\nexport type EmailFieldValidation = Validate<string, unknown, { username?: string }, EmailField>\n\nexport const email: EmailFieldValidation = (\n  value,\n  {\n    collectionSlug,\n    req: {\n      payload: { collections, config },\n      t,\n    },\n    required,\n    siblingData,\n  },\n) => {\n  if (collectionSlug) {\n    const collection =\n      collections?.[collectionSlug]?.config ??\n      config.collections.find(({ slug }) => slug === collectionSlug)! // If this is run on the client, `collections` will be undefined, but `config.collections` will be available\n\n    if (\n      collection.auth.loginWithUsername &&\n      !collection.auth.loginWithUsername?.requireUsername &&\n      !collection.auth.loginWithUsername?.requireEmail\n    ) {\n      if (!value && !siblingData?.username) {\n        return t('validation:required')\n      }\n    }\n  }\n\n  /**\n   * Disallows emails with double quotes (e.g., \"user\"@example.com, user@\"example.com\", \"<EMAIL>\")\n   * Rejects spaces anywhere in the email (e.g., user @example.com, user@ example.com, user <EMAIL>)\n   * Prevents consecutive dots in the local or domain part (e.g., <EMAIL>, <EMAIL>)\n   * Disallows domains that start or end with a hyphen (e.g., <EMAIL>, <EMAIL>)\n   * Allows standard email formats (e.g., <EMAIL>, <EMAIL>, <EMAIL>)\n   * Allows domains with consecutive hyphens as long as they are not leading/trailing (e.g., <EMAIL>)\n   * Supports multiple subdomains (e.g., <EMAIL>)\n   */\n  const emailRegex =\n    /^(?!.*\\.\\.)[\\w!#$%&'*+/=?^`{|}~-](?:[\\w!#$%&'*+/=?^`{|}~.-]*[\\w!#$%&'*+/=?^`{|}~-])?@[a-z0-9](?:[a-z0-9-]*[a-z0-9])?(?:\\.[a-z0-9](?:[a-z0-9-]*[a-z0-9])?)*\\.[a-z]{2,}$/i\n\n  if ((value && !emailRegex.test(value)) || (!value && required)) {\n    return t('validation:emailAddress')\n  }\n\n  return true\n}\n\nexport type UsernameFieldValidation = Validate<string, unknown, { email?: string }, TextField>\n\nexport const username: UsernameFieldValidation = (\n  value,\n  {\n    collectionSlug,\n    req: {\n      payload: { collections, config },\n      t,\n    },\n    required,\n    siblingData,\n  },\n) => {\n  let maxLength!: number\n\n  if (collectionSlug) {\n    const collection =\n      collections?.[collectionSlug]?.config ??\n      config.collections.find(({ slug }) => slug === collectionSlug)! // If this is run on the client, `collections` will be undefined, but `config.collections` will be available\n\n    if (\n      collection.auth.loginWithUsername &&\n      !collection.auth.loginWithUsername?.requireUsername &&\n      !collection.auth.loginWithUsername?.requireEmail\n    ) {\n      if (!value && !siblingData?.email) {\n        return t('validation:required')\n      }\n    }\n  }\n\n  if (typeof config?.defaultMaxTextLength === 'number') {\n    maxLength = config.defaultMaxTextLength\n  }\n\n  if (value && maxLength && value.length > maxLength) {\n    return t('validation:shorterThanMax', { maxLength })\n  }\n\n  if (!value && required) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type TextareaFieldValidation = Validate<string, unknown, unknown, TextareaField>\n\nexport const textarea: TextareaFieldValidation = (\n  value,\n  {\n    maxLength: fieldMaxLength,\n    minLength,\n    req: {\n      payload: { config },\n      t,\n    },\n    required,\n  },\n) => {\n  let maxLength!: number\n\n  if (typeof config?.defaultMaxTextLength === 'number') {\n    maxLength = config.defaultMaxTextLength\n  }\n  if (typeof fieldMaxLength === 'number') {\n    maxLength = fieldMaxLength\n  }\n  if (value && maxLength && value.length > maxLength) {\n    return t('validation:shorterThanMax', { maxLength })\n  }\n\n  if (value && minLength && value.length < minLength) {\n    return t('validation:longerThanMin', { minLength })\n  }\n\n  if (required && !value) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type CodeFieldValidation = Validate<string, unknown, unknown, CodeField>\n\nexport const code: CodeFieldValidation = (value, { req: { t }, required }) => {\n  if (required && value === undefined) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type JSONFieldValidation = Validate<\n  string,\n  unknown,\n  unknown,\n  { jsonError?: string } & JSONField\n>\n\nexport const json: JSONFieldValidation = (\n  value,\n  { jsonError, jsonSchema, req: { t }, required },\n) => {\n  const isNotEmpty = (value: null | string | undefined) => {\n    if (value === undefined || value === null) {\n      return false\n    }\n\n    if (Array.isArray(value) && value.length === 0) {\n      return false\n    }\n\n    if (typeof value === 'object' && Object.keys(value).length === 0) {\n      return false\n    }\n\n    return true\n  }\n\n  const fetchSchema = ({ schema, uri }: { schema: JSONSchema4; uri: string }) => {\n    if (uri && schema) {\n      return schema\n    }\n    return fetch(uri)\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error('Network response was not ok')\n        }\n        return response.json()\n      })\n      .then((_json) => {\n        const json = _json as {\n          id: string\n        }\n        const jsonSchemaSanitizations = {\n          id: undefined,\n          $id: json.id,\n          $schema: 'http://json-schema.org/draft-07/schema#',\n        }\n\n        return Object.assign(json, jsonSchemaSanitizations)\n      })\n  }\n\n  if (required && !value) {\n    return t('validation:required')\n  }\n\n  if (jsonError !== undefined) {\n    return t('validation:invalidInput')\n  }\n\n  if (jsonSchema && isNotEmpty(value)) {\n    try {\n      jsonSchema.schema = fetchSchema(jsonSchema)\n      const { schema } = jsonSchema\n      // @ts-expect-error missing types\n      const ajv = new Ajv()\n\n      if (!ajv.validate(schema, value)) {\n        return ajv.errorsText()\n      }\n    } catch (error) {\n      return error instanceof Error ? error.message : 'Unknown error'\n    }\n  }\n  return true\n}\n\nexport type CheckboxFieldValidation = Validate<boolean, unknown, unknown, CheckboxField>\n\nexport const checkbox: CheckboxFieldValidation = (value, { req: { t }, required }) => {\n  if ((value && typeof value !== 'boolean') || (required && typeof value !== 'boolean')) {\n    return t('validation:trueOrFalse')\n  }\n\n  return true\n}\n\nexport type DateFieldValidation = Validate<Date, unknown, unknown, DateField>\n\nexport const date: DateFieldValidation = (\n  value,\n  { name, req: { t }, required, siblingData, timezone },\n) => {\n  const validDate = value && !isNaN(Date.parse(value.toString()))\n\n  // We need to also check for the timezone data based on this field's config\n  // We cannot do this inside the timezone field validation as it's visually hidden\n  const hasRequiredTimezone = timezone && required\n  const selectedTimezone: string = siblingData?.[`${name}_tz` as keyof typeof siblingData]\n  // Always resolve to true if the field is not required, as timezone may be optional too then\n  const validTimezone = hasRequiredTimezone ? Boolean(selectedTimezone) : true\n\n  if (validDate && validTimezone) {\n    return true\n  }\n\n  if (validDate && !validTimezone) {\n    return t('validation:timezoneRequired')\n  }\n\n  if (value) {\n    return t('validation:notValidDate', { value })\n  }\n\n  if (required) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type RichTextFieldValidation = Validate<object, unknown, unknown, RichTextField>\n\nexport const richText: RichTextFieldValidation = async (value, options) => {\n  if (!options?.editor) {\n    throw new Error('richText field has no editor property.')\n  }\n  if (typeof options?.editor === 'function') {\n    throw new Error('Attempted to access unsanitized rich text editor.')\n  }\n\n  const editor: RichTextAdapter = options?.editor\n\n  return editor.validate(value, options)\n}\n\nconst validateArrayLength = (\n  value: unknown,\n  options: {\n    maxRows?: number\n    minRows?: number\n    required?: boolean\n    t: TFunction\n  },\n) => {\n  const { maxRows, minRows, required, t } = options\n\n  const arrayLength = Array.isArray(value) ? value.length : (value as number) || 0\n\n  if (!required && arrayLength === 0) {\n    return true\n  }\n\n  if (minRows && arrayLength < minRows) {\n    return t('validation:requiresAtLeast', { count: minRows, label: t('general:rows') })\n  }\n\n  if (maxRows && arrayLength > maxRows) {\n    return t('validation:requiresNoMoreThan', { count: maxRows, label: t('general:rows') })\n  }\n\n  if (required && !arrayLength) {\n    return t('validation:requiresAtLeast', { count: 1, label: t('general:row') })\n  }\n\n  return true\n}\n\nexport type NumberFieldValidation = Validate<number | number[], unknown, unknown, NumberField>\n\nexport type NumberFieldManyValidation = Validate<number[], unknown, unknown, NumberField>\n\nexport type NumberFieldSingleValidation = Validate<number, unknown, unknown, NumberField>\n\nexport const number: NumberFieldValidation = (\n  value,\n  { hasMany, max, maxRows, min, minRows, req: { t }, required },\n) => {\n  if (hasMany === true) {\n    const lengthValidationResult = validateArrayLength(value, { maxRows, minRows, required, t })\n    if (typeof lengthValidationResult === 'string') {\n      return lengthValidationResult\n    }\n  }\n\n  if (!value && !isNumber(value)) {\n    // if no value is present, validate based on required\n    if (required) {\n      return t('validation:required')\n    }\n    if (!required) {\n      return true\n    }\n  }\n\n  const numbersToValidate: number[] = Array.isArray(value) ? value : [value!]\n\n  for (const number of numbersToValidate) {\n    if (!isNumber(number)) {\n      return t('validation:enterNumber')\n    }\n\n    const numberValue = parseFloat(number as unknown as string)\n\n    if (typeof max === 'number' && numberValue > max) {\n      return t('validation:greaterThanMax', { label: t('general:value'), max, value })\n    }\n\n    if (typeof min === 'number' && numberValue < min) {\n      return t('validation:lessThanMin', { label: t('general:value'), min, value })\n    }\n  }\n\n  return true\n}\n\nexport type ArrayFieldValidation = Validate<unknown[], unknown, unknown, ArrayField>\n\nexport const array: ArrayFieldValidation = (value, { maxRows, minRows, req: { t }, required }) => {\n  return validateArrayLength(value, { maxRows, minRows, required, t })\n}\n\nexport type BlocksFieldValidation = Validate<unknown, unknown, unknown, BlocksField>\n\n/**\n * This function validates the blocks in a blocks field against the provided filterOptions.\n * It will return a list of all block slugs found in the value, the allowed block slugs (if any),\n * and a list of invalid block slugs that are used despite being disallowed.\n *\n * @internal - this may break or be removed at any time\n */\nexport async function validateBlocksFilterOptions({\n  id,\n  data,\n  filterOptions,\n  req,\n  siblingData,\n  value,\n}: { value: Parameters<BlocksFieldValidation>[0] } & Pick<\n  Parameters<BlocksFieldValidation>[1],\n  'data' | 'filterOptions' | 'id' | 'req' | 'siblingData'\n>): Promise<{\n  /**\n   * All block slugs found in the value of the blocks field\n   */\n  allBlockSlugs: string[]\n  /**\n   * All block slugs that are allowed. If undefined, all blocks are allowed.\n   */\n  allowedBlockSlugs: string[] | undefined\n  /**\n   * A list of block slugs that are used despite being disallowed. If undefined, field passed validation.\n   */\n  invalidBlockSlugs: string[] | undefined\n}> {\n  const allBlockSlugs = Array.isArray(value)\n    ? (value as Array<{ blockType?: string }>)\n        .map((b) => b.blockType)\n        .filter((s): s is string => Boolean(s))\n    : []\n\n  // if undefined => all blocks allowed\n  let allowedBlockSlugs: string[] | undefined = undefined\n\n  if (typeof filterOptions === 'function') {\n    const result = await filterOptions({\n      id: id!, // original code asserted presence\n      data,\n      req,\n      siblingData,\n      user: req.user,\n    })\n    if (result !== true && Array.isArray(result)) {\n      allowedBlockSlugs = result\n    }\n  } else if (Array.isArray(filterOptions)) {\n    allowedBlockSlugs = filterOptions\n  }\n\n  const invalidBlockSlugs: string[] = []\n  if (allowedBlockSlugs) {\n    for (const blockSlug of allBlockSlugs) {\n      if (!allowedBlockSlugs.includes(blockSlug)) {\n        invalidBlockSlugs.push(blockSlug)\n      }\n    }\n  }\n\n  return {\n    allBlockSlugs,\n    allowedBlockSlugs,\n    invalidBlockSlugs,\n  }\n}\nexport const blocks: BlocksFieldValidation = async (\n  value,\n  { id, data, filterOptions, maxRows, minRows, req: { t }, req, required, siblingData },\n) => {\n  const lengthValidationResult = validateArrayLength(value, { maxRows, minRows, required, t })\n  if (typeof lengthValidationResult === 'string') {\n    return lengthValidationResult\n  }\n\n  if (filterOptions) {\n    const { invalidBlockSlugs } = await validateBlocksFilterOptions({\n      id,\n      data,\n      filterOptions,\n      req,\n      siblingData,\n      value,\n    })\n    if (invalidBlockSlugs?.length) {\n      return t('validation:invalidBlocks', { blocks: invalidBlockSlugs.join(', ') })\n    }\n  }\n\n  return true\n}\n\nconst validateFilterOptions: Validate<\n  unknown,\n  unknown,\n  unknown,\n  RelationshipField | UploadField\n> = async (\n  value,\n  { id, blockData, data, filterOptions, relationTo, req, req: { t, user }, siblingData },\n) => {\n  if (typeof filterOptions !== 'undefined' && value) {\n    const options: {\n      [collection: string]: (number | string)[]\n    } = {}\n\n    const falseCollections: CollectionSlug[] = []\n    const collections = !Array.isArray(relationTo) ? [relationTo] : relationTo\n    const values = Array.isArray(value) ? value : [value]\n\n    for (const collection of collections) {\n      try {\n        let optionFilter =\n          typeof filterOptions === 'function'\n            ? await filterOptions({\n                id: id!,\n                blockData,\n                data,\n                relationTo: collection,\n                req,\n                siblingData,\n                user,\n              })\n            : filterOptions\n\n        if (optionFilter === true) {\n          optionFilter = null\n        }\n\n        const valueIDs: (number | string)[] = []\n\n        values.forEach((val) => {\n          if (typeof val === 'object') {\n            if (val?.value) {\n              valueIDs.push(val.value)\n            } else if (ObjectId.isValid(val)) {\n              valueIDs.push(new ObjectId(val).toHexString())\n            }\n          }\n\n          if (typeof val === 'string' || typeof val === 'number') {\n            valueIDs.push(val)\n          }\n        })\n\n        if (valueIDs.length > 0) {\n          const findWhere: Where = {\n            and: [{ id: { in: valueIDs } }],\n          }\n\n          // @ts-expect-error - I don't understand why optionFilter is inferred as `false | Where | null` instead of `boolean | Where | null`\n          if (optionFilter && optionFilter !== true) {\n            findWhere.and?.push(optionFilter)\n          }\n\n          if (optionFilter === false) {\n            falseCollections.push(collection)\n          }\n\n          const result = await req.payloadDataLoader.find({\n            collection,\n            depth: 0,\n            limit: 0,\n            pagination: false,\n            req,\n            where: findWhere,\n          })\n\n          options[collection] = result.docs.map((doc) => doc.id)\n        } else {\n          options[collection] = []\n        }\n      } catch (err) {\n        req.payload.logger.error({\n          err,\n          msg: `Error validating filter options for collection ${collection}`,\n        })\n        options[collection] = []\n      }\n    }\n\n    const invalidRelationships = values.filter((val) => {\n      let collection: string\n      let requestedID: number | string\n\n      if (typeof relationTo === 'string') {\n        collection = relationTo\n\n        if (typeof val === 'string' || typeof val === 'number') {\n          requestedID = val\n        }\n\n        if (typeof val === 'object' && ObjectId.isValid(val)) {\n          requestedID = new ObjectId(val).toHexString()\n        }\n      }\n\n      if (Array.isArray(relationTo) && typeof val === 'object' && val?.relationTo) {\n        collection = val.relationTo\n        requestedID = val.value\n      }\n\n      if (falseCollections.find((slug) => relationTo === slug)) {\n        return true\n      }\n\n      if (!options[collection!]) {\n        return true\n      }\n\n      return options[collection!]!.indexOf(requestedID!) === -1\n    })\n\n    if (invalidRelationships.length > 0) {\n      return invalidRelationships.reduce((err, invalid, i) => {\n        return `${err} ${JSON.stringify(invalid)}${\n          invalidRelationships.length === i + 1 ? ',' : ''\n        } `\n      }, t('validation:invalidSelections')) as string\n    }\n\n    return true\n  }\n\n  return true\n}\n\nexport type UploadFieldValidation = Validate<unknown, unknown, unknown, UploadField>\n\nexport type UploadFieldManyValidation = Validate<unknown[], unknown, unknown, UploadField>\n\nexport type UploadFieldSingleValidation = Validate<unknown, unknown, unknown, UploadField>\n\nexport const upload: UploadFieldValidation = async (value, options) => {\n  const {\n    event,\n    maxRows,\n    minRows,\n    relationTo,\n    req: { payload, t },\n    required,\n  } = options\n\n  if (\n    ((!value && typeof value !== 'number') || (Array.isArray(value) && value.length === 0)) &&\n    required\n  ) {\n    return t('validation:required')\n  }\n\n  if (Array.isArray(value) && value.length > 0) {\n    if (minRows && value.length < minRows) {\n      return t('validation:lessThanMin', {\n        label: t('general:rows'),\n        min: minRows,\n        value: value.length,\n      })\n    }\n\n    if (maxRows && value.length > maxRows) {\n      return t('validation:greaterThanMax', {\n        label: t('general:rows'),\n        max: maxRows,\n        value: value.length,\n      })\n    }\n  }\n\n  if (typeof value !== 'undefined' && value !== null) {\n    const values = Array.isArray(value) ? value : [value]\n\n    const invalidRelationships = values.filter((val) => {\n      let collectionSlug: string\n      let requestedID\n\n      if (typeof relationTo === 'string') {\n        collectionSlug = relationTo\n\n        // custom id\n        if (val || typeof val === 'number') {\n          requestedID = val\n        }\n      }\n\n      if (Array.isArray(relationTo) && typeof val === 'object' && val?.relationTo) {\n        collectionSlug = val.relationTo\n        requestedID = val.value\n      }\n\n      if (requestedID === null) {\n        return false\n      }\n\n      const idType =\n        payload.collections[collectionSlug!]?.customIDType || payload?.db?.defaultIDType || 'text'\n\n      return !isValidID(requestedID, idType)\n    })\n\n    if (invalidRelationships.length > 0) {\n      return `This relationship field has the following invalid relationships: ${invalidRelationships\n        .map((err, invalid) => {\n          return `${err} ${JSON.stringify(invalid)}`\n        })\n        .join(', ')}`\n    }\n  }\n\n  if (event === 'onChange') {\n    return true\n  }\n\n  return validateFilterOptions(value, options)\n}\n\nexport type RelationshipFieldValidation = Validate<\n  RelationshipValue,\n  unknown,\n  unknown,\n  RelationshipField\n>\n\nexport type RelationshipFieldManyValidation = Validate<\n  RelationshipValueMany,\n  unknown,\n  unknown,\n  RelationshipField\n>\n\nexport type RelationshipFieldSingleValidation = Validate<\n  RelationshipValueSingle,\n  unknown,\n  unknown,\n  RelationshipField\n>\n\nexport const relationship: RelationshipFieldValidation = async (value, options) => {\n  const {\n    event,\n    maxRows,\n    minRows,\n    relationTo,\n    req: { payload, t },\n    required,\n  } = options\n\n  if (\n    ((!value && typeof value !== 'number') || (Array.isArray(value) && value.length === 0)) &&\n    required\n  ) {\n    return t('validation:required')\n  }\n\n  if (Array.isArray(value) && value.length > 0) {\n    if (minRows && value.length < minRows) {\n      return t('validation:lessThanMin', {\n        label: t('general:rows'),\n        min: minRows,\n        value: value.length,\n      })\n    }\n\n    if (maxRows && value.length > maxRows) {\n      return t('validation:greaterThanMax', {\n        label: t('general:rows'),\n        max: maxRows,\n        value: value.length,\n      })\n    }\n  }\n\n  if (typeof value !== 'undefined' && value !== null) {\n    const values = Array.isArray(value) ? value : [value]\n\n    const invalidRelationships = values.filter((val) => {\n      let collectionSlug: string\n      let requestedID: number | string | undefined | ValueWithRelation\n\n      if (typeof relationTo === 'string') {\n        collectionSlug = relationTo\n\n        // custom id\n        if (val || typeof val === 'number') {\n          requestedID = val\n        }\n      }\n\n      if (Array.isArray(relationTo) && typeof val === 'object' && val?.relationTo) {\n        collectionSlug = val.relationTo\n        requestedID = val.value\n      }\n\n      if (requestedID === null) {\n        return false\n      }\n\n      const idType =\n        payload.collections[collectionSlug!]?.customIDType || payload?.db?.defaultIDType || 'text'\n\n      return !isValidID(requestedID as number | string, idType)\n    })\n\n    if (invalidRelationships.length > 0) {\n      return `This relationship field has the following invalid relationships: ${invalidRelationships\n        .map((err, invalid) => {\n          return `${err} ${JSON.stringify(invalid)}`\n        })\n        .join(', ')}`\n    }\n  }\n\n  if (event === 'onChange') {\n    return true\n  }\n\n  return validateFilterOptions(value, options)\n}\n\nexport type SelectFieldValidation = Validate<string | string[], unknown, unknown, SelectField>\n\nexport type SelectFieldManyValidation = Validate<string[], unknown, unknown, SelectField>\n\nexport type SelectFieldSingleValidation = Validate<string, unknown, unknown, SelectField>\n\nexport const select: SelectFieldValidation = (\n  value,\n  { data, filterOptions, hasMany, options, req, req: { t }, required, siblingData },\n) => {\n  const filteredOptions =\n    typeof filterOptions === 'function'\n      ? filterOptions({\n          data,\n          options,\n          req,\n          siblingData,\n        })\n      : options\n\n  if (\n    Array.isArray(value) &&\n    value.some(\n      (input) =>\n        !filteredOptions.some(\n          (option) => option === input || (typeof option !== 'string' && option?.value === input),\n        ),\n    )\n  ) {\n    return t('validation:invalidSelection')\n  }\n\n  if (\n    typeof value === 'string' &&\n    !filteredOptions.some(\n      (option) => option === value || (typeof option !== 'string' && option.value === value),\n    )\n  ) {\n    return t('validation:invalidSelection')\n  }\n\n  if (\n    required &&\n    (typeof value === 'undefined' ||\n      value === null ||\n      (hasMany && Array.isArray(value) && (value as [])?.length === 0))\n  ) {\n    return t('validation:required')\n  }\n\n  return true\n}\n\nexport type RadioFieldValidation = Validate<unknown, unknown, unknown, RadioField>\n\nexport const radio: RadioFieldValidation = (value, { options, req: { t }, required }) => {\n  if (value) {\n    const valueMatchesOption = options.some(\n      (option) => option === value || (typeof option !== 'string' && option.value === value),\n    )\n    return valueMatchesOption || t('validation:invalidSelection')\n  }\n\n  return required ? t('validation:required') : true\n}\n\nexport type PointFieldValidation = Validate<\n  [number | string, number | string],\n  unknown,\n  unknown,\n  PointField\n>\n\nexport const point: PointFieldValidation = (value = ['', ''], { req: { t }, required }) => {\n  if (value === null) {\n    if (required) {\n      return t('validation:required')\n    }\n\n    return true\n  }\n\n  const lng = parseFloat(String(value[0]))\n  const lat = parseFloat(String(value[1]))\n  if (\n    required &&\n    ((value[0] && value[1] && typeof lng !== 'number' && typeof lat !== 'number') ||\n      Number.isNaN(lng) ||\n      Number.isNaN(lat) ||\n      (Array.isArray(value) && value.length !== 2))\n  ) {\n    return t('validation:requiresTwoNumbers')\n  }\n\n  if ((value[1] && Number.isNaN(lng)) || (value[0] && Number.isNaN(lat))) {\n    return t('validation:invalidInput')\n  }\n\n  return true\n}\n\n/**\n * Built-in field validations used by Payload\n *\n * These can be re-used in custom validations\n */\nexport const validations = {\n  array,\n  blocks,\n  checkbox,\n  code,\n  confirmPassword,\n  date,\n  email,\n  json,\n  number,\n  password,\n  point,\n  radio,\n  relationship,\n  richText,\n  select,\n  text,\n  textarea,\n  upload,\n}\n"], "names": ["Ajv", "ObjectIdImport", "ObjectId", "default", "isNumber", "isValidID", "text", "value", "hasMany", "max<PERSON><PERSON><PERSON>", "fieldMax<PERSON>ength", "maxRows", "<PERSON><PERSON><PERSON><PERSON>", "minRows", "req", "payload", "config", "t", "required", "undefined", "lengthValidationResult", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultMaxTextLength", "stringsToValidate", "Array", "isArray", "stringValue", "length", "label", "password", "confirmPassword", "siblingData", "email", "collectionSlug", "collections", "collection", "find", "slug", "auth", "loginWithUsername", "requireUsername", "requireEmail", "username", "emailRegex", "test", "textarea", "code", "json", "jsonError", "jsonSchema", "isNotEmpty", "Object", "keys", "fetchSchema", "schema", "uri", "fetch", "then", "response", "ok", "Error", "_json", "jsonSchemaSanitizations", "id", "$id", "$schema", "assign", "ajv", "validate", "errorsText", "error", "message", "checkbox", "date", "name", "timezone", "validDate", "isNaN", "Date", "parse", "toString", "hasRequiredTimezone", "selectedTimezone", "validTimezone", "Boolean", "richText", "options", "editor", "array<PERSON>ength", "count", "number", "max", "min", "numbersToValidate", "numberValue", "parseFloat", "array", "validateBlocksFilterOptions", "data", "filterOptions", "allBlockSlugs", "map", "b", "blockType", "filter", "s", "allowedBlockSlugs", "result", "user", "invalidBlockSlugs", "blockSlug", "includes", "push", "blocks", "join", "validateFilterOptions", "blockData", "relationTo", "falseCollections", "values", "optionFilter", "valueIDs", "for<PERSON>ach", "val", "<PERSON><PERSON><PERSON><PERSON>", "toHexString", "findWhere", "and", "in", "payloadDataLoader", "depth", "limit", "pagination", "where", "docs", "doc", "err", "logger", "msg", "invalidRelationships", "requestedID", "indexOf", "reduce", "invalid", "i", "JSON", "stringify", "upload", "event", "idType", "customIDType", "db", "defaultIDType", "relationship", "select", "filteredOptions", "some", "input", "option", "radio", "valueMatchesOption", "point", "lng", "String", "lat", "Number", "validations"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,SAAS,MAAK;AACrB,OAAOC,oBAAoB,gBAAe;AAkC1C,SAASG,QAAQ,QAAQ,2BAA0B;AACnD,SAASC,SAAS,QAAQ,4BAA2B;;;AAjCrD,MAAMH,WAAW,aAAaD,iLAAAA,GAAiBA,iLAAAA,CAAeE,OAAO,GAAGF,iLAAAA;;;AAyCjE,MAAMK,OAA4B,CACvCC,OACA,EACEC,OAAO,EACPC,WAAWC,cAAc,EACzBC,OAAO,EACPC,SAAS,EACTC,OAAO,EACPC,KAAK,EACHC,SAAS,EAAEC,MAAM,EAAE,EACnBC,CAAC,EACF,EACDC,QAAQ,EACT;IAED,IAAIT;IAEJ,IAAI,CAACS,UAAU;QACb,IAAIX,UAAUY,aAAaZ,UAAU,MAAM;YACzC,OAAO;QACT;IACF;IAEA,IAAIC,YAAY,MAAM;QACpB,MAAMY,yBAAyBC,oBAAoBd,OAAO;YAAEI;YAASE;YAASK;YAAUD;QAAE;QAC1F,IAAI,OAAOG,2BAA2B,UAAU;YAC9C,OAAOA;QACT;IACF;IAEA,IAAI,OAAOJ,QAAQM,yBAAyB,UAAU;QACpDb,YAAYO,OAAOM,oBAAoB;IACzC;IACA,IAAI,OAAOZ,mBAAmB,UAAU;QACtCD,YAAYC;IACd;IAEA,MAAMa,oBAA8BC,MAAMC,OAAO,CAAClB,SAASA,QAAQ;QAACA;KAAO;IAE3E,KAAK,MAAMmB,eAAeH,kBAAmB;QAC3C,MAAMI,SAASD,aAAaC,UAAU;QAEtC,IAAI,OAAOlB,cAAc,YAAYkB,SAASlB,WAAW;YACvD,OAAOQ,EAAE,6BAA6B;gBAAEW,OAAOX,EAAE;gBAAkBR;gBAAWiB;YAAY;QAC5F;QAEA,IAAI,OAAOd,cAAc,YAAYe,SAASf,WAAW;YACvD,OAAOK,EAAE,4BAA4B;gBAAEW,OAAOX,EAAE;gBAAkBL;gBAAWc;YAAY;QAC3F;IACF;IAEA,IAAIR,UAAU;QACZ,IAAI,CAAE,CAAA,OAAOX,UAAU,YAAYiB,MAAMC,OAAO,CAAClB,MAAK,KAAMA,OAAOoB,WAAW,GAAG;YAC/E,OAAOV,EAAE;QACX;IACF;IAEA,OAAO;AACT,EAAC;AAIM,MAAMY,WAAoC,CAC/CtB,OACA,EACEE,WAAWC,cAAc,EACzBE,YAAY,CAAC,EACbE,KAAK,EACHC,SAAS,EAAEC,MAAM,EAAE,EACnBC,CAAC,EACF,EACDC,QAAQ,EACT;IAED,IAAIT;IAEJ,IAAI,OAAOO,QAAQM,yBAAyB,UAAU;QACpDb,YAAYO,OAAOM,oBAAoB;IACzC;IACA,IAAI,OAAOZ,mBAAmB,UAAU;QACtCD,YAAYC;IACd;IAEA,IAAIH,SAASE,aAAaF,MAAMoB,MAAM,GAAGlB,WAAW;QAClD,OAAOQ,EAAE,6BAA6B;YAAER;QAAU;IACpD;IAEA,IAAIF,SAASK,aAAaL,MAAMoB,MAAM,GAAGf,WAAW;QAClD,OAAOK,EAAE,4BAA4B;YAAEL;QAAU;IACnD;IAEA,IAAIM,YAAY,CAACX,OAAO;QACtB,OAAOU,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AASM,MAAMa,kBAAkD,CAC7DvB,OACA,EAAEO,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAEa,WAAW,EAAE;IAErC,IAAIb,YAAY,CAACX,OAAO;QACtB,OAAOU,EAAE;IACX;IAEA,IAAIV,SAASA,UAAUwB,YAAYF,QAAQ,EAAE;QAC3C,OAAOZ,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAMe,QAA8B,CACzCzB,OACA,EACE0B,cAAc,EACdnB,KAAK,EACHC,SAAS,EAAEmB,WAAW,EAAElB,MAAM,EAAE,EAChCC,CAAC,EACF,EACDC,QAAQ,EACRa,WAAW,EACZ;IAED,IAAIE,gBAAgB;QAClB,MAAME,aACJD,aAAa,CAACD,eAAe,EAAEjB,UAC/BA,OAAOkB,WAAW,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE,GAAKA,SAASJ,gBAAiB,4GAA4G;;QAE9K,IACEE,WAAWG,IAAI,CAACC,iBAAiB,IACjC,CAACJ,WAAWG,IAAI,CAACC,iBAAiB,EAAEC,mBACpC,CAACL,WAAWG,IAAI,CAACC,iBAAiB,EAAEE,cACpC;YACA,IAAI,CAAClC,SAAS,CAACwB,aAAaW,UAAU;gBACpC,OAAOzB,EAAE;YACX;QACF;IACF;IAEA;;;;;;;;GAQC,GACD,MAAM0B,aACJ;IAEF,IAAKpC,SAAS,CAACoC,WAAWC,IAAI,CAACrC,UAAY,CAACA,SAASW,UAAW;QAC9D,OAAOD,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAMyB,WAAoC,CAC/CnC,OACA,EACE0B,cAAc,EACdnB,KAAK,EACHC,SAAS,EAAEmB,WAAW,EAAElB,MAAM,EAAE,EAChCC,CAAC,EACF,EACDC,QAAQ,EACRa,WAAW,EACZ;IAED,IAAItB;IAEJ,IAAIwB,gBAAgB;QAClB,MAAME,aACJD,aAAa,CAACD,eAAe,EAAEjB,UAC/BA,OAAOkB,WAAW,CAACE,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE,GAAKA,SAASJ,gBAAiB,4GAA4G;;QAE9K,IACEE,WAAWG,IAAI,CAACC,iBAAiB,IACjC,CAACJ,WAAWG,IAAI,CAACC,iBAAiB,EAAEC,mBACpC,CAACL,WAAWG,IAAI,CAACC,iBAAiB,EAAEE,cACpC;YACA,IAAI,CAAClC,SAAS,CAACwB,aAAaC,OAAO;gBACjC,OAAOf,EAAE;YACX;QACF;IACF;IAEA,IAAI,OAAOD,QAAQM,yBAAyB,UAAU;QACpDb,YAAYO,OAAOM,oBAAoB;IACzC;IAEA,IAAIf,SAASE,aAAaF,MAAMoB,MAAM,GAAGlB,WAAW;QAClD,OAAOQ,EAAE,6BAA6B;YAAER;QAAU;IACpD;IAEA,IAAI,CAACF,SAASW,UAAU;QACtB,OAAOD,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAM4B,WAAoC,CAC/CtC,OACA,EACEE,WAAWC,cAAc,EACzBE,SAAS,EACTE,KAAK,EACHC,SAAS,EAAEC,MAAM,EAAE,EACnBC,CAAC,EACF,EACDC,QAAQ,EACT;IAED,IAAIT;IAEJ,IAAI,OAAOO,QAAQM,yBAAyB,UAAU;QACpDb,YAAYO,OAAOM,oBAAoB;IACzC;IACA,IAAI,OAAOZ,mBAAmB,UAAU;QACtCD,YAAYC;IACd;IACA,IAAIH,SAASE,aAAaF,MAAMoB,MAAM,GAAGlB,WAAW;QAClD,OAAOQ,EAAE,6BAA6B;YAAER;QAAU;IACpD;IAEA,IAAIF,SAASK,aAAaL,MAAMoB,MAAM,GAAGf,WAAW;QAClD,OAAOK,EAAE,4BAA4B;YAAEL;QAAU;IACnD;IAEA,IAAIM,YAAY,CAACX,OAAO;QACtB,OAAOU,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAM6B,OAA4B,CAACvC,OAAO,EAAEO,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IACvE,IAAIA,YAAYX,UAAUY,WAAW;QACnC,OAAOF,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AASM,MAAM8B,OAA4B,CACvCxC,OACA,EAAEyC,SAAS,EAAEC,UAAU,EAAEnC,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IAE/C,MAAMgC,aAAa,CAAC3C;QAClB,IAAIA,UAAUY,aAAaZ,UAAU,MAAM;YACzC,OAAO;QACT;QAEA,IAAIiB,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,IAAI,OAAOpB,UAAU,YAAY4C,OAAOC,IAAI,CAAC7C,OAAOoB,MAAM,KAAK,GAAG;YAChE,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM0B,cAAc,CAAC,EAAEC,MAAM,EAAEC,GAAG,EAAwC;QACxE,IAAIA,OAAOD,QAAQ;YACjB,OAAOA;QACT;QACA,OAAOE,MAAMD,KACVE,IAAI,CAAC,CAACC;YACL,IAAI,CAACA,SAASC,EAAE,EAAE;gBAChB,MAAM,IAAIC,MAAM;YAClB;YACA,OAAOF,SAASX,IAAI;QACtB,GACCU,IAAI,CAAC,CAACI;YACL,MAAMd,OAAOc;YAGb,MAAMC,0BAA0B;gBAC9BC,IAAI5C;gBACJ6C,KAAKjB,KAAKgB,EAAE;gBACZE,SAAS;YACX;YAEA,OAAOd,OAAOe,MAAM,CAACnB,MAAMe;QAC7B;IACJ;IAEA,IAAI5C,YAAY,CAACX,OAAO;QACtB,OAAOU,EAAE;IACX;IAEA,IAAI+B,cAAc7B,WAAW;QAC3B,OAAOF,EAAE;IACX;IAEA,IAAIgC,cAAcC,WAAW3C,QAAQ;QACnC,IAAI;YACF0C,WAAWK,MAAM,GAAGD,YAAYJ;YAChC,MAAM,EAAEK,MAAM,EAAE,GAAGL;YACnB,iCAAiC;YACjC,MAAMkB,MAAM,IAAInE,kMAAAA;YAEhB,IAAI,CAACmE,IAAIC,QAAQ,CAACd,QAAQ/C,QAAQ;gBAChC,OAAO4D,IAAIE,UAAU;YACvB;QACF,EAAE,OAAOC,OAAO;YACd,OAAOA,iBAAiBV,QAAQU,MAAMC,OAAO,GAAG;QAClD;IACF;IACA,OAAO;AACT,EAAC;AAIM,MAAMC,WAAoC,CAACjE,OAAO,EAAEO,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IAC/E,IAAKX,SAAS,OAAOA,UAAU,aAAeW,YAAY,OAAOX,UAAU,WAAY;QACrF,OAAOU,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAMwD,OAA4B,CACvClE,OACA,EAAEmE,IAAI,EAAE5D,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAEa,WAAW,EAAE4C,QAAQ,EAAE;IAErD,MAAMC,YAAYrE,SAAS,CAACsE,MAAMC,KAAKC,KAAK,CAACxE,MAAMyE,QAAQ;IAE3D,2EAA2E;IAC3E,iFAAiF;IACjF,MAAMC,sBAAsBN,YAAYzD;IACxC,MAAMgE,mBAA2BnD,aAAa,CAAC,GAAG2C,KAAK,GAAG,CAAC,CAA6B;IACxF,4FAA4F;IAC5F,MAAMS,gBAAgBF,sBAAsBG,QAAQF,oBAAoB;IAExE,IAAIN,aAAaO,eAAe;QAC9B,OAAO;IACT;IAEA,IAAIP,aAAa,CAACO,eAAe;QAC/B,OAAOlE,EAAE;IACX;IAEA,IAAIV,OAAO;QACT,OAAOU,EAAE,2BAA2B;YAAEV;QAAM;IAC9C;IAEA,IAAIW,UAAU;QACZ,OAAOD,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAMoE,WAAoC,OAAO9E,OAAO+E;IAC7D,IAAI,CAACA,SAASC,QAAQ;QACpB,MAAM,IAAI3B,MAAM;IAClB;IACA,IAAI,OAAO0B,SAASC,WAAW,YAAY;QACzC,MAAM,IAAI3B,MAAM;IAClB;IAEA,MAAM2B,SAA0BD,SAASC;IAEzC,OAAOA,OAAOnB,QAAQ,CAAC7D,OAAO+E;AAChC,EAAC;AAED,MAAMjE,sBAAsB,CAC1Bd,OACA+E;IAOA,MAAM,EAAE3E,OAAO,EAAEE,OAAO,EAAEK,QAAQ,EAAED,CAAC,EAAE,GAAGqE;IAE1C,MAAME,cAAchE,MAAMC,OAAO,CAAClB,SAASA,MAAMoB,MAAM,GAAIpB,SAAoB;IAE/E,IAAI,CAACW,YAAYsE,gBAAgB,GAAG;QAClC,OAAO;IACT;IAEA,IAAI3E,WAAW2E,cAAc3E,SAAS;QACpC,OAAOI,EAAE,8BAA8B;YAAEwE,OAAO5E;YAASe,OAAOX,EAAE;QAAgB;IACpF;IAEA,IAAIN,WAAW6E,cAAc7E,SAAS;QACpC,OAAOM,EAAE,iCAAiC;YAAEwE,OAAO9E;YAASiB,OAAOX,EAAE;QAAgB;IACvF;IAEA,IAAIC,YAAY,CAACsE,aAAa;QAC5B,OAAOvE,EAAE,8BAA8B;YAAEwE,OAAO;YAAG7D,OAAOX,EAAE;QAAe;IAC7E;IAEA,OAAO;AACT;AAQO,MAAMyE,SAAgC,CAC3CnF,OACA,EAAEC,OAAO,EAAEmF,GAAG,EAAEhF,OAAO,EAAEiF,GAAG,EAAE/E,OAAO,EAAEC,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IAE7D,IAAIV,YAAY,MAAM;QACpB,MAAMY,yBAAyBC,oBAAoBd,OAAO;YAAEI;YAASE;YAASK;YAAUD;QAAE;QAC1F,IAAI,OAAOG,2BAA2B,UAAU;YAC9C,OAAOA;QACT;IACF;IAEA,IAAI,CAACb,SAAS,KAACH,8LAAAA,EAASG,QAAQ;QAC9B,qDAAqD;QACrD,IAAIW,UAAU;YACZ,OAAOD,EAAE;QACX;QACA,IAAI,CAACC,UAAU;YACb,OAAO;QACT;IACF;IAEA,MAAM2E,oBAA8BrE,MAAMC,OAAO,CAAClB,SAASA,QAAQ;QAACA;KAAO;IAE3E,KAAK,MAAMmF,UAAUG,kBAAmB;QACtC,IAAI,KAACzF,8LAAAA,EAASsF,SAAS;YACrB,OAAOzE,EAAE;QACX;QAEA,MAAM6E,cAAcC,WAAWL;QAE/B,IAAI,OAAOC,QAAQ,YAAYG,cAAcH,KAAK;YAChD,OAAO1E,EAAE,6BAA6B;gBAAEW,OAAOX,EAAE;gBAAkB0E;gBAAKpF;YAAM;QAChF;QAEA,IAAI,OAAOqF,QAAQ,YAAYE,cAAcF,KAAK;YAChD,OAAO3E,EAAE,0BAA0B;gBAAEW,OAAOX,EAAE;gBAAkB2E;gBAAKrF;YAAM;QAC7E;IACF;IAEA,OAAO;AACT,EAAC;AAIM,MAAMyF,QAA8B,CAACzF,OAAO,EAAEI,OAAO,EAAEE,OAAO,EAAEC,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IAC3F,OAAOG,oBAAoBd,OAAO;QAAEI;QAASE;QAASK;QAAUD;IAAE;AACpE,EAAC;AAWM,eAAegF,4BAA4B,EAChDlC,EAAE,EACFmC,IAAI,EACJC,aAAa,EACbrF,GAAG,EACHiB,WAAW,EACXxB,KAAK,EAIN;IAcC,MAAM6F,gBAAgB5E,MAAMC,OAAO,CAAClB,SAC/BA,MACE8F,GAAG,CAAC,CAACC,IAAMA,EAAEC,SAAS,EACtBC,MAAM,CAAC,CAACC,IAAmBrB,QAAQqB,MACtC,EAAE;IAEN,qCAAqC;IACrC,IAAIC,oBAA0CvF;IAE9C,IAAI,OAAOgF,kBAAkB,YAAY;QACvC,MAAMQ,SAAS,MAAMR,cAAc;YACjCpC,IAAIA;YACJmC;YACApF;YACAiB;YACA6E,MAAM9F,IAAI8F,IAAI;QAChB;QACA,IAAID,WAAW,QAAQnF,MAAMC,OAAO,CAACkF,SAAS;YAC5CD,oBAAoBC;QACtB;IACF,OAAO,IAAInF,MAAMC,OAAO,CAAC0E,gBAAgB;QACvCO,oBAAoBP;IACtB;IAEA,MAAMU,oBAA8B,EAAE;IACtC,IAAIH,mBAAmB;QACrB,KAAK,MAAMI,aAAaV,cAAe;YACrC,IAAI,CAACM,kBAAkBK,QAAQ,CAACD,YAAY;gBAC1CD,kBAAkBG,IAAI,CAACF;YACzB;QACF;IACF;IAEA,OAAO;QACLV;QACAM;QACAG;IACF;AACF;AACO,MAAMI,SAAgC,OAC3C1G,OACA,EAAEwD,EAAE,EAAEmC,IAAI,EAAEC,aAAa,EAAExF,OAAO,EAAEE,OAAO,EAAEC,KAAK,EAAEG,CAAC,EAAE,EAAEH,GAAG,EAAEI,QAAQ,EAAEa,WAAW,EAAE;IAErF,MAAMX,yBAAyBC,oBAAoBd,OAAO;QAAEI;QAASE;QAASK;QAAUD;IAAE;IAC1F,IAAI,OAAOG,2BAA2B,UAAU;QAC9C,OAAOA;IACT;IAEA,IAAI+E,eAAe;QACjB,MAAM,EAAEU,iBAAiB,EAAE,GAAG,MAAMZ,4BAA4B;YAC9DlC;YACAmC;YACAC;YACArF;YACAiB;YACAxB;QACF;QACA,IAAIsG,mBAAmBlF,QAAQ;YAC7B,OAAOV,EAAE,4BAA4B;gBAAEgG,QAAQJ,kBAAkBK,IAAI,CAAC;YAAM;QAC9E;IACF;IAEA,OAAO;AACT,EAAC;AAED,MAAMC,wBAKF,OACF5G,OACA,EAAEwD,EAAE,EAAEqD,SAAS,EAAElB,IAAI,EAAEC,aAAa,EAAEkB,UAAU,EAAEvG,GAAG,EAAEA,KAAK,EAAEG,CAAC,EAAE2F,IAAI,EAAE,EAAE7E,WAAW,EAAE;IAEtF,IAAI,OAAOoE,kBAAkB,eAAe5F,OAAO;QACjD,MAAM+E,UAEF,CAAC;QAEL,MAAMgC,mBAAqC,EAAE;QAC7C,MAAMpF,cAAc,CAACV,MAAMC,OAAO,CAAC4F,cAAc;YAACA;SAAW,GAAGA;QAChE,MAAME,SAAS/F,MAAMC,OAAO,CAAClB,SAASA,QAAQ;YAACA;SAAM;QAErD,KAAK,MAAM4B,cAAcD,YAAa;YACpC,IAAI;gBACF,IAAIsF,eACF,OAAOrB,kBAAkB,aACrB,MAAMA,cAAc;oBAClBpC,IAAIA;oBACJqD;oBACAlB;oBACAmB,YAAYlF;oBACZrB;oBACAiB;oBACA6E;gBACF,KACAT;gBAEN,IAAIqB,iBAAiB,MAAM;oBACzBA,eAAe;gBACjB;gBAEA,MAAMC,WAAgC,EAAE;gBAExCF,OAAOG,OAAO,CAAC,CAACC;oBACd,IAAI,OAAOA,QAAQ,UAAU;wBAC3B,IAAIA,KAAKpH,OAAO;4BACdkH,SAAST,IAAI,CAACW,IAAIpH,KAAK;wBACzB,OAAO,IAAIL,SAAS0H,OAAO,CAACD,MAAM;4BAChCF,SAAST,IAAI,CAAC,IAAI9G,SAASyH,KAAKE,WAAW;wBAC7C;oBACF;oBAEA,IAAI,OAAOF,QAAQ,YAAY,OAAOA,QAAQ,UAAU;wBACtDF,SAAST,IAAI,CAACW;oBAChB;gBACF;gBAEA,IAAIF,SAAS9F,MAAM,GAAG,GAAG;oBACvB,MAAMmG,YAAmB;wBACvBC,KAAK;4BAAC;gCAAEhE,IAAI;oCAAEiE,IAAIP;gCAAS;4BAAE;yBAAE;oBACjC;oBAEA,mIAAmI;oBACnI,IAAID,gBAAgBA,iBAAiB,MAAM;wBACzCM,UAAUC,GAAG,EAAEf,KAAKQ;oBACtB;oBAEA,IAAIA,iBAAiB,OAAO;wBAC1BF,iBAAiBN,IAAI,CAAC7E;oBACxB;oBAEA,MAAMwE,SAAS,MAAM7F,IAAImH,iBAAiB,CAAC7F,IAAI,CAAC;wBAC9CD;wBACA+F,OAAO;wBACPC,OAAO;wBACPC,YAAY;wBACZtH;wBACAuH,OAAOP;oBACT;oBAEAxC,OAAO,CAACnD,WAAW,GAAGwE,OAAO2B,IAAI,CAACjC,GAAG,CAAC,CAACkC,MAAQA,IAAIxE,EAAE;gBACvD,OAAO;oBACLuB,OAAO,CAACnD,WAAW,GAAG,EAAE;gBAC1B;YACF,EAAE,OAAOqG,KAAK;gBACZ1H,IAAIC,OAAO,CAAC0H,MAAM,CAACnE,KAAK,CAAC;oBACvBkE;oBACAE,KAAK,CAAC,+CAA+C,EAAEvG,YAAY;gBACrE;gBACAmD,OAAO,CAACnD,WAAW,GAAG,EAAE;YAC1B;QACF;QAEA,MAAMwG,uBAAuBpB,OAAOf,MAAM,CAAC,CAACmB;YAC1C,IAAIxF;YACJ,IAAIyG;YAEJ,IAAI,OAAOvB,eAAe,UAAU;gBAClClF,aAAakF;gBAEb,IAAI,OAAOM,QAAQ,YAAY,OAAOA,QAAQ,UAAU;oBACtDiB,cAAcjB;gBAChB;gBAEA,IAAI,OAAOA,QAAQ,YAAYzH,SAAS0H,OAAO,CAACD,MAAM;oBACpDiB,cAAc,IAAI1I,SAASyH,KAAKE,WAAW;gBAC7C;YACF;YAEA,IAAIrG,MAAMC,OAAO,CAAC4F,eAAe,OAAOM,QAAQ,YAAYA,KAAKN,YAAY;gBAC3ElF,aAAawF,IAAIN,UAAU;gBAC3BuB,cAAcjB,IAAIpH,KAAK;YACzB;YAEA,IAAI+G,iBAAiBlF,IAAI,CAAC,CAACC,OAASgF,eAAehF,OAAO;gBACxD,OAAO;YACT;YAEA,IAAI,CAACiD,OAAO,CAACnD,WAAY,EAAE;gBACzB,OAAO;YACT;YAEA,OAAOmD,OAAO,CAACnD,WAAY,CAAE0G,OAAO,CAACD,iBAAkB,CAAC;QAC1D;QAEA,IAAID,qBAAqBhH,MAAM,GAAG,GAAG;YACnC,OAAOgH,qBAAqBG,MAAM,CAAC,CAACN,KAAKO,SAASC;gBAChD,OAAO,GAAGR,IAAI,CAAC,EAAES,KAAKC,SAAS,CAACH,WAC9BJ,qBAAqBhH,MAAM,KAAKqH,IAAI,IAAI,MAAM,GAC/C,CAAC,CAAC;YACL,GAAG/H,EAAE;QACP;QAEA,OAAO;IACT;IAEA,OAAO;AACT;AAQO,MAAMkI,SAAgC,OAAO5I,OAAO+E;IACzD,MAAM,EACJ8D,KAAK,EACLzI,OAAO,EACPE,OAAO,EACPwG,UAAU,EACVvG,KAAK,EAAEC,OAAO,EAAEE,CAAC,EAAE,EACnBC,QAAQ,EACT,GAAGoE;IAEJ,IACG,CAAC,CAAC/E,SAAS,OAAOA,UAAU,YAAciB,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,KAAK,CAAC,KACrFT,UACA;QACA,OAAOD,EAAE;IACX;IAEA,IAAIO,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,GAAG,GAAG;QAC5C,IAAId,WAAWN,MAAMoB,MAAM,GAAGd,SAAS;YACrC,OAAOI,EAAE,0BAA0B;gBACjCW,OAAOX,EAAE;gBACT2E,KAAK/E;gBACLN,OAAOA,MAAMoB,MAAM;YACrB;QACF;QAEA,IAAIhB,WAAWJ,MAAMoB,MAAM,GAAGhB,SAAS;YACrC,OAAOM,EAAE,6BAA6B;gBACpCW,OAAOX,EAAE;gBACT0E,KAAKhF;gBACLJ,OAAOA,MAAMoB,MAAM;YACrB;QACF;IACF;IAEA,IAAI,OAAOpB,UAAU,eAAeA,UAAU,MAAM;QAClD,MAAMgH,SAAS/F,MAAMC,OAAO,CAAClB,SAASA,QAAQ;YAACA;SAAM;QAErD,MAAMoI,uBAAuBpB,OAAOf,MAAM,CAAC,CAACmB;YAC1C,IAAI1F;YACJ,IAAI2G;YAEJ,IAAI,OAAOvB,eAAe,UAAU;gBAClCpF,iBAAiBoF;gBAEjB,YAAY;gBACZ,IAAIM,OAAO,OAAOA,QAAQ,UAAU;oBAClCiB,cAAcjB;gBAChB;YACF;YAEA,IAAInG,MAAMC,OAAO,CAAC4F,eAAe,OAAOM,QAAQ,YAAYA,KAAKN,YAAY;gBAC3EpF,iBAAiB0F,IAAIN,UAAU;gBAC/BuB,cAAcjB,IAAIpH,KAAK;YACzB;YAEA,IAAIqI,gBAAgB,MAAM;gBACxB,OAAO;YACT;YAEA,MAAMS,SACJtI,QAAQmB,WAAW,CAACD,eAAgB,EAAEqH,gBAAgBvI,SAASwI,IAAIC,iBAAiB;YAEtF,OAAO,KAACnJ,gMAAAA,EAAUuI,aAAaS;QACjC;QAEA,IAAIV,qBAAqBhH,MAAM,GAAG,GAAG;YACnC,OAAO,CAAC,iEAAiE,EAAEgH,qBACxEtC,GAAG,CAAC,CAACmC,KAAKO;gBACT,OAAO,GAAGP,IAAI,CAAC,EAAES,KAAKC,SAAS,CAACH,UAAU;YAC5C,GACC7B,IAAI,CAAC,OAAO;QACjB;IACF;IAEA,IAAIkC,UAAU,YAAY;QACxB,OAAO;IACT;IAEA,OAAOjC,sBAAsB5G,OAAO+E;AACtC,EAAC;AAuBM,MAAMmE,eAA4C,OAAOlJ,OAAO+E;IACrE,MAAM,EACJ8D,KAAK,EACLzI,OAAO,EACPE,OAAO,EACPwG,UAAU,EACVvG,KAAK,EAAEC,OAAO,EAAEE,CAAC,EAAE,EACnBC,QAAQ,EACT,GAAGoE;IAEJ,IACG,CAAC,CAAC/E,SAAS,OAAOA,UAAU,YAAciB,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,KAAK,CAAC,KACrFT,UACA;QACA,OAAOD,EAAE;IACX;IAEA,IAAIO,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,GAAG,GAAG;QAC5C,IAAId,WAAWN,MAAMoB,MAAM,GAAGd,SAAS;YACrC,OAAOI,EAAE,0BAA0B;gBACjCW,OAAOX,EAAE;gBACT2E,KAAK/E;gBACLN,OAAOA,MAAMoB,MAAM;YACrB;QACF;QAEA,IAAIhB,WAAWJ,MAAMoB,MAAM,GAAGhB,SAAS;YACrC,OAAOM,EAAE,6BAA6B;gBACpCW,OAAOX,EAAE;gBACT0E,KAAKhF;gBACLJ,OAAOA,MAAMoB,MAAM;YACrB;QACF;IACF;IAEA,IAAI,OAAOpB,UAAU,eAAeA,UAAU,MAAM;QAClD,MAAMgH,SAAS/F,MAAMC,OAAO,CAAClB,SAASA,QAAQ;YAACA;SAAM;QAErD,MAAMoI,uBAAuBpB,OAAOf,MAAM,CAAC,CAACmB;YAC1C,IAAI1F;YACJ,IAAI2G;YAEJ,IAAI,OAAOvB,eAAe,UAAU;gBAClCpF,iBAAiBoF;gBAEjB,YAAY;gBACZ,IAAIM,OAAO,OAAOA,QAAQ,UAAU;oBAClCiB,cAAcjB;gBAChB;YACF;YAEA,IAAInG,MAAMC,OAAO,CAAC4F,eAAe,OAAOM,QAAQ,YAAYA,KAAKN,YAAY;gBAC3EpF,iBAAiB0F,IAAIN,UAAU;gBAC/BuB,cAAcjB,IAAIpH,KAAK;YACzB;YAEA,IAAIqI,gBAAgB,MAAM;gBACxB,OAAO;YACT;YAEA,MAAMS,SACJtI,QAAQmB,WAAW,CAACD,eAAgB,EAAEqH,gBAAgBvI,SAASwI,IAAIC,iBAAiB;YAEtF,OAAO,KAACnJ,gMAAAA,EAAUuI,aAAgCS;QACpD;QAEA,IAAIV,qBAAqBhH,MAAM,GAAG,GAAG;YACnC,OAAO,CAAC,iEAAiE,EAAEgH,qBACxEtC,GAAG,CAAC,CAACmC,KAAKO;gBACT,OAAO,GAAGP,IAAI,CAAC,EAAES,KAAKC,SAAS,CAACH,UAAU;YAC5C,GACC7B,IAAI,CAAC,OAAO;QACjB;IACF;IAEA,IAAIkC,UAAU,YAAY;QACxB,OAAO;IACT;IAEA,OAAOjC,sBAAsB5G,OAAO+E;AACtC,EAAC;AAQM,MAAMoE,SAAgC,CAC3CnJ,OACA,EAAE2F,IAAI,EAAEC,aAAa,EAAE3F,OAAO,EAAE8E,OAAO,EAAExE,GAAG,EAAEA,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAEa,WAAW,EAAE;IAEjF,MAAM4H,kBACJ,OAAOxD,kBAAkB,aACrBA,cAAc;QACZD;QACAZ;QACAxE;QACAiB;IACF,KACAuD;IAEN,IACE9D,MAAMC,OAAO,CAAClB,UACdA,MAAMqJ,IAAI,CACR,CAACC,QACC,CAACF,gBAAgBC,IAAI,CACnB,CAACE,SAAWA,WAAWD,SAAU,OAAOC,WAAW,YAAYA,QAAQvJ,UAAUsJ,SAGvF;QACA,OAAO5I,EAAE;IACX;IAEA,IACE,OAAOV,UAAU,YACjB,CAACoJ,gBAAgBC,IAAI,CACnB,CAACE,SAAWA,WAAWvJ,SAAU,OAAOuJ,WAAW,YAAYA,OAAOvJ,KAAK,KAAKA,QAElF;QACA,OAAOU,EAAE;IACX;IAEA,IACEC,YACC,CAAA,OAAOX,UAAU,eAChBA,UAAU,QACTC,WAAWgB,MAAMC,OAAO,CAAClB,UAAWA,OAAcoB,WAAW,CAAC,GACjE;QACA,OAAOV,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAIM,MAAM8I,QAA8B,CAACxJ,OAAO,EAAE+E,OAAO,EAAExE,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IAClF,IAAIX,OAAO;QACT,MAAMyJ,qBAAqB1E,QAAQsE,IAAI,CACrC,CAACE,SAAWA,WAAWvJ,SAAU,OAAOuJ,WAAW,YAAYA,OAAOvJ,KAAK,KAAKA;QAElF,OAAOyJ,sBAAsB/I,EAAE;IACjC;IAEA,OAAOC,WAAWD,EAAE,yBAAyB;AAC/C,EAAC;AASM,MAAMgJ,QAA8B,CAAC1J,QAAQ;IAAC;IAAI;CAAG,EAAE,EAAEO,KAAK,EAAEG,CAAC,EAAE,EAAEC,QAAQ,EAAE;IACpF,IAAIX,UAAU,MAAM;QAClB,IAAIW,UAAU;YACZ,OAAOD,EAAE;QACX;QAEA,OAAO;IACT;IAEA,MAAMiJ,MAAMnE,WAAWoE,OAAO5J,KAAK,CAAC,EAAE;IACtC,MAAM6J,MAAMrE,WAAWoE,OAAO5J,KAAK,CAAC,EAAE;IACtC,IACEW,YACC,CAACX,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,IAAI,OAAO2J,QAAQ,YAAY,OAAOE,QAAQ,YAClEC,OAAOxF,KAAK,CAACqF,QACbG,OAAOxF,KAAK,CAACuF,QACZ5I,MAAMC,OAAO,CAAClB,UAAUA,MAAMoB,MAAM,KAAK,CAAC,GAC7C;QACA,OAAOV,EAAE;IACX;IAEA,IAAKV,KAAK,CAAC,EAAE,IAAI8J,OAAOxF,KAAK,CAACqF,QAAU3J,KAAK,CAAC,EAAE,IAAI8J,OAAOxF,KAAK,CAACuF,MAAO;QACtE,OAAOnJ,EAAE;IACX;IAEA,OAAO;AACT,EAAC;AAOM,MAAMqJ,cAAc;IACzBtE;IACAiB;IACAzC;IACA1B;IACAhB;IACA2C;IACAzC;IACAe;IACA2C;IACA7D;IACAoI;IACAF;IACAN;IACApE;IACAqE;IACApJ;IACAuC;IACAsG;AACF,EAAC", "debugId": null}}, {"offset": {"line": 2506, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeDuplicate/promise.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\nimport type { Block, Field, FieldHookArgs, TabAsField } from '../../config/types.js'\n\nimport { fieldAffectsData, fieldShouldBeLocalized } from '../../config/types.js'\nimport { getFieldPathsModified as getFieldPaths } from '../../getFieldPaths.js'\nimport { traverseFields } from './traverseFields.js'\n\ntype Args<T> = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  doc: T\n  field: Field | TabAsField\n  fieldIndex: number\n  id?: number | string\n  overrideAccess: boolean\n  parentIndexPath: string\n  parentIsLocalized: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingDoc: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n}\n\nexport const promise = async <T>({\n  id,\n  blockData,\n  collection,\n  context,\n  doc,\n  field,\n  fieldIndex,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingDoc,\n  siblingFields,\n}: Args<T>): Promise<void> => {\n  const { indexPath, path, schemaPath } = getFieldPaths({\n    field,\n    index: fieldIndex,\n    parentIndexPath,\n    parentPath,\n    parentSchemaPath,\n  })\n\n  const { localization } = req.payload.config\n\n  const pathSegments = path ? path.split('.') : []\n  const schemaPathSegments = schemaPath ? schemaPath.split('.') : []\n  const indexPathSegments = indexPath ? indexPath.split('-').filter(Boolean)?.map(Number) : []\n\n  if (fieldAffectsData(field)) {\n    let fieldData = siblingDoc?.[field.name!]\n    const fieldIsLocalized = localization && fieldShouldBeLocalized({ field, parentIsLocalized })\n\n    // Run field beforeDuplicate hooks.\n    // These hooks are responsible for resetting the `id` field values of array and block rows. See `baseIDField`.\n    if (Array.isArray('hooks' in field && field.hooks?.beforeDuplicate)) {\n      if (fieldIsLocalized) {\n        const localeData: JsonObject = {}\n\n        for (const locale of localization.localeCodes) {\n          const beforeDuplicateArgs: FieldHookArgs = {\n            blockData,\n            collection,\n            context,\n            data: doc as Partial<T>,\n            field,\n            global: undefined!,\n            indexPath: indexPathSegments,\n            path: pathSegments,\n            previousSiblingDoc: siblingDoc,\n            previousValue: siblingDoc[field.name!]?.[locale],\n            req,\n            schemaPath: schemaPathSegments,\n            siblingData: siblingDoc,\n            siblingDocWithLocales: siblingDoc,\n            siblingFields: siblingFields!,\n            value: siblingDoc[field.name!]?.[locale],\n          }\n\n          let hookResult\n          if ('hooks' in field && field.hooks?.beforeDuplicate) {\n            for (const hook of field.hooks.beforeDuplicate) {\n              hookResult = await hook(beforeDuplicateArgs)\n            }\n          }\n\n          if (typeof hookResult !== 'undefined') {\n            localeData[locale] = hookResult\n          }\n        }\n\n        siblingDoc[field.name!] = localeData\n      } else {\n        const beforeDuplicateArgs: FieldHookArgs = {\n          blockData,\n          collection,\n          context,\n          data: doc as Partial<T>,\n          field,\n          global: undefined!,\n          indexPath: indexPathSegments,\n          path: pathSegments,\n          previousSiblingDoc: siblingDoc,\n          previousValue: siblingDoc[field.name!]!,\n          req,\n          schemaPath: schemaPathSegments,\n          siblingData: siblingDoc,\n          siblingDocWithLocales: siblingDoc,\n          siblingFields: siblingFields!,\n          value: siblingDoc[field.name!]!,\n        }\n\n        let hookResult\n        if ('hooks' in field && field.hooks?.beforeDuplicate) {\n          for (const hook of field.hooks.beforeDuplicate) {\n            hookResult = await hook(beforeDuplicateArgs)\n          }\n        }\n\n        if (typeof hookResult !== 'undefined') {\n          siblingDoc[field.name!] = hookResult\n        }\n      }\n    }\n\n    // First, for any localized fields, we will loop over locales\n    // and if locale data is present, traverse the sub fields.\n    // There are only a few different fields where this is possible.\n    if (fieldIsLocalized) {\n      if (typeof fieldData !== 'object' || fieldData === null) {\n        siblingDoc[field.name!] = {}\n        fieldData = siblingDoc[field.name!]\n      }\n\n      const promises: Promise<void>[] = []\n\n      localization.localeCodes.forEach((locale) => {\n        if (fieldData[locale]) {\n          switch (field.type) {\n            case 'array': {\n              const rows = fieldData[locale]\n\n              if (Array.isArray(rows)) {\n                const promises: Promise<void>[] = []\n\n                rows.forEach((row, rowIndex) => {\n                  promises.push(\n                    traverseFields({\n                      id,\n                      blockData,\n                      collection,\n                      context,\n                      doc,\n                      fields: field.fields,\n                      overrideAccess,\n                      parentIndexPath: '',\n                      parentIsLocalized: parentIsLocalized || field.localized!,\n                      parentPath: path + '.' + rowIndex,\n                      parentSchemaPath: schemaPath,\n                      req,\n                      siblingDoc: row,\n                    }),\n                  )\n                })\n              }\n\n              break\n            }\n\n            case 'blocks': {\n              const rows = fieldData[locale]\n\n              if (Array.isArray(rows)) {\n                const promises: Promise<void>[] = []\n\n                rows.forEach((row, rowIndex) => {\n                  const blockTypeToMatch = row.blockType\n\n                  const block: Block | undefined =\n                    req.payload.blocks[blockTypeToMatch] ??\n                    ((field.blockReferences ?? field.blocks).find(\n                      (curBlock) =>\n                        typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n                    ) as Block | undefined)\n\n                  promises.push(\n                    traverseFields({\n                      id,\n                      blockData: row,\n                      collection,\n                      context,\n                      doc,\n                      fields: block!.fields,\n                      overrideAccess,\n                      parentIndexPath: '',\n                      parentIsLocalized: parentIsLocalized || field.localized!,\n                      parentPath: path + '.' + rowIndex,\n                      parentSchemaPath: schemaPath + '.' + block!.slug,\n                      req,\n                      siblingDoc: row,\n                    }),\n                  )\n                })\n              }\n              break\n            }\n\n            case 'group':\n            case 'tab': {\n              promises.push(\n                traverseFields({\n                  id,\n                  blockData,\n                  collection,\n                  context,\n                  doc,\n                  fields: field.fields,\n                  overrideAccess,\n                  parentIndexPath: '',\n                  parentIsLocalized: parentIsLocalized || field.localized!,\n                  parentPath: path,\n                  parentSchemaPath: schemaPath,\n                  req,\n                  siblingDoc: fieldData[locale],\n                }),\n              )\n\n              break\n            }\n          }\n        }\n      })\n\n      await Promise.all(promises)\n    } else {\n      // If the field is not localized, but it affects data,\n      // we need to further traverse its children\n      // so the child fields can run beforeDuplicate hooks\n      switch (field.type) {\n        case 'array': {\n          const rows = siblingDoc[field.name]\n\n          if (Array.isArray(rows)) {\n            const promises: Promise<void>[] = []\n\n            rows.forEach((row, rowIndex) => {\n              promises.push(\n                traverseFields({\n                  id,\n                  blockData,\n                  collection,\n                  context,\n                  doc,\n                  fields: field.fields,\n                  overrideAccess,\n                  parentIndexPath: '',\n                  parentIsLocalized: parentIsLocalized || field.localized!,\n                  parentPath: path + '.' + rowIndex,\n                  parentSchemaPath: schemaPath,\n                  req,\n                  siblingDoc: row,\n                }),\n              )\n            })\n\n            await Promise.all(promises)\n          }\n\n          break\n        }\n\n        case 'blocks': {\n          const rows = siblingDoc[field.name]\n\n          if (Array.isArray(rows)) {\n            const promises: Promise<void>[] = []\n\n            rows.forEach((row, rowIndex) => {\n              const blockTypeToMatch = row.blockType\n\n              const block: Block | undefined =\n                req.payload.blocks[blockTypeToMatch] ??\n                ((field.blockReferences ?? field.blocks).find(\n                  (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n                ) as Block | undefined)\n\n              if (block) {\n                ;(row as JsonObject).blockType = blockTypeToMatch\n\n                promises.push(\n                  traverseFields({\n                    id,\n                    blockData: row,\n                    collection,\n                    context,\n                    doc,\n                    fields: block.fields,\n                    overrideAccess,\n                    parentIndexPath: '',\n                    parentIsLocalized: parentIsLocalized || field.localized!,\n                    parentPath: path + '.' + rowIndex,\n                    parentSchemaPath: schemaPath + '.' + block.slug,\n                    req,\n                    siblingDoc: row,\n                  }),\n                )\n              }\n            })\n\n            await Promise.all(promises)\n          }\n\n          break\n        }\n\n        case 'group': {\n          if (typeof siblingDoc[field.name] !== 'object') {\n            siblingDoc[field.name] = {}\n          }\n\n          const groupDoc = siblingDoc[field.name] as JsonObject\n\n          await traverseFields({\n            id,\n            blockData,\n            collection,\n            context,\n            doc,\n            fields: field.fields,\n            overrideAccess,\n            parentIndexPath: '',\n            parentIsLocalized: parentIsLocalized || field.localized!,\n            parentPath: path,\n            parentSchemaPath: schemaPath,\n            req,\n            siblingDoc: groupDoc,\n          })\n\n          break\n        }\n\n        case 'tab': {\n          if (typeof siblingDoc[field.name!] !== 'object') {\n            siblingDoc[field.name!] = {}\n          }\n\n          const tabDoc = siblingDoc[field.name!] as JsonObject\n\n          await traverseFields({\n            id,\n            blockData,\n            collection,\n            context,\n            doc,\n            fields: field.fields,\n            overrideAccess,\n            parentIndexPath: '',\n            parentIsLocalized: parentIsLocalized || field.localized!,\n            parentPath: path,\n            parentSchemaPath: schemaPath,\n            req,\n            siblingDoc: tabDoc,\n          })\n\n          break\n        }\n      }\n    }\n  } else {\n    // Finally, we traverse fields which do not affect data here - collapsibles, rows, unnamed groups\n    switch (field.type) {\n      case 'collapsible':\n      case 'group':\n      case 'row': {\n        await traverseFields({\n          id,\n          blockData,\n          collection,\n          context,\n          doc,\n          fields: field.fields,\n          overrideAccess,\n          parentIndexPath: indexPath,\n          parentIsLocalized,\n          parentPath,\n          parentSchemaPath: schemaPath,\n          req,\n          siblingDoc,\n        })\n\n        break\n      }\n\n      // Unnamed Tab\n      // @ts-expect-error `fieldAffectsData` inferred return type doesn't account for TabAsField\n      case 'tab': {\n        await traverseFields({\n          id,\n          blockData,\n          collection,\n          context,\n          doc,\n          // @ts-expect-error `fieldAffectsData` inferred return type doesn't account for TabAsField\n          fields: field.fields,\n          overrideAccess,\n          parentIndexPath: indexPath,\n          parentIsLocalized,\n          parentPath,\n          parentSchemaPath: schemaPath,\n          req,\n          siblingDoc,\n        })\n\n        break\n      }\n\n      case 'tabs': {\n        await traverseFields({\n          id,\n          blockData,\n          collection,\n          context,\n          doc,\n          fields: field.tabs.map((tab) => ({ ...tab, type: 'tab' })),\n          overrideAccess,\n          parentIndexPath: indexPath,\n          parentIsLocalized,\n          parentPath: path,\n          parentSchemaPath: schemaPath,\n          req,\n          siblingDoc,\n        })\n\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n  }\n}\n"], "names": ["fieldAffectsData", "fieldShouldBeLocalized", "getFieldPathsModified", "getFieldPaths", "traverseFields", "promise", "id", "blockData", "collection", "context", "doc", "field", "fieldIndex", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingDoc", "siblingFields", "indexPath", "path", "schemaPath", "index", "localization", "payload", "config", "pathSegments", "split", "schemaPathSegments", "indexPathSegments", "filter", "Boolean", "map", "Number", "fieldData", "name", "fieldIsLocalized", "Array", "isArray", "hooks", "beforeDuplicate", "localeData", "locale", "localeCodes", "beforeDuplicateArgs", "data", "global", "undefined", "previousSiblingDoc", "previousValue", "siblingData", "siblingDocWithLocales", "value", "hookResult", "hook", "promises", "for<PERSON>ach", "type", "rows", "row", "rowIndex", "push", "fields", "localized", "blockTypeToMatch", "blockType", "block", "blocks", "blockReferences", "find", "curBlock", "slug", "Promise", "all", "groupDoc", "tabDoc", "tabs", "tab"], "mappings": ";;;;AAKA,SAASA,gBAAgB,EAAEC,sBAAsB,QAAQ,wBAAuB;AAChF,SAASC,yBAAyBC,aAAa,QAAQ,yBAAwB;AAC/E,SAASC,cAAc,QAAQ,sBAAqB;;;;AAuB7C,MAAMC,UAAU,OAAU,EAC/BC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,UAAU,EACVC,aAAa,EACL;IACR,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAE,OAAGpB,6MAAAA,EAAc;QACpDQ;QACAa,OAAOZ;QACPE;QACAE;QACAC;IACF;IAEA,MAAM,EAAEQ,YAAY,EAAE,GAAGP,IAAIQ,OAAO,CAACC,MAAM;IAE3C,MAAMC,eAAeN,OAAOA,KAAKO,KAAK,CAAC,OAAO,EAAE;IAChD,MAAMC,qBAAqBP,aAAaA,WAAWM,KAAK,CAAC,OAAO,EAAE;IAClE,MAAME,oBAAoBV,YAAYA,UAAUQ,KAAK,CAAC,KAAKG,MAAM,CAACC,UAAUC,IAAIC,UAAU,EAAE;IAE5F,QAAInC,0MAAAA,EAAiBW,QAAQ;QAC3B,IAAIyB,YAAYjB,YAAY,CAACR,MAAM0B,IAAI,CAAE;QACzC,MAAMC,mBAAmBb,oBAAgBxB,gNAAAA,EAAuB;YAAEU;YAAOI;QAAkB;QAE3F,mCAAmC;QACnC,8GAA8G;QAC9G,IAAIwB,MAAMC,OAAO,CAAC,WAAW7B,SAASA,MAAM8B,KAAK,EAAEC,kBAAkB;YACnE,IAAIJ,kBAAkB;gBACpB,MAAMK,aAAyB,CAAC;gBAEhC,KAAK,MAAMC,UAAUnB,aAAaoB,WAAW,CAAE;oBAC7C,MAAMC,sBAAqC;wBACzCvC;wBACAC;wBACAC;wBACAsC,MAAMrC;wBACNC;wBACAqC,QAAQC;wBACR5B,WAAWU;wBACXT,MAAMM;wBACNsB,oBAAoB/B;wBACpBgC,eAAehC,UAAU,CAACR,MAAM0B,IAAI,CAAE,EAAE,CAACO,OAAO;wBAChD1B;wBACAK,YAAYO;wBACZsB,aAAajC;wBACbkC,uBAAuBlC;wBACvBC,eAAeA;wBACfkC,OAAOnC,UAAU,CAACR,MAAM0B,IAAI,CAAE,EAAE,CAACO,OAAO;oBAC1C;oBAEA,IAAIW;oBACJ,IAAI,WAAW5C,SAASA,MAAM8B,KAAK,EAAEC,iBAAiB;wBACpD,KAAK,MAAMc,QAAQ7C,MAAM8B,KAAK,CAACC,eAAe,CAAE;4BAC9Ca,aAAa,MAAMC,KAAKV;wBAC1B;oBACF;oBAEA,IAAI,OAAOS,eAAe,aAAa;wBACrCZ,UAAU,CAACC,OAAO,GAAGW;oBACvB;gBACF;gBAEApC,UAAU,CAACR,MAAM0B,IAAI,CAAE,GAAGM;YAC5B,OAAO;gBACL,MAAMG,sBAAqC;oBACzCvC;oBACAC;oBACAC;oBACAsC,MAAMrC;oBACNC;oBACAqC,QAAQC;oBACR5B,WAAWU;oBACXT,MAAMM;oBACNsB,oBAAoB/B;oBACpBgC,eAAehC,UAAU,CAACR,MAAM0B,IAAI,CAAE;oBACtCnB;oBACAK,YAAYO;oBACZsB,aAAajC;oBACbkC,uBAAuBlC;oBACvBC,eAAeA;oBACfkC,OAAOnC,UAAU,CAACR,MAAM0B,IAAI,CAAE;gBAChC;gBAEA,IAAIkB;gBACJ,IAAI,WAAW5C,SAASA,MAAM8B,KAAK,EAAEC,iBAAiB;oBACpD,KAAK,MAAMc,QAAQ7C,MAAM8B,KAAK,CAACC,eAAe,CAAE;wBAC9Ca,aAAa,MAAMC,KAAKV;oBAC1B;gBACF;gBAEA,IAAI,OAAOS,eAAe,aAAa;oBACrCpC,UAAU,CAACR,MAAM0B,IAAI,CAAE,GAAGkB;gBAC5B;YACF;QACF;QAEA,6DAA6D;QAC7D,0DAA0D;QAC1D,gEAAgE;QAChE,IAAIjB,kBAAkB;YACpB,IAAI,OAAOF,cAAc,YAAYA,cAAc,MAAM;gBACvDjB,UAAU,CAACR,MAAM0B,IAAI,CAAE,GAAG,CAAC;gBAC3BD,YAAYjB,UAAU,CAACR,MAAM0B,IAAI,CAAE;YACrC;YAEA,MAAMoB,WAA4B,EAAE;YAEpChC,aAAaoB,WAAW,CAACa,OAAO,CAAC,CAACd;gBAChC,IAAIR,SAAS,CAACQ,OAAO,EAAE;oBACrB,OAAQjC,MAAMgD,IAAI;wBAChB,KAAK;4BAAS;gCACZ,MAAMC,OAAOxB,SAAS,CAACQ,OAAO;gCAE9B,IAAIL,MAAMC,OAAO,CAACoB,OAAO;oCACvB,MAAMH,WAA4B,EAAE;oCAEpCG,KAAKF,OAAO,CAAC,CAACG,KAAKC;wCACjBL,SAASM,IAAI,CACX3D,yOAAe;4CACbE;4CACAC;4CACAC;4CACAC;4CACAC;4CACAsD,QAAQrD,MAAMqD,MAAM;4CACpBnD;4CACAC,iBAAiB;4CACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;4CACvDjD,YAAYM,OAAO,MAAMwC;4CACzB7C,kBAAkBM;4CAClBL;4CACAC,YAAY0C;wCACd;oCAEJ;gCACF;gCAEA;4BACF;wBAEA,KAAK;4BAAU;gCACb,MAAMD,OAAOxB,SAAS,CAACQ,OAAO;gCAE9B,IAAIL,MAAMC,OAAO,CAACoB,OAAO;oCACvB,MAAMH,WAA4B,EAAE;oCAEpCG,KAAKF,OAAO,CAAC,CAACG,KAAKC;wCACjB,MAAMI,mBAAmBL,IAAIM,SAAS;wCAEtC,MAAMC,QACJlD,IAAIQ,OAAO,CAAC2C,MAAM,CAACH,iBAAiB,IAClCvD,CAAAA,MAAM2D,eAAe,IAAI3D,MAAM0D,MAAK,EAAGE,IAAI,CAC3C,CAACC,WACC,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKP;wCAGxDT,SAASM,IAAI,CACX3D,yOAAe;4CACbE;4CACAC,WAAWsD;4CACXrD;4CACAC;4CACAC;4CACAsD,QAAQI,MAAOJ,MAAM;4CACrBnD;4CACAC,iBAAiB;4CACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;4CACvDjD,YAAYM,OAAO,MAAMwC;4CACzB7C,kBAAkBM,aAAa,MAAM6C,MAAOK,IAAI;4CAChDvD;4CACAC,YAAY0C;wCACd;oCAEJ;gCACF;gCACA;4BACF;wBAEA,KAAK;wBACL,KAAK;4BAAO;gCACVJ,SAASM,IAAI,KACX3D,mOAAAA,EAAe;oCACbE;oCACAC;oCACAC;oCACAC;oCACAC;oCACAsD,QAAQrD,MAAMqD,MAAM;oCACpBnD;oCACAC,iBAAiB;oCACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;oCACvDjD,YAAYM;oCACZL,kBAAkBM;oCAClBL;oCACAC,YAAYiB,SAAS,CAACQ,OAAO;gCAC/B;gCAGF;4BACF;oBACF;gBACF;YACF;YAEA,MAAM8B,QAAQC,GAAG,CAAClB;QACpB,OAAO;YACL,sDAAsD;YACtD,2CAA2C;YAC3C,oDAAoD;YACpD,OAAQ9C,MAAMgD,IAAI;gBAChB,KAAK;oBAAS;wBACZ,MAAMC,OAAOzC,UAAU,CAACR,MAAM0B,IAAI,CAAC;wBAEnC,IAAIE,MAAMC,OAAO,CAACoB,OAAO;4BACvB,MAAMH,WAA4B,EAAE;4BAEpCG,KAAKF,OAAO,CAAC,CAACG,KAAKC;gCACjBL,SAASM,IAAI,KACX3D,mOAAAA,EAAe;oCACbE;oCACAC;oCACAC;oCACAC;oCACAC;oCACAsD,QAAQrD,MAAMqD,MAAM;oCACpBnD;oCACAC,iBAAiB;oCACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;oCACvDjD,YAAYM,OAAO,MAAMwC;oCACzB7C,kBAAkBM;oCAClBL;oCACAC,YAAY0C;gCACd;4BAEJ;4BAEA,MAAMa,QAAQC,GAAG,CAAClB;wBACpB;wBAEA;oBACF;gBAEA,KAAK;oBAAU;wBACb,MAAMG,OAAOzC,UAAU,CAACR,MAAM0B,IAAI,CAAC;wBAEnC,IAAIE,MAAMC,OAAO,CAACoB,OAAO;4BACvB,MAAMH,WAA4B,EAAE;4BAEpCG,KAAKF,OAAO,CAAC,CAACG,KAAKC;gCACjB,MAAMI,mBAAmBL,IAAIM,SAAS;gCAEtC,MAAMC,QACJlD,IAAIQ,OAAO,CAAC2C,MAAM,CAACH,iBAAiB,IAClCvD,CAAAA,MAAM2D,eAAe,IAAI3D,MAAM0D,MAAK,EAAGE,IAAI,CAC3C,CAACC,WAAa,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKP;gCAGpE,IAAIE,OAAO;;oCACPP,IAAmBM,SAAS,GAAGD;oCAEjCT,SAASM,IAAI,KACX3D,mOAAAA,EAAe;wCACbE;wCACAC,WAAWsD;wCACXrD;wCACAC;wCACAC;wCACAsD,QAAQI,MAAMJ,MAAM;wCACpBnD;wCACAC,iBAAiB;wCACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;wCACvDjD,YAAYM,OAAO,MAAMwC;wCACzB7C,kBAAkBM,aAAa,MAAM6C,MAAMK,IAAI;wCAC/CvD;wCACAC,YAAY0C;oCACd;gCAEJ;4BACF;4BAEA,MAAMa,QAAQC,GAAG,CAAClB;wBACpB;wBAEA;oBACF;gBAEA,KAAK;oBAAS;wBACZ,IAAI,OAAOtC,UAAU,CAACR,MAAM0B,IAAI,CAAC,KAAK,UAAU;4BAC9ClB,UAAU,CAACR,MAAM0B,IAAI,CAAC,GAAG,CAAC;wBAC5B;wBAEA,MAAMuC,WAAWzD,UAAU,CAACR,MAAM0B,IAAI,CAAC;wBAEvC,UAAMjC,mOAAAA,EAAe;4BACnBE;4BACAC;4BACAC;4BACAC;4BACAC;4BACAsD,QAAQrD,MAAMqD,MAAM;4BACpBnD;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;4BACvDjD,YAAYM;4BACZL,kBAAkBM;4BAClBL;4BACAC,YAAYyD;wBACd;wBAEA;oBACF;gBAEA,KAAK;oBAAO;wBACV,IAAI,OAAOzD,UAAU,CAACR,MAAM0B,IAAI,CAAE,KAAK,UAAU;4BAC/ClB,UAAU,CAACR,MAAM0B,IAAI,CAAE,GAAG,CAAC;wBAC7B;wBAEA,MAAMwC,SAAS1D,UAAU,CAACR,MAAM0B,IAAI,CAAE;wBAEtC,UAAMjC,mOAAAA,EAAe;4BACnBE;4BACAC;4BACAC;4BACAC;4BACAC;4BACAsD,QAAQrD,MAAMqD,MAAM;4BACpBnD;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBJ,MAAMsD,SAAS;4BACvDjD,YAAYM;4BACZL,kBAAkBM;4BAClBL;4BACAC,YAAY0D;wBACd;wBAEA;oBACF;YACF;QACF;IACF,OAAO;QACL,iGAAiG;QACjG,OAAQlE,MAAMgD,IAAI;YAChB,KAAK;YACL,KAAK;YACL,KAAK;gBAAO;oBACV,UAAMvD,mOAAAA,EAAe;wBACnBE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAsD,QAAQrD,MAAMqD,MAAM;wBACpBnD;wBACAC,iBAAiBO;wBACjBN;wBACAC;wBACAC,kBAAkBM;wBAClBL;wBACAC;oBACF;oBAEA;gBACF;YAEA,cAAc;YACd,0FAA0F;YAC1F,KAAK;gBAAO;oBACV,UAAMf,mOAAAA,EAAe;wBACnBE;wBACAC;wBACAC;wBACAC;wBACAC;wBACA,0FAA0F;wBAC1FsD,QAAQrD,MAAMqD,MAAM;wBACpBnD;wBACAC,iBAAiBO;wBACjBN;wBACAC;wBACAC,kBAAkBM;wBAClBL;wBACAC;oBACF;oBAEA;gBACF;YAEA,KAAK;gBAAQ;oBACX,UAAMf,mOAAAA,EAAe;wBACnBE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAsD,QAAQrD,MAAMmE,IAAI,CAAC5C,GAAG,CAAC,CAAC6C,MAAS,CAAA;gCAAE,GAAGA,GAAG;gCAAEpB,MAAM;4BAAM,CAAA;wBACvD9C;wBACAC,iBAAiBO;wBACjBN;wBACAC,YAAYM;wBACZL,kBAAkBM;wBAClBL;wBACAC;oBACF;oBAEA;gBACF;YAEA;gBAAS;oBACP;gBACF;QACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 2877, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeDuplicate/traverseFields.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\nimport type { Field, TabAsField } from '../../config/types.js'\n\nimport { promise } from './promise.js'\n\ntype Args<T> = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  doc: T\n  fields: (Field | TabAsField)[]\n  id?: number | string\n  overrideAccess: boolean\n  parentIndexPath: string\n  parentIsLocalized: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingDoc: JsonObject\n}\n\nexport const traverseFields = async <T>({\n  id,\n  blockData,\n  collection,\n  context,\n  doc,\n  fields,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingDoc,\n}: Args<T>): Promise<void> => {\n  const promises: Promise<void>[] = []\n\n  fields.forEach((field, fieldIndex) => {\n    promises.push(\n      promise({\n        id,\n        blockData,\n        collection,\n        context,\n        doc,\n        field,\n        fieldIndex,\n        overrideAccess,\n        parentIndexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath,\n        req,\n        siblingDoc,\n        siblingFields: fields,\n      }),\n    )\n  })\n  await Promise.all(promises)\n}\n"], "names": ["promise", "traverseFields", "id", "blockData", "collection", "context", "doc", "fields", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingDoc", "promises", "for<PERSON>ach", "field", "fieldIndex", "push", "siblingFields", "Promise", "all"], "mappings": ";;;;AAKA,SAASA,OAAO,QAAQ,eAAc;;AAqB/B,MAAMC,iBAAiB,OAAU,EACtCC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,UAAU,EACF;IACR,MAAMC,WAA4B,EAAE;IAEpCR,OAAOS,OAAO,CAAC,CAACC,OAAOC;QACrBH,SAASI,IAAI,KACXnB,qNAAAA,EAAQ;YACNE;YACAC;YACAC;YACAC;YACAC;YACAW;YACAC;YACAV;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAM,eAAeb;QACjB;IAEJ;IACA,MAAMc,QAAQC,GAAG,CAACP;AACpB,EAAC", "debugId": null}}, {"offset": {"line": 2910, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeDuplicate/index.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\n\nimport { traverseFields } from './traverseFields.js'\n\ntype Args<T extends JsonObject> = {\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  doc?: T\n  id?: number | string\n  overrideAccess: boolean\n  req: PayloadRequest\n}\n\n/**\n * This function is responsible for running beforeDuplicate hooks\n * against a document including all locale data.\n * It will run each field's beforeDuplicate hook\n * and return the resulting docWithLocales.\n */\nexport const beforeDuplicate = async <T extends JsonObject>({\n  id,\n  collection,\n  context,\n  doc,\n  overrideAccess,\n  req,\n}: Args<T>): Promise<T> => {\n  await traverseFields({\n    id,\n    collection,\n    context,\n    doc,\n    fields: collection!.fields,\n    overrideAccess,\n    parentIndexPath: '',\n    parentIsLocalized: false,\n    parentPath: '',\n    parentSchemaPath: '',\n    req,\n    siblingDoc: doc!,\n  })\n\n  return doc!\n}\n"], "names": ["traverseFields", "beforeDuplicate", "id", "collection", "context", "doc", "overrideAccess", "req", "fields", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "siblingDoc"], "mappings": ";;;;AAIA,SAASA,cAAc,QAAQ,sBAAqB;;AAiB7C,MAAMC,kBAAkB,OAA6B,EAC1DC,EAAE,EACFC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,cAAc,EACdC,GAAG,EACK;IACR,UAAMP,mOAAAA,EAAe;QACnBE;QACAC;QACAC;QACAC;QACAG,QAAQL,WAAYK,MAAM;QAC1BF;QACAG,iBAAiB;QACjBC,mBAAmB;QACnBC,YAAY;QACZC,kBAAkB;QAClBL;QACAM,YAAYR;IACd;IAEA,OAAOA;AACT,EAAC", "debugId": null}}, {"offset": {"line": 2937, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterChange/promise.ts"], "sourcesContent": ["import type { RichTextAdapter } from '../../../admin/RichText.js'\nimport type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\nimport type { Block, Field, TabAsField } from '../../config/types.js'\n\nimport { MissingEditorProp } from '../../../errors/index.js'\nimport { fieldAffectsData, tabHasName } from '../../config/types.js'\nimport { getFieldPathsModified as getFieldPaths } from '../../getFieldPaths.js'\nimport { traverseFields } from './traverseFields.js'\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: JsonObject\n  doc: JsonObject\n  field: Field | TabAsField\n  fieldIndex: number\n  global: null | SanitizedGlobalConfig\n  operation: 'create' | 'update'\n  parentIndexPath: string\n  parentIsLocalized: boolean\n  parentPath: string\n  parentSchemaPath: string\n  previousDoc: JsonObject\n  previousSiblingDoc: JsonObject\n  req: PayloadRequest\n  siblingData: JsonObject\n  siblingDoc: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n}\n\n// This function is responsible for the following actions, in order:\n// - Execute field hooks\n\nexport const promise = async ({\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  field,\n  fieldIndex,\n  global,\n  operation,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  previousDoc,\n  previousSiblingDoc,\n  req,\n  siblingData,\n  siblingDoc,\n  siblingFields,\n}: Args): Promise<void> => {\n  const { indexPath, path, schemaPath } = getFieldPaths({\n    field,\n    index: fieldIndex,\n    parentIndexPath,\n    parentPath,\n    parentSchemaPath,\n  })\n\n  const pathSegments = path ? path.split('.') : []\n  const schemaPathSegments = schemaPath ? schemaPath.split('.') : []\n  const indexPathSegments = indexPath ? indexPath.split('-').filter(Boolean)?.map(Number) : []\n\n  if (fieldAffectsData(field)) {\n    // Execute hooks\n    if ('hooks' in field && field.hooks?.afterChange) {\n      for (const hook of field.hooks.afterChange) {\n        const hookedValue = await hook({\n          blockData,\n          collection,\n          context,\n          data,\n          field,\n          global,\n          indexPath: indexPathSegments,\n          operation,\n          originalDoc: doc,\n          path: pathSegments,\n          previousDoc,\n          previousSiblingDoc,\n          previousValue: previousDoc?.[field.name],\n          req,\n          schemaPath: schemaPathSegments,\n          siblingData,\n          siblingFields: siblingFields!,\n          value: siblingDoc?.[field.name],\n        })\n\n        if (hookedValue !== undefined) {\n          siblingDoc[field.name] = hookedValue\n        }\n      }\n    }\n  }\n\n  // Traverse subfields\n  switch (field.type) {\n    case 'array': {\n      const rows = siblingDoc[field.name]\n\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n        rows.forEach((row, rowIndex) => {\n          promises.push(\n            traverseFields({\n              blockData,\n              collection,\n              context,\n              data,\n              doc,\n              fields: field.fields,\n              global,\n              operation,\n              parentIndexPath: '',\n              parentIsLocalized: parentIsLocalized || field.localized,\n              parentPath: path + '.' + rowIndex,\n              parentSchemaPath: schemaPath,\n              previousDoc,\n              previousSiblingDoc: previousDoc?.[field.name]?.[rowIndex] || ({} as JsonObject),\n              req,\n              siblingData: siblingData?.[field.name]?.[rowIndex] || {},\n              siblingDoc: row ? { ...row } : {},\n            }),\n          )\n        })\n        await Promise.all(promises)\n      }\n\n      break\n    }\n\n    case 'blocks': {\n      const rows = siblingDoc[field.name]\n\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n\n        rows.forEach((row, rowIndex) => {\n          const blockTypeToMatch = (row as JsonObject).blockType\n\n          const block: Block | undefined =\n            req.payload.blocks[blockTypeToMatch] ??\n            ((field.blockReferences ?? field.blocks).find(\n              (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n            ) as Block | undefined)\n\n          if (block) {\n            promises.push(\n              traverseFields({\n                blockData: siblingData?.[field.name]?.[rowIndex],\n                collection,\n                context,\n                data,\n                doc,\n                fields: block.fields,\n                global,\n                operation,\n                parentIndexPath: '',\n                parentIsLocalized: parentIsLocalized || field.localized,\n                parentPath: path + '.' + rowIndex,\n                parentSchemaPath: schemaPath + '.' + block.slug,\n                previousDoc,\n                previousSiblingDoc: previousDoc?.[field.name]?.[rowIndex] || ({} as JsonObject),\n                req,\n                siblingData: siblingData?.[field.name]?.[rowIndex] || {},\n                siblingDoc: row ? { ...row } : {},\n              }),\n            )\n          }\n        })\n\n        await Promise.all(promises)\n      }\n\n      break\n    }\n\n    case 'collapsible':\n    case 'row': {\n      await traverseFields({\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.fields,\n        global,\n        operation,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath: schemaPath,\n        previousDoc,\n        previousSiblingDoc: { ...previousSiblingDoc },\n        req,\n        siblingData: siblingData || {},\n        siblingDoc: { ...siblingDoc },\n      })\n\n      break\n    }\n\n    case 'group': {\n      if (fieldAffectsData(field)) {\n        await traverseFields({\n          blockData,\n          collection,\n          context,\n          data,\n          doc,\n          fields: field.fields,\n          global,\n          operation,\n          parentIndexPath: '',\n          parentIsLocalized: parentIsLocalized || field.localized,\n          parentPath: path,\n          parentSchemaPath: schemaPath,\n          previousDoc,\n          previousSiblingDoc: (previousDoc?.[field.name] as JsonObject) || {},\n          req,\n          siblingData: (siblingData?.[field.name] as JsonObject) || {},\n          siblingDoc: (siblingDoc?.[field.name] as JsonObject) || {},\n        })\n      } else {\n        await traverseFields({\n          blockData,\n          collection,\n          context,\n          data,\n          doc,\n          fields: field.fields,\n          global,\n          operation,\n          parentIndexPath: indexPath,\n          parentIsLocalized,\n          parentPath,\n          parentSchemaPath: schemaPath,\n          previousDoc,\n          previousSiblingDoc: { ...previousSiblingDoc },\n          req,\n          siblingData: siblingData || {},\n          siblingDoc: { ...siblingDoc },\n        })\n      }\n\n      break\n    }\n\n    case 'richText': {\n      if (!field?.editor) {\n        throw new MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n\n      if (typeof field.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      const editor: RichTextAdapter = field.editor\n\n      if (editor?.hooks?.afterChange?.length) {\n        for (const hook of editor.hooks.afterChange) {\n          const hookedValue = await hook({\n            collection,\n            context,\n            data,\n            field,\n            global,\n            indexPath: indexPathSegments,\n            operation,\n            originalDoc: doc,\n            parentIsLocalized,\n            path: pathSegments,\n            previousDoc,\n            previousSiblingDoc,\n            previousValue: previousDoc?.[field.name],\n            req,\n            schemaPath: schemaPathSegments,\n            siblingData,\n            value: siblingDoc?.[field.name],\n          })\n\n          if (hookedValue !== undefined) {\n            siblingDoc[field.name] = hookedValue\n          }\n        }\n      }\n      break\n    }\n\n    case 'tab': {\n      let tabSiblingData = siblingData\n      let tabSiblingDoc = siblingDoc\n      let tabPreviousSiblingDoc = siblingDoc\n\n      const isNamedTab = tabHasName(field)\n\n      if (isNamedTab) {\n        tabSiblingData = (siblingData?.[field.name] ?? {}) as JsonObject\n        tabSiblingDoc = (siblingDoc?.[field.name] ?? {}) as JsonObject\n        tabPreviousSiblingDoc = (previousDoc?.[field.name] ?? {}) as JsonObject\n      }\n\n      await traverseFields({\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.fields,\n        global,\n        operation,\n        parentIndexPath: isNamedTab ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedTab ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        previousDoc,\n        previousSiblingDoc: tabPreviousSiblingDoc,\n        req,\n        siblingData: tabSiblingData,\n        siblingDoc: tabSiblingDoc,\n      })\n\n      break\n    }\n\n    case 'tabs': {\n      await traverseFields({\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.tabs.map((tab) => ({ ...tab, type: 'tab' })),\n        global,\n        operation,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath: path,\n        parentSchemaPath: schemaPath,\n        previousDoc,\n        previousSiblingDoc: { ...previousSiblingDoc },\n        req,\n        siblingData: siblingData || {},\n        siblingDoc: { ...siblingDoc },\n      })\n\n      break\n    }\n\n    default: {\n      break\n    }\n  }\n}\n"], "names": ["MissingEditorProp", "fieldAffectsData", "tabHasName", "getFieldPathsModified", "getFieldPaths", "traverseFields", "promise", "blockData", "collection", "context", "data", "doc", "field", "fieldIndex", "global", "operation", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "previousDoc", "previousSiblingDoc", "req", "siblingData", "siblingDoc", "siblingFields", "indexPath", "path", "schemaPath", "index", "pathSegments", "split", "schemaPathSegments", "indexPathSegments", "filter", "Boolean", "map", "Number", "hooks", "afterChange", "hook", "hookedValue", "originalDoc", "previousValue", "name", "value", "undefined", "type", "rows", "Array", "isArray", "promises", "for<PERSON>ach", "row", "rowIndex", "push", "fields", "localized", "Promise", "all", "blockTypeToMatch", "blockType", "block", "payload", "blocks", "blockReferences", "find", "curBlock", "slug", "editor", "Error", "length", "tabSiblingData", "tabSiblingDoc", "tabPreviousSiblingDoc", "isNamedTab", "tabs", "tab"], "mappings": ";;;;AAOA,SAASA,iBAAiB,QAAQ,2BAA0B;AAC5D,SAASC,gBAAgB,EAAEC,UAAU,QAAQ,wBAAuB;AACpE,SAASC,yBAAyBC,aAAa,QAAQ,yBAAwB;AAC/E,SAASC,cAAc,QAAQ,sBAAqB;;;;;AA8B7C,MAAMC,UAAU,OAAO,EAC5BC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,kBAAkB,EAClBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,aAAa,EACR;IACL,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAE,OAAGxB,6MAAAA,EAAc;QACpDQ;QACAiB,OAAOhB;QACPG;QACAE;QACAC;IACF;IAEA,MAAMW,eAAeH,OAAOA,KAAKI,KAAK,CAAC,OAAO,EAAE;IAChD,MAAMC,qBAAqBJ,aAAaA,WAAWG,KAAK,CAAC,OAAO,EAAE;IAClE,MAAME,oBAAoBP,YAAYA,UAAUK,KAAK,CAAC,KAAKG,MAAM,CAACC,UAAUC,IAAIC,UAAU,EAAE;IAE5F,QAAIpC,0MAAAA,EAAiBW,QAAQ;QAC3B,gBAAgB;QAChB,IAAI,WAAWA,SAASA,MAAM0B,KAAK,EAAEC,aAAa;YAChD,KAAK,MAAMC,QAAQ5B,MAAM0B,KAAK,CAACC,WAAW,CAAE;gBAC1C,MAAME,cAAc,MAAMD,KAAK;oBAC7BjC;oBACAC;oBACAC;oBACAC;oBACAE;oBACAE;oBACAY,WAAWO;oBACXlB;oBACA2B,aAAa/B;oBACbgB,MAAMG;oBACNV;oBACAC;oBACAsB,eAAevB,aAAa,CAACR,MAAMgC,IAAI,CAAC;oBACxCtB;oBACAM,YAAYI;oBACZT;oBACAE,eAAeA;oBACfoB,OAAOrB,YAAY,CAACZ,MAAMgC,IAAI,CAAC;gBACjC;gBAEA,IAAIH,gBAAgBK,WAAW;oBAC7BtB,UAAU,CAACZ,MAAMgC,IAAI,CAAC,GAAGH;gBAC3B;YACF;QACF;IACF;IAEA,qBAAqB;IACrB,OAAQ7B,MAAMmC,IAAI;QAChB,KAAK;YAAS;gBACZ,MAAMC,OAAOxB,UAAU,CAACZ,MAAMgC,IAAI,CAAC;gBAEnC,IAAIK,MAAMC,OAAO,CAACF,OAAO;oBACvB,MAAMG,WAA4B,EAAE;oBACpCH,KAAKI,OAAO,CAAC,CAACC,KAAKC;wBACjBH,SAASI,IAAI,KACXlD,+NAAAA,EAAe;4BACbE;4BACAC;4BACAC;4BACAC;4BACAC;4BACA6C,QAAQ5C,MAAM4C,MAAM;4BACpB1C;4BACAC;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBL,MAAM6C,SAAS;4BACvDvC,YAAYS,OAAO,MAAM2B;4BACzBnC,kBAAkBS;4BAClBR;4BACAC,oBAAoBD,aAAa,CAACR,MAAMgC,IAAI,CAAC,EAAE,CAACU,SAAS,IAAK,CAAC;4BAC/DhC;4BACAC,aAAaA,aAAa,CAACX,MAAMgC,IAAI,CAAC,EAAE,CAACU,SAAS,IAAI,CAAC;4BACvD9B,YAAY6B,MAAM;gCAAE,GAAGA,GAAG;4BAAC,IAAI,CAAC;wBAClC;oBAEJ;oBACA,MAAMK,QAAQC,GAAG,CAACR;gBACpB;gBAEA;YACF;QAEA,KAAK;YAAU;gBACb,MAAMH,OAAOxB,UAAU,CAACZ,MAAMgC,IAAI,CAAC;gBAEnC,IAAIK,MAAMC,OAAO,CAACF,OAAO;oBACvB,MAAMG,WAA4B,EAAE;oBAEpCH,KAAKI,OAAO,CAAC,CAACC,KAAKC;wBACjB,MAAMM,mBAAoBP,IAAmBQ,SAAS;wBAEtD,MAAMC,QACJxC,IAAIyC,OAAO,CAACC,MAAM,CAACJ,iBAAiB,IAClChD,CAAAA,MAAMqD,eAAe,IAAIrD,MAAMoD,MAAK,EAAGE,IAAI,CAC3C,CAACC,WAAa,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKR;wBAGpE,IAAIE,OAAO;4BACTX,SAASI,IAAI,KACXlD,+NAAAA,EAAe;gCACbE,WAAWgB,aAAa,CAACX,MAAMgC,IAAI,CAAC,EAAE,CAACU,SAAS;gCAChD9C;gCACAC;gCACAC;gCACAC;gCACA6C,QAAQM,MAAMN,MAAM;gCACpB1C;gCACAC;gCACAC,iBAAiB;gCACjBC,mBAAmBA,qBAAqBL,MAAM6C,SAAS;gCACvDvC,YAAYS,OAAO,MAAM2B;gCACzBnC,kBAAkBS,aAAa,MAAMkC,MAAMM,IAAI;gCAC/ChD;gCACAC,oBAAoBD,aAAa,CAACR,MAAMgC,IAAI,CAAC,EAAE,CAACU,SAAS,IAAK,CAAC;gCAC/DhC;gCACAC,aAAaA,aAAa,CAACX,MAAMgC,IAAI,CAAC,EAAE,CAACU,SAAS,IAAI,CAAC;gCACvD9B,YAAY6B,MAAM;oCAAE,GAAGA,GAAG;gCAAC,IAAI,CAAC;4BAClC;wBAEJ;oBACF;oBAEA,MAAMK,QAAQC,GAAG,CAACR;gBACpB;gBAEA;YACF;QAEA,KAAK;QACL,KAAK;YAAO;gBACV,UAAM9C,+NAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACA6C,QAAQ5C,MAAM4C,MAAM;oBACpB1C;oBACAC;oBACAC,iBAAiBU;oBACjBT;oBACAC;oBACAC,kBAAkBS;oBAClBR;oBACAC,oBAAoB;wBAAE,GAAGA,kBAAkB;oBAAC;oBAC5CC;oBACAC,aAAaA,eAAe,CAAC;oBAC7BC,YAAY;wBAAE,GAAGA,UAAU;oBAAC;gBAC9B;gBAEA;YACF;QAEA,KAAK;YAAS;gBACZ,QAAIvB,0MAAAA,EAAiBW,QAAQ;oBAC3B,UAAMP,+NAAAA,EAAe;wBACnBE;wBACAC;wBACAC;wBACAC;wBACAC;wBACA6C,QAAQ5C,MAAM4C,MAAM;wBACpB1C;wBACAC;wBACAC,iBAAiB;wBACjBC,mBAAmBA,qBAAqBL,MAAM6C,SAAS;wBACvDvC,YAAYS;wBACZR,kBAAkBS;wBAClBR;wBACAC,oBAAqBD,aAAa,CAACR,MAAMgC,IAAI,CAAC,IAAmB,CAAC;wBAClEtB;wBACAC,aAAcA,aAAa,CAACX,MAAMgC,IAAI,CAAC,IAAmB,CAAC;wBAC3DpB,YAAaA,YAAY,CAACZ,MAAMgC,IAAI,CAAC,IAAmB,CAAC;oBAC3D;gBACF,OAAO;oBACL,UAAMvC,+NAAAA,EAAe;wBACnBE;wBACAC;wBACAC;wBACAC;wBACAC;wBACA6C,QAAQ5C,MAAM4C,MAAM;wBACpB1C;wBACAC;wBACAC,iBAAiBU;wBACjBT;wBACAC;wBACAC,kBAAkBS;wBAClBR;wBACAC,oBAAoB;4BAAE,GAAGA,kBAAkB;wBAAC;wBAC5CC;wBACAC,aAAaA,eAAe,CAAC;wBAC7BC,YAAY;4BAAE,GAAGA,UAAU;wBAAC;oBAC9B;gBACF;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAACZ,OAAOyD,QAAQ;oBAClB,MAAM,IAAIrE,6MAAAA,CAAkBY,OAAO,8HAA8H;;gBACnK;gBAEA,IAAI,OAAOA,MAAMyD,MAAM,KAAK,YAAY;oBACtC,MAAM,IAAIC,MAAM;gBAClB;gBAEA,MAAMD,SAA0BzD,MAAMyD,MAAM;gBAE5C,IAAIA,QAAQ/B,OAAOC,aAAagC,QAAQ;oBACtC,KAAK,MAAM/B,QAAQ6B,OAAO/B,KAAK,CAACC,WAAW,CAAE;wBAC3C,MAAME,cAAc,MAAMD,KAAK;4BAC7BhC;4BACAC;4BACAC;4BACAE;4BACAE;4BACAY,WAAWO;4BACXlB;4BACA2B,aAAa/B;4BACbM;4BACAU,MAAMG;4BACNV;4BACAC;4BACAsB,eAAevB,aAAa,CAACR,MAAMgC,IAAI,CAAC;4BACxCtB;4BACAM,YAAYI;4BACZT;4BACAsB,OAAOrB,YAAY,CAACZ,MAAMgC,IAAI,CAAC;wBACjC;wBAEA,IAAIH,gBAAgBK,WAAW;4BAC7BtB,UAAU,CAACZ,MAAMgC,IAAI,CAAC,GAAGH;wBAC3B;oBACF;gBACF;gBACA;YACF;QAEA,KAAK;YAAO;gBACV,IAAI+B,iBAAiBjD;gBACrB,IAAIkD,gBAAgBjD;gBACpB,IAAIkD,wBAAwBlD;gBAE5B,MAAMmD,iBAAazE,oMAAAA,EAAWU;gBAE9B,IAAI+D,YAAY;oBACdH,iBAAkBjD,aAAa,CAACX,MAAMgC,IAAI,CAAC,IAAI,CAAC;oBAChD6B,gBAAiBjD,YAAY,CAACZ,MAAMgC,IAAI,CAAC,IAAI,CAAC;oBAC9C8B,wBAAyBtD,aAAa,CAACR,MAAMgC,IAAI,CAAC,IAAI,CAAC;gBACzD;gBAEA,UAAMvC,+NAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACA6C,QAAQ5C,MAAM4C,MAAM;oBACpB1C;oBACAC;oBACAC,iBAAiB2D,aAAa,KAAKjD;oBACnCT,mBAAmBA,qBAAqBL,MAAM6C,SAAS;oBACvDvC,YAAYyD,aAAahD,OAAOT;oBAChCC,kBAAkBS;oBAClBR;oBACAC,oBAAoBqD;oBACpBpD;oBACAC,aAAaiD;oBACbhD,YAAYiD;gBACd;gBAEA;YACF;QAEA,KAAK;YAAQ;gBACX,UAAMpE,+NAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACA6C,QAAQ5C,MAAMgE,IAAI,CAACxC,GAAG,CAAC,CAACyC,MAAS,CAAA;4BAAE,GAAGA,GAAG;4BAAE9B,MAAM;wBAAM,CAAA;oBACvDjC;oBACAC;oBACAC,iBAAiBU;oBACjBT;oBACAC,YAAYS;oBACZR,kBAAkBS;oBAClBR;oBACAC,oBAAoB;wBAAE,GAAGA,kBAAkB;oBAAC;oBAC5CC;oBACAC,aAAaA,eAAe,CAAC;oBAC7BC,YAAY;wBAAE,GAAGA,UAAU;oBAAC;gBAC9B;gBAEA;YACF;QAEA;YAAS;gBACP;YACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 3247, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterChange/traverseFields.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\nimport type { Field, TabAsField } from '../../config/types.js'\n\nimport { promise } from './promise.js'\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: JsonObject\n  doc: JsonObject\n  fields: (Field | TabAsField)[]\n  global: null | SanitizedGlobalConfig\n  operation: 'create' | 'update'\n  parentIndexPath: string\n  /**\n   * @todo make required in v4.0\n   */\n  parentIsLocalized?: boolean\n  parentPath: string\n  parentSchemaPath: string\n  previousDoc: JsonObject\n  previousSiblingDoc: JsonObject\n  req: PayloadRequest\n  siblingData: JsonObject\n  siblingDoc: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n}\n\nexport const traverseFields = async ({\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  fields,\n  global,\n  operation,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  previousDoc,\n  previousSiblingDoc,\n  req,\n  siblingData,\n  siblingDoc,\n  siblingFields,\n}: Args): Promise<void> => {\n  const promises: Promise<void>[] = []\n\n  fields.forEach((field, fieldIndex) => {\n    promises.push(\n      promise({\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        field,\n        fieldIndex,\n        global,\n        operation,\n        parentIndexPath,\n        parentIsLocalized: parentIsLocalized!,\n        parentPath,\n        parentSchemaPath,\n        previousDoc,\n        previousSiblingDoc,\n        req,\n        siblingData,\n        siblingDoc,\n        siblingFields,\n      }),\n    )\n  })\n\n  await Promise.all(promises)\n}\n"], "names": ["promise", "traverseFields", "blockData", "collection", "context", "data", "doc", "fields", "global", "operation", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "previousDoc", "previousSiblingDoc", "req", "siblingData", "siblingDoc", "siblingFields", "promises", "for<PERSON>ach", "field", "fieldIndex", "push", "Promise", "all"], "mappings": ";;;;AAMA,SAASA,OAAO,QAAQ,eAAc;;AA6B/B,MAAMC,iBAAiB,OAAO,EACnCC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,kBAAkB,EAClBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,aAAa,EACR;IACL,MAAMC,WAA4B,EAAE;IAEpCb,OAAOc,OAAO,CAAC,CAACC,OAAOC;QACrBH,SAASI,IAAI,KACXxB,iNAAAA,EAAQ;YACNE;YACAC;YACAC;YACAC;YACAC;YACAgB;YACAC;YACAf;YACAC;YACAC;YACAC,mBAAmBA;YACnBC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;QACF;IAEJ;IAEA,MAAMM,QAAQC,GAAG,CAACN;AACpB,EAAC", "debugId": null}}, {"offset": {"line": 3284, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/afterChange/index.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\n\nimport { traverseFields } from './traverseFields.js'\n\ntype Args<T extends JsonObject> = {\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  /**\n   * The data before hooks\n   */\n  data: T\n  /**\n   * The data after hooks\n   */\n  doc: T\n  global: null | SanitizedGlobalConfig\n  operation: 'create' | 'update'\n  previousDoc: T\n  req: PayloadRequest\n}\n\n/**\n * This function is responsible for the following actions, in order:\n * - Execute field hooks\n */\nexport const afterChange = async <T extends JsonObject>({\n  collection,\n  context,\n  data,\n  doc: incomingDoc,\n  global,\n  operation,\n  previousDoc,\n  req,\n}: Args<T>): Promise<T> => {\n  await traverseFields({\n    collection,\n    context,\n    data,\n    doc: incomingDoc,\n    fields: (collection?.fields || global?.fields)!,\n    global,\n    operation,\n    parentIndexPath: '',\n    parentIsLocalized: false,\n    parentPath: '',\n    parentSchemaPath: '',\n    previousDoc,\n    previousSiblingDoc: previousDoc,\n    req,\n    siblingData: data,\n    siblingDoc: incomingDoc,\n  })\n\n  return incomingDoc\n}\n"], "names": ["traverseFields", "afterChange", "collection", "context", "data", "doc", "incomingDoc", "global", "operation", "previousDoc", "req", "fields", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "previousSiblingDoc", "siblingData", "siblingDoc"], "mappings": ";;;;AAKA,SAASA,cAAc,QAAQ,sBAAqB;;AAuB7C,MAAMC,cAAc,OAA6B,EACtDC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,KAAKC,WAAW,EAChBC,MAAM,EACNC,SAAS,EACTC,WAAW,EACXC,GAAG,EACK;IACR,UAAMV,+NAAAA,EAAe;QACnBE;QACAC;QACAC;QACAC,KAAKC;QACLK,QAAST,YAAYS,UAAUJ,QAAQI;QACvCJ;QACAC;QACAI,iBAAiB;QACjBC,mBAAmB;QACnBC,YAAY;QACZC,kBAAkB;QAClBN;QACAO,oBAAoBP;QACpBC;QACAO,aAAab;QACbc,YAAYZ;IACd;IAEA,OAAOA;AACT,EAAC", "debugId": null}}, {"offset": {"line": 3315, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeChange/getExistingRowDoc.ts"], "sourcesContent": ["/**\n * If there is an incoming row id,\n * and it matches the existing sibling doc id,\n * this is an existing row, so it should be merged.\n * Otherwise, return an empty object.\n */\nimport type { JsonObject } from '../../../types/index.js'\n\nexport const getExistingRowDoc = (incomingRow: JsonObject, existingRows?: unknown): JsonObject => {\n  if (incomingRow.id && Array.isArray(existingRows)) {\n    const matchedExistingRow = existingRows.find((existingRow) => {\n      if (typeof existingRow === 'object' && 'id' in existingRow) {\n        if (existingRow.id === incomingRow.id) {\n          return existingRow\n        }\n      }\n\n      return false\n    })\n\n    if (matchedExistingRow) {\n      return matchedExistingRow\n    }\n  }\n\n  return {}\n}\n"], "names": ["getExistingRowDoc", "incomingRow", "existingRows", "id", "Array", "isArray", "matchedExistingRow", "find", "existingRow"], "mappings": "AAAA;;;;;CAKC,GAGD;;;;AAAO,MAAMA,oBAAoB,CAACC,aAAyBC;IACzD,IAAID,YAAYE,EAAE,IAAIC,MAAMC,OAAO,CAACH,eAAe;QACjD,MAAMI,qBAAqBJ,aAAaK,IAAI,CAAC,CAACC;YAC5C,IAAI,OAAOA,gBAAgB,YAAY,QAAQA,aAAa;gBAC1D,IAAIA,YAAYL,EAAE,KAAKF,YAAYE,EAAE,EAAE;oBACrC,OAAOK;gBACT;YACF;YAEA,OAAO;QACT;QAEA,IAAIF,oBAAoB;YACtB,OAAOA;QACT;IACF;IAEA,OAAO,CAAC;AACV,EAAC", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeChange/promise.ts"], "sourcesContent": ["import type { RichTextAdapter } from '../../../admin/RichText.js'\nimport type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { ValidationFieldError } from '../../../errors/index.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { JsonObject, Operation, PayloadRequest } from '../../../types/index.js'\nimport type { Block, Field, TabAsField, Validate } from '../../config/types.js'\n\nimport { MissingEditorProp } from '../../../errors/index.js'\nimport { type RequestContext, validateBlocksFilterOptions } from '../../../index.js'\nimport { deepMergeWithSourceArrays } from '../../../utilities/deepMerge.js'\nimport { getTranslatedLabel } from '../../../utilities/getTranslatedLabel.js'\nimport { fieldAffectsData, fieldShouldBeLocalized, tabHasName } from '../../config/types.js'\nimport { getFieldPathsModified as getFieldPaths } from '../../getFieldPaths.js'\nimport { getExistingRowDoc } from './getExistingRowDoc.js'\nimport { traverseFields } from './traverseFields.js'\n\nfunction buildFieldLabel(parentLabel: string, label: string | undefined): string {\n  if (!label) {\n    return parentLabel\n  }\n  const capitalizedLabel = label.charAt(0).toUpperCase() + label.slice(1)\n  return parentLabel && capitalizedLabel\n    ? `${parentLabel} > ${capitalizedLabel}`\n    : capitalizedLabel || parentLabel\n}\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: JsonObject\n  doc: JsonObject\n  docWithLocales: JsonObject\n  errors: ValidationFieldError[]\n  field: Field | TabAsField\n  fieldIndex: number\n  /**\n   * Built up labels of parent fields\n   *\n   * @example \"Group Field > Tab Field > Text Field\"\n   */\n  fieldLabelPath: string\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  mergeLocaleActions: (() => Promise<void> | void)[]\n  operation: Operation\n  overrideAccess: boolean\n  parentIndexPath: string\n  parentIsLocalized: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingData: JsonObject\n  siblingDoc: JsonObject\n  siblingDocWithLocales?: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n  skipValidation: boolean\n}\n\n// This function is responsible for the following actions, in order:\n// - Run condition\n// - Execute field hooks\n// - Validate data\n// - Transform data for storage\n// - beforeDuplicate hooks (if duplicate)\n// - Unflatten locales\n\nexport const promise = async ({\n  id,\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  docWithLocales,\n  errors,\n  field,\n  fieldIndex,\n  fieldLabelPath,\n  global,\n  mergeLocaleActions,\n  operation,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingData,\n  siblingDoc,\n  siblingDocWithLocales,\n  siblingFields,\n  skipValidation,\n}: Args): Promise<void> => {\n  const { indexPath, path, schemaPath } = getFieldPaths({\n    field,\n    index: fieldIndex,\n    parentIndexPath,\n    parentPath,\n    parentSchemaPath,\n  })\n\n  const { localization } = req.payload.config\n  const defaultLocale = localization ? localization?.defaultLocale : 'en'\n  const operationLocale = req.locale || defaultLocale\n\n  const pathSegments = path ? path.split('.') : []\n  const schemaPathSegments = schemaPath ? schemaPath.split('.') : []\n  const indexPathSegments = indexPath ? indexPath.split('-').filter(Boolean)?.map(Number) : []\n\n  const passesCondition = field.admin?.condition\n    ? Boolean(\n        field.admin.condition(data, siblingData, {\n          blockData: blockData!,\n          operation,\n          path: pathSegments,\n          user: req.user,\n        }),\n      )\n    : true\n  let skipValidationFromHere = skipValidation || !passesCondition\n\n  if (fieldAffectsData(field)) {\n    // skip validation if the field is localized and the incoming data is null\n    if (fieldShouldBeLocalized({ field, parentIsLocalized }) && operationLocale !== defaultLocale) {\n      if (['array', 'blocks'].includes(field.type) && siblingData[field.name!] === null) {\n        skipValidationFromHere = true\n      }\n    }\n\n    // Execute hooks\n    if ('hooks' in field && field.hooks?.beforeChange) {\n      for (const hook of field.hooks.beforeChange) {\n        const hookedValue = await hook({\n          blockData,\n          collection,\n          context,\n          data,\n          field,\n          global,\n          indexPath: indexPathSegments,\n          operation,\n          originalDoc: doc,\n          path: pathSegments,\n          previousSiblingDoc: siblingDoc,\n          previousValue: siblingDoc[field.name],\n          req,\n          schemaPath: schemaPathSegments,\n          siblingData,\n          siblingDocWithLocales,\n          siblingFields: siblingFields!,\n          value: siblingData[field.name],\n        })\n\n        if (hookedValue !== undefined) {\n          siblingData[field.name] = hookedValue\n        }\n      }\n    }\n\n    // Validate\n    if (!skipValidationFromHere && 'validate' in field && field.validate) {\n      const valueToValidate = siblingData[field.name]\n      let jsonError: object\n\n      if (field.type === 'json' && typeof siblingData[field.name] === 'string') {\n        try {\n          JSON.parse(siblingData[field.name] as string)\n        } catch (e) {\n          jsonError = e as object\n        }\n      }\n\n      const validateFn: Validate<object, object, object, object> = field.validate as Validate<\n        object,\n        object,\n        object,\n        object\n      >\n\n      const validationResult = await validateFn(valueToValidate as never, {\n        ...field,\n        id,\n        blockData: blockData!,\n        collectionSlug: collection?.slug,\n        data: deepMergeWithSourceArrays(doc, data),\n        event: 'submit',\n        // @ts-expect-error\n        jsonError,\n        operation,\n        overrideAccess,\n        path: pathSegments,\n        preferences: { fields: {} },\n        previousValue: siblingDoc[field.name],\n        req,\n        siblingData: deepMergeWithSourceArrays(siblingDoc, siblingData),\n      })\n\n      if (typeof validationResult === 'string') {\n        let filterOptionsError = false\n\n        if (field.type === 'blocks' && field.filterOptions) {\n          // Re-run filteroptions. If the validation error is due to filteroptions, we need to add error paths to all the blocks\n          // that are no longer valid\n          const validationResult = await validateBlocksFilterOptions({\n            id,\n            data,\n            filterOptions: field.filterOptions,\n            req,\n            siblingData,\n            value: siblingData[field.name],\n          })\n          if (validationResult?.invalidBlockSlugs?.length) {\n            filterOptionsError = true\n            let rowIndex = -1\n            for (const block of siblingData[field.name] as JsonObject[]) {\n              rowIndex++\n              if (validationResult.invalidBlockSlugs.includes(block.blockType as string)) {\n                const blockConfigOrSlug = (field.blockReferences ?? field.blocks).find(\n                  (blockFromField) =>\n                    typeof blockFromField === 'string'\n                      ? blockFromField === block.blockType\n                      : blockFromField.slug === block.blockType,\n                ) as Block | undefined\n                const blockConfig =\n                  typeof blockConfigOrSlug !== 'string'\n                    ? blockConfigOrSlug\n                    : req.payload.config?.blocks?.[blockConfigOrSlug]\n\n                const blockLabelPath =\n                  field?.label === false\n                    ? fieldLabelPath\n                    : buildFieldLabel(\n                        fieldLabelPath,\n                        `${getTranslatedLabel(field?.label || field?.name, req.i18n)} > ${req.t('fields:block')} ${rowIndex + 1} (${getTranslatedLabel(blockConfig?.labels?.singular || block.blockType, req.i18n)})`,\n                      )\n\n                errors.push({\n                  label: blockLabelPath,\n                  message: req.t('validation:invalidBlock', { block: block.blockType }),\n                  path: `${path}.${rowIndex}.id`,\n                })\n              }\n            }\n          }\n        }\n\n        if (!filterOptionsError) {\n          // If the error is due to block filterOptions, we want to push the errors for each individual block, not the blocks\n          // field itself => only push the error if the field is not a block field with validation failure due to filterOptions\n          const fieldLabel = buildFieldLabel(\n            fieldLabelPath,\n            getTranslatedLabel(field?.label || field?.name, req.i18n),\n          )\n\n          errors.push({\n            label: fieldLabel,\n            message: validationResult,\n            path,\n          })\n        }\n      }\n    }\n\n    // Push merge locale action if applicable\n    if (localization && fieldShouldBeLocalized({ field, parentIsLocalized })) {\n      mergeLocaleActions.push(() => {\n        const localeData: Record<string, unknown> = {}\n\n        for (const locale of localization.localeCodes) {\n          const fieldValue =\n            locale === req.locale\n              ? siblingData[field.name!]\n              : siblingDocWithLocales?.[field.name!]?.[locale]\n\n          // update locale value if it's not undefined\n          if (typeof fieldValue !== 'undefined') {\n            localeData[locale] = fieldValue\n          }\n        }\n\n        // If there are locales with data, set the data\n        if (Object.keys(localeData).length > 0) {\n          siblingData[field.name!] = localeData\n        }\n      })\n    }\n  }\n\n  switch (field.type) {\n    case 'array': {\n      const rows = siblingData[field.name]\n\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n\n        rows.forEach((row, rowIndex) => {\n          promises.push(\n            traverseFields({\n              id,\n              blockData,\n              collection,\n              context,\n              data,\n              doc,\n              docWithLocales,\n              errors,\n              fieldLabelPath:\n                field?.label === false\n                  ? fieldLabelPath\n                  : buildFieldLabel(\n                      fieldLabelPath,\n                      `${getTranslatedLabel(field?.label || field?.name, req.i18n)} ${rowIndex + 1}`,\n                    ),\n              fields: field.fields,\n              global,\n              mergeLocaleActions,\n              operation,\n              overrideAccess,\n              parentIndexPath: '',\n              parentIsLocalized: parentIsLocalized || field.localized,\n              parentPath: path + '.' + rowIndex,\n              parentSchemaPath: schemaPath,\n              req,\n              siblingData: row as JsonObject,\n              siblingDoc: getExistingRowDoc(row as JsonObject, siblingDoc[field.name]),\n              siblingDocWithLocales: getExistingRowDoc(\n                row as JsonObject,\n                siblingDocWithLocales?.[field.name],\n              ),\n              skipValidation: skipValidationFromHere,\n            }),\n          )\n        })\n\n        await Promise.all(promises)\n      }\n\n      break\n    }\n\n    case 'blocks': {\n      const rows = siblingData[field.name]\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n\n        rows.forEach((row, rowIndex) => {\n          const rowSiblingDoc = getExistingRowDoc(row as JsonObject, siblingDoc[field.name])\n\n          const rowSiblingDocWithLocales = getExistingRowDoc(\n            row as JsonObject,\n            siblingDocWithLocales ? siblingDocWithLocales[field.name] : {},\n          )\n\n          const blockTypeToMatch = (row as JsonObject).blockType || rowSiblingDoc.blockType\n\n          const block: Block | undefined =\n            req.payload.blocks[blockTypeToMatch] ??\n            ((field.blockReferences ?? field.blocks).find(\n              (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n            ) as Block | undefined)\n\n          const blockLabelPath =\n            field?.label === false\n              ? fieldLabelPath\n              : buildFieldLabel(\n                  fieldLabelPath,\n                  `${getTranslatedLabel(field?.label || field?.name, req.i18n)} > ${req.t('fields:block')} ${rowIndex + 1} (${getTranslatedLabel(block?.labels?.singular || blockTypeToMatch, req.i18n)})`,\n                )\n\n          if (block) {\n            promises.push(\n              traverseFields({\n                id,\n                blockData: row,\n                collection,\n                context,\n                data,\n                doc,\n                docWithLocales,\n                errors,\n                fieldLabelPath: blockLabelPath,\n\n                fields: block.fields,\n                global,\n                mergeLocaleActions,\n                operation,\n                overrideAccess,\n                parentIndexPath: '',\n                parentIsLocalized: parentIsLocalized || field.localized,\n                parentPath: path + '.' + rowIndex,\n                parentSchemaPath: schemaPath + '.' + block.slug,\n                req,\n                siblingData: row as JsonObject,\n                siblingDoc: rowSiblingDoc,\n                siblingDocWithLocales: rowSiblingDocWithLocales,\n                skipValidation: skipValidationFromHere,\n              }),\n            )\n          }\n        })\n\n        await Promise.all(promises)\n      }\n\n      break\n    }\n\n    case 'collapsible':\n    case 'row': {\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        docWithLocales,\n        errors,\n        fieldLabelPath:\n          field.type === 'row' || field?.label === false\n            ? fieldLabelPath\n            : buildFieldLabel(\n                fieldLabelPath,\n                getTranslatedLabel(field?.label || field?.type, req.i18n),\n              ),\n        fields: field.fields,\n        global,\n        mergeLocaleActions,\n        operation,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n        siblingDocWithLocales: siblingDocWithLocales!,\n        skipValidation: skipValidationFromHere,\n      })\n\n      break\n    }\n\n    case 'group': {\n      let groupSiblingData = siblingData\n      let groupSiblingDoc = siblingDoc\n      let groupSiblingDocWithLocales = siblingDocWithLocales\n\n      const isNamedGroup = fieldAffectsData(field)\n\n      if (isNamedGroup) {\n        if (typeof siblingData[field.name] !== 'object') {\n          siblingData[field.name] = {}\n        }\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          siblingDoc[field.name] = {}\n        }\n\n        if (typeof siblingDocWithLocales![field.name] !== 'object') {\n          siblingDocWithLocales![field.name] = {}\n        }\n        if (typeof siblingData[field.name] !== 'object') {\n          siblingData[field.name] = {}\n        }\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          siblingDoc[field.name] = {}\n        }\n\n        if (typeof siblingDocWithLocales![field.name] !== 'object') {\n          siblingDocWithLocales![field.name] = {}\n        }\n\n        groupSiblingData = siblingData[field.name] as JsonObject\n        groupSiblingDoc = siblingDoc[field.name] as JsonObject\n        groupSiblingDocWithLocales = siblingDocWithLocales![field.name] as JsonObject\n      }\n\n      const fallbackLabel = field?.label || (isNamedGroup ? field.name : field?.type)\n\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        docWithLocales,\n        errors,\n        fieldLabelPath:\n          field?.label === false\n            ? fieldLabelPath\n            : buildFieldLabel(fieldLabelPath, getTranslatedLabel(fallbackLabel, req.i18n)),\n        fields: field.fields,\n        global,\n        mergeLocaleActions,\n        operation,\n        overrideAccess,\n        parentIndexPath: isNamedGroup ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedGroup ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData: groupSiblingData,\n        siblingDoc: groupSiblingDoc,\n        siblingDocWithLocales: groupSiblingDocWithLocales!,\n        skipValidation: skipValidationFromHere,\n      })\n\n      break\n    }\n\n    case 'point': {\n      // Transform point data for storage\n      if (\n        Array.isArray(siblingData[field.name]) &&\n        siblingData[field.name][0] !== null &&\n        siblingData[field.name][1] !== null\n      ) {\n        siblingData[field.name] = {\n          type: 'Point',\n          coordinates: [\n            parseFloat(siblingData[field.name][0]),\n            parseFloat(siblingData[field.name][1]),\n          ],\n        }\n      }\n\n      break\n    }\n\n    case 'richText': {\n      if (!field?.editor) {\n        throw new MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n\n      if (typeof field?.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      const editor: RichTextAdapter = field?.editor\n\n      if (editor?.hooks?.beforeChange?.length) {\n        for (const hook of editor.hooks.beforeChange) {\n          const hookedValue = await hook({\n            collection,\n            context,\n            data,\n            docWithLocales,\n            errors,\n            field,\n            fieldLabelPath:\n              field?.label === false\n                ? fieldLabelPath\n                : buildFieldLabel(\n                    fieldLabelPath,\n                    getTranslatedLabel(field?.label || field?.name, req.i18n),\n                  ),\n            global,\n            indexPath: indexPathSegments,\n            mergeLocaleActions,\n            operation,\n            originalDoc: doc,\n            overrideAccess,\n            parentIsLocalized,\n            path: pathSegments,\n            previousSiblingDoc: siblingDoc,\n            previousValue: siblingDoc[field.name],\n            req,\n            schemaPath: schemaPathSegments,\n            siblingData,\n            siblingDocWithLocales,\n            skipValidation,\n            value: siblingData[field.name],\n          })\n\n          if (hookedValue !== undefined) {\n            siblingData[field.name] = hookedValue\n          }\n        }\n      }\n\n      break\n    }\n\n    case 'tab': {\n      let tabSiblingData = siblingData\n      let tabSiblingDoc = siblingDoc\n      let tabSiblingDocWithLocales = siblingDocWithLocales\n\n      const isNamedTab = tabHasName(field)\n\n      if (isNamedTab) {\n        if (typeof siblingData[field.name] !== 'object') {\n          siblingData[field.name] = {}\n        }\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          siblingDoc[field.name] = {}\n        }\n\n        if (typeof siblingDocWithLocales![field.name] !== 'object') {\n          siblingDocWithLocales![field.name] = {}\n        }\n\n        tabSiblingData = siblingData[field.name] as JsonObject\n        tabSiblingDoc = siblingDoc[field.name] as JsonObject\n        tabSiblingDocWithLocales = siblingDocWithLocales![field.name] as JsonObject\n      }\n\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        docWithLocales,\n        errors,\n        fieldLabelPath:\n          field?.label === false\n            ? fieldLabelPath\n            : buildFieldLabel(\n                fieldLabelPath,\n                getTranslatedLabel(field?.label || field.name, req.i18n),\n              ),\n        fields: field.fields,\n        global,\n        mergeLocaleActions,\n        operation,\n        overrideAccess,\n        parentIndexPath: isNamedTab ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedTab ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData: tabSiblingData,\n        siblingDoc: tabSiblingDoc,\n        siblingDocWithLocales: tabSiblingDocWithLocales!,\n        skipValidation: skipValidationFromHere,\n      })\n\n      break\n    }\n\n    case 'tabs': {\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        docWithLocales,\n        errors,\n        fieldLabelPath:\n          field?.label === false\n            ? fieldLabelPath\n            : buildFieldLabel(fieldLabelPath, getTranslatedLabel(field?.label || '', req.i18n)),\n        fields: field.tabs.map((tab) => ({ ...tab, type: 'tab' })),\n        global,\n        mergeLocaleActions,\n        operation,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath: path,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n        siblingDocWithLocales: siblingDocWithLocales!,\n        skipValidation: skipValidationFromHere,\n      })\n\n      break\n    }\n\n    default: {\n      break\n    }\n  }\n}\n"], "names": ["MissingEditorProp", "validateBlocksFilterOptions", "deepMergeWithSourceArrays", "getTranslatedLabel", "fieldAffectsData", "fieldShouldBeLocalized", "tabHasName", "getFieldPathsModified", "getFieldPaths", "getExistingRowDoc", "traverseFields", "buildFieldLabel", "parentLabel", "label", "capitalizedLabel", "char<PERSON>t", "toUpperCase", "slice", "promise", "id", "blockData", "collection", "context", "data", "doc", "docWithLocales", "errors", "field", "fieldIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "global", "mergeLocaleActions", "operation", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingData", "siblingDoc", "siblingDocWithLocales", "siblingFields", "skipValidation", "indexPath", "path", "schemaPath", "index", "localization", "payload", "config", "defaultLocale", "operationLocale", "locale", "pathSegments", "split", "schemaPathSegments", "indexPathSegments", "filter", "Boolean", "map", "Number", "passesCondition", "admin", "condition", "user", "skipValidationFromHere", "includes", "type", "name", "hooks", "beforeChange", "hook", "hookedValue", "originalDoc", "previousSiblingDoc", "previousValue", "value", "undefined", "validate", "valueToValidate", "jsonError", "JSON", "parse", "e", "validateFn", "validationResult", "collectionSlug", "slug", "event", "preferences", "fields", "filterOptionsError", "filterOptions", "invalidBlockSlugs", "length", "rowIndex", "block", "blockType", "blockConfigOrSlug", "blockReferences", "blocks", "find", "blockFromField", "blockConfig", "blockLabelPath", "i18n", "t", "labels", "singular", "push", "message", "<PERSON><PERSON><PERSON><PERSON>", "localeData", "localeCodes", "fieldValue", "Object", "keys", "rows", "Array", "isArray", "promises", "for<PERSON>ach", "row", "localized", "Promise", "all", "rowSiblingDoc", "rowSiblingDocWithLocales", "blockTypeToMatch", "curBlock", "groupSiblingData", "groupSiblingDoc", "groupSiblingDocWithLocales", "isNamedGroup", "fallback<PERSON><PERSON><PERSON>", "coordinates", "parseFloat", "editor", "Error", "tabSiblingData", "tabSiblingDoc", "tabSiblingDocWithLocales", "isNamedTab", "tabs", "tab"], "mappings": ";;;;AAOA,SAASA,iBAAiB,QAAQ,2BAA0B;AAC5D,SAA8BC,2BAA2B,QAAQ,oBAAmB;AACpF,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,2CAA0C;AAC7E,SAASC,gBAAgB,EAAEC,sBAAsB,EAAEC,UAAU,QAAQ,wBAAuB;AAC5F,SAASC,yBAAyBC,aAAa,QAAQ,yBAAwB;AAC/E,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,cAAc,QAAQ,sBAAqB;;;;;;;;;AAEpD,SAASC,gBAAgBC,WAAmB,EAAEC,KAAyB;IACrE,IAAI,CAACA,OAAO;QACV,OAAOD;IACT;IACA,MAAME,mBAAmBD,MAAME,MAAM,CAAC,GAAGC,WAAW,KAAKH,MAAMI,KAAK,CAAC;IACrE,OAAOL,eAAeE,mBAClB,GAAGF,YAAY,GAAG,EAAEE,kBAAkB,GACtCA,oBAAoBF;AAC1B;AA8CO,MAAMM,UAAU,OAAO,EAC5BC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,qBAAqB,EACrBC,aAAa,EACbC,cAAc,EACT;IACL,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAE,OAAGtC,6MAAAA,EAAc;QACpDmB;QACAoB,OAAOnB;QACPM;QACAE;QACAC;IACF;IAEA,MAAM,EAAEW,YAAY,EAAE,GAAGV,IAAIW,OAAO,CAACC,MAAM;IAC3C,MAAMC,gBAAgBH,eAAeA,cAAcG,gBAAgB;IACnE,MAAMC,kBAAkBd,IAAIe,MAAM,IAAIF;IAEtC,MAAMG,eAAeT,OAAOA,KAAKU,KAAK,CAAC,OAAO,EAAE;IAChD,MAAMC,qBAAqBV,aAAaA,WAAWS,KAAK,CAAC,OAAO,EAAE;IAClE,MAAME,oBAAoBb,YAAYA,UAAUW,KAAK,CAAC,KAAKG,MAAM,CAACC,UAAUC,IAAIC,UAAU,EAAE;IAE5F,MAAMC,kBAAkBnC,MAAMoC,KAAK,EAAEC,YACjCL,QACEhC,MAAMoC,KAAK,CAACC,SAAS,CAACzC,MAAMgB,aAAa;QACvCnB,WAAWA;QACXY;QACAa,MAAMS;QACNW,MAAM3B,IAAI2B,IAAI;IAChB,MAEF;IACJ,IAAIC,yBAAyBvB,kBAAkB,CAACmB;IAEhD,QAAI1D,0MAAAA,EAAiBuB,QAAQ;QAC3B,0EAA0E;QAC1E,QAAItB,gNAAAA,EAAuB;YAAEsB;YAAOQ;QAAkB,MAAMiB,oBAAoBD,eAAe;YAC7F,IAAI;gBAAC;gBAAS;aAAS,CAACgB,QAAQ,CAACxC,MAAMyC,IAAI,KAAK7B,WAAW,CAACZ,MAAM0C,IAAI,CAAE,KAAK,MAAM;gBACjFH,yBAAyB;YAC3B;QACF;QAEA,gBAAgB;QAChB,IAAI,WAAWvC,SAASA,MAAM2C,KAAK,EAAEC,cAAc;YACjD,KAAK,MAAMC,QAAQ7C,MAAM2C,KAAK,CAACC,YAAY,CAAE;gBAC3C,MAAME,cAAc,MAAMD,KAAK;oBAC7BpD;oBACAC;oBACAC;oBACAC;oBACAI;oBACAG;oBACAc,WAAWa;oBACXzB;oBACA0C,aAAalD;oBACbqB,MAAMS;oBACNqB,oBAAoBnC;oBACpBoC,eAAepC,UAAU,CAACb,MAAM0C,IAAI,CAAC;oBACrC/B;oBACAQ,YAAYU;oBACZjB;oBACAE;oBACAC,eAAeA;oBACfmC,OAAOtC,WAAW,CAACZ,MAAM0C,IAAI,CAAC;gBAChC;gBAEA,IAAII,gBAAgBK,WAAW;oBAC7BvC,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAGI;gBAC5B;YACF;QACF;QAEA,WAAW;QACX,IAAI,CAACP,0BAA0B,cAAcvC,SAASA,MAAMoD,QAAQ,EAAE;YACpE,MAAMC,kBAAkBzC,WAAW,CAACZ,MAAM0C,IAAI,CAAC;YAC/C,IAAIY;YAEJ,IAAItD,MAAMyC,IAAI,KAAK,UAAU,OAAO7B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,KAAK,UAAU;gBACxE,IAAI;oBACFa,KAAKC,KAAK,CAAC5C,WAAW,CAACZ,MAAM0C,IAAI,CAAC;gBACpC,EAAE,OAAOe,GAAG;oBACVH,YAAYG;gBACd;YACF;YAEA,MAAMC,aAAuD1D,MAAMoD,QAAQ;YAO3E,MAAMO,mBAAmB,MAAMD,WAAWL,iBAA0B;gBAClE,GAAGrD,KAAK;gBACRR;gBACAC,WAAWA;gBACXmE,gBAAgBlE,YAAYmE;gBAC5BjE,UAAMrB,gOAAAA,EAA0BsB,KAAKD;gBACrCkE,OAAO;gBACP,mBAAmB;gBACnBR;gBACAjD;gBACAC;gBACAY,MAAMS;gBACNoC,aAAa;oBAAEC,QAAQ,CAAC;gBAAE;gBAC1Bf,eAAepC,UAAU,CAACb,MAAM0C,IAAI,CAAC;gBACrC/B;gBACAC,iBAAarC,gOAAAA,EAA0BsC,YAAYD;YACrD;YAEA,IAAI,OAAO+C,qBAAqB,UAAU;gBACxC,IAAIM,qBAAqB;gBAEzB,IAAIjE,MAAMyC,IAAI,KAAK,YAAYzC,MAAMkE,aAAa,EAAE;oBAClD,sHAAsH;oBACtH,2BAA2B;oBAC3B,MAAMP,mBAAmB,UAAMrF,iNAAAA,EAA4B;wBACzDkB;wBACAI;wBACAsE,eAAelE,MAAMkE,aAAa;wBAClCvD;wBACAC;wBACAsC,OAAOtC,WAAW,CAACZ,MAAM0C,IAAI,CAAC;oBAChC;oBACA,IAAIiB,kBAAkBQ,mBAAmBC,QAAQ;wBAC/CH,qBAAqB;wBACrB,IAAII,WAAW,CAAC;wBAChB,KAAK,MAAMC,SAAS1D,WAAW,CAACZ,MAAM0C,IAAI,CAAC,CAAkB;4BAC3D2B;4BACA,IAAIV,iBAAiBQ,iBAAiB,CAAC3B,QAAQ,CAAC8B,MAAMC,SAAS,GAAa;gCAC1E,MAAMC,oBAAqBxE,CAAAA,MAAMyE,eAAe,IAAIzE,MAAM0E,MAAK,EAAGC,IAAI,CACpE,CAACC,iBACC,OAAOA,mBAAmB,WACtBA,mBAAmBN,MAAMC,SAAS,GAClCK,eAAef,IAAI,KAAKS,MAAMC,SAAS;gCAE/C,MAAMM,cACJ,OAAOL,sBAAsB,WACzBA,oBACA7D,IAAIW,OAAO,CAACC,MAAM,EAAEmD,QAAQ,CAACF,kBAAkB;gCAErD,MAAMM,iBACJ9E,OAAOd,UAAU,QACbgB,iBACAlB,gBACEkB,gBACA,OAAG1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAO0C,MAAM/B,IAAIoE,IAAI,EAAE,GAAG,EAAEpE,IAAIqE,CAAC,CAAC,gBAAgB,CAAC,EAAEX,WAAW,EAAE,EAAE,MAAE7F,kNAAAA,EAAmBqG,aAAaI,QAAQC,YAAYZ,MAAMC,SAAS,EAAE5D,IAAIoE,IAAI,EAAE,CAAC,CAAC;gCAGrMhF,OAAOoF,IAAI,CAAC;oCACVjG,OAAO4F;oCACPM,SAASzE,IAAIqE,CAAC,CAAC,2BAA2B;wCAAEV,OAAOA,MAAMC,SAAS;oCAAC;oCACnErD,MAAM,GAAGA,KAAK,CAAC,EAAEmD,SAAS,GAAG,CAAC;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEA,IAAI,CAACJ,oBAAoB;oBACvB,mHAAmH;oBACnH,qHAAqH;oBACrH,MAAMoB,aAAarG,gBACjBkB,oBACA1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAO0C,MAAM/B,IAAIoE,IAAI;oBAG1DhF,OAAOoF,IAAI,CAAC;wBACVjG,OAAOmG;wBACPD,SAASzB;wBACTzC;oBACF;gBACF;YACF;QACF;QAEA,yCAAyC;QACzC,IAAIG,oBAAgB3C,gNAAAA,EAAuB;YAAEsB;YAAOQ;QAAkB,IAAI;YACxEJ,mBAAmB+E,IAAI,CAAC;gBACtB,MAAMG,aAAsC,CAAC;gBAE7C,KAAK,MAAM5D,UAAUL,aAAakE,WAAW,CAAE;oBAC7C,MAAMC,aACJ9D,WAAWf,IAAIe,MAAM,GACjBd,WAAW,CAACZ,MAAM0C,IAAI,CAAE,GACxB5B,uBAAuB,CAACd,MAAM0C,IAAI,CAAE,EAAE,CAAChB,OAAO;oBAEpD,4CAA4C;oBAC5C,IAAI,OAAO8D,eAAe,aAAa;wBACrCF,UAAU,CAAC5D,OAAO,GAAG8D;oBACvB;gBACF;gBAEA,+CAA+C;gBAC/C,IAAIC,OAAOC,IAAI,CAACJ,YAAYlB,MAAM,GAAG,GAAG;oBACtCxD,WAAW,CAACZ,MAAM0C,IAAI,CAAE,GAAG4C;gBAC7B;YACF;QACF;IACF;IAEA,OAAQtF,MAAMyC,IAAI;QAChB,KAAK;YAAS;gBACZ,MAAMkD,OAAO/E,WAAW,CAACZ,MAAM0C,IAAI,CAAC;gBAEpC,IAAIkD,MAAMC,OAAO,CAACF,OAAO;oBACvB,MAAMG,WAA4B,EAAE;oBAEpCH,KAAKI,OAAO,CAAC,CAACC,KAAK3B;wBACjByB,SAASX,IAAI,KACXpG,gOAAAA,EAAe;4BACbS;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACAG,gBACEF,OAAOd,UAAU,QACbgB,iBACAlB,gBACEkB,gBACA,OAAG1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAO0C,MAAM/B,IAAIoE,IAAI,EAAE,CAAC,EAAEV,WAAW,GAAG;4BAEtFL,QAAQhE,MAAMgE,MAAM;4BACpB7D;4BACAC;4BACAC;4BACAC;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBR,MAAMiG,SAAS;4BACvDxF,YAAYS,OAAO,MAAMmD;4BACzB3D,kBAAkBS;4BAClBR;4BACAC,aAAaoF;4BACbnF,gBAAY/B,sOAAAA,EAAkBkH,KAAmBnF,UAAU,CAACb,MAAM0C,IAAI,CAAC;4BACvE5B,2BAAuBhC,sOAAAA,EACrBkH,KACAlF,uBAAuB,CAACd,MAAM0C,IAAI,CAAC;4BAErC1B,gBAAgBuB;wBAClB;oBAEJ;oBAEA,MAAM2D,QAAQC,GAAG,CAACL;gBACpB;gBAEA;YACF;QAEA,KAAK;YAAU;gBACb,MAAMH,OAAO/E,WAAW,CAACZ,MAAM0C,IAAI,CAAC;gBACpC,IAAIkD,MAAMC,OAAO,CAACF,OAAO;oBACvB,MAAMG,WAA4B,EAAE;oBAEpCH,KAAKI,OAAO,CAAC,CAACC,KAAK3B;wBACjB,MAAM+B,oBAAgBtH,sOAAAA,EAAkBkH,KAAmBnF,UAAU,CAACb,MAAM0C,IAAI,CAAC;wBAEjF,MAAM2D,+BAA2BvH,sOAAAA,EAC/BkH,KACAlF,wBAAwBA,qBAAqB,CAACd,MAAM0C,IAAI,CAAC,GAAG,CAAC;wBAG/D,MAAM4D,mBAAoBN,IAAmBzB,SAAS,IAAI6B,cAAc7B,SAAS;wBAEjF,MAAMD,QACJ3D,IAAIW,OAAO,CAACoD,MAAM,CAAC4B,iBAAiB,IAClCtG,CAAAA,MAAMyE,eAAe,IAAIzE,MAAM0E,MAAK,EAAGC,IAAI,CAC3C,CAAC4B,WAAa,OAAOA,aAAa,YAAYA,SAAS1C,IAAI,KAAKyC;wBAGpE,MAAMxB,iBACJ9E,OAAOd,UAAU,QACbgB,iBACAlB,gBACEkB,gBACA,OAAG1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAO0C,MAAM/B,IAAIoE,IAAI,EAAE,GAAG,EAAEpE,IAAIqE,CAAC,CAAC,gBAAgB,CAAC,EAAEX,WAAW,EAAE,EAAE,MAAE7F,kNAAAA,EAAmB8F,OAAOW,QAAQC,YAAYoB,kBAAkB3F,IAAIoE,IAAI,EAAE,CAAC,CAAC;wBAGhM,IAAIT,OAAO;4BACTwB,SAASX,IAAI,KACXpG,gOAAAA,EAAe;gCACbS;gCACAC,WAAWuG;gCACXtG;gCACAC;gCACAC;gCACAC;gCACAC;gCACAC;gCACAG,gBAAgB4E;gCAEhBd,QAAQM,MAAMN,MAAM;gCACpB7D;gCACAC;gCACAC;gCACAC;gCACAC,iBAAiB;gCACjBC,mBAAmBA,qBAAqBR,MAAMiG,SAAS;gCACvDxF,YAAYS,OAAO,MAAMmD;gCACzB3D,kBAAkBS,aAAa,MAAMmD,MAAMT,IAAI;gCAC/ClD;gCACAC,aAAaoF;gCACbnF,YAAYuF;gCACZtF,uBAAuBuF;gCACvBrF,gBAAgBuB;4BAClB;wBAEJ;oBACF;oBAEA,MAAM2D,QAAQC,GAAG,CAACL;gBACpB;gBAEA;YACF;QAEA,KAAK;QACL,KAAK;YAAO;gBACV,UAAM/G,gOAAAA,EAAe;oBACnBS;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG,gBACEF,MAAMyC,IAAI,KAAK,SAASzC,OAAOd,UAAU,QACrCgB,iBACAlB,gBACEkB,oBACA1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAOyC,MAAM9B,IAAIoE,IAAI;oBAEhEf,QAAQhE,MAAMgE,MAAM;oBACpB7D;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBU;oBACjBT;oBACAC;oBACAC,kBAAkBS;oBAClBR;oBACAC;oBACAC;oBACAC,uBAAuBA;oBACvBE,gBAAgBuB;gBAClB;gBAEA;YACF;QAEA,KAAK;YAAS;gBACZ,IAAIiE,mBAAmB5F;gBACvB,IAAI6F,kBAAkB5F;gBACtB,IAAI6F,6BAA6B5F;gBAEjC,MAAM6F,mBAAelI,0MAAAA,EAAiBuB;gBAEtC,IAAI2G,cAAc;oBAChB,IAAI,OAAO/F,WAAW,CAACZ,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC/C9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC7B;oBAEA,IAAI,OAAO7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC9C7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC5B;oBAEA,IAAI,OAAO5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC1D5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBACxC;oBACA,IAAI,OAAO9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC/C9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC7B;oBAEA,IAAI,OAAO7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC9C7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC5B;oBAEA,IAAI,OAAO5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC1D5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBACxC;oBAEA8D,mBAAmB5F,WAAW,CAACZ,MAAM0C,IAAI,CAAC;oBAC1C+D,kBAAkB5F,UAAU,CAACb,MAAM0C,IAAI,CAAC;oBACxCgE,6BAA6B5F,qBAAsB,CAACd,MAAM0C,IAAI,CAAC;gBACjE;gBAEA,MAAMkE,gBAAgB5G,OAAOd,SAAUyH,CAAAA,eAAe3G,MAAM0C,IAAI,GAAG1C,OAAOyC,IAAG;gBAE7E,UAAM1D,gOAAAA,EAAe;oBACnBS;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG,gBACEF,OAAOd,UAAU,QACbgB,iBACAlB,gBAAgBkB,oBAAgB1B,kNAAAA,EAAmBoI,eAAejG,IAAIoE,IAAI;oBAChFf,QAAQhE,MAAMgE,MAAM;oBACpB7D;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBoG,eAAe,KAAK1F;oBACrCT,mBAAmBA,qBAAqBR,MAAMiG,SAAS;oBACvDxF,YAAYkG,eAAezF,OAAOT;oBAClCC,kBAAkBS;oBAClBR;oBACAC,aAAa4F;oBACb3F,YAAY4F;oBACZ3F,uBAAuB4F;oBACvB1F,gBAAgBuB;gBAClB;gBAEA;YACF;QAEA,KAAK;YAAS;gBACZ,mCAAmC;gBACnC,IACEqD,MAAMC,OAAO,CAACjF,WAAW,CAACZ,MAAM0C,IAAI,CAAC,KACrC9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,CAAC,EAAE,KAAK,QAC/B9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,CAAC,EAAE,KAAK,MAC/B;oBACA9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAG;wBACxBD,MAAM;wBACNoE,aAAa;4BACXC,WAAWlG,WAAW,CAACZ,MAAM0C,IAAI,CAAC,CAAC,EAAE;4BACrCoE,WAAWlG,WAAW,CAACZ,MAAM0C,IAAI,CAAC,CAAC,EAAE;yBACtC;oBACH;gBACF;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAAC1C,OAAO+G,QAAQ;oBAClB,MAAM,IAAI1I,6MAAAA,CAAkB2B,OAAO,8HAA8H;;gBACnK;gBAEA,IAAI,OAAOA,OAAO+G,WAAW,YAAY;oBACvC,MAAM,IAAIC,MAAM;gBAClB;gBAEA,MAAMD,SAA0B/G,OAAO+G;gBAEvC,IAAIA,QAAQpE,OAAOC,cAAcwB,QAAQ;oBACvC,KAAK,MAAMvB,QAAQkE,OAAOpE,KAAK,CAACC,YAAY,CAAE;wBAC5C,MAAME,cAAc,MAAMD,KAAK;4BAC7BnD;4BACAC;4BACAC;4BACAE;4BACAC;4BACAC;4BACAE,gBACEF,OAAOd,UAAU,QACbgB,iBACAlB,gBACEkB,oBACA1B,kNAAAA,EAAmBwB,OAAOd,SAASc,OAAO0C,MAAM/B,IAAIoE,IAAI;4BAEhE5E;4BACAc,WAAWa;4BACX1B;4BACAC;4BACA0C,aAAalD;4BACbS;4BACAE;4BACAU,MAAMS;4BACNqB,oBAAoBnC;4BACpBoC,eAAepC,UAAU,CAACb,MAAM0C,IAAI,CAAC;4BACrC/B;4BACAQ,YAAYU;4BACZjB;4BACAE;4BACAE;4BACAkC,OAAOtC,WAAW,CAACZ,MAAM0C,IAAI,CAAC;wBAChC;wBAEA,IAAII,gBAAgBK,WAAW;4BAC7BvC,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAGI;wBAC5B;oBACF;gBACF;gBAEA;YACF;QAEA,KAAK;YAAO;gBACV,IAAImE,iBAAiBrG;gBACrB,IAAIsG,gBAAgBrG;gBACpB,IAAIsG,2BAA2BrG;gBAE/B,MAAMsG,iBAAazI,oMAAAA,EAAWqB;gBAE9B,IAAIoH,YAAY;oBACd,IAAI,OAAOxG,WAAW,CAACZ,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC/C9B,WAAW,CAACZ,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC7B;oBAEA,IAAI,OAAO7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC9C7B,UAAU,CAACb,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBAC5B;oBAEA,IAAI,OAAO5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,KAAK,UAAU;wBAC1D5B,qBAAsB,CAACd,MAAM0C,IAAI,CAAC,GAAG,CAAC;oBACxC;oBAEAuE,iBAAiBrG,WAAW,CAACZ,MAAM0C,IAAI,CAAC;oBACxCwE,gBAAgBrG,UAAU,CAACb,MAAM0C,IAAI,CAAC;oBACtCyE,2BAA2BrG,qBAAsB,CAACd,MAAM0C,IAAI,CAAC;gBAC/D;gBAEA,UAAM3D,gOAAAA,EAAe;oBACnBS;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG,gBACEF,OAAOd,UAAU,QACbgB,iBACAlB,gBACEkB,oBACA1B,kNAAAA,EAAmBwB,OAAOd,SAASc,MAAM0C,IAAI,EAAE/B,IAAIoE,IAAI;oBAE/Df,QAAQhE,MAAMgE,MAAM;oBACpB7D;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiB6G,aAAa,KAAKnG;oBACnCT,mBAAmBA,qBAAqBR,MAAMiG,SAAS;oBACvDxF,YAAY2G,aAAalG,OAAOT;oBAChCC,kBAAkBS;oBAClBR;oBACAC,aAAaqG;oBACbpG,YAAYqG;oBACZpG,uBAAuBqG;oBACvBnG,gBAAgBuB;gBAClB;gBAEA;YACF;QAEA,KAAK;YAAQ;gBACX,UAAMxD,gOAAAA,EAAe;oBACnBS;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAG,gBACEF,OAAOd,UAAU,QACbgB,iBACAlB,gBAAgBkB,oBAAgB1B,kNAAAA,EAAmBwB,OAAOd,SAAS,IAAIyB,IAAIoE,IAAI;oBACrFf,QAAQhE,MAAMqH,IAAI,CAACpF,GAAG,CAAC,CAACqF,MAAS,CAAA;4BAAE,GAAGA,GAAG;4BAAE7E,MAAM;wBAAM,CAAA;oBACvDtC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBU;oBACjBT;oBACAC,YAAYS;oBACZR,kBAAkBS;oBAClBR;oBACAC;oBACAC;oBACAC,uBAAuBA;oBACvBE,gBAAgBuB;gBAClB;gBAEA;YACF;QAEA;YAAS;gBACP;YACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeChange/traverseFields.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { ValidationFieldError } from '../../../errors/index.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, Operation, PayloadRequest } from '../../../types/index.js'\nimport type { Field, TabAsField } from '../../config/types.js'\n\nimport { promise } from './promise.js'\n\ntype Args = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: JsonObject\n  /**\n   * The original data (not modified by any hooks)\n   */\n  doc: JsonObject\n  /**\n   * The original data with locales (not modified by any hooks)\n   */\n  docWithLocales: JsonObject\n  errors: ValidationFieldError[]\n  /**\n   * Built up labels of parent fields\n   *\n   * @example \"Group Field > Tab Field > Text Field\"\n   */\n  fieldLabelPath: string\n  fields: (Field | TabAsField)[]\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  mergeLocaleActions: (() => Promise<void> | void)[]\n  operation: Operation\n  overrideAccess: boolean\n  parentIndexPath: string\n  /**\n   * @todo make required in v4.0\n   */\n  parentIsLocalized?: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingData: JsonObject\n  /**\n   * The original siblingData (not modified by any hooks)\n   */\n  siblingDoc: JsonObject\n  /**\n   * The original siblingData with locales (not modified by any hooks)\n   */\n  siblingDocWithLocales: JsonObject\n  skipValidation?: boolean\n}\n\n/**\n * This function is responsible for the following actions, in order:\n * - Run condition\n * - Execute field hooks\n * - Validate data\n * - Transform data for storage\n * - Unflatten locales. The input `data` is the normal document for one locale. The output result will become the document with locales.\n */\nexport const traverseFields = async ({\n  id,\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  docWithLocales,\n  errors,\n  fieldLabelPath,\n  fields,\n  global,\n  mergeLocaleActions,\n  operation,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingData,\n  siblingDoc,\n  siblingDocWithLocales,\n  skipValidation,\n}: Args): Promise<void> => {\n  const promises: Promise<void>[] = []\n\n  fields.forEach((field, fieldIndex) => {\n    promises.push(\n      promise({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        docWithLocales,\n        errors,\n        field,\n        fieldIndex,\n        fieldLabelPath,\n        global,\n        mergeLocaleActions,\n        operation,\n        overrideAccess,\n        parentIndexPath,\n        parentIsLocalized: parentIsLocalized!,\n        parentPath,\n        parentSchemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n        siblingDocWithLocales,\n        siblingFields: fields,\n        skipValidation: skipValidation!,\n      }),\n    )\n  })\n\n  await Promise.all(promises)\n}\n"], "names": ["promise", "traverseFields", "id", "blockData", "collection", "context", "data", "doc", "docWithLocales", "errors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "global", "mergeLocaleActions", "operation", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingData", "siblingDoc", "siblingDocWithLocales", "skipValidation", "promises", "for<PERSON>ach", "field", "fieldIndex", "push", "siblingFields", "Promise", "all"], "mappings": ";;;;AAOA,SAASA,OAAO,QAAQ,eAAc;;AA2D/B,MAAMC,iBAAiB,OAAO,EACnCC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,MAAM,EACNC,cAAc,EACdC,MAAM,EACNC,MAAM,EACNC,kBAAkB,EAClBC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,qBAAqB,EACrBC,cAAc,EACT;IACL,MAAMC,WAA4B,EAAE;IAEpCd,OAAOe,OAAO,CAAC,CAACC,OAAOC;QACrBH,SAASI,IAAI,KACX7B,kNAAAA,EAAQ;YACNE;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAkB;YACAC;YACAlB;YACAE;YACAC;YACAC;YACAC;YACAC;YACAC,mBAAmBA;YACnBC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAO,eAAenB;YACfa,gBAAgBA;QAClB;IAEJ;IAEA,MAAMO,QAAQC,GAAG,CAACP;AACpB,EAAC", "debugId": null}}, {"offset": {"line": 3887, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeChange/index.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { ValidationFieldError } from '../../../errors/index.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, Operation, PayloadRequest } from '../../../types/index.js'\n\nimport { ValidationError } from '../../../errors/index.js'\nimport { deepCopyObjectSimple } from '../../../utilities/deepCopyObject.js'\nimport { traverseFields } from './traverseFields.js'\n\nexport type Args<T extends JsonObject> = {\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: T\n  doc: T\n  docWithLocales: JsonObject\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  operation: Operation\n  overrideAccess?: boolean\n  req: PayloadRequest\n  skipValidation?: boolean\n}\n\n/**\n * This function is responsible for the following actions, in order:\n * - Run condition\n * - Execute field hooks\n * - Validate data\n * - Transform data for storage\n * - Unflatten locales. The input `data` is the normal document for one locale. The output result will become the document with locales.\n */\n\nexport const beforeChange = async <T extends JsonObject>({\n  id,\n  collection,\n  context,\n  data: incomingData,\n  doc,\n  docWithLocales,\n  global,\n  operation,\n  overrideAccess,\n  req,\n  skipValidation,\n}: Args<T>): Promise<T> => {\n  const data = deepCopyObjectSimple(incomingData)\n  const mergeLocaleActions: (() => Promise<void> | void)[] = []\n  const errors: ValidationFieldError[] = []\n\n  await traverseFields({\n    id,\n    collection,\n    context,\n    data,\n    doc,\n    docWithLocales,\n    errors,\n    fieldLabelPath: '',\n    fields: (collection?.fields || global?.fields)!,\n    global,\n    mergeLocaleActions,\n    operation,\n    overrideAccess: overrideAccess!,\n    parentIndexPath: '',\n    parentIsLocalized: false,\n    parentPath: '',\n    parentSchemaPath: '',\n    req,\n    siblingData: data,\n    siblingDoc: doc,\n    siblingDocWithLocales: docWithLocales,\n    skipValidation,\n  })\n\n  if (errors.length > 0) {\n    throw new ValidationError(\n      {\n        id,\n        collection: collection?.slug,\n        errors,\n        global: global?.slug,\n        req,\n      },\n      req.t,\n    )\n  }\n\n  for (const action of mergeLocaleActions) {\n    await action()\n  }\n\n  return data\n}\n"], "names": ["ValidationError", "deepCopyObjectSimple", "traverseFields", "beforeChange", "id", "collection", "context", "data", "incomingData", "doc", "docWithLocales", "global", "operation", "overrideAccess", "req", "skipValidation", "mergeLocaleActions", "errors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "siblingData", "siblingDoc", "siblingDocWithLocales", "length", "slug", "t", "action"], "mappings": ";;;;AAMA,SAASA,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,oBAAoB,QAAQ,uCAAsC;AAC3E,SAASC,cAAc,QAAQ,sBAAqB;;;;AAyB7C,MAAMC,eAAe,OAA6B,EACvDC,EAAE,EACFC,UAAU,EACVC,OAAO,EACPC,MAAMC,YAAY,EAClBC,GAAG,EACHC,cAAc,EACdC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,GAAG,EACHC,cAAc,EACN;IACR,MAAMR,WAAON,gNAAAA,EAAqBO;IAClC,MAAMQ,qBAAqD,EAAE;IAC7D,MAAMC,SAAiC,EAAE;IAEzC,UAAMf,gOAAAA,EAAe;QACnBE;QACAC;QACAC;QACAC;QACAE;QACAC;QACAO;QACAC,gBAAgB;QAChBC,QAASd,YAAYc,UAAUR,QAAQQ;QACvCR;QACAK;QACAJ;QACAC,gBAAgBA;QAChBO,iBAAiB;QACjBC,mBAAmB;QACnBC,YAAY;QACZC,kBAAkB;QAClBT;QACAU,aAAajB;QACbkB,YAAYhB;QACZiB,uBAAuBhB;QACvBK;IACF;IAEA,IAAIE,OAAOU,MAAM,GAAG,GAAG;QACrB,MAAM,IAAI3B,yMAAAA,CACR;YACEI;YACAC,YAAYA,YAAYuB;YACxBX;YACAN,QAAQA,QAAQiB;YAChBd;QACF,GACAA,IAAIe,CAAC;IAET;IAEA,KAAK,MAAMC,UAAUd,mBAAoB;QACvC,MAAMc;IACR;IAEA,OAAOvB;AACT,EAAC", "debugId": null}}, {"offset": {"line": 3943, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeChange/cloneDataFromOriginalDoc.ts"], "sourcesContent": ["import type { JsonArray, JsonObject } from '../../../types/index.js'\n\nexport const cloneDataFromOriginalDoc = (\n  originalDocData: JsonArray | JsonObject,\n): JsonArray | JsonObject => {\n  if (Array.isArray(originalDocData)) {\n    return originalDocData.map((row) => {\n      if (typeof row === 'object' && row != null) {\n        return {\n          ...row,\n        }\n      }\n\n      return row\n    })\n  }\n\n  if (typeof originalDocData === 'object' && originalDocData !== null) {\n    return { ...originalDocData }\n  }\n\n  return originalDocData\n}\n"], "names": ["cloneDataFromOriginalDoc", "originalDocData", "Array", "isArray", "map", "row"], "mappings": ";;;;AAEO,MAAMA,2BAA2B,CACtCC;IAEA,IAAIC,MAAMC,OAAO,CAACF,kBAAkB;QAClC,OAAOA,gBAAgBG,GAAG,CAAC,CAACC;YAC1B,IAAI,OAAOA,QAAQ,YAAYA,OAAO,MAAM;gBAC1C,OAAO;oBACL,GAAGA,GAAG;gBACR;YACF;YAEA,OAAOA;QACT;IACF;IAEA,IAAI,OAAOJ,oBAAoB,YAAYA,oBAAoB,MAAM;QACnE,OAAO;YAAE,GAAGA,eAAe;QAAC;IAC9B;IAEA,OAAOA;AACT,EAAC", "debugId": null}}, {"offset": {"line": 3969, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeValidate/getFallbackValue.ts"], "sourcesContent": ["import type { JsonObject, JsonValue, PayloadRequest } from '../../../types/index.js'\nimport type { FieldAffectingData } from '../../config/types.js'\n\nimport { getDefaultValue } from '../../getDefaultValue.js'\nimport { cloneDataFromOriginalDoc } from '../beforeChange/cloneDataFromOriginalDoc.js'\n\nexport async function getFallbackValue({\n  field,\n  req,\n  siblingDoc,\n}: {\n  field: FieldAffectingData\n  req: PayloadRequest\n  siblingDoc: JsonObject\n}): Promise<JsonValue> {\n  let fallbackValue: JsonValue = undefined\n  if ('name' in field && field.name) {\n    if (typeof siblingDoc[field.name] !== 'undefined') {\n      fallbackValue = cloneDataFromOriginalDoc(siblingDoc[field.name])\n    } else if ('defaultValue' in field && typeof field.defaultValue !== 'undefined') {\n      fallbackValue = await getDefaultValue({\n        defaultValue: field.defaultValue,\n        locale: req.locale || '',\n        req,\n        user: req.user,\n      })\n    }\n  }\n\n  return fallbackValue\n}\n"], "names": ["getDefaultValue", "cloneDataFromOriginalDoc", "getFallbackValue", "field", "req", "siblingDoc", "fallback<PERSON><PERSON><PERSON>", "undefined", "name", "defaultValue", "locale", "user"], "mappings": ";;;;AAGA,SAASA,eAAe,QAAQ,2BAA0B;AAC1D,SAASC,wBAAwB,QAAQ,8CAA6C;;;AAE/E,eAAeC,iBAAiB,EACrCC,KAAK,EACLC,GAAG,EACHC,UAAU,EAKX;IACC,IAAIC,gBAA2BC;IAC/B,IAAI,UAAUJ,SAASA,MAAMK,IAAI,EAAE;QACjC,IAAI,OAAOH,UAAU,CAACF,MAAMK,IAAI,CAAC,KAAK,aAAa;YACjDF,oBAAgBL,oPAAAA,EAAyBI,UAAU,CAACF,MAAMK,IAAI,CAAC;QACjE,OAAO,IAAI,kBAAkBL,SAAS,OAAOA,MAAMM,YAAY,KAAK,aAAa;YAC/EH,gBAAgB,UAAMN,yMAAAA,EAAgB;gBACpCS,cAAcN,MAAMM,YAAY;gBAChCC,QAAQN,IAAIM,MAAM,IAAI;gBACtBN;gBACAO,MAAMP,IAAIO,IAAI;YAChB;QACF;IACF;IAEA,OAAOL;AACT", "debugId": null}}, {"offset": {"line": 3997, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeValidate/promise.ts"], "sourcesContent": ["import type { RichTextAdapter } from '../../../admin/RichText.js'\nimport type { SanitizedCollectionConfig, TypeWithID } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, JsonValue, PayloadRequest } from '../../../types/index.js'\nimport type { Block, Field, TabAsField } from '../../config/types.js'\n\nimport { MissingEditorProp } from '../../../errors/index.js'\nimport { fieldAffectsData, tabHasName, valueIsValueWithRelation } from '../../config/types.js'\nimport { getFieldPathsModified as getFieldPaths } from '../../getFieldPaths.js'\nimport { getExistingRowDoc } from '../beforeChange/getExistingRowDoc.js'\nimport { getFallbackValue } from './getFallbackValue.js'\nimport { traverseFields } from './traverseFields.js'\n\ntype Args<T> = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: T\n  /**\n   * The original data (not modified by any hooks)\n   */\n  doc: T\n  field: Field | TabAsField\n  fieldIndex: number\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  operation: 'create' | 'update'\n  overrideAccess: boolean\n  parentIndexPath: string\n  parentIsLocalized: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingData: JsonObject\n  /**\n   * The original siblingData (not modified by any hooks)\n   */\n  siblingDoc: JsonObject\n  siblingFields?: (Field | TabAsField)[]\n}\n\n// This function is responsible for the following actions, in order:\n// - Sanitize incoming data\n// - Execute field hooks\n// - Execute field access control\n// - Merge original document data into incoming data\n// - Compute default values for undefined fields\n\nexport const promise = async <T>({\n  id,\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  field,\n  fieldIndex,\n  global,\n  operation,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingData,\n  siblingDoc,\n  siblingFields,\n}: Args<T>): Promise<void> => {\n  const { indexPath, path, schemaPath } = getFieldPaths({\n    field,\n    index: fieldIndex,\n    parentIndexPath,\n    parentPath,\n    parentSchemaPath,\n  })\n\n  const pathSegments = path ? path.split('.') : []\n  const schemaPathSegments = schemaPath ? schemaPath.split('.') : []\n  const indexPathSegments = indexPath ? indexPath.split('-').filter(Boolean)?.map(Number) : []\n\n  if (fieldAffectsData(field)) {\n    if (field.name === 'id') {\n      if (field.type === 'number' && typeof siblingData[field.name] === 'string') {\n        const value = siblingData[field.name] as string\n\n        siblingData[field.name] = parseFloat(value)\n      }\n\n      if (\n        field.type === 'text' &&\n        typeof siblingData[field.name]?.toString === 'function' &&\n        typeof siblingData[field.name] !== 'string'\n      ) {\n        siblingData[field.name] = siblingData[field.name].toString()\n      }\n    }\n\n    // Sanitize incoming data\n    switch (field.type) {\n      case 'array':\n      case 'blocks': {\n        // Handle cases of arrays being intentionally set to 0\n        if (siblingData[field.name] === '0' || siblingData[field.name] === 0) {\n          siblingData[field.name] = []\n        }\n\n        break\n      }\n\n      case 'checkbox': {\n        if (siblingData[field.name] === 'true') {\n          siblingData[field.name] = true\n        }\n        if (siblingData[field.name] === 'false') {\n          siblingData[field.name] = false\n        }\n        if (siblingData[field.name] === '') {\n          siblingData[field.name] = false\n        }\n\n        break\n      }\n\n      case 'number': {\n        if (typeof siblingData[field.name] === 'string') {\n          const value = siblingData[field.name] as string\n          const trimmed = value.trim()\n          siblingData[field.name] = trimmed.length === 0 ? null : parseFloat(trimmed)\n        }\n\n        break\n      }\n\n      case 'point': {\n        if (Array.isArray(siblingData[field.name])) {\n          siblingData[field.name] = (siblingData[field.name] as string[]).map((coordinate, i) => {\n            if (typeof coordinate === 'string') {\n              const value = siblingData[field.name][i] as string\n              const trimmed = value.trim()\n              return trimmed.length === 0 ? null : parseFloat(trimmed)\n            }\n            return coordinate\n          })\n        }\n\n        break\n      }\n      case 'relationship':\n      case 'upload': {\n        if (\n          siblingData[field.name] === '' ||\n          siblingData[field.name] === 'none' ||\n          siblingData[field.name] === 'null' ||\n          siblingData[field.name] === null\n        ) {\n          if (field.hasMany === true) {\n            siblingData[field.name] = []\n          } else {\n            siblingData[field.name] = null\n          }\n        }\n\n        const value = siblingData[field.name]\n\n        if (Array.isArray(field.relationTo)) {\n          if (Array.isArray(value)) {\n            value.forEach((relatedDoc: { relationTo: string; value: JsonValue }, i) => {\n              const relatedCollection = req.payload.collections?.[relatedDoc.relationTo]?.config\n\n              if (\n                typeof relatedDoc.value === 'object' &&\n                relatedDoc.value &&\n                'id' in relatedDoc.value\n              ) {\n                relatedDoc.value = relatedDoc.value.id\n              }\n\n              if (relatedCollection?.fields) {\n                const relationshipIDField = relatedCollection.fields.find(\n                  (collectionField) =>\n                    fieldAffectsData(collectionField) && collectionField.name === 'id',\n                )\n                if (relationshipIDField?.type === 'number') {\n                  siblingData[field.name][i] = {\n                    ...relatedDoc,\n                    value: parseFloat(relatedDoc.value as string),\n                  }\n                }\n              }\n            })\n          }\n          if (field.hasMany !== true && valueIsValueWithRelation(value)) {\n            const relatedCollection = req.payload.collections?.[value.relationTo]?.config\n\n            if (typeof value.value === 'object' && value.value && 'id' in value.value) {\n              value.value = (value.value as TypeWithID).id\n            }\n\n            if (relatedCollection?.fields) {\n              const relationshipIDField = relatedCollection.fields.find(\n                (collectionField) =>\n                  fieldAffectsData(collectionField) && collectionField.name === 'id',\n              )\n              if (relationshipIDField?.type === 'number') {\n                siblingData[field.name] = { ...value, value: parseFloat(value.value as string) }\n              }\n            }\n          }\n        } else {\n          if (Array.isArray(value)) {\n            value.forEach((relatedDoc: unknown, i) => {\n              const relatedCollection = Array.isArray(field.relationTo)\n                ? undefined\n                : req.payload.collections?.[field.relationTo]?.config\n\n              if (typeof relatedDoc === 'object' && relatedDoc && 'id' in relatedDoc) {\n                value[i] = relatedDoc.id\n              }\n\n              if (relatedCollection?.fields) {\n                const relationshipIDField = relatedCollection.fields.find(\n                  (collectionField) =>\n                    fieldAffectsData(collectionField) && collectionField.name === 'id',\n                )\n                if (relationshipIDField?.type === 'number') {\n                  siblingData[field.name][i] = parseFloat(relatedDoc as string)\n                }\n              }\n            })\n          }\n          if (field.hasMany !== true && value) {\n            const relatedCollection = req.payload.collections?.[field.relationTo]?.config\n\n            if (typeof value === 'object' && value && 'id' in value) {\n              siblingData[field.name] = value.id\n            }\n\n            if (relatedCollection?.fields) {\n              const relationshipIDField = relatedCollection.fields.find(\n                (collectionField) =>\n                  fieldAffectsData(collectionField) && collectionField.name === 'id',\n              )\n              if (relationshipIDField?.type === 'number') {\n                siblingData[field.name] = parseFloat(value as string)\n              }\n            }\n          }\n        }\n        break\n      }\n      case 'richText': {\n        if (typeof siblingData[field.name] === 'string') {\n          try {\n            const richTextJSON = JSON.parse(siblingData[field.name] as string)\n            siblingData[field.name] = richTextJSON\n          } catch {\n            // Disregard this data as it is not valid.\n            // Will be reported to user by field validation\n          }\n        }\n\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    // ensure the fallback value is only computed one time\n    // either here or when access control returns false\n    const fallbackResult: { executed: boolean; value: unknown } = {\n      executed: false,\n      value: undefined,\n    }\n    if (typeof siblingData[field.name!] === 'undefined') {\n      fallbackResult.value = await getFallbackValue({ field, req, siblingDoc })\n      fallbackResult.executed = true\n    }\n\n    // Execute hooks\n    if ('hooks' in field && field.hooks?.beforeValidate) {\n      for (const hook of field.hooks.beforeValidate) {\n        const hookedValue = await hook({\n          blockData,\n          collection,\n          context,\n          data: data as Partial<T>,\n          field,\n          global,\n          indexPath: indexPathSegments,\n          operation,\n          originalDoc: doc,\n          overrideAccess,\n          path: pathSegments,\n          previousSiblingDoc: siblingDoc,\n          previousValue: siblingDoc[field.name],\n          req,\n          schemaPath: schemaPathSegments,\n          siblingData,\n          siblingFields: siblingFields!,\n          value:\n            typeof siblingData[field.name] === 'undefined'\n              ? fallbackResult.value\n              : siblingData[field.name],\n        })\n\n        if (hookedValue !== undefined) {\n          siblingData[field.name] = hookedValue\n        }\n      }\n    }\n\n    // Execute access control\n    if (field.access && field.access[operation]) {\n      const result = overrideAccess\n        ? true\n        : await field.access[operation]({\n            id,\n            blockData,\n            data: data as Partial<T>,\n            doc,\n            req,\n            siblingData,\n          })\n\n      if (!result) {\n        delete siblingData[field.name!]\n      }\n    }\n\n    if (typeof siblingData[field.name!] === 'undefined') {\n      siblingData[field.name!] = !fallbackResult.executed\n        ? await getFallbackValue({ field, req, siblingDoc })\n        : fallbackResult.value\n    }\n  }\n\n  // Traverse subfields\n  switch (field.type) {\n    case 'array': {\n      const rows = siblingData[field.name]\n\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n\n        rows.forEach((row, rowIndex) => {\n          promises.push(\n            traverseFields({\n              id,\n              blockData,\n              collection,\n              context,\n              data,\n              doc,\n              fields: field.fields,\n              global,\n              operation,\n              overrideAccess,\n              parentIndexPath: '',\n              parentIsLocalized: parentIsLocalized || field.localized,\n              parentPath: path + '.' + rowIndex,\n              parentSchemaPath: schemaPath,\n              req,\n              siblingData: row as JsonObject,\n              siblingDoc: getExistingRowDoc(row as JsonObject, siblingDoc[field.name]),\n            }),\n          )\n        })\n\n        await Promise.all(promises)\n      }\n      break\n    }\n\n    case 'blocks': {\n      const rows = siblingData[field.name]\n\n      if (Array.isArray(rows)) {\n        const promises: Promise<void>[] = []\n\n        rows.forEach((row, rowIndex) => {\n          const rowSiblingDoc = getExistingRowDoc(row as JsonObject, siblingDoc[field.name])\n          const blockTypeToMatch = (row as JsonObject).blockType || rowSiblingDoc.blockType\n\n          const block: Block | undefined =\n            req.payload.blocks[blockTypeToMatch] ??\n            ((field.blockReferences ?? field.blocks).find(\n              (curBlock) => typeof curBlock !== 'string' && curBlock.slug === blockTypeToMatch,\n            ) as Block | undefined)\n\n          if (block) {\n            ;(row as JsonObject).blockType = blockTypeToMatch\n\n            promises.push(\n              traverseFields({\n                id,\n                blockData: row,\n                collection,\n                context,\n                data,\n                doc,\n                fields: block.fields,\n                global,\n                operation,\n                overrideAccess,\n                parentIndexPath: '',\n                parentIsLocalized: parentIsLocalized || field.localized,\n                parentPath: path + '.' + rowIndex,\n                parentSchemaPath: schemaPath + '.' + block.slug,\n                req,\n                siblingData: row as JsonObject,\n                siblingDoc: rowSiblingDoc,\n              }),\n            )\n          }\n        })\n\n        await Promise.all(promises)\n      }\n\n      break\n    }\n\n    case 'collapsible':\n    case 'row': {\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.fields,\n        global,\n        operation,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n      })\n\n      break\n    }\n\n    case 'group': {\n      let groupSiblingData = siblingData\n      let groupSiblingDoc = siblingDoc\n\n      const isNamedGroup = fieldAffectsData(field)\n\n      if (isNamedGroup) {\n        if (typeof siblingData[field.name] !== 'object') {\n          siblingData[field.name] = {}\n        }\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          siblingDoc[field.name] = {}\n        }\n\n        groupSiblingData = siblingData[field.name] as Record<string, unknown>\n        groupSiblingDoc = siblingDoc[field.name] as Record<string, unknown>\n      }\n\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.fields,\n        global,\n        operation,\n        overrideAccess,\n        parentIndexPath: isNamedGroup ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedGroup ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData: groupSiblingData,\n        siblingDoc: groupSiblingDoc,\n      })\n\n      break\n    }\n\n    case 'richText': {\n      if (!field?.editor) {\n        throw new MissingEditorProp(field) // while we allow disabling editor functionality, you should not have any richText fields defined if you do not have an editor\n      }\n\n      if (typeof field?.editor === 'function') {\n        throw new Error('Attempted to access unsanitized rich text editor.')\n      }\n\n      const editor: RichTextAdapter = field?.editor\n\n      if (editor?.hooks?.beforeValidate?.length) {\n        for (const hook of editor.hooks.beforeValidate) {\n          const hookedValue = await hook({\n            collection,\n            context,\n            data: data as Partial<T>,\n            field,\n            global,\n            indexPath: indexPathSegments,\n            operation,\n            originalDoc: doc,\n            overrideAccess,\n            parentIsLocalized,\n            path: pathSegments,\n            previousSiblingDoc: siblingDoc,\n            previousValue: siblingData[field.name],\n            req,\n            schemaPath: schemaPathSegments,\n            siblingData,\n            value: siblingData[field.name],\n          })\n\n          if (hookedValue !== undefined) {\n            siblingData[field.name] = hookedValue\n          }\n        }\n      }\n      break\n    }\n\n    case 'tab': {\n      let tabSiblingData\n      let tabSiblingDoc\n\n      const isNamedTab = tabHasName(field)\n\n      if (isNamedTab) {\n        if (typeof siblingData[field.name] !== 'object') {\n          siblingData[field.name] = {}\n        }\n\n        if (typeof siblingDoc[field.name] !== 'object') {\n          siblingDoc[field.name] = {}\n        }\n\n        tabSiblingData = siblingData[field.name] as Record<string, unknown>\n        tabSiblingDoc = siblingDoc[field.name] as Record<string, unknown>\n      } else {\n        tabSiblingData = siblingData\n        tabSiblingDoc = siblingDoc\n      }\n\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.fields,\n        global,\n        operation,\n        overrideAccess,\n        parentIndexPath: isNamedTab ? '' : indexPath,\n        parentIsLocalized: parentIsLocalized || field.localized,\n        parentPath: isNamedTab ? path : parentPath,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData: tabSiblingData,\n        siblingDoc: tabSiblingDoc,\n      })\n\n      break\n    }\n\n    case 'tabs': {\n      await traverseFields({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        fields: field.tabs.map((tab) => ({ ...tab, type: 'tab' })),\n        global,\n        operation,\n        overrideAccess,\n        parentIndexPath: indexPath,\n        parentIsLocalized,\n        parentPath: path,\n        parentSchemaPath: schemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n      })\n\n      break\n    }\n\n    default: {\n      break\n    }\n  }\n}\n"], "names": ["MissingEditorProp", "fieldAffectsData", "tabHasName", "valueIsValueWithRelation", "getFieldPathsModified", "getFieldPaths", "getExistingRowDoc", "getFallbackValue", "traverseFields", "promise", "id", "blockData", "collection", "context", "data", "doc", "field", "fieldIndex", "global", "operation", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingData", "siblingDoc", "siblingFields", "indexPath", "path", "schemaPath", "index", "pathSegments", "split", "schemaPathSegments", "indexPathSegments", "filter", "Boolean", "map", "Number", "name", "type", "value", "parseFloat", "toString", "trimmed", "trim", "length", "Array", "isArray", "coordinate", "i", "hasMany", "relationTo", "for<PERSON>ach", "relatedDoc", "relatedCollection", "payload", "collections", "config", "fields", "relationshipIDField", "find", "collectionField", "undefined", "richTextJSON", "JSON", "parse", "fallback<PERSON><PERSON><PERSON>", "executed", "hooks", "beforeValidate", "hook", "hookedValue", "originalDoc", "previousSiblingDoc", "previousValue", "access", "result", "rows", "promises", "row", "rowIndex", "push", "localized", "Promise", "all", "rowSiblingDoc", "blockTypeToMatch", "blockType", "block", "blocks", "blockReferences", "curBlock", "slug", "groupSiblingData", "groupSiblingDoc", "isNamedGroup", "editor", "Error", "tabSiblingData", "tabSiblingDoc", "isNamedTab", "tabs", "tab"], "mappings": ";;;;AAOA,SAASA,iBAAiB,QAAQ,2BAA0B;AAC5D,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,wBAAwB,QAAQ,wBAAuB;AAC9F,SAASC,yBAAyBC,aAAa,QAAQ,yBAAwB;AAC/E,SAASC,iBAAiB,QAAQ,uCAAsC;AACxE,SAASC,gBAAgB,QAAQ,wBAAuB;AACxD,SAASC,cAAc,QAAQ,sBAAqB;;;;;;;AAwC7C,MAAMC,UAAU,OAAU,EAC/BC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACVC,aAAa,EACL;IACR,MAAM,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAE,OAAG1B,6MAAAA,EAAc;QACpDW;QACAgB,OAAOf;QACPI;QACAE;QACAC;IACF;IAEA,MAAMS,eAAeH,OAAOA,KAAKI,KAAK,CAAC,OAAO,EAAE;IAChD,MAAMC,qBAAqBJ,aAAaA,WAAWG,KAAK,CAAC,OAAO,EAAE;IAClE,MAAME,oBAAoBP,YAAYA,UAAUK,KAAK,CAAC,KAAKG,MAAM,CAACC,UAAUC,IAAIC,UAAU,EAAE;IAE5F,QAAIvC,0MAAAA,EAAiBe,QAAQ;QAC3B,IAAIA,MAAMyB,IAAI,KAAK,MAAM;YACvB,IAAIzB,MAAM0B,IAAI,KAAK,YAAY,OAAOhB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAAU;gBAC1E,MAAME,QAAQjB,WAAW,CAACV,MAAMyB,IAAI,CAAC;gBAErCf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGG,WAAWD;YACvC;YAEA,IACE3B,MAAM0B,IAAI,KAAK,UACf,OAAOhB,WAAW,CAACV,MAAMyB,IAAI,CAAC,EAAEI,aAAa,cAC7C,OAAOnB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UACnC;gBACAf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGf,WAAW,CAACV,MAAMyB,IAAI,CAAC,CAACI,QAAQ;YAC5D;QACF;QAEA,yBAAyB;QACzB,OAAQ7B,MAAM0B,IAAI;YAChB,KAAK;YACL,KAAK;gBAAU;oBACb,sDAAsD;oBACtD,IAAIhB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,OAAOf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,GAAG;wBACpEf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG,EAAE;oBAC9B;oBAEA;gBACF;YAEA,KAAK;gBAAY;oBACf,IAAIf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,QAAQ;wBACtCf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;oBAC5B;oBACA,IAAIf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,SAAS;wBACvCf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;oBAC5B;oBACA,IAAIf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,IAAI;wBAClCf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;oBAC5B;oBAEA;gBACF;YAEA,KAAK;gBAAU;oBACb,IAAI,OAAOf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC/C,MAAME,QAAQjB,WAAW,CAACV,MAAMyB,IAAI,CAAC;wBACrC,MAAMK,UAAUH,MAAMI,IAAI;wBAC1BrB,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGK,QAAQE,MAAM,KAAK,IAAI,OAAOJ,WAAWE;oBACrE;oBAEA;gBACF;YAEA,KAAK;gBAAS;oBACZ,IAAIG,MAAMC,OAAO,CAACxB,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;wBAC1Cf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAIf,WAAW,CAACV,MAAMyB,IAAI,CAAC,CAAcF,GAAG,CAAC,CAACY,YAAYC;4BAC/E,IAAI,OAAOD,eAAe,UAAU;gCAClC,MAAMR,QAAQjB,WAAW,CAACV,MAAMyB,IAAI,CAAC,CAACW,EAAE;gCACxC,MAAMN,UAAUH,MAAMI,IAAI;gCAC1B,OAAOD,QAAQE,MAAM,KAAK,IAAI,OAAOJ,WAAWE;4BAClD;4BACA,OAAOK;wBACT;oBACF;oBAEA;gBACF;YACA,KAAK;YACL,KAAK;gBAAU;oBACb,IACEzB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,MAC5Bf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAC5Bf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAC5Bf,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,MAC5B;wBACA,IAAIzB,MAAMqC,OAAO,KAAK,MAAM;4BAC1B3B,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG,EAAE;wBAC9B,OAAO;4BACLf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;wBAC5B;oBACF;oBAEA,MAAME,QAAQjB,WAAW,CAACV,MAAMyB,IAAI,CAAC;oBAErC,IAAIQ,MAAMC,OAAO,CAAClC,MAAMsC,UAAU,GAAG;wBACnC,IAAIL,MAAMC,OAAO,CAACP,QAAQ;4BACxBA,MAAMY,OAAO,CAAC,CAACC,YAAsDJ;gCACnE,MAAMK,oBAAoBhC,IAAIiC,OAAO,CAACC,WAAW,EAAE,CAACH,WAAWF,UAAU,CAAC,EAAEM;gCAE5E,IACE,OAAOJ,WAAWb,KAAK,KAAK,YAC5Ba,WAAWb,KAAK,IAChB,QAAQa,WAAWb,KAAK,EACxB;oCACAa,WAAWb,KAAK,GAAGa,WAAWb,KAAK,CAACjC,EAAE;gCACxC;gCAEA,IAAI+C,mBAAmBI,QAAQ;oCAC7B,MAAMC,sBAAsBL,kBAAkBI,MAAM,CAACE,IAAI,CACvD,CAACC,sBACC/D,0MAAAA,EAAiB+D,oBAAoBA,gBAAgBvB,IAAI,KAAK;oCAElE,IAAIqB,qBAAqBpB,SAAS,UAAU;wCAC1ChB,WAAW,CAACV,MAAMyB,IAAI,CAAC,CAACW,EAAE,GAAG;4CAC3B,GAAGI,UAAU;4CACbb,OAAOC,WAAWY,WAAWb,KAAK;wCACpC;oCACF;gCACF;4BACF;wBACF;wBACA,IAAI3B,MAAMqC,OAAO,KAAK,YAAQlD,kNAAAA,EAAyBwC,QAAQ;4BAC7D,MAAMc,oBAAoBhC,IAAIiC,OAAO,CAACC,WAAW,EAAE,CAAChB,MAAMW,UAAU,CAAC,EAAEM;4BAEvE,IAAI,OAAOjB,MAAMA,KAAK,KAAK,YAAYA,MAAMA,KAAK,IAAI,QAAQA,MAAMA,KAAK,EAAE;gCACzEA,MAAMA,KAAK,GAAIA,MAAMA,KAAK,CAAgBjC,EAAE;4BAC9C;4BAEA,IAAI+C,mBAAmBI,QAAQ;gCAC7B,MAAMC,sBAAsBL,kBAAkBI,MAAM,CAACE,IAAI,CACvD,CAACC,sBACC/D,0MAAAA,EAAiB+D,oBAAoBA,gBAAgBvB,IAAI,KAAK;gCAElE,IAAIqB,qBAAqBpB,SAAS,UAAU;oCAC1ChB,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG;wCAAE,GAAGE,KAAK;wCAAEA,OAAOC,WAAWD,MAAMA,KAAK;oCAAY;gCACjF;4BACF;wBACF;oBACF,OAAO;wBACL,IAAIM,MAAMC,OAAO,CAACP,QAAQ;4BACxBA,MAAMY,OAAO,CAAC,CAACC,YAAqBJ;gCAClC,MAAMK,oBAAoBR,MAAMC,OAAO,CAAClC,MAAMsC,UAAU,IACpDW,YACAxC,IAAIiC,OAAO,CAACC,WAAW,EAAE,CAAC3C,MAAMsC,UAAU,CAAC,EAAEM;gCAEjD,IAAI,OAAOJ,eAAe,YAAYA,cAAc,QAAQA,YAAY;oCACtEb,KAAK,CAACS,EAAE,GAAGI,WAAW9C,EAAE;gCAC1B;gCAEA,IAAI+C,mBAAmBI,QAAQ;oCAC7B,MAAMC,sBAAsBL,kBAAkBI,MAAM,CAACE,IAAI,CACvD,CAACC,sBACC/D,0MAAAA,EAAiB+D,oBAAoBA,gBAAgBvB,IAAI,KAAK;oCAElE,IAAIqB,qBAAqBpB,SAAS,UAAU;wCAC1ChB,WAAW,CAACV,MAAMyB,IAAI,CAAC,CAACW,EAAE,GAAGR,WAAWY;oCAC1C;gCACF;4BACF;wBACF;wBACA,IAAIxC,MAAMqC,OAAO,KAAK,QAAQV,OAAO;4BACnC,MAAMc,oBAAoBhC,IAAIiC,OAAO,CAACC,WAAW,EAAE,CAAC3C,MAAMsC,UAAU,CAAC,EAAEM;4BAEvE,IAAI,OAAOjB,UAAU,YAAYA,SAAS,QAAQA,OAAO;gCACvDjB,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGE,MAAMjC,EAAE;4BACpC;4BAEA,IAAI+C,mBAAmBI,QAAQ;gCAC7B,MAAMC,sBAAsBL,kBAAkBI,MAAM,CAACE,IAAI,CACvD,CAACC,sBACC/D,0MAAAA,EAAiB+D,oBAAoBA,gBAAgBvB,IAAI,KAAK;gCAElE,IAAIqB,qBAAqBpB,SAAS,UAAU;oCAC1ChB,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGG,WAAWD;gCACvC;4BACF;wBACF;oBACF;oBACA;gBACF;YACA,KAAK;gBAAY;oBACf,IAAI,OAAOjB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC/C,IAAI;4BACF,MAAMyB,eAAeC,KAAKC,KAAK,CAAC1C,WAAW,CAACV,MAAMyB,IAAI,CAAC;4BACvDf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGyB;wBAC5B,EAAE,OAAM;wBACN,0CAA0C;wBAC1C,+CAA+C;wBACjD;oBACF;oBAEA;gBACF;YAEA;gBAAS;oBACP;gBACF;QACF;QAEA,sDAAsD;QACtD,mDAAmD;QACnD,MAAMG,iBAAwD;YAC5DC,UAAU;YACV3B,OAAOsB;QACT;QACA,IAAI,OAAOvC,WAAW,CAACV,MAAMyB,IAAI,CAAE,KAAK,aAAa;YACnD4B,eAAe1B,KAAK,GAAG,UAAMpC,sOAAAA,EAAiB;gBAAES;gBAAOS;gBAAKE;YAAW;YACvE0C,eAAeC,QAAQ,GAAG;QAC5B;QAEA,gBAAgB;QAChB,IAAI,WAAWtD,SAASA,MAAMuD,KAAK,EAAEC,gBAAgB;YACnD,KAAK,MAAMC,QAAQzD,MAAMuD,KAAK,CAACC,cAAc,CAAE;gBAC7C,MAAME,cAAc,MAAMD,KAAK;oBAC7B9D;oBACAC;oBACAC;oBACAC,MAAMA;oBACNE;oBACAE;oBACAW,WAAWO;oBACXjB;oBACAwD,aAAa5D;oBACbK;oBACAU,MAAMG;oBACN2C,oBAAoBjD;oBACpBkD,eAAelD,UAAU,CAACX,MAAMyB,IAAI,CAAC;oBACrChB;oBACAM,YAAYI;oBACZT;oBACAE,eAAeA;oBACfe,OACE,OAAOjB,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,cAC/B4B,eAAe1B,KAAK,GACpBjB,WAAW,CAACV,MAAMyB,IAAI,CAAC;gBAC/B;gBAEA,IAAIiC,gBAAgBT,WAAW;oBAC7BvC,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGiC;gBAC5B;YACF;QACF;QAEA,yBAAyB;QACzB,IAAI1D,MAAM8D,MAAM,IAAI9D,MAAM8D,MAAM,CAAC3D,UAAU,EAAE;YAC3C,MAAM4D,SAAS3D,iBACX,OACA,MAAMJ,MAAM8D,MAAM,CAAC3D,UAAU,CAAC;gBAC5BT;gBACAC;gBACAG,MAAMA;gBACNC;gBACAU;gBACAC;YACF;YAEJ,IAAI,CAACqD,QAAQ;gBACX,OAAOrD,WAAW,CAACV,MAAMyB,IAAI,CAAE;YACjC;QACF;QAEA,IAAI,OAAOf,WAAW,CAACV,MAAMyB,IAAI,CAAE,KAAK,aAAa;YACnDf,WAAW,CAACV,MAAMyB,IAAI,CAAE,GAAG,CAAC4B,eAAeC,QAAQ,GAC/C,UAAM/D,sOAAAA,EAAiB;gBAAES;gBAAOS;gBAAKE;YAAW,KAChD0C,eAAe1B,KAAK;QAC1B;IACF;IAEA,qBAAqB;IACrB,OAAQ3B,MAAM0B,IAAI;QAChB,KAAK;YAAS;gBACZ,MAAMsC,OAAOtD,WAAW,CAACV,MAAMyB,IAAI,CAAC;gBAEpC,IAAIQ,MAAMC,OAAO,CAAC8B,OAAO;oBACvB,MAAMC,WAA4B,EAAE;oBAEpCD,KAAKzB,OAAO,CAAC,CAAC2B,KAAKC;wBACjBF,SAASG,IAAI,KACX5E,kOAAAA,EAAe;4BACbE;4BACAC;4BACAC;4BACAC;4BACAC;4BACAC;4BACA8C,QAAQ7C,MAAM6C,MAAM;4BACpB3C;4BACAC;4BACAC;4BACAC,iBAAiB;4BACjBC,mBAAmBA,qBAAqBN,MAAMqE,SAAS;4BACvD9D,YAAYO,OAAO,MAAMqD;4BACzB3D,kBAAkBO;4BAClBN;4BACAC,aAAawD;4BACbvD,gBAAYrB,sOAAAA,EAAkB4E,KAAmBvD,UAAU,CAACX,MAAMyB,IAAI,CAAC;wBACzE;oBAEJ;oBAEA,MAAM6C,QAAQC,GAAG,CAACN;gBACpB;gBACA;YACF;QAEA,KAAK;YAAU;gBACb,MAAMD,OAAOtD,WAAW,CAACV,MAAMyB,IAAI,CAAC;gBAEpC,IAAIQ,MAAMC,OAAO,CAAC8B,OAAO;oBACvB,MAAMC,WAA4B,EAAE;oBAEpCD,KAAKzB,OAAO,CAAC,CAAC2B,KAAKC;wBACjB,MAAMK,oBAAgBlF,sOAAAA,EAAkB4E,KAAmBvD,UAAU,CAACX,MAAMyB,IAAI,CAAC;wBACjF,MAAMgD,mBAAoBP,IAAmBQ,SAAS,IAAIF,cAAcE,SAAS;wBAEjF,MAAMC,QACJlE,IAAIiC,OAAO,CAACkC,MAAM,CAACH,iBAAiB,IAClCzE,CAAAA,MAAM6E,eAAe,IAAI7E,MAAM4E,MAAK,EAAG7B,IAAI,CAC3C,CAAC+B,WAAa,OAAOA,aAAa,YAAYA,SAASC,IAAI,KAAKN;wBAGpE,IAAIE,OAAO;;4BACPT,IAAmBQ,SAAS,GAAGD;4BAEjCR,SAASG,IAAI,KACX5E,kOAAAA,EAAe;gCACbE;gCACAC,WAAWuE;gCACXtE;gCACAC;gCACAC;gCACAC;gCACA8C,QAAQ8B,MAAM9B,MAAM;gCACpB3C;gCACAC;gCACAC;gCACAC,iBAAiB;gCACjBC,mBAAmBA,qBAAqBN,MAAMqE,SAAS;gCACvD9D,YAAYO,OAAO,MAAMqD;gCACzB3D,kBAAkBO,aAAa,MAAM4D,MAAMI,IAAI;gCAC/CtE;gCACAC,aAAawD;gCACbvD,YAAY6D;4BACd;wBAEJ;oBACF;oBAEA,MAAMF,QAAQC,GAAG,CAACN;gBACpB;gBAEA;YACF;QAEA,KAAK;QACL,KAAK;YAAO;gBACV,UAAMzE,kOAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA8C,QAAQ7C,MAAM6C,MAAM;oBACpB3C;oBACAC;oBACAC;oBACAC,iBAAiBQ;oBACjBP;oBACAC;oBACAC,kBAAkBO;oBAClBN;oBACAC;oBACAC;gBACF;gBAEA;YACF;QAEA,KAAK;YAAS;gBACZ,IAAIqE,mBAAmBtE;gBACvB,IAAIuE,kBAAkBtE;gBAEtB,MAAMuE,mBAAejG,0MAAAA,EAAiBe;gBAEtC,IAAIkF,cAAc;oBAChB,IAAI,OAAOxE,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC/Cf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG,CAAC;oBAC7B;oBAEA,IAAI,OAAOd,UAAU,CAACX,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC9Cd,UAAU,CAACX,MAAMyB,IAAI,CAAC,GAAG,CAAC;oBAC5B;oBAEAuD,mBAAmBtE,WAAW,CAACV,MAAMyB,IAAI,CAAC;oBAC1CwD,kBAAkBtE,UAAU,CAACX,MAAMyB,IAAI,CAAC;gBAC1C;gBAEA,UAAMjC,kOAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA8C,QAAQ7C,MAAM6C,MAAM;oBACpB3C;oBACAC;oBACAC;oBACAC,iBAAiB6E,eAAe,KAAKrE;oBACrCP,mBAAmBA,qBAAqBN,MAAMqE,SAAS;oBACvD9D,YAAY2E,eAAepE,OAAOP;oBAClCC,kBAAkBO;oBAClBN;oBACAC,aAAasE;oBACbrE,YAAYsE;gBACd;gBAEA;YACF;QAEA,KAAK;YAAY;gBACf,IAAI,CAACjF,OAAOmF,QAAQ;oBAClB,MAAM,IAAInG,6MAAAA,CAAkBgB,OAAO,8HAA8H;;gBACnK;gBAEA,IAAI,OAAOA,OAAOmF,WAAW,YAAY;oBACvC,MAAM,IAAIC,MAAM;gBAClB;gBAEA,MAAMD,SAA0BnF,OAAOmF;gBAEvC,IAAIA,QAAQ5B,OAAOC,gBAAgBxB,QAAQ;oBACzC,KAAK,MAAMyB,QAAQ0B,OAAO5B,KAAK,CAACC,cAAc,CAAE;wBAC9C,MAAME,cAAc,MAAMD,KAAK;4BAC7B7D;4BACAC;4BACAC,MAAMA;4BACNE;4BACAE;4BACAW,WAAWO;4BACXjB;4BACAwD,aAAa5D;4BACbK;4BACAE;4BACAQ,MAAMG;4BACN2C,oBAAoBjD;4BACpBkD,eAAenD,WAAW,CAACV,MAAMyB,IAAI,CAAC;4BACtChB;4BACAM,YAAYI;4BACZT;4BACAiB,OAAOjB,WAAW,CAACV,MAAMyB,IAAI,CAAC;wBAChC;wBAEA,IAAIiC,gBAAgBT,WAAW;4BAC7BvC,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAGiC;wBAC5B;oBACF;gBACF;gBACA;YACF;QAEA,KAAK;YAAO;gBACV,IAAI2B;gBACJ,IAAIC;gBAEJ,MAAMC,iBAAarG,oMAAAA,EAAWc;gBAE9B,IAAIuF,YAAY;oBACd,IAAI,OAAO7E,WAAW,CAACV,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC/Cf,WAAW,CAACV,MAAMyB,IAAI,CAAC,GAAG,CAAC;oBAC7B;oBAEA,IAAI,OAAOd,UAAU,CAACX,MAAMyB,IAAI,CAAC,KAAK,UAAU;wBAC9Cd,UAAU,CAACX,MAAMyB,IAAI,CAAC,GAAG,CAAC;oBAC5B;oBAEA4D,iBAAiB3E,WAAW,CAACV,MAAMyB,IAAI,CAAC;oBACxC6D,gBAAgB3E,UAAU,CAACX,MAAMyB,IAAI,CAAC;gBACxC,OAAO;oBACL4D,iBAAiB3E;oBACjB4E,gBAAgB3E;gBAClB;gBAEA,UAAMnB,kOAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA8C,QAAQ7C,MAAM6C,MAAM;oBACpB3C;oBACAC;oBACAC;oBACAC,iBAAiBkF,aAAa,KAAK1E;oBACnCP,mBAAmBA,qBAAqBN,MAAMqE,SAAS;oBACvD9D,YAAYgF,aAAazE,OAAOP;oBAChCC,kBAAkBO;oBAClBN;oBACAC,aAAa2E;oBACb1E,YAAY2E;gBACd;gBAEA;YACF;QAEA,KAAK;YAAQ;gBACX,UAAM9F,kOAAAA,EAAe;oBACnBE;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA8C,QAAQ7C,MAAMwF,IAAI,CAACjE,GAAG,CAAC,CAACkE,MAAS,CAAA;4BAAE,GAAGA,GAAG;4BAAE/D,MAAM;wBAAM,CAAA;oBACvDxB;oBACAC;oBACAC;oBACAC,iBAAiBQ;oBACjBP;oBACAC,YAAYO;oBACZN,kBAAkBO;oBAClBN;oBACAC;oBACAC;gBACF;gBAEA;YACF;QAEA;YAAS;gBACP;YACF;IACF;AACF,EAAC", "debugId": null}}, {"offset": {"line": 4479, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeValidate/traverseFields.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { RequestContext } from '../../../index.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\nimport type { Field, TabAsField } from '../../config/types.js'\n\nimport { promise } from './promise.js'\n\ntype Args<T> = {\n  /**\n   * Data of the nearest parent block. If no parent block exists, this will be the `undefined`\n   */\n  blockData?: JsonObject\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: T\n  /**\n   * The original data (not modified by any hooks)\n   */\n  doc: T\n  fields: (Field | TabAsField)[]\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  operation: 'create' | 'update'\n  overrideAccess: boolean\n  parentIndexPath: string\n  /**\n   * @todo make required in v4.0\n   */\n  parentIsLocalized?: boolean\n  parentPath: string\n  parentSchemaPath: string\n  req: PayloadRequest\n  siblingData: JsonObject\n  /**\n   * The original siblingData (not modified by any hooks)\n   */\n  siblingDoc: JsonObject\n}\n\nexport const traverseFields = async <T>({\n  id,\n  blockData,\n  collection,\n  context,\n  data,\n  doc,\n  fields,\n  global,\n  operation,\n  overrideAccess,\n  parentIndexPath,\n  parentIsLocalized,\n  parentPath,\n  parentSchemaPath,\n  req,\n  siblingData,\n  siblingDoc,\n}: Args<T>): Promise<void> => {\n  const promises: Promise<void>[] = []\n\n  fields.forEach((field, fieldIndex) => {\n    promises.push(\n      promise({\n        id,\n        blockData,\n        collection,\n        context,\n        data,\n        doc,\n        field,\n        fieldIndex,\n        global,\n        operation,\n        overrideAccess,\n        parentIndexPath,\n        parentIsLocalized: parentIsLocalized!,\n        parentPath,\n        parentSchemaPath,\n        req,\n        siblingData,\n        siblingDoc,\n        siblingFields: fields,\n      }),\n    )\n  })\n\n  await Promise.all(promises)\n}\n"], "names": ["promise", "traverseFields", "id", "blockData", "collection", "context", "data", "doc", "fields", "global", "operation", "overrideAccess", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "req", "siblingData", "siblingDoc", "promises", "for<PERSON>ach", "field", "fieldIndex", "push", "siblingFields", "Promise", "all"], "mappings": ";;;;AAMA,SAASA,OAAO,QAAQ,eAAc;;AAkC/B,MAAMC,iBAAiB,OAAU,EACtCC,EAAE,EACFC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,UAAU,EACVC,gBAAgB,EAChBC,GAAG,EACHC,WAAW,EACXC,UAAU,EACF;IACR,MAAMC,WAA4B,EAAE;IAEpCX,OAAOY,OAAO,CAAC,CAACC,OAAOC;QACrBH,SAASI,IAAI,KACXvB,oNAAAA,EAAQ;YACNE;YACAC;YACAC;YACAC;YACAC;YACAC;YACAc;YACAC;YACAb;YACAC;YACAC;YACAC;YACAC,mBAAmBA;YACnBC;YACAC;YACAC;YACAC;YACAC;YACAM,eAAehB;QACjB;IAEJ;IAEA,MAAMiB,QAAQC,GAAG,CAACP;AACpB,EAAC", "debugId": null}}, {"offset": {"line": 4516, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/payload/src/fields/hooks/beforeValidate/index.ts"], "sourcesContent": ["import type { SanitizedCollectionConfig } from '../../../collections/config/types.js'\nimport type { SanitizedGlobalConfig } from '../../../globals/config/types.js'\nimport type { JsonObject, PayloadRequest } from '../../../types/index.js'\n\nimport { type RequestContext } from '../../../index.js'\nimport { traverseFields } from './traverseFields.js'\n\ntype Args<T extends JsonObject> = {\n  collection: null | SanitizedCollectionConfig\n  context: RequestContext\n  data: T\n  doc?: T\n  duplicate?: boolean\n  global: null | SanitizedGlobalConfig\n  id?: number | string\n  operation: 'create' | 'update'\n  overrideAccess: boolean\n  req: PayloadRequest\n}\n\n/**\n * This function is responsible for the following actions, in order:\n * - Sanitize incoming data\n * - Execute field hooks\n * - Execute field access control\n * - Merge original document data into incoming data\n * - Compute default values for undefined fields\n */\nexport const beforeValidate = async <T extends JsonObject>({\n  id,\n  collection,\n  context,\n  data: incomingData,\n  doc,\n  global,\n  operation,\n  overrideAccess,\n  req,\n}: Args<T>): Promise<T> => {\n  await traverseFields({\n    id,\n    collection,\n    context,\n    data: incomingData,\n    doc,\n    fields: (collection?.fields || global?.fields)!,\n    global,\n    operation,\n    overrideAccess,\n    parentIndexPath: '',\n    parentIsLocalized: false,\n    parentPath: '',\n    parentSchemaPath: '',\n    req,\n    siblingData: incomingData,\n    siblingDoc: doc!,\n  })\n\n  return incomingData\n}\n"], "names": ["traverseFields", "beforeValidate", "id", "collection", "context", "data", "incomingData", "doc", "global", "operation", "overrideAccess", "req", "fields", "parentIndexPath", "parentIsLocalized", "parentPath", "parentSchemaPath", "siblingData", "siblingDoc"], "mappings": ";;;;AAKA,SAASA,cAAc,QAAQ,sBAAqB;;AAuB7C,MAAMC,iBAAiB,OAA6B,EACzDC,EAAE,EACFC,UAAU,EACVC,OAAO,EACPC,MAAMC,YAAY,EAClBC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,cAAc,EACdC,GAAG,EACK;IACR,UAAMX,kOAAAA,EAAe;QACnBE;QACAC;QACAC;QACAC,MAAMC;QACNC;QACAK,QAAST,YAAYS,UAAUJ,QAAQI;QACvCJ;QACAC;QACAC;QACAG,iBAAiB;QACjBC,mBAAmB;QACnBC,YAAY;QACZC,kBAAkB;QAClBL;QACAM,aAAaX;QACbY,YAAYX;IACd;IAEA,OAAOD;AACT,EAAC", "debugId": null}}]}