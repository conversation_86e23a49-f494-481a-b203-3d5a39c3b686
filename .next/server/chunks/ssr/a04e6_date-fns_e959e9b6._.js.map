{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"по-малко от секунда\",\n    other: \"по-малко от {{count}} секунди\",\n  },\n\n  xSeconds: {\n    one: \"1 секунда\",\n    other: \"{{count}} секунди\",\n  },\n\n  halfAMinute: \"половин минута\",\n\n  lessThanXMinutes: {\n    one: \"по-малко от минута\",\n    other: \"по-малко от {{count}} минути\",\n  },\n\n  xMinutes: {\n    one: \"1 минута\",\n    other: \"{{count}} минути\",\n  },\n\n  aboutXHours: {\n    one: \"около час\",\n    other: \"около {{count}} часа\",\n  },\n\n  xHours: {\n    one: \"1 час\",\n    other: \"{{count}} часа\",\n  },\n\n  xDays: {\n    one: \"1 ден\",\n    other: \"{{count}} дни\",\n  },\n\n  aboutXWeeks: {\n    one: \"около седмица\",\n    other: \"около {{count}} седмици\",\n  },\n\n  xWeeks: {\n    one: \"1 седмица\",\n    other: \"{{count}} седмици\",\n  },\n\n  aboutXMonths: {\n    one: \"около месец\",\n    other: \"около {{count}} месеца\",\n  },\n\n  xMonths: {\n    one: \"1 месец\",\n    other: \"{{count}} месеца\",\n  },\n\n  aboutXYears: {\n    one: \"около година\",\n    other: \"около {{count}} години\",\n  },\n\n  xYears: {\n    one: \"1 година\",\n    other: \"{{count}} години\",\n  },\n\n  overXYears: {\n    one: \"над година\",\n    other: \"над {{count}} години\",\n  },\n\n  almostXYears: {\n    one: \"почти година\",\n    other: \"почти {{count}} години\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"след \" + result;\n    } else {\n      return \"преди \" + result;\n    }\n  }\n\n  return result;\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IAC3B,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;IAEb,kBAAkB;QAChB,KAAK;QACL,OAAO;IACT;IAEA,UAAU;QACR,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,OAAO;QACL,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;IAEA,SAAS;QACP,KAAK;QACL,OAAO;IACT;IAEA,aAAa;QACX,KAAK;QACL,OAAO;IACT;IAEA,QAAQ;QACN,KAAK;QACL,OAAO;IACT;IAEA,YAAY;QACV,KAAK;QACL,OAAO;IACT;IAEA,cAAc;QACZ,KAAK;QACL,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,CAAC,OAAO,OAAO;IAC3C,IAAI;IAEJ,MAAM,aAAa,oBAAoB,CAAC,MAAM;IAC9C,IAAI,OAAO,eAAe,UAAU;QAClC,SAAS;IACX,OAAO,IAAI,UAAU,GAAG;QACtB,SAAS,WAAW,GAAG;IACzB,OAAO;QACL,SAAS,WAAW,KAAK,CAAC,OAAO,CAAC,aAAa,OAAO;IACxD;IAEA,IAAI,SAAS,WAAW;QACtB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,GAAG,GAAG;YAChD,OAAO,UAAU;QACnB,OAAO;YACL,OAAO,WAAW;QACpB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"EEEE, dd MMMM yyyy\",\n  long: \"dd MMMM yyyy\",\n  medium: \"dd MMM yyyy\",\n  short: \"dd.MM.yyyy\",\n};\n\nconst timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"H:mm\",\n};\n\nconst dateTimeFormats = {\n  any: \"{{date}} {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"any\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,cAAc;IAClB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;AACT;AAEA,MAAM,kBAAkB;IACtB,KAAK;AACP;AAEO,MAAM,aAAa;IACxB,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,IAAA,iNAAiB,EAAC;QACtB,SAAS;QACT,cAAc;IAChB;IAEA,UAAU,IAAA,iNAAiB,EAAC;QAC1B,SAAS;QACT,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/isSameWeek.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link isSameWeek} function options.\n */\n\n/**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameWeek(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    +startOfWeek(laterDate_, options) === +startOfWeek(earlierDate_, options)\n  );\n}\n\n// Fallback for modularized imports:\nexport default isSameWeek;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAsCO,SAAS,WAAW,SAAS,EAAE,WAAW,EAAE,OAAO;IACxD,MAAM,CAAC,YAAY,aAAa,GAAG,IAAA,iMAAc,EAC/C,SAAS,IACT,WACA;IAEF,OACE,CAAC,IAAA,mLAAW,EAAC,YAAY,aAAa,CAAC,IAAA,mLAAW,EAAC,cAAc;AAErE;uCAGe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\nimport { toDate } from \"../../../toDate.js\";\n\n// Adapted from the `ru` translation\n\nconst weekdays = [\n  \"неделя\",\n  \"понеделник\",\n  \"вторник\",\n  \"сряда\",\n  \"четвъртък\",\n  \"петък\",\n  \"събота\",\n];\n\nfunction lastWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'миналата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'миналия \" + weekday + \" в' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = weekdays[day];\n\n  if (day === 2 /* Tue */) {\n    return \"'във \" + weekday + \" в' p\";\n  } else {\n    return \"'в \" + weekday + \" в' p\";\n  }\n}\n\nfunction nextWeek(day) {\n  const weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следващата \" + weekday + \" в' p\";\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следващия \" + weekday + \" в' p\";\n  }\n}\n\nconst lastWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nconst nextWeekFormatToken = (dirtyDate, baseDate, options) => {\n  const date = toDate(dirtyDate);\n  const day = date.getDay();\n  if (isSameWeek(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nconst formatRelativeLocale = {\n  lastWeek: lastWeekFormatToken,\n  yesterday: \"'вчера в' p\",\n  today: \"'днес в' p\",\n  tomorrow: \"'утре в' p\",\n  nextWeek: nextWeekFormatToken,\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,oCAAoC;AAEpC,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,SAAS,GAAG;IACnB,MAAM,UAAU,QAAQ,CAAC,IAAI;IAE7B,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,eAAe,UAAU;QAClC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,cAAc,UAAU;IACnC;AACF;AAEA,SAAS,SAAS,GAAG;IACnB,MAAM,UAAU,QAAQ,CAAC,IAAI;IAE7B,IAAI,QAAQ,EAAE,OAAO,KAAI;QACvB,OAAO,UAAU,UAAU;IAC7B,OAAO;QACL,OAAO,QAAQ,UAAU;IAC3B;AACF;AAEA,SAAS,SAAS,GAAG;IACnB,MAAM,UAAU,QAAQ,CAAC,IAAI;IAE7B,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,iBAAiB,UAAU;QACpC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,gBAAgB,UAAU;IACrC;AACF;AAEA,MAAM,sBAAsB,CAAC,WAAW,UAAU;IAChD,MAAM,OAAO,IAAA,yKAAM,EAAC;IACpB,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,IAAA,iLAAU,EAAC,MAAM,UAAU,UAAU;QACvC,OAAO,SAAS;IAClB,OAAO;QACL,OAAO,SAAS;IAClB;AACF;AAEA,MAAM,sBAAsB,CAAC,WAAW,UAAU;IAChD,MAAM,OAAO,IAAA,yKAAM,EAAC;IACpB,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,IAAA,iLAAU,EAAC,MAAM,UAAU,UAAU;QACvC,OAAO,SAAS;IAClB,OAAO;QACL,OAAO,SAAS;IAClB;AACF;AAEA,MAAM,uBAAuB;IAC3B,UAAU;IACV,WAAW;IACX,OAAO;IACP,UAAU;IACV,UAAU;IACV,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC,OAAO,MAAM,UAAU;IACpD,MAAM,SAAS,oBAAoB,CAAC,MAAM;IAE1C,IAAI,OAAO,WAAW,YAAY;QAChC,OAAO,OAAO,MAAM,UAAU;IAChC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"преди н. е.\", \"н. е.\"],\n  wide: [\"преди новата ера\", \"новата ера\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-во тримес.\", \"2-ро тримес.\", \"3-то тримес.\", \"4-то тримес.\"],\n\n  wide: [\n    \"1-во тримесечие\",\n    \"2-ро тримесечие\",\n    \"3-то тримесечие\",\n    \"4-то тримесечие\",\n  ],\n};\n\nconst monthValues = {\n  abbreviated: [\n    \"яну\",\n    \"фев\",\n    \"мар\",\n    \"апр\",\n    \"май\",\n    \"юни\",\n    \"юли\",\n    \"авг\",\n    \"сеп\",\n    \"окт\",\n    \"ное\",\n    \"дек\",\n  ],\n\n  wide: [\n    \"януари\",\n    \"февруари\",\n    \"март\",\n    \"април\",\n    \"май\",\n    \"юни\",\n    \"юли\",\n    \"август\",\n    \"септември\",\n    \"октомври\",\n    \"ноември\",\n    \"декември\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сря\", \"чет\", \"пет\", \"съб\"],\n  wide: [\n    \"неделя\",\n    \"понеделник\",\n    \"вторник\",\n    \"сряда\",\n    \"четвъртък\",\n    \"петък\",\n    \"събота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  wide: {\n    am: \"преди обяд\",\n    pm: \"след обяд\",\n    midnight: \"в полунощ\",\n    noon: \"на обяд\",\n    morning: \"сутринта\",\n    afternoon: \"следобед\",\n    evening: \"вечерта\",\n    night: \"през нощта\",\n  },\n};\n\nfunction isFeminine(unit) {\n  return (\n    unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\"\n  );\n}\n\nfunction isNeuter(unit) {\n  return unit === \"quarter\";\n}\n\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  const suffix = isNeuter(unit)\n    ? neuter\n    : isFeminine(unit)\n      ? feminine\n      : masculine;\n  return number + \"-\" + suffix;\n}\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n\n  if (number === 0) {\n    return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n  }\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n      case 2:\n        return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n    }\n  }\n\n  return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAW;KAAO;IAC3B,aAAa;QAAC;QAAe;KAAQ;IACrC,MAAM;QAAC;QAAoB;KAAa;AAC1C;AAEA,MAAM,gBAAgB;IACpB,QAAQ;QAAC;QAAK;QAAK;QAAK;KAAI;IAC5B,aAAa;QAAC;QAAgB;QAAgB;QAAgB;KAAe;IAE7E,MAAM;QACJ;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,cAAc;IAClB,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,YAAY;IAChB,QAAQ;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IAC3C,OAAO;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IACjD,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IAC9D,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,kBAAkB;IACtB,MAAM;QACJ,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEA,SAAS,WAAW,IAAI;IACtB,OACE,SAAS,UAAU,SAAS,UAAU,SAAS,YAAY,SAAS;AAExE;AAEA,SAAS,SAAS,IAAI;IACpB,OAAO,SAAS;AAClB;AAEA,SAAS,iBAAiB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM;IACjE,MAAM,SAAS,SAAS,QACpB,SACA,WAAW,QACT,WACA;IACN,OAAO,SAAS,MAAM;AACxB;AAEA,MAAM,gBAAgB,CAAC,aAAa;IAClC,MAAM,SAAS,OAAO;IACtB,MAAM,OAAO,SAAS;IAEtB,IAAI,WAAW,GAAG;QAChB,OAAO,iBAAiB,GAAG,MAAM,MAAM,OAAO;IAChD,OAAO,IAAI,SAAS,SAAS,GAAG;QAC9B,OAAO,iBAAiB,QAAQ,MAAM,MAAM,MAAM;IACpD,OAAO,IAAI,SAAS,QAAQ,GAAG;QAC7B,OAAO,iBAAiB,QAAQ,MAAM,OAAO,OAAO;IACtD;IAEA,MAAM,SAAS,SAAS;IACxB,IAAI,SAAS,MAAM,SAAS,IAAI;QAC9B,OAAQ,SAAS;YACf,KAAK;gBACH,OAAO,iBAAiB,QAAQ,MAAM,MAAM,MAAM;YACpD,KAAK;gBACH,OAAO,iBAAiB,QAAQ,MAAM,MAAM,MAAM;YACpD,KAAK;YACL,KAAK;gBACH,OAAO,iBAAiB,QAAQ,MAAM,MAAM,MAAM;QACtD;IACF;IAEA,OAAO,iBAAiB,QAAQ,MAAM,MAAM,MAAM;AACpD;AAEO,MAAM,WAAW;IACtB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,SAAS,IAAA,6MAAe,EAAC;QACvB,QAAQ;QACR,cAAc;QACd,kBAAkB,CAAC,UAAY,UAAU;IAC3C;IAEA,OAAO,IAAA,6MAAe,EAAC;QACrB,QAAQ;QACR,cAAc;IAChB;IAEA,KAAK,IAAA,6MAAe,EAAC;QACnB,QAAQ;QACR,cAAc;IAChB;IAEA,WAAW,IAAA,6MAAe,EAAC;QACzB,QAAQ;QACR,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg/_lib/match.js"], "sourcesContent": ["import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern =\n  /^(\\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^((пр)?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((пр)?н\\.?\\s?е\\.?)/i,\n  wide: /^(преди новата ера|новата ера|нова ера)/i,\n};\nconst parseEraPatterns = {\n  any: [/^п/i, /^н/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[врт]?o?)? тримес.?/i,\n  wide: /^[1234](-?[врт]?о?)? тримесечие/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)/i,\n  abbreviated: /^(нед|пон|вто|сря|чет|пет|съб)/i,\n  wide: /^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i,\n};\n\nconst parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н[ед]/i, /^п[он]/i, /^вт/i, /^ср/i, /^ч[ет]/i, /^п[ет]/i, /^с[ъб]/i],\n};\n\nconst matchMonthPatterns = {\n  abbreviated: /^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,\n  wide: /^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i,\n};\n\nconst parseMonthPatterns = {\n  any: [\n    /^я/i,\n    /^ф/i,\n    /^мар/i,\n    /^ап/i,\n    /^май/i,\n    /^юн/i,\n    /^юл/i,\n    /^ав/i,\n    /^се/i,\n    /^окт/i,\n    /^но/i,\n    /^де/i,\n  ],\n};\n\nconst matchDayPeriodPatterns = {\n  any: /^(преди о|след о|в по|на о|през|веч|сут|следо)/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^преди о/i,\n    pm: /^след о/i,\n    midnight: /^в пол/i,\n    noon: /^на об/i,\n    morning: /^сут/i,\n    afternoon: /^следо/i,\n    evening: /^веч/i,\n    night: /^през н/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,4BACJ;AACF,MAAM,4BAA4B;AAElC,MAAM,mBAAmB;IACvB,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,mBAAmB;IACvB,KAAK;QAAC;QAAO;KAAM;AACrB;AAEA,MAAM,uBAAuB;IAC3B,QAAQ;IACR,aAAa;IACb,MAAM;AACR;AACA,MAAM,uBAAuB;IAC3B,KAAK;QAAC;QAAM;QAAM;QAAM;KAAK;AAC/B;AAEA,MAAM,mBAAmB;IACvB,QAAQ;IACR,OAAO;IACP,aAAa;IACb,MAAM;AACR;AAEA,MAAM,mBAAmB;IACvB,QAAQ;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAAM;IACzD,KAAK;QAAC;QAAW;QAAW;QAAQ;QAAQ;QAAW;QAAW;KAAU;AAC9E;AAEA,MAAM,qBAAqB;IACzB,aAAa;IACb,MAAM;AACR;AAEA,MAAM,qBAAqB;IACzB,KAAK;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,yBAAyB;IAC7B,KAAK;AACP;AACA,MAAM,yBAAyB;IAC7B,KAAK;QACH,IAAI;QACJ,IAAI;QACJ,UAAU;QACV,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;AACF;AAEO,MAAM,QAAQ;IACnB,eAAe,IAAA,qNAAmB,EAAC;QACjC,cAAc;QACd,cAAc;QACd,eAAe,CAAC,QAAU,SAAS,OAAO;IAC5C;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,SAAS,IAAA,uMAAY,EAAC;QACpB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;QACnB,eAAe,CAAC,QAAU,QAAQ;IACpC;IAEA,OAAO,IAAA,uMAAY,EAAC;QAClB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,KAAK,IAAA,uMAAY,EAAC;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;IAEA,WAAW,IAAA,uMAAY,EAAC;QACtB,eAAe;QACf,mBAAmB;QACnB,eAAe;QACf,mBAAmB;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/date-fns/locale/bg.js"], "sourcesContent": ["import { formatDistance } from \"./bg/_lib/formatDistance.js\";\nimport { formatLong } from \"./bg/_lib/formatLong.js\";\nimport { formatRelative } from \"./bg/_lib/formatRelative.js\";\nimport { localize } from \"./bg/_lib/localize.js\";\nimport { match } from \"./bg/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> [@fintara](https://github.com/fintara)\n */\nexport const bg = {\n  code: \"bg\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default bg;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAUO,MAAM,KAAK;IAChB,MAAM;IACN,gBAAgB,iNAAc;IAC9B,YAAY,yMAAU;IACtB,gBAAgB,iNAAc;IAC9B,UAAU,qMAAQ;IAClB,OAAO,+LAAK;IACZ,SAAS;QACP,cAAc,EAAE,UAAU;QAC1B,uBAAuB;IACzB;AACF;uCAGe", "ignoreList": [0], "debugId": null}}]}