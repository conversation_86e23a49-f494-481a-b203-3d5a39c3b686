module.exports = [
"[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatDistance.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatDistance",
    ()=>formatDistance
]);
const formatDistanceLocale = {
    lessThanXSeconds: {
        one: "أقل من ثانية",
        two: "أقل من ثانيتين",
        threeToTen: "أقل من {{count}} ثواني",
        other: "أقل من {{count}} ثانية"
    },
    xSeconds: {
        one: "ثانية واحدة",
        two: "ثانيتان",
        threeToTen: "{{count}} ثواني",
        other: "{{count}} ثانية"
    },
    halfAMinute: "نصف دقيقة",
    lessThanXMinutes: {
        one: "أقل من دقيقة",
        two: "أقل من دقيقتين",
        threeToTen: "أقل من {{count}} دقائق",
        other: "أقل من {{count}} دقيقة"
    },
    xMinutes: {
        one: "دقيقة واحدة",
        two: "دقيقتان",
        threeToTen: "{{count}} دقائق",
        other: "{{count}} دقيقة"
    },
    aboutXHours: {
        one: "ساعة واحدة تقريباً",
        two: "ساعتين تقريبا",
        threeToTen: "{{count}} ساعات تقريباً",
        other: "{{count}} ساعة تقريباً"
    },
    xHours: {
        one: "ساعة واحدة",
        two: "ساعتان",
        threeToTen: "{{count}} ساعات",
        other: "{{count}} ساعة"
    },
    xDays: {
        one: "يوم واحد",
        two: "يومان",
        threeToTen: "{{count}} أيام",
        other: "{{count}} يوم"
    },
    aboutXWeeks: {
        one: "أسبوع واحد تقريبا",
        two: "أسبوعين تقريبا",
        threeToTen: "{{count}} أسابيع تقريبا",
        other: "{{count}} أسبوعا تقريبا"
    },
    xWeeks: {
        one: "أسبوع واحد",
        two: "أسبوعان",
        threeToTen: "{{count}} أسابيع",
        other: "{{count}} أسبوعا"
    },
    aboutXMonths: {
        one: "شهر واحد تقريباً",
        two: "شهرين تقريبا",
        threeToTen: "{{count}} أشهر تقريبا",
        other: "{{count}} شهرا تقريباً"
    },
    xMonths: {
        one: "شهر واحد",
        two: "شهران",
        threeToTen: "{{count}} أشهر",
        other: "{{count}} شهرا"
    },
    aboutXYears: {
        one: "سنة واحدة تقريباً",
        two: "سنتين تقريبا",
        threeToTen: "{{count}} سنوات تقريباً",
        other: "{{count}} سنة تقريباً"
    },
    xYears: {
        one: "سنة واحد",
        two: "سنتان",
        threeToTen: "{{count}} سنوات",
        other: "{{count}} سنة"
    },
    overXYears: {
        one: "أكثر من سنة",
        two: "أكثر من سنتين",
        threeToTen: "أكثر من {{count}} سنوات",
        other: "أكثر من {{count}} سنة"
    },
    almostXYears: {
        one: "ما يقارب سنة واحدة",
        two: "ما يقارب سنتين",
        threeToTen: "ما يقارب {{count}} سنوات",
        other: "ما يقارب {{count}} سنة"
    }
};
const formatDistance = (token, count, options)=>{
    const usageGroup = formatDistanceLocale[token];
    let result;
    if (typeof usageGroup === "string") {
        result = usageGroup;
    } else if (count === 1) {
        result = usageGroup.one;
    } else if (count === 2) {
        result = usageGroup.two;
    } else if (count <= 10) {
        result = usageGroup.threeToTen.replace("{{count}}", String(count));
    } else {
        result = usageGroup.other.replace("{{count}}", String(count));
    }
    if (options?.addSuffix) {
        if (options.comparison && options.comparison > 0) {
            return "خلال " + result;
        } else {
            return "منذ " + result;
        }
    }
    return result;
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatLong.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatLong",
    ()=>formatLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildFormatLongFn.js [app-rsc] (ecmascript)");
;
const dateFormats = {
    full: "EEEE، do MMMM y",
    long: "do MMMM y",
    medium: "d MMM y",
    short: "dd/MM/yyyy"
};
const timeFormats = {
    full: "HH:mm:ss",
    long: "HH:mm:ss",
    medium: "HH:mm:ss",
    short: "HH:mm"
};
const dateTimeFormats = {
    full: "{{date}} 'عند الساعة' {{time}}",
    long: "{{date}} 'عند الساعة' {{time}}",
    medium: "{{date}}, {{time}}",
    short: "{{date}}, {{time}}"
};
const formatLong = {
    date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateFormats,
        defaultWidth: "full"
    }),
    time: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: timeFormats,
        defaultWidth: "full"
    }),
    dateTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateTimeFormats,
        defaultWidth: "full"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatRelative.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatRelative",
    ()=>formatRelative
]);
const formatRelativeLocale = {
    lastWeek: "eeee 'الماضي عند الساعة' p",
    yesterday: "'الأمس عند الساعة' p",
    today: "'اليوم عند الساعة' p",
    tomorrow: "'غدا عند الساعة' p",
    nextWeek: "eeee 'القادم عند الساعة' p",
    other: "P"
};
const formatRelative = (token)=>formatRelativeLocale[token];
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/localize.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "localize",
    ()=>localize
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildLocalizeFn.js [app-rsc] (ecmascript)");
;
const eraValues = {
    narrow: [
        "ق",
        "ب"
    ],
    abbreviated: [
        "ق.م.",
        "ب.م."
    ],
    wide: [
        "قبل الميلاد",
        "بعد الميلاد"
    ]
};
const quarterValues = {
    narrow: [
        "1",
        "2",
        "3",
        "4"
    ],
    abbreviated: [
        "ر1",
        "ر2",
        "ر3",
        "ر4"
    ],
    wide: [
        "الربع الأول",
        "الربع الثاني",
        "الربع الثالث",
        "الربع الرابع"
    ]
};
const monthValues = {
    narrow: [
        "ي",
        "ف",
        "م",
        "أ",
        "م",
        "ي",
        "ي",
        "أ",
        "س",
        "أ",
        "ن",
        "د"
    ],
    abbreviated: [
        "يناير",
        "فبراير",
        "مارس",
        "أبريل",
        "مايو",
        "يونيو",
        "يوليو",
        "أغسطس",
        "سبتمبر",
        "أكتوبر",
        "نوفمبر",
        "ديسمبر"
    ],
    wide: [
        "يناير",
        "فبراير",
        "مارس",
        "أبريل",
        "مايو",
        "يونيو",
        "يوليو",
        "أغسطس",
        "سبتمبر",
        "أكتوبر",
        "نوفمبر",
        "ديسمبر"
    ]
};
const dayValues = {
    narrow: [
        "ح",
        "ن",
        "ث",
        "ر",
        "خ",
        "ج",
        "س"
    ],
    short: [
        "أحد",
        "اثنين",
        "ثلاثاء",
        "أربعاء",
        "خميس",
        "جمعة",
        "سبت"
    ],
    abbreviated: [
        "أحد",
        "اثنين",
        "ثلاثاء",
        "أربعاء",
        "خميس",
        "جمعة",
        "سبت"
    ],
    wide: [
        "الأحد",
        "الاثنين",
        "الثلاثاء",
        "الأربعاء",
        "الخميس",
        "الجمعة",
        "السبت"
    ]
};
const dayPeriodValues = {
    narrow: {
        am: "ص",
        pm: "م",
        morning: "الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "المساء",
        night: "الليل",
        midnight: "منتصف الليل"
    },
    abbreviated: {
        am: "ص",
        pm: "م",
        morning: "الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "المساء",
        night: "الليل",
        midnight: "منتصف الليل"
    },
    wide: {
        am: "ص",
        pm: "م",
        morning: "الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "المساء",
        night: "الليل",
        midnight: "منتصف الليل"
    }
};
const formattingDayPeriodValues = {
    narrow: {
        am: "ص",
        pm: "م",
        morning: "في الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "في المساء",
        night: "في الليل",
        midnight: "منتصف الليل"
    },
    abbreviated: {
        am: "ص",
        pm: "م",
        morning: "في الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "في المساء",
        night: "في الليل",
        midnight: "منتصف الليل"
    },
    wide: {
        am: "ص",
        pm: "م",
        morning: "في الصباح",
        noon: "الظهر",
        afternoon: "بعد الظهر",
        evening: "في المساء",
        night: "في الليل",
        midnight: "منتصف الليل"
    }
};
const ordinalNumber = (num)=>String(num);
const localize = {
    ordinalNumber: ordinalNumber,
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: eraValues,
        defaultWidth: "wide"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: quarterValues,
        defaultWidth: "wide",
        argumentCallback: (quarter)=>quarter - 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: monthValues,
        defaultWidth: "wide"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayValues,
        defaultWidth: "wide"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayPeriodValues,
        defaultWidth: "wide",
        formattingValues: formattingDayPeriodValues,
        defaultFormattingWidth: "wide"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/match.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "match",
    ()=>match
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchFn.js [app-rsc] (ecmascript)");
;
;
const matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
const parseOrdinalNumberPattern = /\d+/i;
const matchEraPatterns = {
    narrow: /[قب]/,
    abbreviated: /[قب]\.م\./,
    wide: /(قبل|بعد) الميلاد/
};
const parseEraPatterns = {
    any: [
        /قبل/,
        /بعد/
    ]
};
const matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /ر[1234]/,
    wide: /الربع (الأول|الثاني|الثالث|الرابع)/
};
const parseQuarterPatterns = {
    any: [
        /1/i,
        /2/i,
        /3/i,
        /4/i
    ]
};
const matchMonthPatterns = {
    narrow: /^[أيفمسند]/,
    abbreviated: /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/,
    wide: /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/
};
const parseMonthPatterns = {
    narrow: [
        /^ي/i,
        /^ف/i,
        /^م/i,
        /^أ/i,
        /^م/i,
        /^ي/i,
        /^ي/i,
        /^أ/i,
        /^س/i,
        /^أ/i,
        /^ن/i,
        /^د/i
    ],
    any: [
        /^يناير/i,
        /^فبراير/i,
        /^مارس/i,
        /^أبريل/i,
        /^مايو/i,
        /^يونيو/i,
        /^يوليو/i,
        /^أغسطس/i,
        /^سبتمبر/i,
        /^أكتوبر/i,
        /^نوفمبر/i,
        /^ديسمبر/i
    ]
};
const matchDayPatterns = {
    narrow: /^[حنثرخجس]/i,
    short: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,
    abbreviated: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,
    wide: /^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i
};
const parseDayPatterns = {
    narrow: [
        /^ح/i,
        /^ن/i,
        /^ث/i,
        /^ر/i,
        /^خ/i,
        /^ج/i,
        /^س/i
    ],
    wide: [
        /^الأحد/i,
        /^الاثنين/i,
        /^الثلاثاء/i,
        /^الأربعاء/i,
        /^الخميس/i,
        /^الجمعة/i,
        /^السبت/i
    ],
    any: [
        /^أح/i,
        /^اث/i,
        /^ث/i,
        /^أر/i,
        /^خ/i,
        /^ج/i,
        /^س/i
    ]
};
const matchDayPeriodPatterns = {
    narrow: /^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/,
    any: /^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/
};
const parseDayPeriodPatterns = {
    any: {
        am: /^ص/,
        pm: /^م/,
        midnight: /منتصف الليل/,
        noon: /الظهر/,
        afternoon: /بعد الظهر/,
        morning: /في الصباح/,
        evening: /في المساء/,
        night: /في الليل/
    }
};
const match = {
    ordinalNumber: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchPatternFn"])({
        matchPattern: matchOrdinalNumberPattern,
        parsePattern: parseOrdinalNumberPattern,
        valueCallback: (value)=>parseInt(value, 10)
    }),
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchEraPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseEraPatterns,
        defaultParseWidth: "any"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchQuarterPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseQuarterPatterns,
        defaultParseWidth: "any",
        valueCallback: (index)=>index + 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchMonthPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseMonthPatterns,
        defaultParseWidth: "any"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPatterns,
        defaultParseWidth: "any"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPeriodPatterns,
        defaultMatchWidth: "any",
        parsePatterns: parseDayPeriodPatterns,
        defaultParseWidth: "any"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ar.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ar",
    ()=>ar,
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatDistance.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatLong.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/formatRelative.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$localize$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/localize.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$match$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ar/_lib/match.js [app-rsc] (ecmascript)");
;
;
;
;
;
const ar = {
    code: "ar",
    formatDistance: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDistance"],
    formatLong: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatLong"],
    formatRelative: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatRelative"],
    localize: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$localize$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["localize"],
    match: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ar$2f$_lib$2f$match$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"],
    options: {
        weekStartsOn: 6 /* Saturday */ ,
        firstWeekContainsDate: 1
    }
};
const __TURBOPACK__default__export__ = ar;
}),
];

//# sourceMappingURL=a04e6_date-fns_locale_f049b079._.js.map