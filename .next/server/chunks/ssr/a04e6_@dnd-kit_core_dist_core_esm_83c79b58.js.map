{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@dnd-kit/core/dist/core.esm.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndMonitor/context.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndMonitor/useDndMonitorProvider.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/Accessibility/defaults.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/Accessibility/Accessibility.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/store/actions.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/other/noop.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/useSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/useSensors.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/coordinates/constants.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/coordinates/distanceBetweenPoints.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/coordinates/getRelativeTransformOrigin.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/algorithms/helpers.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCenter.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/algorithms/closestCorners.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/algorithms/rectIntersection.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/algorithms/pointerWithin.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/adjustScale.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/getRectDelta.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/rectAdjustment.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/transform/parseTransform.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/transform/inverseTransform.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/getRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/getWindowClientRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/isFixed.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/isScrollable.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableAncestors.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollableElement.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollCoordinates.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/types/direction.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/documentScrollingElement.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollPosition.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollDirectionAndSpeed.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollElementRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/getScrollOffsets.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/scroll/scrollIntoViewIfNeeded.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/rect/Rect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/utilities/Listeners.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/utilities/getEventListenerTarget.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/utilities/hasExceededDistance.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/events.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/keyboard/types.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/keyboard/defaults.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/keyboard/KeyboardSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/pointer/AbstractPointerSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/pointer/PointerSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/mouse/MouseSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/sensors/touch/TouchSensor.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useAutoScroller.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useCachedNode.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useCombineActivators.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useDroppableMeasuring.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialValue.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useInitialRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useMutationObserver.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useResizeObserver.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useRectDelta.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollableAncestors.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsets.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useScrollOffsetsDelta.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useSensorSetup.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useSyntheticListeners.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useWindowRect.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useRects.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/utilities/nodes/getMeasurableNode.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/utilities/useDragOverlayMeasuring.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndContext/defaults.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/store/constructors.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/store/context.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/store/reducer.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/Accessibility/components/RestoreFocus.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/modifiers/applyModifiers.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useMeasuringConfiguration.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndContext/hooks/useLayoutShiftScrollCompensation.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DndContext/DndContext.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/useDraggable.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/useDndContext.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/hooks/useDroppable.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/AnimationManager/AnimationManager.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/NullifiedContextProvider/NullifiedContextProvider.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/components/PositionedOverlay/PositionedOverlay.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useDropAnimation.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/hooks/useKey.ts", "file:///home/<USER>/git/payload-test/node_modules/%40dnd-kit/core/src/components/DragOverlay/DragOverlay.tsx"], "sourcesContent": ["import {createContext} from 'react';\n\nimport type {RegisterListener} from './types';\n\nexport const DndMonitorContext = createContext<RegisterListener | null>(null);\n", "import {useContext, useEffect} from 'react';\n\nimport {DndMonitorContext} from './context';\nimport type {DndMonitorListener} from './types';\n\nexport function useDndMonitor(listener: DndMonitorListener) {\n  const registerListener = useContext(DndMonitorContext);\n\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error(\n        'useDndMonitor must be used within a children of <DndContext>'\n      );\n    }\n\n    const unsubscribe = registerListener(listener);\n\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n", "import {useCallback, useState} from 'react';\n\nimport type {DndMonitorListener, DndMonitorEvent} from './types';\n\nexport function useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set<DndMonitorListener>());\n\n  const registerListener = useCallback(\n    (listener) => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    },\n    [listeners]\n  );\n\n  const dispatch = useCallback(\n    ({type, event}: DndMonitorEvent) => {\n      listeners.forEach((listener) => listener[type]?.(event as any));\n    },\n    [listeners]\n  );\n\n  return [dispatch, registerListener] as const;\n}\n", "import type {Announcements, ScreenReaderInstructions} from './types';\n\nexport const defaultScreenReaderInstructions: ScreenReaderInstructions = {\n  draggable: `\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  `,\n};\n\nexport const defaultAnnouncements: Announcements = {\n  onDragStart({active}) {\n    return `Picked up draggable item ${active.id}.`;\n  },\n  onDragOver({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was moved over droppable area ${over.id}.`;\n    }\n\n    return `Draggable item ${active.id} is no longer over a droppable area.`;\n  },\n  onDragEnd({active, over}) {\n    if (over) {\n      return `Draggable item ${active.id} was dropped over droppable area ${over.id}`;\n    }\n\n    return `Draggable item ${active.id} was dropped.`;\n  },\n  onDragCancel({active}) {\n    return `Dragging was cancelled. Draggable item ${active.id} was dropped.`;\n  },\n};\n", "import React, {useEffect, useMemo, useState} from 'react';\nimport {createPortal} from 'react-dom';\nimport {useUniqueId} from '@dnd-kit/utilities';\nimport {HiddenText, LiveRegion, useAnnouncement} from '@dnd-kit/accessibility';\n\nimport {DndMonitorListener, useDndMonitor} from '../DndMonitor';\n\nimport type {Announcements, ScreenReaderInstructions} from './types';\nimport {\n  defaultAnnouncements,\n  defaultScreenReaderInstructions,\n} from './defaults';\n\ninterface Props {\n  announcements?: Announcements;\n  container?: Element;\n  screenReaderInstructions?: ScreenReaderInstructions;\n  hiddenTextDescribedById: string;\n}\n\nexport function Accessibility({\n  announcements = defaultAnnouncements,\n  container,\n  hiddenTextDescribedById,\n  screenReaderInstructions = defaultScreenReaderInstructions,\n}: Props) {\n  const {announce, announcement} = useAnnouncement();\n  const liveRegionId = useUniqueId(`DndLiveRegion`);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useDndMonitor(\n    useMemo<DndMonitorListener>(\n      () => ({\n        onDragStart({active}) {\n          announce(announcements.onDragStart({active}));\n        },\n        onDragMove({active, over}) {\n          if (announcements.onDragMove) {\n            announce(announcements.onDragMove({active, over}));\n          }\n        },\n        onDragOver({active, over}) {\n          announce(announcements.onDragOver({active, over}));\n        },\n        onDragEnd({active, over}) {\n          announce(announcements.onDragEnd({active, over}));\n        },\n        onDragCancel({active, over}) {\n          announce(announcements.onDragCancel({active, over}));\n        },\n      }),\n      [announce, announcements]\n    )\n  );\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = (\n    <>\n      <HiddenText\n        id={hiddenTextDescribedById}\n        value={screenReaderInstructions.draggable}\n      />\n      <LiveRegion id={liveRegionId} announcement={announcement} />\n    </>\n  );\n\n  return container ? createPortal(markup, container) : markup;\n}\n", "import type {Coordinates, UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\nexport enum Action {\n  DragStart = 'dragStart',\n  DragMove = 'dragMove',\n  DragEnd = 'dragEnd',\n  DragCancel = 'dragCancel',\n  DragOver = 'dragOver',\n  RegisterDroppable = 'registerDroppable',\n  SetDroppableDisabled = 'setDroppableDisabled',\n  UnregisterDroppable = 'unregisterDroppable',\n}\n\nexport type Actions =\n  | {\n      type: Action.DragStart;\n      active: UniqueIdentifier;\n      initialCoordinates: Coordinates;\n    }\n  | {type: Action.DragMove; coordinates: Coordinates}\n  | {type: Action.DragEnd}\n  | {type: Action.DragCancel}\n  | {\n      type: Action.RegisterDroppable;\n      element: DroppableContainer;\n    }\n  | {\n      type: Action.SetDroppableDisabled;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n      disabled: boolean;\n    }\n  | {\n      type: Action.UnregisterDroppable;\n      id: UniqueIdentifier;\n      key: UniqueIdentifier;\n    };\n", "export function noop(..._args: any) {}\n", "import {useMemo} from 'react';\n\nimport type {Sensor, SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensor<T extends SensorOptions>(\n  sensor: Sensor<T>,\n  options?: T\n): SensorDescriptor<T> {\n  return useMemo(\n    () => ({\n      sensor,\n      options: options ?? ({} as T),\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [sensor, options]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorDescriptor, SensorOptions} from './types';\n\nexport function useSensors(\n  ...sensors: (SensorDescriptor<any> | undefined | null)[]\n): SensorDescriptor<SensorOptions>[] {\n  return useMemo(\n    () =>\n      [...sensors].filter(\n        (sensor): sensor is SensorDescriptor<any> => sensor != null\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...sensors]\n  );\n}\n", "import type {Coordinates} from '../../types';\n\nexport const defaultCoordinates: Coordinates = Object.freeze({\n  x: 0,\n  y: 0,\n});\n", "import type {Coordinates} from '../../types';\n\n/**\n * Returns the distance between two points\n */\nexport function distanceBetween(p1: Coordinates, p2: Coordinates) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n", "import {getEventCoordinates} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function getRelativeTransformOrigin(\n  event: MouseEvent | TouchEvent | KeyboardEvent,\n  rect: ClientRect\n) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: ((eventCoordinates.x - rect.left) / rect.width) * 100,\n    y: ((eventCoordinates.y - rect.top) / rect.height) * 100,\n  };\n\n  return `${transformOrigin.x}% ${transformOrigin.y}%`;\n}\n", "/* eslint-disable no-redeclare */\nimport type {ClientRect} from '../../types';\n\nimport type {Collision, CollisionDescriptor} from './types';\n\n/**\n * Sort collisions from smallest to greatest value\n */\nexport function sortCollisionsAsc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return a - b;\n}\n\n/**\n * Sort collisions from greatest to smallest value\n */\nexport function sortCollisionsDesc(\n  {data: {value: a}}: CollisionDescriptor,\n  {data: {value: b}}: CollisionDescriptor\n) {\n  return b - a;\n}\n\n/**\n * Returns the coordinates of the corners of a given rectangle:\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\n */\nexport function cornersOfRectangle({left, top, height, width}: ClientRect) {\n  return [\n    {\n      x: left,\n      y: top,\n    },\n    {\n      x: left + width,\n      y: top,\n    },\n    {\n      x: left,\n      y: top + height,\n    },\n    {\n      x: left + width,\n      y: top + height,\n    },\n  ];\n}\n\n/**\n * Returns the first collision, or null if there isn't one.\n * If a property is specified, returns the specified property of the first collision.\n */\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined\n): Collision | null;\nexport function getFirstCollision<T extends keyof Collision>(\n  collisions: Collision[] | null | undefined,\n  property: T\n): Collision[T] | null;\nexport function getFirstCollision(\n  collisions: Collision[] | null | undefined,\n  property?: keyof Collision\n) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n\n  return property ? firstCollision[property] : firstCollision;\n}\n", "import {distanceBetween} from '../coordinates';\nimport type {Coordinates, ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the coordinates of the center of a given ClientRect\n */\nfunction centerOfRectangle(\n  rect: ClientRect,\n  left = rect.left,\n  top = rect.top\n): Coordinates {\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5,\n  };\n}\n\n/**\n * Returns the closest rectangles from an array of rectangles to the center of a given\n * rectangle.\n */\nexport const closestCenter: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const centerRect = centerOfRectangle(\n    collisionRect,\n    collisionRect.left,\n    collisionRect.top\n  );\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n\n      collisions.push({id, data: {droppableContainer, value: distBetween}});\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Returns the closest rectangles from an array of rectangles to the corners of\n * another rectangle.\n */\nexport const closestCorners: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {ClientRect} from '../../types';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {sortCollisionsDesc} from './helpers';\n\n/**\n * Returns the intersecting rectangle area between two rectangles\n */\nexport function getIntersectionRatio(\n  entry: ClientRect,\n  target: ClientRect\n): number {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio =\n      intersectionArea / (targetArea + entryArea - intersectionArea);\n\n    return Number(intersectionRatio.toFixed(4));\n  }\n\n  // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n  return 0;\n}\n\n/**\n * Returns the rectangles that has the greatest intersection area with a given\n * rectangle in an array of rectangles.\n */\nexport const rectIntersection: CollisionDetection = ({\n  collisionRect,\n  droppableRects,\n  droppableContainers,\n}) => {\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {droppableContainer, value: intersectionRatio},\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {distanceBetween} from '../coordinates';\n\nimport type {CollisionDescriptor, CollisionDetection} from './types';\nimport {cornersOfRectangle, sortCollisionsAsc} from './helpers';\n\n/**\n * Check if a given point is contained within a bounding rectangle\n */\nfunction isPointWithinRect(point: Coordinates, rect: ClientRect): boolean {\n  const {top, left, bottom, right} = rect;\n\n  return (\n    top <= point.y && point.y <= bottom && left <= point.x && point.x <= right\n  );\n}\n\n/**\n * Returns the rectangles that the pointer is hovering over\n */\nexport const pointerWithin: CollisionDetection = ({\n  droppableContainers,\n  droppableRects,\n  pointerCoordinates,\n}) => {\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions: CollisionDescriptor[] = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {id} = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\n       * with the pointer coordinates. In order to sort the\n       * colliding rectangles, we measure the distance between\n       * the pointer and the corners of the intersecting rectangle\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n\n      collisions.push({\n        id,\n        data: {droppableContainer, value: effectiveDistance},\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n", "import type {Transform} from '@dnd-kit/utilities';\nimport type {ClientRect} from '../../types';\n\nexport function adjustScale(\n  transform: Transform,\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Transform {\n  return {\n    ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1,\n  };\n}\n", "import type {Coordinates, ClientRect} from '../../types';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getRectDelta(\n  rect1: ClientRect | null,\n  rect2: ClientRect | null\n): Coordinates {\n  return rect1 && rect2\n    ? {\n        x: rect1.left - rect2.left,\n        y: rect1.top - rect2.top,\n      }\n    : defaultCoordinates;\n}\n", "import type {Coordinates, ClientRect} from '../../types';\n\nexport function createRectAdjustmentFn(modifier: number) {\n  return function adjustClientRect(\n    rect: ClientRect,\n    ...adjustments: Coordinates[]\n  ): ClientRect {\n    return adjustments.reduce<ClientRect>(\n      (acc, adjustment) => ({\n        ...acc,\n        top: acc.top + modifier * adjustment.y,\n        bottom: acc.bottom + modifier * adjustment.y,\n        left: acc.left + modifier * adjustment.x,\n        right: acc.right + modifier * adjustment.x,\n      }),\n      {...rect}\n    );\n  };\n}\n\nexport const getAdjustedRect = createRectAdjustmentFn(1);\n", "import type {Transform} from '@dnd-kit/utilities';\n\nexport function parseTransform(transform: string): Transform | null {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5],\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3],\n    };\n  }\n\n  return null;\n}\n", "import type {ClientRect} from '../../types';\n\nimport {parseTransform} from './parseTransform';\n\nexport function inverseTransform(\n  rect: ClientRect,\n  transform: string,\n  transformOrigin: string\n): ClientRect {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {scaleX, scaleY, x: translateX, y: translateY} = parsedTransform;\n\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y =\n    rect.top -\n    translateY -\n    (1 - scaleY) *\n      parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {inverseTransform} from '../transform';\n\ninterface Options {\n  ignoreTransform?: boolean;\n}\n\nconst defaultOptions: Options = {ignoreTransform: false};\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n */\nexport function getClientRect(\n  element: Element,\n  options: Options = defaultOptions\n) {\n  let rect: ClientRect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {transform, transformOrigin} =\n      getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {top, left, width, height, bottom, right} = rect;\n\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right,\n  };\n}\n\n/**\n * Returns the bounding client rect of an element relative to the viewport.\n *\n * @remarks\n * The ClientRect returned by this method does not take into account transforms\n * applied to the element it measures.\n *\n */\nexport function getTransformAgnosticClientRect(element: Element): ClientRect {\n  return getClientRect(element, {ignoreTransform: true});\n}\n", "import type {ClientRect} from '../../types';\n\nexport function getWindowClientRect(element: typeof window): ClientRect {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height,\n  };\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isFixed(\n  node: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(node).getComputedStyle(node)\n): boolean {\n  return computedStyle.position === 'fixed';\n}\n", "import {getWindow} from '@dnd-kit/utilities';\n\nexport function isScrollable(\n  element: HTMLElement,\n  computedStyle: CSSStyleDeclaration = getWindow(element).getComputedStyle(\n    element\n  )\n): boolean {\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n\n  return properties.some((property) => {\n    const value = computedStyle[property as keyof CSSStyleDeclaration];\n\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n", "import {\n  getWindow,\n  isDocument,\n  isHTMLElement,\n  isSVGElement,\n} from '@dnd-kit/utilities';\n\nimport {isFixed} from './isFixed';\nimport {isScrollable} from './isScrollable';\n\nexport function getScrollableAncestors(\n  element: Node | null,\n  limit?: number\n): Element[] {\n  const scrollParents: Element[] = [];\n\n  function findScrollableAncestors(node: Node | null): Element[] {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (\n      isDocument(node) &&\n      node.scrollingElement != null &&\n      !scrollParents.includes(node.scrollingElement)\n    ) {\n      scrollParents.push(node.scrollingElement);\n\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\n\nexport function getFirstScrollableAncestor(node: Node | null): Element | null {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n\n  return firstScrollableAncestor ?? null;\n}\n", "import {\n  canUseDOM,\n  isHTMLElement,\n  isDocument,\n  getOwnerDocument,\n  isNode,\n  isWindow,\n} from '@dnd-kit/utilities';\n\nexport function getScrollableElement(element: EventTarget | null) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (\n    isDocument(element) ||\n    element === getOwnerDocument(element).scrollingElement\n  ) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n", "import {isWindow} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\n\nexport function getScrollXCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\n\nexport function getScrollYCoordinate(element: Element | typeof window): number {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\n\nexport function getScrollCoordinates(\n  element: Element | typeof window\n): Coordinates {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element),\n  };\n}\n", "export enum Direction {\n  Forward = 1,\n  Backward = -1,\n}\n", "import {canUseDOM} from '@dnd-kit/utilities';\n\nexport function isDocumentScrollingElement(element: Element | null) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n", "import {isDocumentScrollingElement} from './documentScrollingElement';\n\nexport function getScrollPosition(scrollingContainer: Element) {\n  const minScroll = {\n    x: 0,\n    y: 0,\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer)\n    ? {\n        height: window.innerHeight,\n        width: window.innerWidth,\n      }\n    : {\n        height: scrollingContainer.clientHeight,\n        width: scrollingContainer.clientWidth,\n      };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height,\n  };\n\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll,\n  };\n}\n", "import {Direction, ClientRect} from '../../types';\nimport {getScrollPosition} from './getScrollPosition';\n\ninterface PositionalCoordinates\n  extends Pick<ClientRect, 'top' | 'left' | 'right' | 'bottom'> {}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2,\n};\n\nexport function getScrollDirectionAndSpeed(\n  scrollContainer: Element,\n  scrollContainerRect: ClientRect,\n  {top, left, right, bottom}: PositionalCoordinates,\n  acceleration = 10,\n  thresholdPercentage = defaultThreshold\n) {\n  const {isTop, isBottom, isLeft, isRight} = getScrollPosition(scrollContainer);\n\n  const direction = {\n    x: 0,\n    y: 0,\n  };\n  const speed = {\n    x: 0,\n    y: 0,\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x,\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.top + threshold.height - top) / threshold.height\n      );\n  } else if (\n    !isBottom &&\n    bottom >= scrollContainerRect.bottom - threshold.height\n  ) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.bottom - threshold.height - bottom) /\n          threshold.height\n      );\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.right - threshold.width - right) / threshold.width\n      );\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x =\n      acceleration *\n      Math.abs(\n        (scrollContainerRect.left + threshold.width - left) / threshold.width\n      );\n  }\n\n  return {\n    direction,\n    speed,\n  };\n}\n", "export function getScrollElementRect(element: Element) {\n  if (element === document.scrollingElement) {\n    const {innerWidth, innerHeight} = window;\n\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight,\n    };\n  }\n\n  const {top, left, right, bottom} = element.getBoundingClientRect();\n\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight,\n  };\n}\n", "import {add} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  getScrollCoordinates,\n  getScrollXCoordinate,\n  getScrollYCoordinate,\n} from './getScrollCoordinates';\nimport {defaultCoordinates} from '../coordinates';\n\nexport function getScrollOffsets(scrollableAncestors: Element[]): Coordinates {\n  return scrollableAncestors.reduce<Coordinates>((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\n\nexport function getScrollXOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\n\nexport function getScrollYOffset(scrollableAncestors: Element[]): number {\n  return scrollableAncestors.reduce<number>((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n", "import type {ClientRect} from '../../types';\nimport {getClientRect} from '../rect/getRect';\nimport {getFirstScrollableAncestor} from './getScrollableAncestors';\n\nexport function scrollIntoViewIfNeeded(\n  element: HTMLElement | null | undefined,\n  measure: (node: HTMLElement) => ClientRect = getClientRect\n) {\n  if (!element) {\n    return;\n  }\n\n  const {top, left, bottom, right} = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (\n    bottom <= 0 ||\n    right <= 0 ||\n    top >= window.innerHeight ||\n    left >= window.innerWidth\n  ) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center',\n    });\n  }\n}\n", "import type {ClientRect} from '../../types/rect';\nimport {\n  getScrollableAncestors,\n  getScrollOffsets,\n  getScrollXOffset,\n  getScrollYOffset,\n} from '../scroll';\n\nconst properties = [\n  ['x', ['left', 'right'], getScrollXOffset],\n  ['y', ['top', 'bottom'], getScrollYOffset],\n] as const;\n\nexport class Rect {\n  constructor(rect: ClientRect, element: Element) {\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n\n    this.rect = {...rect};\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true,\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {enumerable: false});\n  }\n\n  private rect: ClientRect;\n\n  public width: number;\n\n  public height: number;\n\n  // The below properties are set by the `Object.defineProperty` calls in the constructor\n  // @ts-ignore\n  public top: number;\n  // @ts-ignore\n  public bottom: number;\n  // @ts-ignore\n  public right: number;\n  // @ts-ignore\n  public left: number;\n}\n", "export class Listeners {\n  private listeners: [\n    string,\n    EventListenerOrEventListenerObject,\n    AddEventListenerOptions | boolean | undefined\n  ][] = [];\n\n  constructor(private target: EventTarget | null) {}\n\n  public add<T extends Event>(\n    eventName: string,\n    handler: (event: T) => void,\n    options?: AddEventListenerOptions | boolean\n  ) {\n    this.target?.addEventListener(eventName, handler as EventListener, options);\n    this.listeners.push([eventName, handler as EventListener, options]);\n  }\n\n  public removeAll = () => {\n    this.listeners.forEach((listener) =>\n      this.target?.removeEventListener(...listener)\n    );\n  };\n}\n", "import {getOwnerDocument, getWindow} from '@dnd-kit/utilities';\n\nexport function getEventListenerTarget(\n  target: EventTarget | null\n): EventTarget | Document {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n\n  const {EventTarget} = getWindow(target);\n\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n", "import type {Coordinates, DistanceMeasurement} from '../../types';\n\nexport function hasExceededDistance(\n  delta: Coordinates,\n  measurement: DistanceMeasurement\n): boolean {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n", "export enum EventName {\n  Click = 'click',\n  DragStart = 'dragstart',\n  Keydown = 'keydown',\n  ContextMenu = 'contextmenu',\n  Resize = 'resize',\n  SelectionChange = 'selectionchange',\n  VisibilityChange = 'visibilitychange',\n}\n\nexport function preventDefault(event: Event) {\n  event.preventDefault();\n}\n\nexport function stopPropagation(event: Event) {\n  event.stopPropagation();\n}\n", "import type {Coordinates, UniqueIdentifier} from '../../types';\nimport type {SensorContext} from '../types';\n\nexport enum KeyboardCode {\n  Space = 'Space',\n  Down = 'ArrowDown',\n  Right = 'ArrowRight',\n  Left = 'ArrowLeft',\n  Up = 'ArrowUp',\n  Esc = 'Escape',\n  Enter = 'Enter',\n}\n\nexport type KeyboardCodes = {\n  start: KeyboardEvent['code'][];\n  cancel: KeyboardEvent['code'][];\n  end: KeyboardEvent['code'][];\n};\n\nexport type KeyboardCoordinateGetter = (\n  event: KeyboardEvent,\n  args: {\n    active: UniqueIdentifier;\n    currentCoordinates: Coordinates;\n    context: SensorContext;\n  }\n) => Coordinates | void;\n", "import {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\n\nexport const defaultKeyboardCodes: KeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter],\n};\n\nexport const defaultKeyboardCoordinateGetter: KeyboardCoordinateGetter = (\n  event,\n  {currentCoordinates}\n) => {\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x + 25,\n      };\n    case KeyboardCode.Left:\n      return {\n        ...currentCoordinates,\n        x: currentCoordinates.x - 25,\n      };\n    case KeyboardCode.Down:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y + 25,\n      };\n    case KeyboardCode.Up:\n      return {\n        ...currentCoordinates,\n        y: currentCoordinates.y - 25,\n      };\n  }\n\n  return undefined;\n};\n", "import {\n  add as getAdjustedCoordinates,\n  subtract as getCoordinates<PERSON><PERSON><PERSON>,\n  getOwnerDocument,\n  getWindow,\n  isKeyboardEvent,\n} from '@dnd-kit/utilities';\n\nimport type {Coordinates} from '../../types';\nimport {\n  defaultCoordinates,\n  getScrollPosition,\n  getScrollElementRect,\n} from '../../utilities';\nimport {scrollIntoViewIfNeeded} from '../../utilities/scroll';\nimport {EventName} from '../events';\nimport {Listeners} from '../utilities';\nimport type {\n  Activators,\n  SensorInstance,\n  SensorProps,\n  SensorOptions,\n} from '../types';\n\nimport {KeyboardCoordinateGetter, KeyboardCode, KeyboardCodes} from './types';\nimport {\n  defaultKeyboardCodes,\n  defaultKeyboardCoordinateGetter,\n} from './defaults';\n\nexport interface KeyboardSensorOptions extends SensorOptions {\n  keyboardCodes?: KeyboardCodes;\n  coordinateGetter?: KeyboardCoordinateGetter;\n  scrollBehavior?: ScrollBehavior;\n  onActivation?({event}: {event: KeyboardEvent}): void;\n}\n\nexport type KeyboardSensorProps = SensorProps<KeyboardSensorOptions>;\n\nexport class KeyboardSensor implements SensorInstance {\n  public autoScrollEnabled = false;\n  private referenceCoordinates: Coordinates | undefined;\n  private listeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(private props: KeyboardSensorProps) {\n    const {\n      event: {target},\n    } = props;\n\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    this.handleStart();\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  private handleStart() {\n    const {activeNode, onStart} = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  private handleKeyDown(event: Event) {\n    if (isKeyboardEvent(event)) {\n      const {active, context, options} = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth',\n      } = options;\n      const {code} = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {collisionRect} = context.current;\n      const currentCoordinates = collisionRect\n        ? {x: collisionRect.left, y: collisionRect.top}\n        : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates,\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = getCoordinatesDelta(\n          newCoordinates,\n          currentCoordinates\n        );\n        const scrollDelta = {\n          x: 0,\n          y: 0,\n        };\n        const {scrollableAncestors} = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {isTop, isRight, isLeft, isBottom, maxScroll, minScroll} =\n            getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n\n          const clampedCoordinates = {\n            x: Math.min(\n              direction === KeyboardCode.Right\n                ? scrollElementRect.right - scrollElementRect.width / 2\n                : scrollElementRect.right,\n              Math.max(\n                direction === KeyboardCode.Right\n                  ? scrollElementRect.left\n                  : scrollElementRect.left + scrollElementRect.width / 2,\n                newCoordinates.x\n              )\n            ),\n            y: Math.min(\n              direction === KeyboardCode.Down\n                ? scrollElementRect.bottom - scrollElementRect.height / 2\n                : scrollElementRect.bottom,\n              Math.max(\n                direction === KeyboardCode.Down\n                  ? scrollElementRect.top\n                  : scrollElementRect.top + scrollElementRect.height / 2,\n                newCoordinates.y\n              )\n            ),\n          };\n\n          const canScrollX =\n            (direction === KeyboardCode.Right && !isRight) ||\n            (direction === KeyboardCode.Left && !isLeft);\n          const canScrollY =\n            (direction === KeyboardCode.Down && !isBottom) ||\n            (direction === KeyboardCode.Up && !isTop);\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates =\n              scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Right &&\n                newScrollCoordinates <= maxScroll.x) ||\n              (direction === KeyboardCode.Left &&\n                newScrollCoordinates >= minScroll.x);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x =\n                direction === KeyboardCode.Right\n                  ? scrollContainer.scrollLeft - maxScroll.x\n                  : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior,\n              });\n            }\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates =\n              scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates =\n              (direction === KeyboardCode.Down &&\n                newScrollCoordinates <= maxScroll.y) ||\n              (direction === KeyboardCode.Up &&\n                newScrollCoordinates >= minScroll.y);\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior,\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y =\n                direction === KeyboardCode.Down\n                  ? scrollContainer.scrollTop - maxScroll.y\n                  : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior,\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(\n          event,\n          getAdjustedCoordinates(\n            getCoordinatesDelta(newCoordinates, this.referenceCoordinates),\n            scrollDelta\n          )\n        );\n      }\n    }\n  }\n\n  private handleMove(event: Event, coordinates: Coordinates) {\n    const {onMove} = this.props;\n\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  private handleEnd(event: Event) {\n    const {onEnd} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel(event: Event) {\n    const {onCancel} = this.props;\n\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n  static activators: Activators<KeyboardSensorOptions> = [\n    {\n      eventName: 'onKeyDown' as const,\n      handler: (\n        event: React.KeyboardEvent,\n        {keyboardCodes = defaultKeyboardCodes, onActivation},\n        {active}\n      ) => {\n        const {code} = event.nativeEvent;\n\n        if (keyboardCodes.start.includes(code)) {\n          const activator = active.activatorNode.current;\n\n          if (activator && event.target !== activator) {\n            return false;\n          }\n\n          event.preventDefault();\n\n          onActivation?.({event: event.nativeEvent});\n\n          return true;\n        }\n\n        return false;\n      },\n    },\n  ];\n}\n", "import {\n  subtract as getCoordina<PERSON><PERSON><PERSON><PERSON>,\n  getEventCoordinates,\n  getOwnerDocument,\n  getWindow,\n} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\nimport {\n  getEventListenerTarget,\n  hasExceededDistance,\n  Listeners,\n} from '../utilities';\nimport {EventName, preventDefault, stopPropagation} from '../events';\nimport {KeyboardCode} from '../keyboard';\nimport type {SensorInstance, SensorProps, SensorOptions} from '../types';\nimport type {Coordinates, DistanceMeasurement} from '../../types';\n\ninterface DistanceConstraint {\n  distance: DistanceMeasurement;\n  tolerance?: DistanceMeasurement;\n}\n\ninterface DelayConstraint {\n  delay: number;\n  tolerance: DistanceMeasurement;\n}\n\ninterface EventDescriptor {\n  name: keyof DocumentEventMap;\n  passive?: boolean;\n}\n\nexport interface PointerEventHandlers {\n  move: EventDescriptor;\n  end: EventDescriptor;\n}\n\nexport type PointerActivationConstraint = DistanceConstraint | DelayConstraint;\n\nfunction isDistanceConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DistanceConstraint {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(\n  constraint: PointerActivationConstraint\n): constraint is DelayConstraint {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nexport interface AbstractPointerSensorOptions extends SensorOptions {\n  activationConstraint?: PointerActivationConstraint;\n  onActivation?({event}: {event: Event}): void;\n}\n\nexport type AbstractPointerSensorProps = SensorProps<AbstractPointerSensorOptions>;\n\nexport class AbstractPointerSensor implements SensorInstance {\n  public autoScrollEnabled = true;\n  private document: Document;\n  private activated: boolean = false;\n  private initialCoordinates: Coordinates;\n  private timeoutId: NodeJS.Timeout | null = null;\n  private listeners: Listeners;\n  private documentListeners: Listeners;\n  private windowListeners: Listeners;\n\n  constructor(\n    private props: AbstractPointerSensorProps,\n    private events: PointerEventHandlers,\n    listenerTarget = getEventListenerTarget(props.event.target)\n  ) {\n    const {event} = props;\n    const {target} = event;\n\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n\n    this.attach();\n  }\n\n  private attach() {\n    const {\n      events,\n      props: {\n        options: {activationConstraint},\n      },\n    } = this;\n\n    this.listeners.add(events.move.name, this.handleMove, {passive: false});\n    this.listeners.add(events.end.name, this.handleEnd);\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        return;\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(\n          this.handleStart,\n          activationConstraint.delay\n        );\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  private detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n\n    // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  private handleStart() {\n    const {initialCoordinates} = this;\n    const {onStart} = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true;\n\n      // Stop propagation of click events once activation constraints are met\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true,\n      });\n\n      // Remove any text selection from the document\n      this.removeTextSelection();\n\n      // Prevent further text selection while dragging\n      this.documentListeners.add(\n        EventName.SelectionChange,\n        this.removeTextSelection\n      );\n\n      onStart(initialCoordinates);\n    }\n  }\n\n  private handleMove(event: Event) {\n    const {activated, initialCoordinates, props} = this;\n    const {\n      onMove,\n      options: {activationConstraint},\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = getEventCoordinates(event) ?? defaultCoordinates;\n    const delta = getCoordinatesDelta(initialCoordinates, coordinates);\n\n    if (!activated && activationConstraint) {\n      // Constraint validation\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        if (\n          activationConstraint.tolerance != null &&\n          hasExceededDistance(delta, activationConstraint.tolerance)\n        ) {\n          return this.handleCancel();\n        }\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n\n        return;\n      }\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  private handleEnd() {\n    const {onEnd} = this.props;\n\n    this.detach();\n    onEnd();\n  }\n\n  private handleCancel() {\n    const {onCancel} = this.props;\n\n    this.detach();\n    onCancel();\n  }\n\n  private handleKeydown(event: KeyboardEvent) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  private removeTextSelection() {\n    this.document.getSelection()?.removeAllRanges();\n  }\n}\n", "import type {PointerEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  AbstractPointerSensorOptions,\n  PointerEventHandlers,\n} from './AbstractPointerSensor';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'pointermove'},\n  end: {name: 'pointerup'},\n};\n\nexport interface PointerSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type PointerSensorProps = SensorProps<PointerSensorOptions>;\n\nexport class PointerSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    const {event} = props;\n    // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n    const listenerTarget = getOwnerDocument(event.target);\n\n    super(props, events, listenerTarget);\n  }\n\n  static activators = [\n    {\n      eventName: 'onPointerDown' as const,\n      handler: (\n        {nativeEvent: event}: PointerEvent,\n        {onActivation}: PointerSensorOptions\n      ) => {\n        if (!event.isPrimary || event.button !== 0) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {MouseEvent} from 'react';\nimport {getOwnerDocument} from '@dnd-kit/utilities';\n\nimport type {SensorProps} from '../types';\nimport {\n  AbstractPointerSensor,\n  PointerEventHandlers,\n  AbstractPointerSensorOptions,\n} from '../pointer';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'mousemove'},\n  end: {name: 'mouseup'},\n};\n\nenum MouseButton {\n  RightClick = 2,\n}\n\nexport interface MouseSensorOptions extends AbstractPointerSensorOptions {}\n\nexport type MouseSensorProps = SensorProps<MouseSensorOptions>;\n\nexport class MouseSensor extends AbstractPointerSensor {\n  constructor(props: MouseSensorProps) {\n    super(props, events, getOwnerDocument(props.event.target));\n  }\n\n  static activators = [\n    {\n      eventName: 'onMouseDown' as const,\n      handler: (\n        {nativeEvent: event}: MouseEvent,\n        {onActivation}: MouseSensorOptions\n      ) => {\n        if (event.button === MouseButton.RightClick) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n}\n", "import type {TouchEvent} from 'react';\n\nimport {\n  AbstractPointerSensor,\n  PointerSensorProps,\n  PointerEventHandlers,\n  PointerSensorOptions,\n} from '../pointer';\nimport type {SensorProps} from '../types';\n\nconst events: PointerEventHandlers = {\n  move: {name: 'touchmove'},\n  end: {name: 'touchend'},\n};\n\nexport interface TouchSensorOptions extends PointerSensorOptions {}\n\nexport type TouchSensorProps = SensorProps<TouchSensorOptions>;\n\nexport class TouchSensor extends AbstractPointerSensor {\n  constructor(props: PointerSensorProps) {\n    super(props, events);\n  }\n\n  static activators = [\n    {\n      eventName: 'onTouchStart' as const,\n      handler: (\n        {nativeEvent: event}: TouchEvent,\n        {onActivation}: TouchSensorOptions\n      ) => {\n        const {touches} = event;\n\n        if (touches.length > 1) {\n          return false;\n        }\n\n        onActivation?.({event});\n\n        return true;\n      },\n    },\n  ];\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events.move.name, noop, {\n      capture: false,\n      passive: false,\n    });\n\n    return function teardown() {\n      window.removeEventListener(events.move.name, noop);\n    };\n\n    // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n    function noop() {}\n  }\n}\n", "import {useCallback, useEffect, useMemo, useRef} from 'react';\nimport {useInterval, useLazyMemo, usePrevious} from '@dnd-kit/utilities';\n\nimport {getScrollDirectionAndSpeed} from '../../utilities';\nimport {Direction} from '../../types';\nimport type {Coordinates, ClientRect} from '../../types';\n\nexport type ScrollAncestorSortingFn = (ancestors: Element[]) => Element[];\n\nexport enum AutoScrollActivator {\n  Pointer,\n  DraggableRect,\n}\n\nexport interface Options {\n  acceleration?: number;\n  activator?: AutoScrollActivator;\n  canScroll?: CanScroll;\n  enabled?: boolean;\n  interval?: number;\n  layoutShiftCompensation?:\n    | boolean\n    | {\n        x: boolean;\n        y: boolean;\n      };\n  order?: TraversalOrder;\n  threshold?: {\n    x: number;\n    y: number;\n  };\n}\n\ninterface Arguments extends Options {\n  draggingRect: ClientRect | null;\n  enabled: boolean;\n  pointerCoordinates: Coordinates | null;\n  scrollableAncestors: Element[];\n  scrollableAncestorRects: ClientRect[];\n  delta: Coordinates;\n}\n\nexport type CanScroll = (element: Element) => boolean;\n\nexport enum TraversalOrder {\n  TreeOrder,\n  ReversedTreeOrder,\n}\n\ninterface ScrollDirection {\n  x: 0 | Direction;\n  y: 0 | Direction;\n}\n\nexport function useAutoScroller({\n  acceleration,\n  activator = AutoScrollActivator.Pointer,\n  canScroll,\n  draggingRect,\n  enabled,\n  interval = 5,\n  order = TraversalOrder.TreeOrder,\n  pointerCoordinates,\n  scrollableAncestors,\n  scrollableAncestorRects,\n  delta,\n  threshold,\n}: Arguments) {\n  const scrollIntent = useScrollIntent({delta, disabled: !enabled});\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef<Coordinates>({x: 0, y: 0});\n  const scrollDirection = useRef<ScrollDirection>({x: 0, y: 0});\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates\n          ? {\n              top: pointerCoordinates.y,\n              bottom: pointerCoordinates.y,\n              left: pointerCoordinates.x,\n              right: pointerCoordinates.x,\n            }\n          : null;\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef<Element | null>(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(\n    () =>\n      order === TraversalOrder.TreeOrder\n        ? [...scrollableAncestors].reverse()\n        : scrollableAncestors,\n    [order, scrollableAncestors]\n  );\n\n  useEffect(\n    () => {\n      if (!enabled || !scrollableAncestors.length || !rect) {\n        clearAutoScrollInterval();\n        return;\n      }\n\n      for (const scrollContainer of sortedScrollableAncestors) {\n        if (canScroll?.(scrollContainer) === false) {\n          continue;\n        }\n\n        const index = scrollableAncestors.indexOf(scrollContainer);\n        const scrollContainerRect = scrollableAncestorRects[index];\n\n        if (!scrollContainerRect) {\n          continue;\n        }\n\n        const {direction, speed} = getScrollDirectionAndSpeed(\n          scrollContainer,\n          scrollContainerRect,\n          rect,\n          acceleration,\n          threshold\n        );\n\n        for (const axis of ['x', 'y'] as const) {\n          if (!scrollIntent[axis][direction[axis] as Direction]) {\n            speed[axis] = 0;\n            direction[axis] = 0;\n          }\n        }\n\n        if (speed.x > 0 || speed.y > 0) {\n          clearAutoScrollInterval();\n\n          scrollContainerRef.current = scrollContainer;\n          setAutoScrollInterval(autoScroll, interval);\n\n          scrollSpeed.current = speed;\n          scrollDirection.current = direction;\n\n          return;\n        }\n      }\n\n      scrollSpeed.current = {x: 0, y: 0};\n      scrollDirection.current = {x: 0, y: 0};\n      clearAutoScrollInterval();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      acceleration,\n      autoScroll,\n      canScroll,\n      clearAutoScrollInterval,\n      enabled,\n      interval,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(rect),\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(scrollIntent),\n      setAutoScrollInterval,\n      scrollableAncestors,\n      sortedScrollableAncestors,\n      scrollableAncestorRects,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(threshold),\n    ]\n  );\n}\n\ninterface ScrollIntent {\n  x: Record<Direction, boolean>;\n  y: Record<Direction, boolean>;\n}\n\nconst defaultScrollIntent: ScrollIntent = {\n  x: {[Direction.Backward]: false, [Direction.Forward]: false},\n  y: {[Direction.Backward]: false, [Direction.Forward]: false},\n};\n\nfunction useScrollIntent({\n  delta,\n  disabled,\n}: {\n  delta: Coordinates;\n  disabled: boolean;\n}): ScrollIntent {\n  const previousDelta = usePrevious(delta);\n\n  return useLazyMemo<ScrollIntent>(\n    (previousIntent) => {\n      if (disabled || !previousDelta || !previousIntent) {\n        // Reset scroll intent tracking when auto-scrolling is disabled\n        return defaultScrollIntent;\n      }\n\n      const direction = {\n        x: Math.sign(delta.x - previousDelta.x),\n        y: Math.sign(delta.y - previousDelta.y),\n      };\n\n      // Keep track of the user intent to scroll in each direction for both axis\n      return {\n        x: {\n          [Direction.Backward]:\n            previousIntent.x[Direction.Backward] || direction.x === -1,\n          [Direction.Forward]:\n            previousIntent.x[Direction.Forward] || direction.x === 1,\n        },\n        y: {\n          [Direction.Backward]:\n            previousIntent.y[Direction.Backward] || direction.y === -1,\n          [Direction.Forward]:\n            previousIntent.y[Direction.Forward] || direction.y === 1,\n        },\n      };\n    },\n    [disabled, delta, previousDelta]\n  );\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\nimport type {DraggableNode, DraggableNodes} from '../../store';\nimport type {UniqueIdentifier} from '../../types';\n\nexport function useCachedNode(\n  draggableNodes: DraggableNodes,\n  id: UniqueIdentifier | null\n): DraggableNode['node']['current'] {\n  const draggableNode = id !== null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n\n  return useLazyMemo(\n    (cachedNode) => {\n      if (id === null) {\n        return null;\n      }\n\n      // In some cases, the draggable node can unmount while dragging\n      // This is the case for virtualized lists. In those situations,\n      // we fall back to the last known value for that node.\n      return node ?? cachedNode ?? null;\n    },\n    [node, id]\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SensorActivatorFunction, SensorDescriptor} from '../../sensors';\nimport type {\n  SyntheticListener,\n  SyntheticListeners,\n} from './useSyntheticListeners';\n\nexport function useCombineActivators(\n  sensors: SensorDescriptor<any>[],\n  getSyntheticHandler: (\n    handler: SensorActivatorFunction<any>,\n    sensor: SensorDescriptor<any>\n  ) => SyntheticListener['handler']\n): SyntheticListeners {\n  return useMemo(\n    () =>\n      sensors.reduce<SyntheticListeners>((accumulator, sensor) => {\n        const {sensor: Sensor} = sensor;\n\n        const sensorActivators = Sensor.activators.map((activator) => ({\n          eventName: activator.eventName,\n          handler: getSyntheticHandler(activator.handler, sensor),\n        }));\n\n        return [...accumulator, ...sensorActivators];\n      }, []),\n    [sensors, getSyntheticHandler]\n  );\n}\n", "import {useCallback, useEffect, useRef, useState} from 'react';\nimport {useLatestValue, useLazyMemo} from '@dnd-kit/utilities';\n\nimport {Rect} from '../../utilities/rect';\nimport type {DroppableContainer, RectMap} from '../../store/types';\nimport type {ClientRect, UniqueIdentifier} from '../../types';\n\ninterface Arguments {\n  dragging: boolean;\n  dependencies: any[];\n  config: DroppableMeasuring;\n}\n\nexport enum MeasuringStrategy {\n  Always,\n  BeforeDragging,\n  WhileDragging,\n}\n\nexport enum MeasuringFrequency {\n  Optimized = 'optimized',\n}\n\ntype MeasuringFunction = (element: HTMLElement) => ClientRect;\n\nexport interface DroppableMeasuring {\n  measure: MeasuringFunction;\n  strategy: MeasuringStrategy;\n  frequency: MeasuringFrequency | number;\n}\n\nconst defaultValue: RectMap = new Map();\n\nexport function useDroppableMeasuring(\n  containers: DroppableContainer[],\n  {dragging, dependencies, config}: Arguments\n) {\n  const [queue, setQueue] = useState<UniqueIdentifier[] | null>(null);\n  const {frequency, measure, strategy} = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(\n    (ids: UniqueIdentifier[] = []) => {\n      if (disabledRef.current) {\n        return;\n      }\n\n      setQueue((value) => {\n        if (value === null) {\n          return ids;\n        }\n\n        return value.concat(ids.filter((id) => !value.includes(id)));\n      });\n    },\n    [disabledRef]\n  );\n  const timeoutId = useRef<NodeJS.Timeout | null>(null);\n  const droppableRects = useLazyMemo<RectMap>(\n    (previousValue) => {\n      if (disabled && !dragging) {\n        return defaultValue;\n      }\n\n      if (\n        !previousValue ||\n        previousValue === defaultValue ||\n        containersRef.current !== containers ||\n        queue != null\n      ) {\n        const map: RectMap = new Map();\n\n        for (let container of containers) {\n          if (!container) {\n            continue;\n          }\n\n          if (\n            queue &&\n            queue.length > 0 &&\n            !queue.includes(container.id) &&\n            container.rect.current\n          ) {\n            // This container does not need to be re-measured\n            map.set(container.id, container.rect.current);\n            continue;\n          }\n\n          const node = container.node.current;\n          const rect = node ? new Rect(measure(node), node) : null;\n\n          container.rect.current = rect;\n\n          if (rect) {\n            map.set(container.id, rect);\n          }\n        }\n\n        return map;\n      }\n\n      return previousValue;\n    },\n    [containers, queue, dragging, disabled, measure]\n  );\n\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n\n  useEffect(\n    () => {\n      if (disabled) {\n        return;\n      }\n\n      measureDroppableContainers();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [dragging, disabled]\n  );\n\n  useEffect(\n    () => {\n      if (queue && queue.length > 0) {\n        setQueue(null);\n      }\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(queue)]\n  );\n\n  useEffect(\n    () => {\n      if (\n        disabled ||\n        typeof frequency !== 'number' ||\n        timeoutId.current !== null\n      ) {\n        return;\n      }\n\n      timeoutId.current = setTimeout(() => {\n        measureDroppableContainers();\n        timeoutId.current = null;\n      }, frequency);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [frequency, disabled, measureDroppableContainers, ...dependencies]\n  );\n\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null,\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n      default:\n        return !dragging;\n    }\n  }\n}\n", "import {useLazyMemo} from '@dnd-kit/utilities';\n\ntype AnyFunction = (...args: any) => any;\n\nexport function useInitialValue<\n  T,\n  U extends AnyFunction | undefined = undefined\n>(\n  value: T | null,\n  computeFn?: U\n): U extends AnyFunction ? ReturnType<U> | null : T | null {\n  return useLazyMemo(\n    (previousValue) => {\n      if (!value) {\n        return null;\n      }\n\n      if (previousValue) {\n        return previousValue;\n      }\n\n      return typeof computeFn === 'function' ? computeFn(value) : value;\n    },\n    [computeFn, value]\n  );\n}\n", "import type {ClientRect} from '../../types';\nimport {useInitialValue} from './useInitialValue';\n\nexport function useInitialRect(\n  node: HTMLElement | null,\n  measure: (node: HTMLElement) => ClientRect\n) {\n  return useInitialValue(node, measure);\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: MutationCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new MutationObserver instance.\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useMutationObserver({callback, disabled}: Arguments) {\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (\n      disabled ||\n      typeof window === 'undefined' ||\n      typeof window.MutationObserver === 'undefined'\n    ) {\n      return undefined;\n    }\n\n    const {MutationObserver} = window;\n\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n\n  useEffect(() => {\n    return () => mutationObserver?.disconnect();\n  }, [mutationObserver]);\n\n  return mutationObserver;\n}\n", "import {useEffect, useMemo} from 'react';\nimport {useEvent} from '@dnd-kit/utilities';\n\ninterface Arguments {\n  callback: ResizeObserverCallback;\n  disabled?: boolean;\n}\n\n/**\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\n */\nexport function useResizeObserver({callback, disabled}: Arguments) {\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(\n    () => {\n      if (\n        disabled ||\n        typeof window === 'undefined' ||\n        typeof window.ResizeObserver === 'undefined'\n      ) {\n        return undefined;\n      }\n\n      const {ResizeObserver} = window;\n\n      return new ResizeObserver(handleResize);\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [disabled]\n  );\n\n  useEffect(() => {\n    return () => resizeObserver?.disconnect();\n  }, [resizeObserver]);\n\n  return resizeObserver;\n}\n", "import {useReducer} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {getClientRect, Rect} from '../../utilities';\n\nimport {useMutationObserver} from './useMutationObserver';\nimport {useResizeObserver} from './useResizeObserver';\n\nfunction defaultMeasure(element: HTMLElement) {\n  return new Rect(getClientRect(element), element);\n}\n\nexport function useRect(\n  element: HTMLElement | null,\n  measure: (element: HTMLElement) => ClientRect = defaultMeasure,\n  fallbackRect?: ClientRect | null\n) {\n  const [rect, measureRect] = useReducer(reducer, null);\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {type, target} = record;\n\n        if (\n          type === 'childList' &&\n          target instanceof HTMLElement &&\n          target.contains(element)\n        ) {\n          measureRect();\n          break;\n        }\n      }\n    },\n  });\n  const resizeObserver = useResizeObserver({callback: measureRect});\n\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver?.observe(element);\n      mutationObserver?.observe(document.body, {\n        childList: true,\n        subtree: true,\n      });\n    } else {\n      resizeObserver?.disconnect();\n      mutationObserver?.disconnect();\n    }\n  }, [element]);\n\n  return rect;\n\n  function reducer(currentRect: ClientRect | null) {\n    if (!element) {\n      return null;\n    }\n\n    if (element.isConnected === false) {\n      // Fall back to last rect we measured if the element is\n      // no longer connected to the DOM.\n      return currentRect ?? fallbackRect ?? null;\n    }\n\n    const newRect = measure(element);\n\n    if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n      return currentRect;\n    }\n\n    return newRect;\n  }\n}\n", "import type {ClientRect} from '../../types';\nimport {getRectDelta} from '../../utilities';\n\nimport {useInitialValue} from './useInitialValue';\n\nexport function useRectDelta(rect: ClientRect | null) {\n  const initialRect = useInitialValue(rect);\n\n  return getRectDelta(rect, initialRect);\n}\n", "import {useEffect, useRef} from 'react';\nimport {useLazyMemo} from '@dnd-kit/utilities';\n\nimport {getScrollableAncestors} from '../../utilities';\n\nconst defaultValue: Element[] = [];\n\nexport function useScrollableAncestors(node: HTMLElement | null) {\n  const previousNode = useRef(node);\n\n  const ancestors = useLazyMemo<Element[]>(\n    (previousValue) => {\n      if (!node) {\n        return defaultValue;\n      }\n\n      if (\n        previousValue &&\n        previousValue !== defaultValue &&\n        node &&\n        previousNode.current &&\n        node.parentNode === previousNode.current.parentNode\n      ) {\n        return previousValue;\n      }\n\n      return getScrollableAncestors(node);\n    },\n    [node]\n  );\n\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n\n  return ancestors;\n}\n", "import {useState, useCallback, useMemo, useRef, useEffect} from 'react';\nimport {add} from '@dnd-kit/utilities';\n\nimport {\n  defaultCoordinates,\n  getScrollableElement,\n  getScrollCoordinates,\n  getScrollOffsets,\n} from '../../utilities';\nimport type {Coordinates} from '../../types';\n\ntype ScrollCoordinates = Map<HTMLElement | Window, Coordinates>;\n\nexport function useScrollOffsets(elements: Element[]): Coordinates {\n  const [\n    scrollCoordinates,\n    setScrollCoordinates,\n  ] = useState<ScrollCoordinates | null>(null);\n  const prevElements = useRef(elements);\n\n  // To-do: Throttle the handleScroll callback\n  const handleScroll = useCallback((event: Event) => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates((scrollCoordinates) => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(\n        scrollingElement,\n        getScrollCoordinates(scrollingElement)\n      );\n\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n\n      const entries = elements\n        .map((element) => {\n          const scrollableElement = getScrollableElement(element);\n\n          if (scrollableElement) {\n            scrollableElement.addEventListener('scroll', handleScroll, {\n              passive: true,\n            });\n\n            return [\n              scrollableElement,\n              getScrollCoordinates(scrollableElement),\n            ] as const;\n          }\n\n          return null;\n        })\n        .filter(\n          (\n            entry\n          ): entry is [\n            HTMLElement | (Window & typeof globalThis),\n            Coordinates\n          ] => entry != null\n        );\n\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements: Element[]) {\n      elements.forEach((element) => {\n        const scrollableElement = getScrollableElement(element);\n\n        scrollableElement?.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates\n        ? Array.from(scrollCoordinates.values()).reduce(\n            (acc, coordinates) => add(acc, coordinates),\n            defaultCoordinates\n          )\n        : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n", "import {useEffect, useRef} from 'react';\nimport {Coordinates, subtract} from '@dnd-kit/utilities';\n\nimport {defaultCoordinates} from '../../utilities';\n\nexport function useScrollOffsetsDelta(\n  scrollOffsets: Coordinates,\n  dependencies: any[] = []\n) {\n  const initialScrollOffsets = useRef<Coordinates | null>(null);\n\n  useEffect(\n    () => {\n      initialScrollOffsets.current = null;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    dependencies\n  );\n\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n\n  return initialScrollOffsets.current\n    ? subtract(scrollOffsets, initialScrollOffsets.current)\n    : defaultCoordinates;\n}\n", "import {useEffect} from 'react';\nimport {canUseDOM} from '@dnd-kit/utilities';\n\nimport type {SensorDescriptor} from '../../sensors';\n\nexport function useSensorSetup(sensors: SensorDescriptor<any>[]) {\n  useEffect(\n    () => {\n      if (!canUseDOM) {\n        return;\n      }\n\n      const teardownFns = sensors.map(({sensor}) => sensor.setup?.());\n\n      return () => {\n        for (const teardown of teardownFns) {\n          teardown?.();\n        }\n      };\n    },\n    // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    sensors.map(({sensor}) => sensor)\n  );\n}\n", "import {useMemo} from 'react';\n\nimport type {SyntheticEventName, UniqueIdentifier} from '../../types';\n\nexport type SyntheticListener = {\n  eventName: SyntheticEventName;\n  handler: (event: React.SyntheticEvent, id: UniqueIdentifier) => void;\n};\n\nexport type SyntheticListeners = SyntheticListener[];\n\nexport type SyntheticListenerMap = Record<string, Function>;\n\nexport function useSyntheticListeners(\n  listeners: SyntheticListeners,\n  id: UniqueIdentifier\n): SyntheticListenerMap {\n  return useMemo(() => {\n    return listeners.reduce<SyntheticListenerMap>(\n      (acc, {eventName, handler}) => {\n        acc[eventName] = (event: React.SyntheticEvent) => {\n          handler(event, id);\n        };\n\n        return acc;\n      },\n      {} as SyntheticListenerMap\n    );\n  }, [listeners, id]);\n}\n", "import {useMemo} from 'react';\n\nimport {getWindowClientRect} from '../../utilities/rect';\n\nexport function useWindowRect(element: typeof window | null) {\n  return useMemo(() => (element ? getWindowClientRect(element) : null), [\n    element,\n  ]);\n}\n", "import {useReducer} from 'react';\nimport {getWindow, useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport type {ClientRect} from '../../types';\nimport {Rect, getClientRect} from '../../utilities/rect';\nimport {isDocumentScrollingElement} from '../../utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {useWindowRect} from './useWindowRect';\n\nconst defaultValue: Rect[] = [];\n\nexport function useRects(\n  elements: Element[],\n  measure: (element: Element) => ClientRect = getClientRect\n): ClientRect[] {\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(\n    firstElement ? getWindow(firstElement) : null\n  );\n  const [rects, measureRects] = useReducer(reducer, defaultValue);\n  const resizeObserver = useResizeObserver({callback: measureRects});\n\n  if (elements.length > 0 && rects === defaultValue) {\n    measureRects();\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (elements.length) {\n      elements.forEach((element) => resizeObserver?.observe(element));\n    } else {\n      resizeObserver?.disconnect();\n      measureRects();\n    }\n  }, [elements]);\n\n  return rects;\n\n  function reducer() {\n    if (!elements.length) {\n      return defaultValue;\n    }\n\n    return elements.map((element) =>\n      isDocumentScrollingElement(element)\n        ? (windowRect as ClientRect)\n        : new Rect(measure(element), element)\n    );\n  }\n}\n", "import {isHTMLElement} from '@dnd-kit/utilities';\n\nexport function getMeasurableNode(\n  node: HTMLElement | undefined | null\n): HTMLElement | null {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n  const firstChild = node.children[0];\n\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n", "import {useMemo, useCallback, useState} from 'react';\nimport {isHTMLElement, useNodeRef} from '@dnd-kit/utilities';\n\nimport {useResizeObserver} from './useResizeObserver';\nimport {getMeasurableNode} from '../../utilities/nodes';\nimport type {PublicContextDescriptor} from '../../store';\nimport type {ClientRect} from '../../types';\n\ninterface Arguments {\n  measure(element: HTMLElement): ClientRect;\n}\n\nexport function useDragOverlayMeasuring({\n  measure,\n}: Arguments): PublicContextDescriptor['dragOverlay'] {\n  const [rect, setRect] = useState<ClientRect | null>(null);\n  const handleResize = useCallback(\n    (entries: ResizeObserverEntry[]) => {\n      for (const {target} of entries) {\n        if (isHTMLElement(target)) {\n          setRect((rect) => {\n            const newRect = measure(target);\n\n            return rect\n              ? {...rect, width: newRect.width, height: newRect.height}\n              : newRect;\n          });\n          break;\n        }\n      }\n    },\n    [measure]\n  );\n  const resizeObserver = useResizeObserver({callback: handleResize});\n  const handleNodeChange = useCallback(\n    (element) => {\n      const node = getMeasurableNode(element);\n\n      resizeObserver?.disconnect();\n\n      if (node) {\n        resizeObserver?.observe(node);\n      }\n\n      setRect(node ? measure(node) : null);\n    },\n    [measure, resizeObserver]\n  );\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n\n  return useMemo(\n    () => ({\n      nodeRef,\n      rect,\n      setRef,\n    }),\n    [rect, nodeRef, setRef]\n  );\n}\n", "import type {DeepRequired} from '@dnd-kit/utilities';\n\nimport type {DataRef} from '../../store/types';\nimport {KeyboardSensor, PointerSensor} from '../../sensors';\nimport {MeasuringStrategy, MeasuringFrequency} from '../../hooks/utilities';\nimport {\n  getClientRect,\n  getTransformAgnosticClientRect,\n} from '../../utilities/rect';\n\nimport type {MeasuringConfiguration} from './types';\n\nexport const defaultSensors = [\n  {sensor: PointerSensor, options: {}},\n  {sensor: KeyboardSensor, options: {}},\n];\n\nexport const defaultData: DataRef = {current: {}};\n\nexport const defaultMeasuringConfiguration: DeepRequired<MeasuringConfiguration> = {\n  draggable: {\n    measure: getTransformAgnosticClientRect,\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized,\n  },\n  dragOverlay: {\n    measure: getClientRect,\n  },\n};\n", "import type {UniqueIdentifier} from '../types';\nimport type {DroppableContainer} from './types';\n\ntype Identifier = UniqueIdentifier | null | undefined;\n\nexport class DroppableContainersMap extends Map<\n  UniqueIdentifier,\n  DroppableContainer\n> {\n  get(id: Identifier) {\n    return id != null ? super.get(id) ?? undefined : undefined;\n  }\n\n  toArray(): DroppableContainer[] {\n    return Array.from(this.values());\n  }\n\n  getEnabled(): DroppableContainer[] {\n    return this.toArray().filter(({disabled}) => !disabled);\n  }\n\n  getNodeFor(id: Identifier) {\n    return this.get(id)?.node.current ?? undefined;\n  }\n}\n", "import {createContext} from 'react';\n\nimport {noop} from '../utilities/other';\nimport {defaultMeasuringConfiguration} from '../components/DndContext/defaults';\nimport {DroppableContainersMap} from './constructors';\nimport type {InternalContextDescriptor, PublicContextDescriptor} from './types';\n\nexport const defaultPublicContext: PublicContextDescriptor = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: new Map(),\n  droppableRects: new Map(),\n  droppableContainers: new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null,\n    },\n    rect: null,\n    setRef: noop,\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false,\n};\n\nexport const defaultInternalContext: InternalContextDescriptor = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: '',\n  },\n  dispatch: noop,\n  draggableNodes: new Map(),\n  over: null,\n  measureDroppableContainers: noop,\n};\n\nexport const InternalContext = createContext<InternalContextDescriptor>(\n  defaultInternalContext\n);\n\nexport const PublicContext = createContext<PublicContextDescriptor>(\n  defaultPublicContext\n);\n", "import {Action, Actions} from './actions';\nimport {DroppableContainersMap} from './constructors';\nimport type {State} from './types';\n\nexport function getInitialState(): State {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {x: 0, y: 0},\n      nodes: new Map(),\n      translate: {x: 0, y: 0},\n    },\n    droppable: {\n      containers: new DroppableContainersMap(),\n    },\n  };\n}\n\nexport function reducer(state: State, action: Actions): State {\n  switch (action.type) {\n    case Action.DragStart:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active,\n        },\n      };\n    case Action.DragMove:\n      if (!state.draggable.active) {\n        return state;\n      }\n\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y,\n          },\n        },\n      };\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return {\n        ...state,\n        draggable: {\n          ...state.draggable,\n          active: null,\n          initialCoordinates: {x: 0, y: 0},\n          translate: {x: 0, y: 0},\n        },\n      };\n\n    case Action.RegisterDroppable: {\n      const {element} = action;\n      const {id} = element;\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, element);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.SetDroppableDisabled: {\n      const {id, key, disabled} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.set(id, {\n        ...element,\n        disabled,\n      });\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    case Action.UnregisterDroppable: {\n      const {id, key} = action;\n      const element = state.droppable.containers.get(id);\n\n      if (!element || key !== element.key) {\n        return state;\n      }\n\n      const containers = new DroppableContainersMap(state.droppable.containers);\n      containers.delete(id);\n\n      return {\n        ...state,\n        droppable: {\n          ...state.droppable,\n          containers,\n        },\n      };\n    }\n\n    default: {\n      return state;\n    }\n  }\n}\n", "import {useContext, useEffect} from 'react';\nimport {\n  findFirstFocusableNode,\n  isKeyboardEvent,\n  usePrevious,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext} from '../../../store';\n\ninterface Props {\n  disabled: boolean;\n}\n\nexport function RestoreFocus({disabled}: Props) {\n  const {active, activatorEvent, draggableNodes} = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active?.id);\n\n  // Restore keyboard focus on the activator node\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {activatorNode, node} = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [\n    activatorEvent,\n    disabled,\n    draggableNodes,\n    previousActiveId,\n    previousActivatorEvent,\n  ]);\n\n  return null;\n}\n", "import type {FirstArgument, Transform} from '@dnd-kit/utilities';\n\nimport type {Modifiers, Modifier} from './types';\n\nexport function applyModifiers(\n  modifiers: Modifiers | undefined,\n  {transform, ...args}: FirstArgument<Modifier>\n): Transform {\n  return modifiers?.length\n    ? modifiers.reduce<Transform>((accumulator, modifier) => {\n        return modifier({\n          transform: accumulator,\n          ...args,\n        });\n      }, transform)\n    : transform;\n}\n", "import {useMemo} from 'react';\nimport type {DeepRequired} from '@dnd-kit/utilities';\n\nimport {defaultMeasuringConfiguration} from '../defaults';\nimport type {MeasuringConfiguration} from '../types';\n\nexport function useMeasuringConfiguration(\n  config: MeasuringConfiguration | undefined\n): DeepRequired<MeasuringConfiguration> {\n  return useMemo(\n    () => ({\n      draggable: {\n        ...defaultMeasuringConfiguration.draggable,\n        ...config?.draggable,\n      },\n      droppable: {\n        ...defaultMeasuringConfiguration.droppable,\n        ...config?.droppable,\n      },\n      dragOverlay: {\n        ...defaultMeasuringConfiguration.dragOverlay,\n        ...config?.dragOverlay,\n      },\n    }),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [config?.draggable, config?.droppable, config?.dragOverlay]\n  );\n}\n", "import {useRef} from 'react';\nimport {useIsomorphicLayoutEffect} from '@dnd-kit/utilities';\n\nimport {getRectDelta} from '../../../utilities/rect';\nimport {getFirstScrollableAncestor} from '../../../utilities/scroll';\nimport type {ClientRect} from '../../../types';\nimport type {DraggableNode} from '../../../store';\nimport type {MeasuringFunction} from '../types';\n\ninterface Options {\n  activeNode: DraggableNode | null | undefined;\n  config: boolean | {x: boolean; y: boolean} | undefined;\n  initialRect: ClientRect | null;\n  measure: MeasuringFunction;\n}\n\nexport function useLayoutShiftScrollCompensation({\n  activeNode,\n  measure,\n  initialRect,\n  config = true,\n}: Options) {\n  const initialized = useRef(false);\n  const {x, y} = typeof config === 'boolean' ? {x: config, y: config} : config;\n\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    }\n\n    // Get the most up to date node ref for the active draggable\n    const node = activeNode?.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    }\n\n    // Only perform layout shift scroll compensation once\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x,\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n", "import React, {\n  memo,\n  createContext,\n  useCallback,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {unstable_batchedUpdates} from 'react-dom';\nimport {\n  add,\n  getEventCoordinates,\n  getWindow,\n  useLatestValue,\n  useIsomorphicLayoutEffect,\n  useUniqueId,\n} from '@dnd-kit/utilities';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {\n  Action,\n  PublicContext,\n  InternalContext,\n  PublicContextDescriptor,\n  InternalContextDescriptor,\n  getInitialState,\n  reducer,\n} from '../../store';\nimport {DndMonitorContext, useDndMonitorProvider} from '../DndMonitor';\nimport {\n  useAutoScroller,\n  useCachedNode,\n  useCombineActivators,\n  useDragOverlayMeasuring,\n  useDroppableMeasuring,\n  useInitialRect,\n  useRect,\n  useRectDelta,\n  useRects,\n  useScrollableAncestors,\n  useScrollOffsets,\n  useScrollOffsetsDelta,\n  useSensorSetup,\n  useWindowRect,\n} from '../../hooks/utilities';\nimport type {AutoScrollOptions, SyntheticListener} from '../../hooks/utilities';\nimport type {\n  Sensor,\n  SensorContext,\n  SensorDescriptor,\n  SensorActivatorFunction,\n  SensorInstance,\n} from '../../sensors';\nimport {\n  adjustScale,\n  CollisionDetection,\n  defaultCoordinates,\n  getAdjustedRect,\n  getFirstCollision,\n  rectIntersection,\n} from '../../utilities';\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport type {Active, Over} from '../../store/types';\nimport type {\n  DragStartEvent,\n  DragCancelEvent,\n  DragEndEvent,\n  DragMoveEvent,\n  DragOverEvent,\n  UniqueIdentifier,\n} from '../../types';\nimport {\n  Accessibility,\n  Announcements,\n  RestoreFocus,\n  ScreenReaderInstructions,\n} from '../Accessibility';\n\nimport {defaultData, defaultSensors} from './defaults';\nimport {\n  useLayoutShiftScrollCompensation,\n  useMeasuringConfiguration,\n} from './hooks';\nimport type {MeasuringConfiguration} from './types';\n\nexport interface Props {\n  id?: string;\n  accessibility?: {\n    announcements?: Announcements;\n    container?: Element;\n    restoreFocus?: boolean;\n    screenReaderInstructions?: ScreenReaderInstructions;\n  };\n  autoScroll?: boolean | AutoScrollOptions;\n  cancelDrop?: CancelDrop;\n  children?: React.ReactNode;\n  collisionDetection?: CollisionDetection;\n  measuring?: MeasuringConfiguration;\n  modifiers?: Modifiers;\n  sensors?: SensorDescriptor<any>[];\n  onDragStart?(event: DragStartEvent): void;\n  onDragMove?(event: DragMoveEvent): void;\n  onDragOver?(event: DragOverEvent): void;\n  onDragEnd?(event: DragEndEvent): void;\n  onDragCancel?(event: DragCancelEvent): void;\n}\n\nexport interface CancelDropArguments extends DragEndEvent {}\n\nexport type CancelDrop = (\n  args: CancelDropArguments\n) => boolean | Promise<boolean>;\n\ninterface DndEvent extends Event {\n  dndKit?: {\n    capturedBy: Sensor<any>;\n  };\n}\n\nexport const ActiveDraggableContext = createContext<Transform>({\n  ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1,\n});\n\nenum Status {\n  Uninitialized,\n  Initializing,\n  Initialized,\n}\n\nexport const DndContext = memo(function DndContext({\n  id,\n  accessibility,\n  autoScroll = true,\n  children,\n  sensors = defaultSensors,\n  collisionDetection = rectIntersection,\n  measuring,\n  modifiers,\n  ...props\n}: Props) {\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] =\n    useDndMonitorProvider();\n  const [status, setStatus] = useState<Status>(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {active: activeId, nodes: draggableNodes, translate},\n    droppable: {containers: droppableContainers},\n  } = state;\n  const node = activeId ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef<Active['rect']['current']>({\n    initial: null,\n    translated: null,\n  });\n  const active = useMemo<Active | null>(\n    () =>\n      activeId != null\n        ? {\n            id: activeId,\n            // It's possible for the active node to unmount while dragging\n            data: node?.data ?? defaultData,\n            rect: activeRects,\n          }\n        : null,\n    [activeId, node]\n  );\n  const activeRef = useRef<UniqueIdentifier | null>(null);\n  const [activeSensor, setActiveSensor] = useState<SensorInstance | null>(null);\n  const [activatorEvent, setActivatorEvent] = useState<Event | null>(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(`DndDescribedBy`, id);\n  const enabledDroppableContainers = useMemo(\n    () => droppableContainers.getEnabled(),\n    [droppableContainers]\n  );\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {droppableRects, measureDroppableContainers, measuringScheduled} =\n    useDroppableMeasuring(enabledDroppableContainers, {\n      dragging: isInitialized,\n      dependencies: [translate.x, translate.y],\n      config: measuringConfiguration.droppable,\n    });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(\n    () => (activatorEvent ? getEventCoordinates(activatorEvent) : null),\n    [activatorEvent]\n  );\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(\n    activeNode,\n    measuringConfiguration.draggable.measure\n  );\n\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure,\n  });\n\n  const activeNodeRect = useRect(\n    activeNode,\n    measuringConfiguration.draggable.measure,\n    initialActiveNodeRect\n  );\n  const containerNodeRect = useRect(\n    activeNode ? activeNode.parentElement : null\n  );\n  const sensorContext = useRef<SensorContext>({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null,\n  });\n  const overNode = droppableContainers.getNodeFor(\n    sensorContext.current.over?.id\n  );\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure,\n  });\n\n  // Use the rect of the drag overlay if it is mounted\n  const draggingNode = dragOverlay.nodeRef.current ?? activeNode;\n  const draggingNodeRect = isInitialized\n    ? dragOverlay.rect ?? activeNodeRect\n    : null;\n  const usesDragOverlay = Boolean(\n    dragOverlay.nodeRef.current && dragOverlay.rect\n  );\n  // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect);\n\n  // Get the window rect of the dragging node\n  const windowRect = useWindowRect(\n    draggingNode ? getWindow(draggingNode) : null\n  );\n\n  // Get scrollable ancestors of the dragging node\n  const scrollableAncestors = useScrollableAncestors(\n    isInitialized ? overNode ?? activeNode : null\n  );\n  const scrollableAncestorRects = useRects(scrollableAncestors);\n\n  // Apply modifiers\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1,\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect,\n  });\n\n  const pointerCoordinates = activationCoordinates\n    ? add(activationCoordinates, translate)\n    : null;\n\n  const scrollOffsets = useScrollOffsets(scrollableAncestors);\n  // Represents the scroll delta since dragging was initiated\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets);\n  // Represents the scroll delta since the last time the active node rect was measured\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [\n    activeNodeRect,\n  ]);\n\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n\n  const collisionRect = draggingNodeRect\n    ? getAdjustedRect(draggingNodeRect, modifiedTranslate)\n    : null;\n\n  const collisions =\n    active && collisionRect\n      ? collisionDetection({\n          active,\n          collisionRect,\n          droppableRects,\n          droppableContainers: enabledDroppableContainers,\n          pointerCoordinates,\n        })\n      : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState<Over | null>(null);\n\n  // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n  const appliedTranslate = usesDragOverlay\n    ? modifiedTranslate\n    : add(modifiedTranslate, activeNodeScrollDelta);\n\n  const transform = adjustScale(\n    appliedTranslate,\n    over?.rect ?? null,\n    activeNodeRect\n  );\n\n  const instantiateSensor = useCallback(\n    (\n      event: React.SyntheticEvent,\n      {sensor: Sensor, options}: SensorDescriptor<any>\n    ) => {\n      if (activeRef.current == null) {\n        return;\n      }\n\n      const activeNode = draggableNodes.get(activeRef.current);\n\n      if (!activeNode) {\n        return;\n      }\n\n      const activatorEvent = event.nativeEvent;\n\n      const sensorInstance = new Sensor({\n        active: activeRef.current,\n        activeNode,\n        event: activatorEvent,\n        options,\n        // Sensors need to be instantiated with refs for arguments that change over time\n        // otherwise they are frozen in time with the stale arguments\n        context: sensorContext,\n        onStart(initialCoordinates) {\n          const id = activeRef.current;\n\n          if (id == null) {\n            return;\n          }\n\n          const draggableNode = draggableNodes.get(id);\n\n          if (!draggableNode) {\n            return;\n          }\n\n          const {onDragStart} = latestProps.current;\n          const event: DragStartEvent = {\n            active: {id, data: draggableNode.data, rect: activeRects},\n          };\n\n          unstable_batchedUpdates(() => {\n            onDragStart?.(event);\n            setStatus(Status.Initializing);\n            dispatch({\n              type: Action.DragStart,\n              initialCoordinates,\n              active: id,\n            });\n            dispatchMonitorEvent({type: 'onDragStart', event});\n          });\n        },\n        onMove(coordinates) {\n          dispatch({\n            type: Action.DragMove,\n            coordinates,\n          });\n        },\n        onEnd: createHandler(Action.DragEnd),\n        onCancel: createHandler(Action.DragCancel),\n      });\n\n      unstable_batchedUpdates(() => {\n        setActiveSensor(sensorInstance);\n        setActivatorEvent(event.nativeEvent);\n      });\n\n      function createHandler(type: Action.DragEnd | Action.DragCancel) {\n        return async function handler() {\n          const {active, collisions, over, scrollAdjustedTranslate} =\n            sensorContext.current;\n          let event: DragEndEvent | null = null;\n\n          if (active && scrollAdjustedTranslate) {\n            const {cancelDrop} = latestProps.current;\n\n            event = {\n              activatorEvent,\n              active: active,\n              collisions,\n              delta: scrollAdjustedTranslate,\n              over,\n            };\n\n            if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n              const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n              if (shouldCancel) {\n                type = Action.DragCancel;\n              }\n            }\n          }\n\n          activeRef.current = null;\n\n          unstable_batchedUpdates(() => {\n            dispatch({type});\n            setStatus(Status.Uninitialized);\n            setOver(null);\n            setActiveSensor(null);\n            setActivatorEvent(null);\n\n            const eventName =\n              type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n            if (event) {\n              const handler = latestProps.current[eventName];\n\n              handler?.(event);\n              dispatchMonitorEvent({type: eventName, event});\n            }\n          });\n        };\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes]\n  );\n\n  const bindActivatorToSensorInstantiator = useCallback(\n    (\n      handler: SensorActivatorFunction<any>,\n      sensor: SensorDescriptor<any>\n    ): SyntheticListener['handler'] => {\n      return (event, active) => {\n        const nativeEvent = event.nativeEvent as DndEvent;\n        const activeDraggableNode = draggableNodes.get(active);\n\n        if (\n          // Another sensor is already instantiating\n          activeRef.current !== null ||\n          // No active draggable\n          !activeDraggableNode ||\n          // Event has already been captured\n          nativeEvent.dndKit ||\n          nativeEvent.defaultPrevented\n        ) {\n          return;\n        }\n\n        const activationContext = {\n          active: activeDraggableNode,\n        };\n        const shouldActivate = handler(\n          event,\n          sensor.options,\n          activationContext\n        );\n\n        if (shouldActivate === true) {\n          nativeEvent.dndKit = {\n            capturedBy: sensor.sensor,\n          };\n\n          activeRef.current = active;\n          instantiateSensor(event, sensor);\n        }\n      };\n    },\n    [draggableNodes, instantiateSensor]\n  );\n\n  const activators = useCombineActivators(\n    sensors,\n    bindActivatorToSensorInstantiator\n  );\n\n  useSensorSetup(sensors);\n\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n\n  useEffect(\n    () => {\n      const {onDragMove} = latestProps.current;\n      const {active, activatorEvent, collisions, over} = sensorContext.current;\n\n      if (!active || !activatorEvent) {\n        return;\n      }\n\n      const event: DragMoveEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        onDragMove?.(event);\n        dispatchMonitorEvent({type: 'onDragMove', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]\n  );\n\n  useEffect(\n    () => {\n      const {\n        active,\n        activatorEvent,\n        collisions,\n        droppableContainers,\n        scrollAdjustedTranslate,\n      } = sensorContext.current;\n\n      if (\n        !active ||\n        activeRef.current == null ||\n        !activatorEvent ||\n        !scrollAdjustedTranslate\n      ) {\n        return;\n      }\n\n      const {onDragOver} = latestProps.current;\n      const overContainer = droppableContainers.get(overId);\n      const over =\n        overContainer && overContainer.rect.current\n          ? {\n              id: overContainer.id,\n              rect: overContainer.rect.current,\n              data: overContainer.data,\n              disabled: overContainer.disabled,\n            }\n          : null;\n      const event: DragOverEvent = {\n        active,\n        activatorEvent,\n        collisions,\n        delta: {\n          x: scrollAdjustedTranslate.x,\n          y: scrollAdjustedTranslate.y,\n        },\n        over,\n      };\n\n      unstable_batchedUpdates(() => {\n        setOver(over);\n        onDragOver?.(event);\n        dispatchMonitorEvent({type: 'onDragOver', event});\n      });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [overId]\n  );\n\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate,\n    };\n\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect,\n    };\n  }, [\n    active,\n    activeNode,\n    collisions,\n    collisionRect,\n    draggableNodes,\n    draggingNode,\n    draggingNodeRect,\n    droppableRects,\n    droppableContainers,\n    over,\n    scrollableAncestors,\n    scrollAdjustedTranslate,\n  ]);\n\n  useAutoScroller({\n    ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n  });\n\n  const publicContext = useMemo(() => {\n    const context: PublicContextDescriptor = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect,\n    };\n\n    return context;\n  }, [\n    active,\n    activeNode,\n    activeNodeRect,\n    activatorEvent,\n    collisions,\n    containerNodeRect,\n    dragOverlay,\n    draggableNodes,\n    droppableContainers,\n    droppableRects,\n    over,\n    measureDroppableContainers,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    measuringConfiguration,\n    measuringScheduled,\n    windowRect,\n  ]);\n\n  const internalContext = useMemo(() => {\n    const context: InternalContextDescriptor = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById,\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers,\n    };\n\n    return context;\n  }, [\n    activatorEvent,\n    activators,\n    active,\n    activeNodeRect,\n    dispatch,\n    draggableDescribedById,\n    draggableNodes,\n    over,\n    measureDroppableContainers,\n  ]);\n\n  return (\n    <DndMonitorContext.Provider value={registerMonitorListener}>\n      <InternalContext.Provider value={internalContext}>\n        <PublicContext.Provider value={publicContext}>\n          <ActiveDraggableContext.Provider value={transform}>\n            {children}\n          </ActiveDraggableContext.Provider>\n        </PublicContext.Provider>\n        <RestoreFocus disabled={accessibility?.restoreFocus === false} />\n      </InternalContext.Provider>\n      <Accessibility\n        {...accessibility}\n        hiddenTextDescribedById={draggableDescribedById}\n      />\n    </DndMonitorContext.Provider>\n  );\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll =\n      activeSensor?.autoScrollEnabled === false;\n    const autoScrollGloballyDisabled =\n      typeof autoScroll === 'object'\n        ? autoScroll.enabled === false\n        : autoScroll === false;\n    const enabled =\n      isInitialized &&\n      !activeSensorDisablesAutoscroll &&\n      !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return {\n        ...autoScroll,\n        enabled,\n      };\n    }\n\n    return {enabled};\n  }\n});\n", "import {createContext, useContext, useMemo} from 'react';\nimport {\n  Transform,\n  useNodeRef,\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Data} from '../store';\nimport type {UniqueIdentifier} from '../types';\nimport {ActiveDraggableContext} from '../components/DndContext';\nimport {useSyntheticListeners, SyntheticListenerMap} from './utilities';\n\nexport interface UseDraggableArguments {\n  id: UniqueIdentifier;\n  data?: Data;\n  disabled?: boolean;\n  attributes?: {\n    role?: string;\n    roleDescription?: string;\n    tabIndex?: number;\n  };\n}\n\nexport interface DraggableAttributes {\n  role: string;\n  tabIndex: number;\n  'aria-disabled': boolean;\n  'aria-pressed': boolean | undefined;\n  'aria-roledescription': string;\n  'aria-describedby': string;\n}\n\nexport type DraggableSyntheticListeners = SyntheticListenerMap | undefined;\n\nconst NullContext = createContext<any>(null);\n\nconst defaultRole = 'button';\n\nconst ID_PREFIX = 'Droppable';\n\nexport function useDraggable({\n  id,\n  data,\n  disabled = false,\n  attributes,\n}: UseDraggableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over,\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0,\n  } = attributes ?? {};\n  const isDragging = active?.id === id;\n  const transform: Transform | null = useContext(\n    isDragging ? ActiveDraggableContext : NullContext\n  );\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      draggableNodes.set(id, {id, key, node, activatorNode, data: dataRef});\n\n      return () => {\n        const node = draggableNodes.get(id);\n\n        if (node && node.key === key) {\n          draggableNodes.delete(id);\n        }\n      };\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [draggableNodes, id]\n  );\n\n  const memoizedAttributes: DraggableAttributes = useMemo(\n    () => ({\n      role,\n      tabIndex,\n      'aria-disabled': disabled,\n      'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n      'aria-roledescription': roleDescription,\n      'aria-describedby': ariaDescribedById.draggable,\n    }),\n    [\n      disabled,\n      role,\n      tabIndex,\n      isDragging,\n      roleDescription,\n      ariaDescribedById.draggable,\n    ]\n  );\n\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform,\n  };\n}\n", "import {ContextType, useContext} from 'react';\nimport {PublicContext} from '../store';\n\nexport function useDndContext() {\n  return useContext(PublicContext);\n}\n\nexport type UseDndContextReturnValue = ContextType<typeof PublicContext>;\n", "import {useCallback, useContext, useEffect, useRef} from 'react';\nimport {\n  useIsomorphicLayoutEffect,\n  useLatestValue,\n  useNodeRef,\n  useUniqueId,\n} from '@dnd-kit/utilities';\n\nimport {InternalContext, Action, Data} from '../store';\nimport type {ClientRect, UniqueIdentifier} from '../types';\n\nimport {useResizeObserver} from './utilities';\n\ninterface ResizeObserverConfig {\n  /** Whether the ResizeObserver should be disabled entirely */\n  disabled?: boolean;\n  /** Resize events may affect the layout and position of other droppable containers.\n   * Specify an array of `UniqueIdentifier` of droppable containers that should also be re-measured\n   * when this droppable container resizes. Specifying an empty array re-measures all droppable containers.\n   */\n  updateMeasurementsFor?: UniqueIdentifier[];\n  /** Represents the debounce timeout between when resize events are observed and when elements are re-measured */\n  timeout?: number;\n}\n\nexport interface UseDroppableArguments {\n  id: UniqueIdentifier;\n  disabled?: boolean;\n  data?: Data;\n  resizeObserverConfig?: ResizeObserverConfig;\n}\n\nconst ID_PREFIX = 'Droppable';\n\nconst defaultResizeObserverConfig = {\n  timeout: 25,\n};\n\nexport function useDroppable({\n  data,\n  disabled = false,\n  id,\n  resizeObserverConfig,\n}: UseDroppableArguments) {\n  const key = useUniqueId(ID_PREFIX);\n  const {active, dispatch, over, measureDroppableContainers} = useContext(\n    InternalContext\n  );\n  const previous = useRef({disabled});\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef<ClientRect | null>(null);\n  const callbackId = useRef<NodeJS.Timeout | null>(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout,\n  } = {\n    ...defaultResizeObserverConfig,\n    ...resizeObserverConfig,\n  };\n  const ids = useLatestValue(updateMeasurementsFor ?? id);\n  const handleResize = useCallback(\n    () => {\n      if (!resizeObserverConnected.current) {\n        // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n        // assuming the element is rendered and displayed.\n        resizeObserverConnected.current = true;\n        return;\n      }\n\n      if (callbackId.current != null) {\n        clearTimeout(callbackId.current);\n      }\n\n      callbackId.current = setTimeout(() => {\n        measureDroppableContainers(\n          Array.isArray(ids.current) ? ids.current : [ids.current]\n        );\n        callbackId.current = null;\n      }, resizeObserverTimeout);\n    },\n    //eslint-disable-next-line react-hooks/exhaustive-deps\n    [resizeObserverTimeout]\n  );\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active,\n  });\n  const handleNodeChange = useCallback(\n    (newElement: HTMLElement | null, previousElement: HTMLElement | null) => {\n      if (!resizeObserver) {\n        return;\n      }\n\n      if (previousElement) {\n        resizeObserver.unobserve(previousElement);\n        resizeObserverConnected.current = false;\n      }\n\n      if (newElement) {\n        resizeObserver.observe(newElement);\n      }\n    },\n    [resizeObserver]\n  );\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n\n  useIsomorphicLayoutEffect(\n    () => {\n      dispatch({\n        type: Action.RegisterDroppable,\n        element: {\n          id,\n          key,\n          disabled,\n          node: nodeRef,\n          rect,\n          data: dataRef,\n        },\n      });\n\n      return () =>\n        dispatch({\n          type: Action.UnregisterDroppable,\n          key,\n          id,\n        });\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [id]\n  );\n\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled,\n      });\n\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n\n  return {\n    active,\n    rect,\n    isOver: over?.id === id,\n    node: nodeRef,\n    over,\n    setNodeRef,\n  };\n}\n", "import React, {cloneElement, useState} from 'react';\nimport {useIsomorphicLayoutEffect, usePrevious} from '@dnd-kit/utilities';\n\nimport type {UniqueIdentifier} from '../../../../types';\n\nexport type Animation = (\n  key: UniqueIdentifier,\n  node: HTMLElement\n) => Promise<void> | void;\n\nexport interface Props {\n  animation: Animation;\n  children: React.ReactElement | null;\n}\n\nexport function AnimationManager({animation, children}: Props) {\n  const [\n    clonedChildren,\n    setClonedChildren,\n  ] = useState<React.ReactElement | null>(null);\n  const [element, setElement] = useState<HTMLElement | null>(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren?.key;\n    const id = clonedChildren?.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n\n  return (\n    <>\n      {children}\n      {clonedChildren ? cloneElement(clonedChildren, {ref: setElement}) : null}\n    </>\n  );\n}\n", "import React from 'react';\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {InternalContext, defaultInternalContext} from '../../../../store';\nimport {ActiveDraggableContext} from '../../../DndContext';\n\ninterface Props {\n  children: React.ReactNode;\n}\n\nconst defaultTransform: Transform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1,\n};\n\nexport function NullifiedContextProvider({children}: Props) {\n  return (\n    <InternalContext.Provider value={defaultInternalContext}>\n      <ActiveDraggableContext.Provider value={defaultTransform}>\n        {children}\n      </ActiveDraggableContext.Provider>\n    </InternalContext.Provider>\n  );\n}\n", "import React, {forwardRef} from 'react';\nimport {CSS, isKeyboardEvent} from '@dnd-kit/utilities';\n\nimport type {Transform} from '@dnd-kit/utilities';\n\nimport {getRelativeTransformOrigin} from '../../../../utilities';\nimport type {ClientRect, UniqueIdentifier} from '../../../../types';\n\ntype TransitionGetter = (\n  activatorEvent: Event | null\n) => React.CSSProperties['transition'] | undefined;\n\nexport interface Props {\n  as: keyof JSX.IntrinsicElements;\n  activatorEvent: Event | null;\n  adjustScale?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  id: UniqueIdentifier;\n  rect: ClientRect | null;\n  style?: React.CSSProperties;\n  transition?: string | TransitionGetter;\n  transform: Transform;\n}\n\nconst baseStyles: React.CSSProperties = {\n  position: 'fixed',\n  touchAction: 'none',\n};\n\nconst defaultTransition: TransitionGetter = (activatorEvent) => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nexport const PositionedOverlay = forwardRef<HTMLElement, Props>(\n  (\n    {\n      as,\n      activatorEvent,\n      adjustScale,\n      children,\n      className,\n      rect,\n      style,\n      transform,\n      transition = defaultTransition,\n    },\n    ref\n  ) => {\n    if (!rect) {\n      return null;\n    }\n\n    const scaleAdjustedTransform = adjustScale\n      ? transform\n      : {\n          ...transform,\n          scaleX: 1,\n          scaleY: 1,\n        };\n    const styles: React.CSSProperties | undefined = {\n      ...baseStyles,\n      width: rect.width,\n      height: rect.height,\n      top: rect.top,\n      left: rect.left,\n      transform: CSS.Transform.toString(scaleAdjustedTransform),\n      transformOrigin:\n        adjustScale && activatorEvent\n          ? getRelativeTransformOrigin(\n              activatorEvent as MouseEvent | KeyboardEvent | TouchEvent,\n              rect\n            )\n          : undefined,\n      transition:\n        typeof transition === 'function'\n          ? transition(activatorEvent)\n          : transition,\n      ...style,\n    };\n\n    return React.createElement(\n      as,\n      {\n        className,\n        style: styles,\n        ref,\n      },\n      children\n    );\n  }\n);\n", "import {CSS, useEvent, getWindow} from '@dnd-kit/utilities';\nimport type {DeepRequired, Transform} from '@dnd-kit/utilities';\n\nimport type {\n  Active,\n  DraggableNode,\n  DraggableNodes,\n  DroppableContainers,\n} from '../../../store';\nimport type {ClientRect, UniqueIdentifier} from '../../../types';\nimport {getMeasurableNode} from '../../../utilities/nodes';\nimport {scrollIntoViewIfNeeded} from '../../../utilities/scroll';\nimport {parseTransform} from '../../../utilities/transform';\nimport type {MeasuringConfiguration} from '../../DndContext';\nimport type {Animation} from '../components';\n\ninterface SharedParameters {\n  active: {\n    id: UniqueIdentifier;\n    data: Active['data'];\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  dragOverlay: {\n    node: HTMLElement;\n    rect: ClientRect;\n  };\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n}\n\nexport interface KeyframeResolverParameters extends SharedParameters {\n  transform: {\n    initial: Transform;\n    final: Transform;\n  };\n}\n\nexport type KeyframeResolver = (\n  parameters: KeyframeResolverParameters\n) => Keyframe[];\n\nexport interface DropAnimationOptions {\n  keyframes?: KeyframeResolver;\n  duration?: number;\n  easing?: string;\n  sideEffects?: DropAnimationSideEffects | null;\n}\n\nexport type DropAnimation = DropAnimationFunction | DropAnimationOptions;\n\ninterface Arguments {\n  draggableNodes: DraggableNodes;\n  droppableContainers: DroppableContainers;\n  measuringConfiguration: DeepRequired<MeasuringConfiguration>;\n  config?: DropAnimation | null;\n}\n\nexport interface DropAnimationFunctionArguments extends SharedParameters {\n  transform: Transform;\n}\n\nexport type DropAnimationFunction = (\n  args: DropAnimationFunctionArguments\n) => Promise<void> | void;\n\ntype CleanupFunction = () => void;\n\nexport interface DropAnimationSideEffectsParameters extends SharedParameters {}\n\nexport type DropAnimationSideEffects = (\n  parameters: DropAnimationSideEffectsParameters\n) => CleanupFunction | void;\n\ntype ExtractStringProperties<T> = {\n  [K in keyof T]?: T[K] extends string ? string : never;\n};\n\ntype Styles = ExtractStringProperties<CSSStyleDeclaration>;\n\ninterface DefaultDropAnimationSideEffectsOptions {\n  className?: {\n    active?: string;\n    dragOverlay?: string;\n  };\n  styles?: {\n    active?: Styles;\n    dragOverlay?: Styles;\n  };\n}\n\nexport const defaultDropAnimationSideEffects = (\n  options: DefaultDropAnimationSideEffectsOptions\n): DropAnimationSideEffects => ({active, dragOverlay}) => {\n  const originalStyles: Record<string, string> = {};\n  const {styles, className} = options;\n\n  if (styles?.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles?.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className?.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className?.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className?.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver: KeyframeResolver = ({\n  transform: {initial, final},\n}) => [\n  {\n    transform: CSS.Transform.toString(initial),\n  },\n  {\n    transform: CSS.Transform.toString(final),\n  },\n];\n\nexport const defaultDropAnimationConfiguration: Required<DropAnimationOptions> = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0',\n      },\n    },\n  }),\n};\n\nexport function useDropAnimation({\n  config,\n  draggableNodes,\n  droppableContainers,\n  measuringConfiguration,\n}: Arguments) {\n  return useEvent<Animation>((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable: DraggableNode | undefined = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n    const {transform} = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation: DropAnimationFunction =\n      typeof config === 'function'\n        ? config\n        : createDefaultDropAnimation(config);\n\n    scrollIntoViewIfNeeded(\n      activeNode,\n      measuringConfiguration.draggable.measure\n    );\n\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode),\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode),\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform,\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(\n  options: DropAnimationOptions | undefined\n): DropAnimationFunction {\n  const {duration, easing, sideEffects, keyframes} = {\n    ...defaultDropAnimationConfiguration,\n    ...options,\n  };\n\n  return ({active, dragOverlay, transform, ...rest}) => {\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top,\n    };\n\n    const scale = {\n      scaleX:\n        transform.scaleX !== 1\n          ? (active.rect.width * transform.scaleX) / dragOverlay.rect.width\n          : 1,\n      scaleY:\n        transform.scaleY !== 1\n          ? (active.rect.height * transform.scaleY) / dragOverlay.rect.height\n          : 1,\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale,\n    };\n\n    const animationKeyframes = keyframes({\n      ...rest,\n      active,\n      dragOverlay,\n      transform: {initial: transform, final: finalTransform},\n    });\n\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects?.({active, dragOverlay, ...rest});\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards',\n    });\n\n    return new Promise((resolve) => {\n      animation.onfinish = () => {\n        cleanup?.();\n        resolve();\n      };\n    });\n  };\n}\n", "import {useMemo} from 'react';\n\nimport type {UniqueIdentifier} from '../../../types';\n\nlet key = 0;\n\nexport function useKey(id: UniqueIdentifier | undefined) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n", "import React, {useContext} from 'react';\n\nimport {applyModifiers, Modifiers} from '../../modifiers';\nimport {ActiveDraggableContext} from '../DndContext';\nimport {useDndContext} from '../../hooks';\nimport {useInitialValue} from '../../hooks/utilities';\n\nimport {\n  AnimationManager,\n  NullifiedContextProvider,\n  PositionedOverlay,\n} from './components';\nimport type {PositionedOverlayProps} from './components';\n\nimport {useDropAnimation, useKey} from './hooks';\nimport type {DropAnimation} from './hooks';\n\nexport interface Props\n  extends Pick<\n    PositionedOverlayProps,\n    'adjustScale' | 'children' | 'className' | 'style' | 'transition'\n  > {\n  dropAnimation?: DropAnimation | null | undefined;\n  modifiers?: Modifiers;\n  wrapperElement?: keyof JSX.IntrinsicElements;\n  zIndex?: number;\n}\n\nexport const DragOverlay = React.memo(\n  ({\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999,\n  }: Props) => {\n    const {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggableNodes,\n      droppableContainers,\n      dragOverlay,\n      over,\n      measuringConfiguration,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      windowRect,\n    } = useDndContext();\n    const transform = useContext(ActiveDraggableContext);\n    const key = useKey(active?.id);\n    const modifiedTransform = applyModifiers(modifiers, {\n      activatorEvent,\n      active,\n      activeNodeRect,\n      containerNodeRect,\n      draggingNodeRect: dragOverlay.rect,\n      over,\n      overlayNodeRect: dragOverlay.rect,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      transform,\n      windowRect,\n    });\n    const initialRect = useInitialValue(activeNodeRect);\n    const dropAnimation = useDropAnimation({\n      config: dropAnimationConfig,\n      draggableNodes,\n      droppableContainers,\n      measuringConfiguration,\n    });\n    // We need to wait for the active node to be measured before connecting the drag overlay ref\n    // otherwise collisions can be computed against a mispositioned drag overlay\n    const ref = initialRect ? dragOverlay.setRef : undefined;\n\n    return (\n      <NullifiedContextProvider>\n        <AnimationManager animation={dropAnimation}>\n          {active && key ? (\n            <PositionedOverlay\n              key={key}\n              id={active.id}\n              ref={ref}\n              as={wrapperElement}\n              activatorEvent={activatorEvent}\n              adjustScale={adjustScale}\n              className={className}\n              transition={transition}\n              rect={initialRect}\n              style={{\n                zIndex,\n                ...style,\n              }}\n              transform={modifiedTransform}\n            >\n              {children}\n            </PositionedOverlay>\n          ) : null}\n        </AnimationManager>\n      </NullifiedContextProvider>\n    );\n  }\n);\n"], "names": ["DndMonitorContext", "createContext", "useDndMonitor", "listener", "registerListener", "useContext", "useEffect", "Error", "unsubscribe", "useDndMonitorProvider", "listeners", "useState", "Set", "useCallback", "add", "delete", "dispatch", "type", "event", "for<PERSON>ach", "defaultScreenReaderInstructions", "draggable", "defaultAnnouncements", "onDragStart", "active", "id", "onDragOver", "over", "onDragEnd", "onDragCancel", "Accessibility", "announcements", "container", "hiddenTextDescribedById", "screenReaderInstructions", "announce", "announcement", "useAnnouncement", "liveRegionId", "useUniqueId", "mounted", "setMounted", "useMemo", "onDragMove", "markup", "React", "HiddenText", "value", "LiveRegion", "createPortal", "Action", "noop", "useSensor", "sensor", "options", "useSensors", "sensors", "filter", "defaultCoordinates", "Object", "freeze", "x", "y", "distanceBetween", "p1", "p2", "Math", "sqrt", "pow", "getRelativeTransformOrigin", "rect", "eventCoordinates", "getEventCoordinates", "transform<PERSON><PERSON>in", "left", "width", "top", "height", "sortCollisionsAsc", "data", "a", "b", "sortCollisionsDesc", "cornersOfRectangle", "getFirstCollision", "collisions", "property", "length", "firstCollision", "centerOfRectangle", "closestCenter", "collisionRect", "droppableRects", "droppableContainers", "centerRect", "droppableContainer", "get", "distBetween", "push", "sort", "closestCorners", "corners", "rectCorners", "distances", "reduce", "accumulator", "corner", "index", "effectiveDistance", "Number", "toFixed", "getIntersectionRatio", "entry", "target", "max", "right", "min", "bottom", "targetArea", "entryArea", "intersectionArea", "intersectionRatio", "rectIntersection", "isPointWithinRect", "point", "pointer<PERSON><PERSON><PERSON>", "pointerCoordinates", "adjustScale", "transform", "rect1", "rect2", "scaleX", "scaleY", "getRectDelta", "createRectAdjustmentFn", "modifier", "adjustClientRect", "adjustments", "acc", "adjustment", "getAdjustedRect", "parseTransform", "startsWith", "transformArray", "slice", "split", "inverseTransform", "parsedTransform", "translateX", "translateY", "parseFloat", "indexOf", "w", "h", "defaultOptions", "ignoreTransform", "getClientRect", "element", "getBoundingClientRect", "getWindow", "getComputedStyle", "getTransformAgnosticClientRect", "getWindowClientRect", "innerWidth", "innerHeight", "isFixed", "node", "computedStyle", "position", "isScrollable", "overflowRegex", "properties", "some", "test", "getScrollableAncestors", "limit", "scrollParents", "findScrollableAncestors", "isDocument", "scrollingElement", "includes", "isHTMLElement", "isSVGElement", "parentNode", "getFirstScrollableAncestor", "firstScrollableAncestor", "getScrollableElement", "canUseDOM", "isWindow", "isNode", "getOwnerDocument", "window", "getScrollXCoordinate", "scrollX", "scrollLeft", "getScrollYCoordinate", "scrollY", "scrollTop", "getScrollCoordinates", "Direction", "isDocumentScrollingElement", "document", "getScrollPosition", "scrollingContainer", "minScroll", "dimensions", "clientHeight", "clientWidth", "maxScroll", "scrollWidth", "scrollHeight", "isTop", "isLeft", "isBottom", "isRight", "defaultThreshold", "getScrollDirectionAndSpeed", "scrollContainer", "scrollContainerRect", "acceleration", "thresholdPercentage", "direction", "speed", "threshold", "Backward", "abs", "Forward", "getScrollElementRect", "getScrollOffsets", "scrollableAncestors", "getScrollXOffset", "getScrollYOffset", "scrollIntoViewIfNeeded", "measure", "scrollIntoView", "block", "inline", "Rect", "constructor", "scrollOffsets", "axis", "keys", "getScrollOffset", "key", "defineProperty", "currentOffsets", "scrollOffsetsDeltla", "enumerable", "Listeners", "removeAll", "removeEventListener", "eventName", "handler", "addEventListener", "getEventListenerTarget", "EventTarget", "hasExceededDistance", "delta", "measurement", "dx", "dy", "EventName", "preventDefault", "stopPropagation", "KeyboardCode", "defaultKeyboardCodes", "start", "Space", "Enter", "cancel", "Esc", "end", "defaultKeyboardCoordinateGetter", "currentCoordinates", "code", "Right", "Left", "Down", "Up", "undefined", "KeyboardSensor", "props", "autoScrollEnabled", "referenceCoordinates", "windowListeners", "handleKeyDown", "bind", "handleCancel", "attach", "handleStart", "Resize", "VisibilityChange", "setTimeout", "Keydown", "activeNode", "onStart", "current", "isKeyboardEvent", "context", "keyboardCodes", "coordinateGetter", "scroll<PERSON>eh<PERSON>or", "handleEnd", "newCoordinates", "coordinates<PERSON><PERSON><PERSON>", "getCoordinatesDelta", "scrollDelta", "scrollElementRect", "clampedCoordinates", "canScrollX", "canScrollY", "newScrollCoordinates", "canScrollToNewCoordinates", "scrollTo", "behavior", "scrollBy", "handleMove", "getAdjustedCoordinates", "coordinates", "onMove", "onEnd", "detach", "onCancel", "activators", "onActivation", "nativeEvent", "activator", "activatorNode", "isDistanceConstraint", "constraint", "Boolean", "isDelayConstraint", "AbstractPointerSensor", "events", "<PERSON><PERSON><PERSON><PERSON>", "activated", "initialCoordinates", "timeoutId", "documentListeners", "handleKeydown", "removeTextSelection", "activationConstraint", "move", "name", "passive", "DragStart", "ContextMenu", "delay", "clearTimeout", "Click", "capture", "SelectionChange", "tolerance", "distance", "cancelable", "getSelection", "removeAllRanges", "PointerSensor", "isPrimary", "button", "MouseB<PERSON>on", "MouseSensor", "RightClick", "TouchSensor", "setup", "teardown", "touches", "AutoScrollActivator", "TraversalOrder", "useAutoScroller", "Pointer", "canScroll", "draggingRect", "enabled", "interval", "order", "TreeOrder", "scrollableAncestorRects", "scrollIntent", "useScrollIntent", "disabled", "setAutoScrollInterval", "clearAutoScrollInterval", "useInterval", "scrollSpeed", "useRef", "scrollDirection", "DraggableRect", "scrollContainerRef", "autoScroll", "sortedScrollableAncestors", "reverse", "JSON", "stringify", "defaultScrollIntent", "previousDel<PERSON>", "usePrevious", "useLazyMemo", "previousIntent", "sign", "useCachedNode", "draggableNodes", "draggableNode", "cachedNode", "useCombineActivators", "getSyntheticHandler", "Sensor", "sensorActivators", "map", "MeasuringStrategy", "MeasuringFrequency", "defaultValue", "Map", "useDroppableMeasuring", "containers", "dragging", "dependencies", "config", "queue", "setQueue", "frequency", "strategy", "containersRef", "isDisabled", "disabledRef", "useLatestValue", "measureDroppableContainers", "ids", "concat", "previousValue", "set", "measuringScheduled", "Always", "BeforeDragging", "useInitialValue", "computeFn", "useInitialRect", "useMutationObserver", "callback", "handleMutations", "useEvent", "mutationObserver", "MutationObserver", "disconnect", "useResizeObserver", "handleResize", "resizeObserver", "ResizeObserver", "defaultMeasure", "useRect", "fallbackRect", "measureRect", "useReducer", "reducer", "records", "record", "HTMLElement", "contains", "useIsomorphicLayoutEffect", "observe", "body", "childList", "subtree", "currentRect", "isConnected", "newRect", "useRectDelta", "initialRect", "useScrollableAncestors", "previousNode", "ancestors", "useScrollOffsets", "elements", "scrollCoordinates", "setScrollCoordinates", "prevElements", "handleScroll", "previousElements", "cleanup", "entries", "scrollableElement", "Array", "from", "values", "useScrollOffsetsDelta", "initialScrollOffsets", "hasScrollOffsets", "subtract", "useSensorSetup", "teardownFns", "useSyntheticListeners", "useWindowRect", "useRects", "firstElement", "windowRect", "rects", "measureRects", "getMeasurableNode", "children", "<PERSON><PERSON><PERSON><PERSON>", "useDragOverlayMeasuring", "setRect", "handleNodeChange", "nodeRef", "setRef", "useNodeRef", "defaultSensors", "defaultData", "defaultMeasuringConfiguration", "droppable", "WhileDragging", "Optimized", "dragOverlay", "DroppableContainersMap", "toArray", "getEnabled", "getNodeFor", "defaultPublicContext", "activatorEvent", "activeNodeRect", "containerNodeRect", "measuringConfiguration", "defaultInternalContext", "ariaDescribedById", "InternalContext", "PublicContext", "getInitialState", "nodes", "translate", "state", "action", "<PERSON><PERSON><PERSON><PERSON>", "DragEnd", "DragCancel", "RegisterDroppable", "SetDroppableDisabled", "UnregisterDroppable", "RestoreFocus", "previousActivatorEvent", "previousActiveId", "activeElement", "requestAnimationFrame", "focusableNode", "findFirstFocusableNode", "focus", "applyModifiers", "modifiers", "args", "useMeasuringConfiguration", "useLayoutShiftScrollCompensation", "initialized", "rectD<PERSON><PERSON>", "ActiveDraggableContext", "Status", "DndContext", "memo", "accessibility", "collisionDetection", "measuring", "store", "dispatchMonitorEvent", "registerMonitorListener", "status", "setStatus", "Uninitialized", "isInitialized", "Initialized", "activeId", "activeRects", "initial", "translated", "activeRef", "activeSensor", "setActiveSensor", "setActivatorEvent", "latestProps", "draggableDescribedById", "enabledDroppableContainers", "activationCoordinates", "autoScrollOptions", "getAutoScrollerOptions", "initialActiveNodeRect", "layoutShiftCompensation", "parentElement", "sensorContext", "draggingNode", "draggingNodeRect", "scrollAdjustedTranslate", "overNode", "usesDragOverlay", "nodeRectDelta", "modifiedTranslate", "overlayNodeRect", "scrollAdjustment", "activeNodeScrollDelta", "overId", "setOver", "appliedTranslate", "instantiateSensor", "sensorInstance", "unstable_batchedUpdates", "Initializing", "createHandler", "cancelDrop", "shouldCancel", "Promise", "resolve", "bindActivatorToSensorInstantiator", "activeDraggableNode", "dndKit", "defaultPrevented", "activationContext", "shouldActivate", "capturedBy", "over<PERSON><PERSON><PERSON>", "publicContext", "internalContext", "Provider", "restoreFocus", "activeSensorDisablesAutoscroll", "autoScrollGloballyDisabled", "NullContext", "defaultRole", "ID_PREFIX", "useDraggable", "attributes", "role", "roleDescription", "tabIndex", "isDragging", "setNodeRef", "setActivatorNodeRef", "dataRef", "memoizedAttributes", "useDndContext", "defaultResizeObserverConfig", "timeout", "useDroppable", "resizeObserverConfig", "previous", "resizeObserverConnected", "callbackId", "resizeObserverDisabled", "updateMeasurementsFor", "resizeObserverTimeout", "isArray", "newElement", "previousElement", "unobserve", "isOver", "AnimationManager", "animation", "cloned<PERSON><PERSON><PERSON><PERSON>", "setClonedChildren", "setElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "cloneElement", "ref", "defaultTransform", "NullifiedContextProvider", "baseStyles", "touchAction", "defaultTransition", "isKeyboardActivator", "PositionedOverlay", "forwardRef", "as", "className", "style", "transition", "scaleAdjustedTransform", "styles", "CSS", "Transform", "toString", "createElement", "defaultDropAnimationSideEffects", "originalStyles", "getPropertyValue", "setProperty", "classList", "remove", "defaultKeyframeResolver", "final", "defaultDropAnimationConfiguration", "duration", "easing", "keyframes", "sideEffects", "opacity", "useDropAnimation", "activeDraggable", "measurableNode", "createDefaultDropAnimation", "rest", "scale", "finalTransform", "animationKeyframes", "firstKeyframe", "lastKeyframe", "animate", "fill", "onfinish", "useKey", "DragOverlay", "dropAnimation", "dropAnimationConfig", "wrapperElement", "zIndex", "modifiedTransform"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAMA,iBAAiB,GAAA,WAAA,OAAGC,gPAAa,EAA0B,IAA1B,CAAvC;SCCSC,cAAcC,QAAAA;IAC5B,MAAMC,gBAAgB,OAAGC,6OAAU,EAACL,iBAAD,CAAnC;QAEAM,4OAAS,EAAC;QACR,IAAI,CAACF,gBAAL,EAAuB;YACrB,MAAM,IAAIG,KAAJ,CACJ,8DADI,CAAN;;QAKF,MAAMC,WAAW,GAAGJ,gBAAgB,CAACD,QAAD,CAApC;QAEA,OAAOK,WAAP;KATO,EAUN;QAACL,QAAD;QAAWC,gBAAX;KAVM,CAAT;AAWD;SCfeK;IACd,MAAM,CAACC,SAAD,CAAA,OAAcC,2OAAQ,EAAC,IAAM,IAAIC,GAAJ,EAAP,CAA5B;IAEA,MAAMR,gBAAgB,OAAGS,8OAAW,GACjCV,QAAD;QACEO,SAAS,CAACI,GAAV,CAAcX,QAAd;QACA,OAAO,IAAMO,SAAS,CAACK,MAAV,CAAiBZ,QAAjB,CAAb;KAHgC,EAKlC;QAACO,SAAD;KALkC,CAApC;IAQA,MAAMM,QAAQ,OAAGH,8OAAW,GAC1B;YAAC,EAACI,IAAD,EAAOC,KAAAA;QACNR,SAAS,CAACS,OAAV,EAAmBhB,QAAD;YAAA,IAAA;YAAA,OAAA,CAAA,iBAAcA,QAAQ,CAACc,IAAD,CAAtB,KAAA,OAAA,KAAA,IAAc,eAAA,IAAA,CAAAd,QAAQ,EAASe,KAAT,CAAtB;SAAlB;KAFwB,EAI1B;QAACR,SAAD;KAJ0B,CAA5B;IAOA,OAAO;QAACM,QAAD;QAAWZ,gBAAX;KAAP;AACD;MCrBYgB,+BAA+B,GAA6B;IACvEC,SAAS,EAAA;AAD8D,CAAlE;AAQP,MAAaC,oBAAoB,GAAkB;IACjDC,WAAW,EAAA,IAAA;YAAC,EAACC,MAAAA;QACX,OAAA,8BAAmCA,MAAM,CAACC,EAA1C,GAAA;KAF+C;IAIjDC,UAAU,EAAA,KAAA;YAAC,EAACF,MAAD,EAASG,IAAAA;QAClB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,oCAAoEE,IAAI,CAACF,EAAzE,GAAA;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAT+C;IAWjDG,SAAS,EAAA,KAAA;YAAC,EAACJ,MAAD,EAASG,IAAAA;QACjB,IAAIA,IAAJ,EAAU;YACR,OAAA,oBAAyBH,MAAM,CAACC,EAAhC,GAAA,sCAAsEE,IAAI,CAACF,EAA3E;;QAGF,OAAA,oBAAyBD,MAAM,CAACC,EAAhC,GAAA;KAhB+C;IAkBjDI,YAAY,EAAA,KAAA;YAAC,EAACL,MAAAA;QACZ,OAAA,4CAAiDA,MAAM,CAACC,EAAxD,GAAA;;AAnB+C,CAA5C;SCUSK,cAAAA,IAAAA;QAAc,EAC5BC,aAAa,GAAGT,oBADY,EAE5BU,SAF4B,EAG5BC,uBAH4B,EAI5BC,wBAAwB,GAAGd,+BAAAA;IAE3B,MAAM,EAACe,QAAD,EAAWC,YAAAA,SAAgBC,0NAAe,EAAhD;IACA,MAAMC,YAAY,OAAGC,8MAAW,EAAA,gBAAhC;IACA,MAAM,CAACC,OAAD,EAAUC,UAAV,CAAA,OAAwB9B,2OAAQ,EAAC,KAAD,CAAtC;QAEAL,4OAAS,EAAC;QACRmC,UAAU,CAAC,IAAD,CAAV;KADO,EAEN,EAFM,CAAT;IAIAvC,aAAa,KACXwC,0OAAO,EACL,IAAA,CAAO;YACLnB,WAAW,EAAA,KAAA;oBAAC,EAACC,MAAAA;gBACXW,QAAQ,CAACJ,aAAa,CAACR,WAAd,CAA0B;oBAACC;iBAA3B,CAAD,CAAR;aAFG;YAILmB,UAAU,EAAA,KAAA;oBAAC,EAACnB,MAAD,EAASG,IAAAA;gBAClB,IAAII,aAAa,CAACY,UAAlB,EAA8B;oBAC5BR,QAAQ,CAACJ,aAAa,CAACY,UAAd,CAAyB;wBAACnB,MAAD;wBAASG;qBAAlC,CAAD,CAAR;;aANC;YASLD,UAAU,EAAA,KAAA;oBAAC,EAACF,MAAD,EAASG,IAAAA;gBAClBQ,QAAQ,CAACJ,aAAa,CAACL,UAAd,CAAyB;oBAACF,MAAD;oBAASG;iBAAlC,CAAD,CAAR;aAVG;YAYLC,SAAS,EAAA,KAAA;oBAAC,EAACJ,MAAD,EAASG,IAAAA;gBACjBQ,QAAQ,CAACJ,aAAa,CAACH,SAAd,CAAwB;oBAACJ,MAAD;oBAASG;iBAAjC,CAAD,CAAR;aAbG;YAeLE,YAAY,EAAA,KAAA;oBAAC,EAACL,MAAD,EAASG,IAAAA;gBACpBQ,QAAQ,CAACJ,aAAa,CAACF,YAAd,CAA2B;oBAACL,MAAD;oBAASG;iBAApC,CAAD,CAAR;;SAhBJ,CADK,EAoBL;QAACQ,QAAD;QAAWJ,aAAX;KApBK,CADI,CAAb;IAyBA,IAAI,CAACS,OAAL,EAAc;QACZ,OAAO,IAAP;;IAGF,MAAMI,MAAM,GACVC,0OAAAA,CAAAA,aAAA,CAAA,0OAAA,CAAA,QAAA,EAAA,IAAA,EACEA,0OAAAA,CAAAA,aAAA,CAACC,qNAAD,EAAA;QACErB,EAAE,EAAEQ;QACJc,KAAK,EAAEb,wBAAwB,CAACb,SAAAA;KAFlC,CADF,EAKEwB,0OAAAA,CAAAA,aAAA,CAACG,qNAAD,EAAA;QAAYvB,EAAE,EAAEa;QAAcF,YAAY,EAAEA;KAA5C,CALF,CADF;IAUA,OAAOJ,SAAS,OAAGiB,sPAAY,EAACL,MAAD,EAASZ,SAAT,CAAf,GAAqCY,MAArD;AACD;ACvED,IAAYM,MAAZ;AAAA,CAAA,SAAYA,MAAAA;IACVA,MAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,MAAAA,CAAAA,aAAA,GAAA,YAAA;IACAA,MAAAA,CAAAA,WAAA,GAAA,UAAA;IACAA,MAAAA,CAAAA,oBAAA,GAAA,mBAAA;IACAA,MAAAA,CAAAA,uBAAA,GAAA,sBAAA;IACAA,MAAAA,CAAAA,sBAAA,GAAA,qBAAA;AACD,CATD,EAAYA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAlB;SCHgBC,QAAAA;SCIAC,UACdC,MAAAA,EACAC,OAAAA;IAEA,WAAOZ,0OAAO,EACZ,IAAA,CAAO;YACLW,MADK;YAELC,OAAO,EAAEA,OAAF,IAAA,OAAEA,OAAF,GAAc,CAAA;SAFvB,CADY,EAMZ;QAACD,MAAD;QAASC,OAAT;KANY,CAAd;AAQD;SCZeC;qCACXC,UAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,OAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAEH,WAAOd,0OAAO,EACZ,IACE,CAAC;eAAGc,OAAJ;SAAA,CAAaC,MAAb,EACGJ,MAAD,GAA6CA,MAAM,IAAI,IADzD,CAFU,EAMZ,CAAC;WAAGG,OAAJ;KANY,CAAd;AAQD;MCbYE,kBAAkB,GAAA,WAAA,GAAgBC,MAAM,CAACC,MAAP,CAAc;IAC3DC,CAAC,EAAE,CADwD;IAE3DC,CAAC,EAAE;AAFwD,CAAd,CAAxC;ACAP;;IAGA,SAAgBC,gBAAgBC,EAAAA,EAAiBC,EAAAA;IAC/C,OAAOC,IAAI,CAACC,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACH,CAAH,GAAOI,EAAE,CAACJ,CAAnB,EAAsB,CAAtB,IAA2BK,IAAI,CAACE,GAAL,CAASJ,EAAE,CAACF,CAAH,GAAOG,EAAE,CAACH,CAAnB,EAAsB,CAAtB,CAArC,CAAP;AACD;SCJeO,2BACdnD,KAAAA,EACAoD,IAAAA;IAEA,MAAMC,gBAAgB,OAAGC,sNAAmB,EAACtD,KAAD,CAA5C;IAEA,IAAI,CAACqD,gBAAL,EAAuB;QACrB,OAAO,KAAP;;IAGF,MAAME,eAAe,GAAG;QACtBZ,CAAC,EAAG,CAACU,gBAAgB,CAACV,CAAjB,GAAqBS,IAAI,CAACI,IAA3B,IAAmCJ,IAAI,CAACK,KAAzC,GAAkD,GAD/B;QAEtBb,CAAC,EAAG,CAACS,gBAAgB,CAACT,CAAjB,GAAqBQ,IAAI,CAACM,GAA3B,IAAkCN,IAAI,CAACO,MAAxC,GAAkD;KAFvD;IAKA,OAAUJ,eAAe,CAACZ,CAA1B,GAAA,OAAgCY,eAAe,CAACX,CAAhD,GAAA;AACD;ACdD;;IAGA,SAAgBgB,kBAAAA,IAAAA,EAAAA,KAAAA;QACd,EAACC,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOD,CAAC,GAAGC,CAAX;AACD;AAED;;IAGA,SAAgBC,mBAAAA,KAAAA,EAAAA,KAAAA;QACd,EAACH,IAAI,EAAE,EAAChC,KAAK,EAAEiC,CAAAA;QACf,EAACD,IAAI,EAAE,EAAChC,KAAK,EAAEkC,CAAAA;IAEf,OAAOA,CAAC,GAAGD,CAAX;AACD;AAED;;;IAIA,SAAgBG,mBAAAA,KAAAA;QAAmB,EAACT,IAAD,EAAOE,GAAP,EAAYC,MAAZ,EAAoBF,KAAAA;IACrD,OAAO;QACL;YACEd,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc;SAHA;QAKL;YACEf,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc;SAPA;QASL;YACEf,CAAC,EAAEa,IADL;YAEEZ,CAAC,EAAEc,GAAG,GAAGC;SAXN;QAaL;YACEhB,CAAC,EAAEa,IAAI,GAAGC,KADZ;YAEEb,CAAC,EAAEc,GAAG,GAAGC;SAfN;KAAP;AAkBD;AAaD,SAAgBO,kBACdC,UAAAA,EACAC,QAAAA;IAEA,IAAI,CAACD,UAAD,IAAeA,UAAU,CAACE,MAAX,KAAsB,CAAzC,EAA4C;QAC1C,OAAO,IAAP;;IAGF,MAAM,CAACC,cAAD,CAAA,GAAmBH,UAAzB;IAEA,OAAOC,QAAQ,GAAGE,cAAc,CAACF,QAAD,CAAjB,GAA8BE,cAA7C;AACD;AClED;;IAGA,SAASC,iBAAT,CACEnB,IADF,EAEEI,IAFF,EAGEE,GAHF;QAEEF,SAAAA,KAAAA,GAAAA;QAAAA,OAAOJ,IAAI,CAACI,IAAAA;;QACZE,QAAAA,KAAAA,GAAAA;QAAAA,MAAMN,IAAI,CAACM,GAAAA;;IAEX,OAAO;QACLf,CAAC,EAAEa,IAAI,GAAGJ,IAAI,CAACK,KAAL,GAAa,GADlB;QAELb,CAAC,EAAEc,GAAG,GAAGN,IAAI,CAACO,MAAL,GAAc;KAFzB;AAID;AAED;;;IAIA,MAAaa,aAAa,IAAuB;QAAC,EAChDC,aADgD,EAEhDC,cAFgD,EAGhDC,mBAAAA;IAEA,MAAMC,UAAU,GAAGL,iBAAiB,CAClCE,aADkC,EAElCA,aAAa,CAACjB,IAFoB,EAGlCiB,aAAa,CAACf,GAHoB,CAApC;IAKA,MAAMS,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAM2B,WAAW,GAAGlC,eAAe,CAAC0B,iBAAiB,CAACnB,IAAD,CAAlB,EAA0BwB,UAA1B,CAAnC;YAEAT,UAAU,CAACa,IAAX,CAAgB;gBAACzE,EAAD;gBAAKsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAEkD;;aAAvD;;;IAIJ,OAAOZ,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAxBM;ACnBP;;;IAIA,MAAasB,cAAc,IAAuB;QAAC,EACjDT,aADiD,EAEjDC,cAFiD,EAGjDC,mBAAAA;IAEA,MAAMQ,OAAO,GAAGlB,kBAAkB,CAACQ,aAAD,CAAlC;IACA,MAAMN,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMgC,WAAW,GAAGnB,kBAAkB,CAACb,IAAD,CAAtC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd,EAAsBC,KAAtB;gBAC/B,OAAOF,WAAW,GAAG1C,eAAe,CAACuC,WAAW,CAACK,KAAD,CAAZ,EAAqBD,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CA3BM;ACJP;;IAGA,SAAgBiC,qBACdC,KAAAA,EACAC,MAAAA;IAEA,MAAMrC,GAAG,GAAGV,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACrC,GAAhB,EAAqBoC,KAAK,CAACpC,GAA3B,CAAZ;IACA,MAAMF,IAAI,GAAGR,IAAI,CAACgD,GAAL,CAASD,MAAM,CAACvC,IAAhB,EAAsBsC,KAAK,CAACtC,IAA5B,CAAb;IACA,MAAMyC,KAAK,GAAGjD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACvC,IAAP,GAAcuC,MAAM,CAACtC,KAA9B,EAAqCqC,KAAK,CAACtC,IAAN,GAAasC,KAAK,CAACrC,KAAxD,CAAd;IACA,MAAM0C,MAAM,GAAGnD,IAAI,CAACkD,GAAL,CAASH,MAAM,CAACrC,GAAP,GAAaqC,MAAM,CAACpC,MAA7B,EAAqCmC,KAAK,CAACpC,GAAN,GAAYoC,KAAK,CAACnC,MAAvD,CAAf;IACA,MAAMF,KAAK,GAAGwC,KAAK,GAAGzC,IAAtB;IACA,MAAMG,MAAM,GAAGwC,MAAM,GAAGzC,GAAxB;IAEA,IAAIF,IAAI,GAAGyC,KAAP,IAAgBvC,GAAG,GAAGyC,MAA1B,EAAkC;QAChC,MAAMC,UAAU,GAAGL,MAAM,CAACtC,KAAP,GAAesC,MAAM,CAACpC,MAAzC;QACA,MAAM0C,SAAS,GAAGP,KAAK,CAACrC,KAAN,GAAcqC,KAAK,CAACnC,MAAtC;QACA,MAAM2C,gBAAgB,GAAG7C,KAAK,GAAGE,MAAjC;QACA,MAAM4C,iBAAiB,GACrBD,gBAAgB,GAAA,CAAIF,UAAU,GAAGC,SAAb,GAAyBC,gBAA7B,CADlB;QAGA,OAAOX,MAAM,CAACY,iBAAiB,CAACX,OAAlB,CAA0B,CAA1B,CAAD,CAAb;;IAIF,OAAO,CAAP;AACD;AAED;;;IAIA,MAAaY,gBAAgB,IAAuB;QAAC,EACnD/B,aADmD,EAEnDC,cAFmD,EAGnDC,mBAAAA;IAEA,MAAMR,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAJ,EAAU;YACR,MAAMmD,iBAAiB,GAAGV,oBAAoB,CAACzC,IAAD,EAAOqB,aAAP,CAA9C;YAEA,IAAI8B,iBAAiB,GAAG,CAAxB,EAA2B;gBACzBpC,UAAU,CAACa,IAAX,CAAgB;oBACdzE,EADc;oBAEdsD,IAAI,EAAE;wBAACgB,kBAAD;wBAAqBhD,KAAK,EAAE0E;;iBAFpC;;;;IAQN,OAAOpC,UAAU,CAACc,IAAX,CAAgBjB,kBAAhB,CAAP;AACD,CAxBM;AC/BP;;IAGA,SAASyC,iBAAT,CAA2BC,KAA3B,EAA+CtD,IAA/C;IACE,MAAM,EAACM,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAAS7C,IAAnC;IAEA,OACEM,GAAG,IAAIgD,KAAK,CAAC9D,CAAb,IAAkB8D,KAAK,CAAC9D,CAAN,IAAWuD,MAA7B,IAAuC3C,IAAI,IAAIkD,KAAK,CAAC/D,CAArD,IAA0D+D,KAAK,CAAC/D,CAAN,IAAWsD,KADvE;AAGD;AAED;;IAGA,MAAaU,aAAa,IAAuB;QAAC,EAChDhC,mBADgD,EAEhDD,cAFgD,EAGhDkC,kBAAAA;IAEA,IAAI,CAACA,kBAAL,EAAyB;QACvB,OAAO,EAAP;;IAGF,MAAMzC,UAAU,GAA0B,EAA1C;IAEA,KAAK,MAAMU,kBAAX,IAAiCF,mBAAjC,CAAsD;QACpD,MAAM,EAACpE,EAAAA,KAAMsE,kBAAb;QACA,MAAMzB,IAAI,GAAGsB,cAAc,CAACI,GAAf,CAAmBvE,EAAnB,CAAb;QAEA,IAAI6C,IAAI,IAAIqD,iBAAiB,CAACG,kBAAD,EAAqBxD,IAArB,CAA7B,EAAyD;;;;;UAMvD,MAAM+B,OAAO,GAAGlB,kBAAkB,CAACb,IAAD,CAAlC;YACA,MAAMiC,SAAS,GAAGF,OAAO,CAACG,MAAR,CAAe,CAACC,WAAD,EAAcC,MAAd;gBAC/B,OAAOD,WAAW,GAAG1C,eAAe,CAAC+D,kBAAD,EAAqBpB,MAArB,CAApC;aADgB,EAEf,CAFe,CAAlB;YAGA,MAAME,iBAAiB,GAAGC,MAAM,CAAC,CAACN,SAAS,GAAG,CAAb,EAAgBO,OAAhB,CAAwB,CAAxB,CAAD,CAAhC;YAEAzB,UAAU,CAACa,IAAX,CAAgB;gBACdzE,EADc;gBAEdsD,IAAI,EAAE;oBAACgB,kBAAD;oBAAqBhD,KAAK,EAAE6D;;aAFpC;;;IAOJ,OAAOvB,UAAU,CAACc,IAAX,CAAgBrB,iBAAhB,CAAP;AACD,CAnCM;SCjBSiD,YACdC,SAAAA,EACAC,KAAAA,EACAC,KAAAA;IAEA,OAAO;QACL,GAAGF,SADE;QAELG,MAAM,EAAEF,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACtD,KAAN,GAAcuD,KAAK,CAACvD,KAArC,GAA6C,CAFhD;QAGLyD,MAAM,EAAEH,KAAK,IAAIC,KAAT,GAAiBD,KAAK,CAACpD,MAAN,GAAeqD,KAAK,CAACrD,MAAtC,GAA+C;KAHzD;AAKD;SCVewD,aACdJ,KAAAA,EACAC,KAAAA;IAEA,OAAOD,KAAK,IAAIC,KAAT,GACH;QACErE,CAAC,EAAEoE,KAAK,CAACvD,IAAN,GAAawD,KAAK,CAACxD,IADxB;QAEEZ,CAAC,EAAEmE,KAAK,CAACrD,GAAN,GAAYsD,KAAK,CAACtD,GAAAA;KAHpB,GAKHlB,kBALJ;AAMD;SCXe4E,uBAAuBC,QAAAA;IACrC,OAAO,SAASC,gBAAT,CACLlE,IADK;yCAEFmE,cAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,WAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QAEH,OAAOA,WAAW,CAACjC,MAAZ,CACL,CAACkC,GAAD,EAAMC,UAAN,GAAA,CAAsB;gBACpB,GAAGD,GADiB;gBAEpB9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAJ,GAAU2D,QAAQ,GAAGI,UAAU,CAAC7E,CAFjB;gBAGpBuD,MAAM,EAAEqB,GAAG,CAACrB,MAAJ,GAAakB,QAAQ,GAAGI,UAAU,CAAC7E,CAHvB;gBAIpBY,IAAI,EAAEgE,GAAG,CAAChE,IAAJ,GAAW6D,QAAQ,GAAGI,UAAU,CAAC9E,CAJnB;gBAKpBsD,KAAK,EAAEuB,GAAG,CAACvB,KAAJ,GAAYoB,QAAQ,GAAGI,UAAU,CAAC9E,CAAAA;aAL3C,CADK,EAQL;YAAC,GAAGS,IAAAA;SARC,CAAP;KAJF;AAeD;AAEM,MAAMsE,eAAe,GAAA,WAAA,GAAGN,sBAAsB,CAAC,CAAD,CAA9C;SClBSO,eAAeb,SAAAA;IAC7B,IAAIA,SAAS,CAACc,UAAV,CAAqB,WAArB,CAAJ,EAAuC;QACrC,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,EAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,EAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;KAHF,MASO,IAAIf,SAAS,CAACc,UAAV,CAAqB,SAArB,CAAJ,EAAqC;QAC1C,MAAMC,cAAc,GAAGf,SAAS,CAACgB,KAAV,CAAgB,CAAhB,EAAmB,CAAC,CAApB,EAAuBC,KAAvB,CAA6B,IAA7B,CAAvB;QAEA,OAAO;YACLpF,CAAC,EAAE,CAACkF,cAAc,CAAC,CAAD,CADb;YAELjF,CAAC,EAAE,CAACiF,cAAc,CAAC,CAAD,CAFb;YAGLZ,MAAM,EAAE,CAACY,cAAc,CAAC,CAAD,CAHlB;YAILX,MAAM,EAAE,CAACW,cAAc,CAAC,CAAD,CAAA;SAJzB;;IAQF,OAAO,IAAP;AACD;SCpBeG,iBACd5E,IAAAA,EACA0D,SAAAA,EACAvD,eAAAA;IAEA,MAAM0E,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;IAEA,IAAI,CAACmB,eAAL,EAAsB;QACpB,OAAO7E,IAAP;;IAGF,MAAM,EAAC6D,MAAD,EAASC,MAAT,EAAiBvE,CAAC,EAAEuF,UAApB,EAAgCtF,CAAC,EAAEuF,UAAAA,KAAcF,eAAvD;IAEA,MAAMtF,CAAC,GAAGS,IAAI,CAACI,IAAL,GAAY0E,UAAZ,GAAyB,CAAC,IAAIjB,MAAL,IAAemB,UAAU,CAAC7E,eAAD,CAA5D;IACA,MAAMX,CAAC,GACLQ,IAAI,CAACM,GAAL,GACAyE,UADA,GAEA,CAAC,IAAIjB,MAAL,IACEkB,UAAU,CAAC7E,eAAe,CAACuE,KAAhB,CAAsBvE,eAAe,CAAC8E,OAAhB,CAAwB,GAAxB,IAA+B,CAArD,CAAD,CAJd;IAKA,MAAMC,CAAC,GAAGrB,MAAM,GAAG7D,IAAI,CAACK,KAAL,GAAawD,MAAhB,GAAyB7D,IAAI,CAACK,KAA9C;IACA,MAAM8E,CAAC,GAAGrB,MAAM,GAAG9D,IAAI,CAACO,MAAL,GAAcuD,MAAjB,GAA0B9D,IAAI,CAACO,MAA/C;IAEA,OAAO;QACLF,KAAK,EAAE6E,CADF;QAEL3E,MAAM,EAAE4E,CAFH;QAGL7E,GAAG,EAAEd,CAHA;QAILqD,KAAK,EAAEtD,CAAC,GAAG2F,CAJN;QAKLnC,MAAM,EAAEvD,CAAC,GAAG2F,CALP;QAML/E,IAAI,EAAEb;KANR;AAQD;ACzBD,MAAM6F,cAAc,GAAY;IAACC,eAAe,EAAE;AAAlB,CAAhC;AAEA;;IAGA,SAAgBC,cACdC,OAAAA,EACAvG,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAAmBoG;;IAEnB,IAAIpF,IAAI,GAAeuF,OAAO,CAACC,qBAAR,EAAvB;IAEA,IAAIxG,OAAO,CAACqG,eAAZ,EAA6B;QAC3B,MAAM,EAAC3B,SAAD,EAAYvD,eAAAA,SAChBsF,4MAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCH,OAApC,CADF;QAGA,IAAI7B,SAAJ,EAAe;YACb1D,IAAI,GAAG4E,gBAAgB,CAAC5E,IAAD,EAAO0D,SAAP,EAAkBvD,eAAlB,CAAvB;;;IAIJ,MAAM,EAACG,GAAD,EAAMF,IAAN,EAAYC,KAAZ,EAAmBE,MAAnB,EAA2BwC,MAA3B,EAAmCF,KAAAA,KAAS7C,IAAlD;IAEA,OAAO;QACLM,GADK;QAELF,IAFK;QAGLC,KAHK;QAILE,MAJK;QAKLwC,MALK;QAMLF;KANF;AAQD;AAED;;;;;;;IAQA,SAAgB8C,+BAA+BJ,OAAAA;IAC7C,OAAOD,aAAa,CAACC,OAAD,EAAU;QAACF,eAAe,EAAE;KAA5B,CAApB;AACD;SCjDeO,oBAAoBL,OAAAA;IAClC,MAAMlF,KAAK,GAAGkF,OAAO,CAACM,UAAtB;IACA,MAAMtF,MAAM,GAAGgF,OAAO,CAACO,WAAvB;IAEA,OAAO;QACLxF,GAAG,EAAE,CADA;QAELF,IAAI,EAAE,CAFD;QAGLyC,KAAK,EAAExC,KAHF;QAIL0C,MAAM,EAAExC,MAJH;QAKLF,KALK;QAMLE;KANF;AAQD;SCZewF,QACdC,IAAAA,EACAC,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,oBAAqCR,4MAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC;;IAErC,OAAOC,aAAa,CAACC,QAAd,KAA2B,OAAlC;AACD;SCLeC,aACdZ,OAAAA,EACAU,aAAAA;QAAAA,kBAAAA,KAAAA,GAAAA;QAAAA,oBAAqCR,4MAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CACnCH,OADmC;;IAIrC,MAAMa,aAAa,GAAG,uBAAtB;IACA,MAAMC,UAAU,GAAG;QAAC,UAAD;QAAa,WAAb;QAA0B,WAA1B;KAAnB;IAEA,OAAOA,UAAU,CAACC,IAAX,EAAiBtF,QAAD;QACrB,MAAMvC,KAAK,GAAGwH,aAAa,CAACjF,QAAD,CAA3B;QAEA,OAAO,OAAOvC,KAAP,KAAiB,QAAjB,GAA4B2H,aAAa,CAACG,IAAd,CAAmB9H,KAAnB,CAA5B,GAAwD,KAA/D;KAHK,CAAP;AAKD;SCNe+H,uBACdjB,OAAAA,EACAkB,KAAAA;IAEA,MAAMC,aAAa,GAAc,EAAjC;IAEA,SAASC,uBAAT,CAAiCX,IAAjC;QACE,IAAIS,KAAK,IAAI,IAAT,IAAiBC,aAAa,CAACzF,MAAd,IAAwBwF,KAA7C,EAAoD;YAClD,OAAOC,aAAP;;QAGF,IAAI,CAACV,IAAL,EAAW;YACT,OAAOU,aAAP;;QAGF,QACEE,6MAAU,EAACZ,IAAD,CAAV,IACAA,IAAI,CAACa,gBAAL,IAAyB,IADzB,IAEA,CAACH,aAAa,CAACI,QAAd,CAAuBd,IAAI,CAACa,gBAA5B,CAHH,EAIE;YACAH,aAAa,CAAC9E,IAAd,CAAmBoE,IAAI,CAACa,gBAAxB;YAEA,OAAOH,aAAP;;QAGF,IAAI,KAACK,gNAAa,EAACf,IAAD,CAAd,QAAwBgB,+MAAY,EAAChB,IAAD,CAAxC,EAAgD;YAC9C,OAAOU,aAAP;;QAGF,IAAIA,aAAa,CAACI,QAAd,CAAuBd,IAAvB,CAAJ,EAAkC;YAChC,OAAOU,aAAP;;QAGF,MAAMT,aAAa,OAAGR,4MAAS,EAACF,OAAD,CAAT,CAAmBG,gBAAnB,CAAoCM,IAApC,CAAtB;QAEA,IAAIA,IAAI,KAAKT,OAAb,EAAsB;YACpB,IAAIY,YAAY,CAACH,IAAD,EAAOC,aAAP,CAAhB,EAAuC;gBACrCS,aAAa,CAAC9E,IAAd,CAAmBoE,IAAnB;;;QAIJ,IAAID,OAAO,CAACC,IAAD,EAAOC,aAAP,CAAX,EAAkC;YAChC,OAAOS,aAAP;;QAGF,OAAOC,uBAAuB,CAACX,IAAI,CAACiB,UAAN,CAA9B;;IAGF,IAAI,CAAC1B,OAAL,EAAc;QACZ,OAAOmB,aAAP;;IAGF,OAAOC,uBAAuB,CAACpB,OAAD,CAA9B;AACD;AAED,SAAgB2B,2BAA2BlB,IAAAA;IACzC,MAAM,CAACmB,uBAAD,CAAA,GAA4BX,sBAAsB,CAACR,IAAD,EAAO,CAAP,CAAxD;IAEA,OAAOmB,uBAAP,IAAA,OAAOA,uBAAP,GAAkC,IAAlC;AACD;SC5DeC,qBAAqB7B,OAAAA;IACnC,IAAI,CAAC8B,4MAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,IAAP;;IAGF,QAAI+B,2MAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAP;;IAGF,IAAI,KAACgC,yMAAM,EAAChC,OAAD,CAAX,EAAsB;QACpB,OAAO,IAAP;;IAGF,QACEqB,6MAAU,EAACrB,OAAD,CAAV,IACAA,OAAO,SAAKiC,mNAAgB,EAACjC,OAAD,CAAhB,CAA0BsB,gBAFxC,EAGE;QACA,OAAOY,MAAP;;IAGF,QAAIV,gNAAa,EAACxB,OAAD,CAAjB,EAA4B;QAC1B,OAAOA,OAAP;;IAGF,OAAO,IAAP;AACD;SC9BemC,qBAAqBnC,OAAAA;IACnC,QAAI+B,2MAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACoC,OAAf;;IAGF,OAAOpC,OAAO,CAACqC,UAAf;AACD;AAED,SAAgBC,qBAAqBtC,OAAAA;IACnC,QAAI+B,2MAAQ,EAAC/B,OAAD,CAAZ,EAAuB;QACrB,OAAOA,OAAO,CAACuC,OAAf;;IAGF,OAAOvC,OAAO,CAACwC,SAAf;AACD;AAED,SAAgBC,qBACdzC,OAAAA;IAEA,OAAO;QACLhG,CAAC,EAAEmI,oBAAoB,CAACnC,OAAD,CADlB;QAEL/F,CAAC,EAAEqI,oBAAoB,CAACtC,OAAD;KAFzB;AAID;AC3BD,IAAY0C,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,SAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,SAAAA,CAAAA,WAAAA,GAAAA,CAAAA,EAAA,GAAA,UAAA;AACD,CAHD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;SCEgBC,2BAA2B3C,OAAAA;IACzC,IAAI,CAAC8B,4MAAD,IAAc,CAAC9B,OAAnB,EAA4B;QAC1B,OAAO,KAAP;;IAGF,OAAOA,OAAO,KAAK4C,QAAQ,CAACtB,gBAA5B;AACD;SCNeuB,kBAAkBC,kBAAAA;IAChC,MAAMC,SAAS,GAAG;QAChB/I,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAM+I,UAAU,GAAGL,0BAA0B,CAACG,kBAAD,CAA1B,GACf;QACE9H,MAAM,EAAEkH,MAAM,CAAC3B,WADjB;QAEEzF,KAAK,EAAEoH,MAAM,CAAC5B,UAAAA;KAHD,GAKf;QACEtF,MAAM,EAAE8H,kBAAkB,CAACG,YAD7B;QAEEnI,KAAK,EAAEgI,kBAAkB,CAACI,WAAAA;KAPhC;IASA,MAAMC,SAAS,GAAG;QAChBnJ,CAAC,EAAE8I,kBAAkB,CAACM,WAAnB,GAAiCJ,UAAU,CAAClI,KAD/B;QAEhBb,CAAC,EAAE6I,kBAAkB,CAACO,YAAnB,GAAkCL,UAAU,CAAChI,MAAAA;KAFlD;IAKA,MAAMsI,KAAK,GAAGR,kBAAkB,CAACN,SAAnB,IAAgCO,SAAS,CAAC9I,CAAxD;IACA,MAAMsJ,MAAM,GAAGT,kBAAkB,CAACT,UAAnB,IAAiCU,SAAS,CAAC/I,CAA1D;IACA,MAAMwJ,QAAQ,GAAGV,kBAAkB,CAACN,SAAnB,IAAgCW,SAAS,CAAClJ,CAA3D;IACA,MAAMwJ,OAAO,GAAGX,kBAAkB,CAACT,UAAnB,IAAiCc,SAAS,CAACnJ,CAA3D;IAEA,OAAO;QACLsJ,KADK;QAELC,MAFK;QAGLC,QAHK;QAILC,OAJK;QAKLN,SALK;QAMLJ;KANF;AAQD;AC5BD,MAAMW,gBAAgB,GAAG;IACvB1J,CAAC,EAAE,GADoB;IAEvBC,CAAC,EAAE;AAFoB,CAAzB;AAKA,SAAgB0J,2BACdC,eAAAA,EACAC,mBAAAA,EAAAA,IAAAA,EAEAC,YAAAA,EACAC,mBAAAA;QAFA,EAAChJ,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA;QACnBsG,iBAAAA,KAAAA,GAAAA;QAAAA,eAAe;;QACfC,wBAAAA,KAAAA,GAAAA;QAAAA,sBAAsBL;;IAEtB,MAAM,EAACJ,KAAD,EAAQE,QAAR,EAAkBD,MAAlB,EAA0BE,OAAAA,KAAWZ,iBAAiB,CAACe,eAAD,CAA5D;IAEA,MAAMI,SAAS,GAAG;QAChBhK,CAAC,EAAE,CADa;QAEhBC,CAAC,EAAE;KAFL;IAIA,MAAMgK,KAAK,GAAG;QACZjK,CAAC,EAAE,CADS;QAEZC,CAAC,EAAE;KAFL;IAIA,MAAMiK,SAAS,GAAG;QAChBlJ,MAAM,EAAE6I,mBAAmB,CAAC7I,MAApB,GAA6B+I,mBAAmB,CAAC9J,CADzC;QAEhBa,KAAK,EAAE+I,mBAAmB,CAAC/I,KAApB,GAA4BiJ,mBAAmB,CAAC/J,CAAAA;KAFzD;IAKA,IAAI,CAACsJ,KAAD,IAAUvI,GAAG,IAAI8I,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAAzD,EAAiE;;QAE/DgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAC9I,GAApB,GAA0BmJ,SAAS,CAAClJ,MAApC,GAA6CD,GAA9C,IAAqDmJ,SAAS,CAAClJ,MADjE,CAFF;KAHF,MAQO,IACL,CAACwI,QAAD,IACAhG,MAAM,IAAIqG,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAF5C,EAGL;;QAEAgJ,SAAS,CAAC/J,CAAV,GAAcyI,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAAChK,CAAN,GACE6J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACrG,MAApB,GAA6B0G,SAAS,CAAClJ,MAAvC,GAAgDwC,MAAjD,IACE0G,SAAS,CAAClJ,MAFd,CAFF;;IAQF,IAAI,CAACyI,OAAD,IAAYnG,KAAK,IAAIuG,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAA/D,EAAsE;;QAEpEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAAC2B,OAAxB;QACAJ,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAACvG,KAApB,GAA4B4G,SAAS,CAACpJ,KAAtC,GAA8CwC,KAA/C,IAAwD4G,SAAS,CAACpJ,KADpE,CAFF;KAHF,MAQO,IAAI,CAACyI,MAAD,IAAW1I,IAAI,IAAIgJ,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAA5D,EAAmE;;QAExEkJ,SAAS,CAAChK,CAAV,GAAc0I,SAAS,CAACyB,QAAxB;QACAF,KAAK,CAACjK,CAAN,GACE8J,YAAY,GACZzJ,IAAI,CAAC+J,GAAL,CACE,CAACP,mBAAmB,CAAChJ,IAApB,GAA2BqJ,SAAS,CAACpJ,KAArC,GAA6CD,IAA9C,IAAsDqJ,SAAS,CAACpJ,KADlE,CAFF;;IAOF,OAAO;QACLkJ,SADK;QAELC;KAFF;AAID;SC7EeK,qBAAqBtE,OAAAA;IACnC,IAAIA,OAAO,KAAK4C,QAAQ,CAACtB,gBAAzB,EAA2C;QACzC,MAAM,EAAChB,UAAD,EAAaC,WAAAA,KAAe2B,MAAlC;QAEA,OAAO;YACLnH,GAAG,EAAE,CADA;YAELF,IAAI,EAAE,CAFD;YAGLyC,KAAK,EAAEgD,UAHF;YAIL9C,MAAM,EAAE+C,WAJH;YAKLzF,KAAK,EAAEwF,UALF;YAMLtF,MAAM,EAAEuF;SANV;;IAUF,MAAM,EAACxF,GAAD,EAAMF,IAAN,EAAYyC,KAAZ,EAAmBE,MAAAA,KAAUwC,OAAO,CAACC,qBAAR,EAAnC;IAEA,OAAO;QACLlF,GADK;QAELF,IAFK;QAGLyC,KAHK;QAILE,MAJK;QAKL1C,KAAK,EAAEkF,OAAO,CAACkD,WALV;QAMLlI,MAAM,EAAEgF,OAAO,CAACiD,YAAAA;KANlB;AAQD;SCdesB,iBAAiBC,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAwC,CAACkC,GAAD,EAAM4B,IAAN;QAC7C,WAAOxJ,sMAAG,EAAC4H,GAAD,EAAM4D,oBAAoB,CAAChC,IAAD,CAA1B,CAAV;KADK,EAEJ5G,kBAFI,CAAP;AAGD;AAED,SAAgB4K,iBAAiBD,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGsD,oBAAoB,CAAC1B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;AAED,SAAgBiE,iBAAiBF,mBAAAA;IAC/B,OAAOA,mBAAmB,CAAC7H,MAApB,CAAmC,CAACkC,GAAD,EAAM4B,IAAN;QACxC,OAAO5B,GAAG,GAAGyD,oBAAoB,CAAC7B,IAAD,CAAjC;KADK,EAEJ,CAFI,CAAP;AAGD;SCtBekE,uBACd3E,OAAAA,EACA4E,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA6C7E;;IAE7C,IAAI,CAACC,OAAL,EAAc;QACZ;;IAGF,MAAM,EAACjF,GAAD,EAAMF,IAAN,EAAY2C,MAAZ,EAAoBF,KAAAA,KAASsH,OAAO,CAAC5E,OAAD,CAA1C;IACA,MAAM4B,uBAAuB,GAAGD,0BAA0B,CAAC3B,OAAD,CAA1D;IAEA,IAAI,CAAC4B,uBAAL,EAA8B;QAC5B;;IAGF,IACEpE,MAAM,IAAI,CAAV,IACAF,KAAK,IAAI,CADT,IAEAvC,GAAG,IAAImH,MAAM,CAAC3B,WAFd,IAGA1F,IAAI,IAAIqH,MAAM,CAAC5B,UAJjB,EAKE;QACAN,OAAO,CAAC6E,cAAR,CAAuB;YACrBC,KAAK,EAAE,QADc;YAErBC,MAAM,EAAE;SAFV;;AAKH;ACtBD,MAAMjE,UAAU,GAAG;IACjB;QAAC,GAAD;QAAM;YAAC,MAAD;YAAS,OAAT;SAAN;QAAyB2D,gBAAzB;KADiB;IAEjB;QAAC,GAAD;QAAM;YAAC,KAAD;YAAQ,QAAR;SAAN;QAAyBC,gBAAzB;KAFiB;CAAnB;AAKA,MAAaM;IACXC,YAAYxK,IAAAA,EAAkBuF,OAAAA,CAAAA;aAyBtBvF,IAAAA,GAAAA,KAAAA;aAEDK,KAAAA,GAAAA,KAAAA;aAEAE,MAAAA,GAAAA,KAAAA;aAIAD,GAAAA,GAAAA,KAAAA;aAEAyC,MAAAA,GAAAA,KAAAA;aAEAF,KAAAA,GAAAA,KAAAA;aAEAzC,IAAAA,GAAAA,KAAAA;QAtCL,MAAM2J,mBAAmB,GAAGvD,sBAAsB,CAACjB,OAAD,CAAlD;QACA,MAAMkF,aAAa,GAAGX,gBAAgB,CAACC,mBAAD,CAAtC;QAEA,IAAA,CAAK/J,IAAL,GAAY;YAAC,GAAGA,IAAAA;SAAhB;QACA,IAAA,CAAKK,KAAL,GAAaL,IAAI,CAACK,KAAlB;QACA,IAAA,CAAKE,MAAL,GAAcP,IAAI,CAACO,MAAnB;QAEA,KAAK,MAAM,CAACmK,IAAD,EAAOC,IAAP,EAAaC,eAAb,CAAX,IAA4CvE,UAA5C,CAAwD;YACtD,KAAK,MAAMwE,GAAX,IAAkBF,IAAlB,CAAwB;gBACtBtL,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4BD,GAA5B,EAAiC;oBAC/BnJ,GAAG,EAAE;wBACH,MAAMqJ,cAAc,GAAGH,eAAe,CAACb,mBAAD,CAAtC;wBACA,MAAMiB,mBAAmB,GAAGP,aAAa,CAACC,IAAD,CAAb,GAAsBK,cAAlD;wBAEA,OAAO,IAAA,CAAK/K,IAAL,CAAU6K,GAAV,CAAA,GAAiBG,mBAAxB;qBAL6B;oBAO/BC,UAAU,EAAE;iBAPd;;;QAYJ5L,MAAM,CAACyL,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;YAACG,UAAU,EAAE;SAAjD;;;MCpCSC;IAOXV,YAAoB7H,MAAAA,CAAAA;aAAAA,MAAAA,GAAAA,KAAAA;aANZvG,SAAAA,GAIF,EAAA;aAaC+O,SAAAA,GAAY;YACjB,IAAA,CAAK/O,SAAL,CAAeS,OAAf,EAAwBhB,QAAD;gBAAA,IAAA;gBAAA,OAAA,CAAA,eACrB,IAAA,CAAK8G,MADgB,KAAA,OAAA,KAAA,IACrB,aAAayI,mBAAb,CAAiC,GAAGvP,QAApC,CADqB;aAAvB;;QAZkB,IAAA,CAAA,MAAA,GAAA8G,MAAA;;IAEbnG,GAAG,CACR6O,SADQ,EAERC,OAFQ,EAGRtM,OAHQ,EAAA;;QAKR,CAAA,gBAAA,IAAA,CAAK2D,MAAL,KAAA,OAAA,KAAA,IAAA,cAAa4I,gBAAb,CAA8BF,SAA9B,EAAyCC,OAAzC,EAAmEtM,OAAnE;QACA,IAAA,CAAK5C,SAAL,CAAewF,IAAf,CAAoB;YAACyJ,SAAD;YAAYC,OAAZ;YAAsCtM,OAAtC;SAApB;;;SCbYwM,uBACd7I,MAAAA;;;;;;IAQA,MAAM,EAAC8I,WAAAA,SAAehG,4MAAS,EAAC9C,MAAD,CAA/B;IAEA,OAAOA,MAAM,YAAY8I,WAAlB,GAAgC9I,MAAhC,OAAyC6E,mNAAgB,EAAC7E,MAAD,CAAhE;AACD;SCZe+I,oBACdC,KAAAA,EACAC,WAAAA;IAEA,MAAMC,EAAE,GAAGjM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACpM,CAAf,CAAX;IACA,MAAMuM,EAAE,GAAGlM,IAAI,CAAC+J,GAAL,CAASgC,KAAK,CAACnM,CAAf,CAAX;IAEA,IAAI,OAAOoM,WAAP,KAAuB,QAA3B,EAAqC;QACnC,OAAOhM,IAAI,CAACC,IAAL,CAAUgM,EAAE,IAAI,CAAN,GAAUC,EAAE,IAAI,CAA1B,IAA+BF,WAAtC;;IAGF,IAAI,OAAOA,WAAP,IAAsB,OAAOA,WAAjC,EAA8C;QAC5C,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAjB,IAAsBuM,EAAE,GAAGF,WAAW,CAACpM,CAA9C;;IAGF,IAAI,OAAOoM,WAAX,EAAwB;QACtB,OAAOC,EAAE,GAAGD,WAAW,CAACrM,CAAxB;;IAGF,IAAI,OAAOqM,WAAX,EAAwB;QACtB,OAAOE,EAAE,GAAGF,WAAW,CAACpM,CAAxB;;IAGF,OAAO,KAAP;AACD;AC1BD,IAAYuM,SAAZ;AAAA,CAAA,SAAYA,SAAAA;IACVA,SAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,SAAAA,CAAAA,YAAA,GAAA,WAAA;IACAA,SAAAA,CAAAA,UAAA,GAAA,SAAA;IACAA,SAAAA,CAAAA,cAAA,GAAA,aAAA;IACAA,SAAAA,CAAAA,SAAA,GAAA,QAAA;IACAA,SAAAA,CAAAA,kBAAA,GAAA,iBAAA;IACAA,SAAAA,CAAAA,mBAAA,GAAA,kBAAA;AACD,CARD,EAAYA,SAAS,IAAA,CAATA,SAAS,GAAA,CAAA,CAAA,CAArB;AAUA,SAAgBC,eAAepP,KAAAA;IAC7BA,KAAK,CAACoP,cAAN;AACD;AAED,SAAgBC,gBAAgBrP,KAAAA;IAC9BA,KAAK,CAACqP,eAAN;AACD;ICbWC,YAAZ;AAAA,CAAA,SAAYA,YAAAA;IACVA,YAAAA,CAAAA,QAAA,GAAA,OAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,YAAA;IACAA,YAAAA,CAAAA,OAAA,GAAA,WAAA;IACAA,YAAAA,CAAAA,KAAA,GAAA,SAAA;IACAA,YAAAA,CAAAA,MAAA,GAAA,QAAA;IACAA,YAAAA,CAAAA,QAAA,GAAA,OAAA;AACD,CARD,EAAYA,YAAY,IAAA,CAAZA,YAAY,GAAA,CAAA,CAAA,CAAxB;ACDO,MAAMC,oBAAoB,GAAkB;IACjDC,KAAK,EAAE;QAACF,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;KAD0C;IAEjDC,MAAM,EAAE;QAACL,YAAY,CAACM,GAAd;KAFyC;IAGjDC,GAAG,EAAE;QAACP,YAAY,CAACG,KAAd;QAAqBH,YAAY,CAACI,KAAlC;KAAA;AAH4C,CAA5C;AAMA,MAAMI,+BAA+B,GAA6B,CACvE9P,KADuE,EAAA;QAEvE,EAAC+P,kBAAAA;IAED,OAAQ/P,KAAK,CAACgQ,IAAd;QACE,KAAKV,YAAY,CAACW,KAAlB;YACE,OAAO;gBACL,GAAGF,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACY,IAAlB;YACE,OAAO;gBACL,GAAGH,kBADE;gBAELpN,CAAC,EAAEoN,kBAAkB,CAACpN,CAAnB,GAAuB;aAF5B;QAIF,KAAK2M,YAAY,CAACa,IAAlB;YACE,OAAO;gBACL,GAAGJ,kBADE;gBAELnN,CAAC,EAAEmN,kBAAkB,CAACnN,CAAnB,GAAuB;aAF5B;QAIF,KAAK0M,YAAY,CAACc,EAAlB;YACE,OAAO;gBACL,GAAGL,kBADE;gBAELnN,CAAC,EAAEmN,kBAAkB,CAACnN,CAAnB,GAAuB;aAF5B;;IAMJ,OAAOyN,SAAP;AACD,CA5BM;MC+BMC;IAMX1C,YAAoB2C,KAAAA,CAAAA;aAAAA,KAAAA,GAAAA,KAAAA;aALbC,iBAAAA,GAAoB;aACnBC,oBAAAA,GAAAA,KAAAA;aACAjR,SAAAA,GAAAA,KAAAA;aACAkR,eAAAA,GAAAA,KAAAA;QAEY,IAAA,CAAA,KAAA,GAAAH,KAAA;QAClB,MAAM,EACJvQ,KAAK,EAAE,EAAC+F,MAAAA,OACNwK,KAFJ;QAIA,IAAA,CAAKA,KAAL,GAAaA,KAAb;QACA,IAAA,CAAK/Q,SAAL,GAAiB,IAAI8O,SAAJ,KAAc1D,mNAAgB,EAAC7E,MAAD,CAA9B,CAAjB;QACA,IAAA,CAAK2K,eAAL,GAAuB,IAAIpC,SAAJ,KAAczF,4MAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAK4K,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBC,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,IAAA,CAAKC,WAAL;QAEA,IAAA,CAAKL,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAAC6B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAAC8B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QAEAK,UAAU,CAAC,IAAM,IAAA,CAAK1R,SAAL,CAAeI,GAAf,CAAmBuP,SAAS,CAACgC,OAA7B,EAAsC,IAAA,CAAKR,aAA3C,CAAP,CAAV;;IAGMI,WAAW,GAAA;QACjB,MAAM,EAACK,UAAD,EAAaC,OAAAA,KAAW,IAAA,CAAKd,KAAnC;QACA,MAAMnH,IAAI,GAAGgI,UAAU,CAAChI,IAAX,CAAgBkI,OAA7B;QAEA,IAAIlI,IAAJ,EAAU;YACRkE,sBAAsB,CAAClE,IAAD,CAAtB;;QAGFiI,OAAO,CAAC7O,kBAAD,CAAP;;IAGMmO,aAAa,CAAC3Q,KAAD,EAAA;QACnB,QAAIuR,kNAAe,EAACvR,KAAD,CAAnB,EAA4B;YAC1B,MAAM,EAACM,MAAD,EAASkR,OAAT,EAAkBpP,OAAAA,KAAW,IAAA,CAAKmO,KAAxC;YACA,MAAM,EACJkB,aAAa,GAAGlC,oBADZ,EAEJmC,gBAAgB,GAAG5B,+BAFf,EAGJ6B,cAAc,GAAG,QAAA,KACfvP,OAJJ;YAKA,MAAM,EAAC4N,IAAAA,KAAQhQ,KAAf;YAEA,IAAIyR,aAAa,CAAC5B,GAAd,CAAkB3F,QAAlB,CAA2B8F,IAA3B,CAAJ,EAAsC;gBACpC,IAAA,CAAK4B,SAAL,CAAe5R,KAAf;gBACA;;YAGF,IAAIyR,aAAa,CAAC9B,MAAd,CAAqBzF,QAArB,CAA8B8F,IAA9B,CAAJ,EAAyC;gBACvC,IAAA,CAAKa,YAAL,CAAkB7Q,KAAlB;gBACA;;YAGF,MAAM,EAACyE,aAAAA,KAAiB+M,OAAO,CAACF,OAAhC;YACA,MAAMvB,kBAAkB,GAAGtL,aAAa,GACpC;gBAAC9B,CAAC,EAAE8B,aAAa,CAACjB,IAAlB;gBAAwBZ,CAAC,EAAE6B,aAAa,CAACf,GAAAA;aADL,GAEpClB,kBAFJ;YAIA,IAAI,CAAC,IAAA,CAAKiO,oBAAV,EAAgC;gBAC9B,IAAA,CAAKA,oBAAL,GAA4BV,kBAA5B;;YAGF,MAAM8B,cAAc,GAAGH,gBAAgB,CAAC1R,KAAD,EAAQ;gBAC7CM,MAD6C;gBAE7CkR,OAAO,EAAEA,OAAO,CAACF,OAF4B;gBAG7CvB;aAHqC,CAAvC;YAMA,IAAI8B,cAAJ,EAAoB;gBAClB,MAAMC,gBAAgB,OAAGC,2MAAmB,EAC1CF,cAD0C,EAE1C9B,kBAF0C,CAA5C;gBAIA,MAAMiC,WAAW,GAAG;oBAClBrP,CAAC,EAAE,CADe;oBAElBC,CAAC,EAAE;iBAFL;gBAIA,MAAM,EAACuK,mBAAAA,KAAuBqE,OAAO,CAACF,OAAtC;gBAEA,KAAK,MAAM/E,eAAX,IAA8BY,mBAA9B,CAAmD;oBACjD,MAAMR,SAAS,GAAG3M,KAAK,CAACgQ,IAAxB;oBACA,MAAM,EAAC/D,KAAD,EAAQG,OAAR,EAAiBF,MAAjB,EAAyBC,QAAzB,EAAmCL,SAAnC,EAA8CJ,SAAAA,KAClDF,iBAAiB,CAACe,eAAD,CADnB;oBAEA,MAAM0F,iBAAiB,GAAGhF,oBAAoB,CAACV,eAAD,CAA9C;oBAEA,MAAM2F,kBAAkB,GAAG;wBACzBvP,CAAC,EAAEK,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACW,KAA3B,GACIgC,iBAAiB,CAAChM,KAAlB,GAA0BgM,iBAAiB,CAACxO,KAAlB,GAA0B,CADxD,GAEIwO,iBAAiB,CAAChM,KAHrB,EAIDjD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACW,KAA3B,GACIgC,iBAAiB,CAACzO,IADtB,GAEIyO,iBAAiB,CAACzO,IAAlB,GAAyByO,iBAAiB,CAACxO,KAAlB,GAA0B,CAHzD,EAIEoO,cAAc,CAAClP,CAJjB,CAJC,CADsB;wBAYzBC,CAAC,EAAEI,IAAI,CAACkD,GAAL,CACDyG,SAAS,KAAK2C,YAAY,CAACa,IAA3B,GACI8B,iBAAiB,CAAC9L,MAAlB,GAA2B8L,iBAAiB,CAACtO,MAAlB,GAA2B,CAD1D,GAEIsO,iBAAiB,CAAC9L,MAHrB,EAIDnD,IAAI,CAACgD,GAAL,CACE2G,SAAS,KAAK2C,YAAY,CAACa,IAA3B,GACI8B,iBAAiB,CAACvO,GADtB,GAEIuO,iBAAiB,CAACvO,GAAlB,GAAwBuO,iBAAiB,CAACtO,MAAlB,GAA2B,CAHzD,EAIEkO,cAAc,CAACjP,CAJjB,CAJC;qBAZL;oBAyBA,MAAMuP,UAAU,GACbxF,SAAS,KAAK2C,YAAY,CAACW,KAA3B,IAAoC,CAAC7D,OAAtC,IACCO,SAAS,KAAK2C,YAAY,CAACY,IAA3B,IAAmC,CAAChE,MAFvC;oBAGA,MAAMkG,UAAU,GACbzF,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IAAmC,CAAChE,QAArC,IACCQ,SAAS,KAAK2C,YAAY,CAACc,EAA3B,IAAiC,CAACnE,KAFrC;oBAIA,IAAIkG,UAAU,IAAID,kBAAkB,CAACvP,CAAnB,KAAyBkP,cAAc,CAAClP,CAA1D,EAA6D;wBAC3D,MAAM0P,oBAAoB,GACxB9F,eAAe,CAACvB,UAAhB,GAA6B8G,gBAAgB,CAACnP,CADhD;wBAEA,MAAM2P,yBAAyB,GAC5B3F,SAAS,KAAK2C,YAAY,CAACW,KAA3B,IACCoC,oBAAoB,IAAIvG,SAAS,CAACnJ,CADpC,IAECgK,SAAS,KAAK2C,YAAY,CAACY,IAA3B,IACCmC,oBAAoB,IAAI3G,SAAS,CAAC/I,CAJtC;wBAMA,IAAI2P,yBAAyB,IAAI,CAACR,gBAAgB,CAAClP,CAAnD,EAAsD;;;4BAGpD2J,eAAe,CAACgG,QAAhB,CAAyB;gCACvB/O,IAAI,EAAE6O,oBADiB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACrP,CAAZ,GAAgB4J,eAAe,CAACvB,UAAhB,GAA6BqH,oBAA7C;yBADF,MAEO;4BACLL,WAAW,CAACrP,CAAZ,GACEgK,SAAS,KAAK2C,YAAY,CAACW,KAA3B,GACI1D,eAAe,CAACvB,UAAhB,GAA6Bc,SAAS,CAACnJ,CAD3C,GAEI4J,eAAe,CAACvB,UAAhB,GAA6BU,SAAS,CAAC/I,CAH7C;;wBAMF,IAAIqP,WAAW,CAACrP,CAAhB,EAAmB;4BACjB4J,eAAe,CAACkG,QAAhB,CAAyB;gCACvBjP,IAAI,EAAE,CAACwO,WAAW,CAACrP,CADI;gCAEvB6P,QAAQ,EAAEb;6BAFZ;;wBAKF;qBAlCF,MAmCO,IAAIS,UAAU,IAAIF,kBAAkB,CAACtP,CAAnB,KAAyBiP,cAAc,CAACjP,CAA1D,EAA6D;wBAClE,MAAMyP,oBAAoB,GACxB9F,eAAe,CAACpB,SAAhB,GAA4B2G,gBAAgB,CAAClP,CAD/C;wBAEA,MAAM0P,yBAAyB,GAC5B3F,SAAS,KAAK2C,YAAY,CAACa,IAA3B,IACCkC,oBAAoB,IAAIvG,SAAS,CAAClJ,CADpC,IAEC+J,SAAS,KAAK2C,YAAY,CAACc,EAA3B,IACCiC,oBAAoB,IAAI3G,SAAS,CAAC9I,CAJtC;wBAMA,IAAI0P,yBAAyB,IAAI,CAACR,gBAAgB,CAACnP,CAAnD,EAAsD;;;4BAGpD4J,eAAe,CAACgG,QAAhB,CAAyB;gCACvB7O,GAAG,EAAE2O,oBADkB;gCAEvBG,QAAQ,EAAEb;6BAFZ;4BAIA;;wBAGF,IAAIW,yBAAJ,EAA+B;4BAC7BN,WAAW,CAACpP,CAAZ,GAAgB2J,eAAe,CAACpB,SAAhB,GAA4BkH,oBAA5C;yBADF,MAEO;4BACLL,WAAW,CAACpP,CAAZ,GACE+J,SAAS,KAAK2C,YAAY,CAACa,IAA3B,GACI5D,eAAe,CAACpB,SAAhB,GAA4BW,SAAS,CAAClJ,CAD1C,GAEI2J,eAAe,CAACpB,SAAhB,GAA4BO,SAAS,CAAC9I,CAH5C;;wBAMF,IAAIoP,WAAW,CAACpP,CAAhB,EAAmB;4BACjB2J,eAAe,CAACkG,QAAhB,CAAyB;gCACvB/O,GAAG,EAAE,CAACsO,WAAW,CAACpP,CADK;gCAEvB4P,QAAQ,EAAEb;6BAFZ;;wBAMF;;;gBAIJ,IAAA,CAAKe,UAAL,CACE1S,KADF,MAEE2S,sMAAsB,MACpBZ,2MAAmB,EAACF,cAAD,EAAiB,IAAA,CAAKpB,oBAAtB,CADC,EAEpBuB,WAFoB,CAFxB;;;;IAWEU,UAAU,CAAC1S,KAAD,EAAe4S,WAAf,EAAA;QAChB,MAAM,EAACC,MAAAA,KAAU,IAAA,CAAKtC,KAAtB;QAEAvQ,KAAK,CAACoP,cAAN;QACAyD,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,CAAC5R,KAAD,EAAA;QACf,MAAM,EAAC8S,KAAAA,KAAS,IAAA,CAAKvC,KAArB;QAEAvQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK2D,MAAL;QACAD,KAAK;;IAGCjC,YAAY,CAAC7Q,KAAD,EAAA;QAClB,MAAM,EAACgT,QAAAA,KAAY,IAAA,CAAKzC,KAAxB;QAEAvQ,KAAK,CAACoP,cAAN;QACA,IAAA,CAAK2D,MAAL;QACAC,QAAQ;;IAGFD,MAAM,GAAA;QACZ,IAAA,CAAKvT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKmC,eAAL,CAAqBnC,SAArB;;;AA1OS+B,eA6OJ2C,UAAAA,GAAgD;IACrD;QACExE,SAAS,EAAE,WADb;QAEEC,OAAO,EAAE,CACP1O,KADO,EAAA,MAAA;gBAEP,EAACyR,aAAa,GAAGlC,oBAAjB,EAAuC2D,YAAAA;gBACvC,EAAC5S,MAAAA;YAED,MAAM,EAAC0P,IAAAA,KAAQhQ,KAAK,CAACmT,WAArB;YAEA,IAAI1B,aAAa,CAACjC,KAAd,CAAoBtF,QAApB,CAA6B8F,IAA7B,CAAJ,EAAwC;gBACtC,MAAMoD,SAAS,GAAG9S,MAAM,CAAC+S,aAAP,CAAqB/B,OAAvC;gBAEA,IAAI8B,SAAS,IAAIpT,KAAK,CAAC+F,MAAN,KAAiBqN,SAAlC,EAA6C;oBAC3C,OAAO,KAAP;;gBAGFpT,KAAK,CAACoP,cAAN;gBAEA8D,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;oBAAClT,KAAK,EAAEA,KAAK,CAACmT,WAAAA;iBAAjB,CAAZ;gBAEA,OAAO,IAAP;;YAGF,OAAO,KAAP;;IAvBJ,CADqD;CAAA;AC5OzD,SAASG,oBAAT,CACEC,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,cAAcA,UAA7B,CAAd;AACD;AAED,SAASE,iBAAT,CACEF,UADF;IAGE,OAAOC,OAAO,CAACD,UAAU,IAAI,WAAWA,UAA1B,CAAd;AACD;AASD,MAAaG;IAUX9F,YACU2C,KAAAA,EACAoD,MAAAA,EACRC,cAAAA,CAAAA;;YAAAA,mBAAAA,KAAAA,GAAAA;YAAAA,iBAAiBhF,sBAAsB,CAAC2B,KAAK,CAACvQ,KAAN,CAAY+F,MAAb;;aAF/BwK,KAAAA,GAAAA,KAAAA;aACAoD,MAAAA,GAAAA,KAAAA;aAXHnD,iBAAAA,GAAoB;aACnBjF,QAAAA,GAAAA,KAAAA;aACAsI,SAAAA,GAAqB;aACrBC,kBAAAA,GAAAA,KAAAA;aACAC,SAAAA,GAAmC;aACnCvU,SAAAA,GAAAA,KAAAA;aACAwU,iBAAAA,GAAAA,KAAAA;aACAtD,eAAAA,GAAAA,KAAAA;QAGE,IAAA,CAAA,KAAA,GAAAH,KAAA;QACA,IAAA,CAAA,MAAA,GAAAoD,MAAA;QAGR,MAAM,EAAC3T,KAAAA,KAASuQ,KAAhB;QACA,MAAM,EAACxK,MAAAA,KAAU/F,KAAjB;QAEA,IAAA,CAAKuQ,KAAL,GAAaA,KAAb;QACA,IAAA,CAAKoD,MAAL,GAAcA,MAAd;QACA,IAAA,CAAKpI,QAAL,OAAgBX,mNAAgB,EAAC7E,MAAD,CAAhC;QACA,IAAA,CAAKiO,iBAAL,GAAyB,IAAI1F,SAAJ,CAAc,IAAA,CAAK/C,QAAnB,CAAzB;QACA,IAAA,CAAK/L,SAAL,GAAiB,IAAI8O,SAAJ,CAAcsF,cAAd,CAAjB;QACA,IAAA,CAAKlD,eAAL,GAAuB,IAAIpC,SAAJ,KAAczF,4MAAS,EAAC9C,MAAD,CAAvB,CAAvB;QACA,IAAA,CAAK+N,kBAAL,GAAA,CAAA,2BAA0BxQ,sNAAmB,EAACtD,KAAD,CAA7C,KAAA,OAAA,uBAAwDwC,kBAAxD;QACA,IAAA,CAAKuO,WAAL,GAAmB,IAAA,CAAKA,WAAL,CAAiBH,IAAjB,CAAsB,IAAtB,CAAnB;QACA,IAAA,CAAK8B,UAAL,GAAkB,IAAA,CAAKA,UAAL,CAAgB9B,IAAhB,CAAqB,IAArB,CAAlB;QACA,IAAA,CAAKgB,SAAL,GAAiB,IAAA,CAAKA,SAAL,CAAehB,IAAf,CAAoB,IAApB,CAAjB;QACA,IAAA,CAAKC,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkBD,IAAlB,CAAuB,IAAvB,CAApB;QACA,IAAA,CAAKqD,aAAL,GAAqB,IAAA,CAAKA,aAAL,CAAmBrD,IAAnB,CAAwB,IAAxB,CAArB;QACA,IAAA,CAAKsD,mBAAL,GAA2B,IAAA,CAAKA,mBAAL,CAAyBtD,IAAzB,CAA8B,IAA9B,CAA3B;QAEA,IAAA,CAAKE,MAAL;;IAGMA,MAAM,GAAA;QACZ,MAAM,EACJ6C,MADI,EAEJpD,KAAK,EAAE,EACLnO,OAAO,EAAE,EAAC+R,oBAAAA,SAEV,IALJ;QAOA,IAAA,CAAK3U,SAAL,CAAeI,GAAf,CAAmB+T,MAAM,CAACS,IAAP,CAAYC,IAA/B,EAAqC,IAAA,CAAK3B,UAA1C,EAAsD;YAAC4B,OAAO,EAAE;SAAhE;QACA,IAAA,CAAK9U,SAAL,CAAeI,GAAf,CAAmB+T,MAAM,CAAC9D,GAAP,CAAWwE,IAA9B,EAAoC,IAAA,CAAKzC,SAAzC;QACA,IAAA,CAAKlB,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAAC6B,MAAnC,EAA2C,IAAA,CAAKH,YAAhD;QACA,IAAA,CAAKH,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAACoF,SAAnC,EAA8CnF,cAA9C;QACA,IAAA,CAAKsB,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAAC8B,gBAAnC,EAAqD,IAAA,CAAKJ,YAA1D;QACA,IAAA,CAAKH,eAAL,CAAqB9Q,GAArB,CAAyBuP,SAAS,CAACqF,WAAnC,EAAgDpF,cAAhD;QACA,IAAA,CAAK4E,iBAAL,CAAuBpU,GAAvB,CAA2BuP,SAAS,CAACgC,OAArC,EAA8C,IAAA,CAAK8C,aAAnD;QAEA,IAAIE,oBAAJ,EAA0B;YACxB,IAAIb,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C;;YAGF,IAAIV,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAA,CAAKJ,SAAL,GAAiB7C,UAAU,CACzB,IAAA,CAAKH,WADoB,EAEzBoD,oBAAoB,CAACM,KAFI,CAA3B;gBAIA;;;QAIJ,IAAA,CAAK1D,WAAL;;IAGMgC,MAAM,GAAA;QACZ,IAAA,CAAKvT,SAAL,CAAe+O,SAAf;QACA,IAAA,CAAKmC,eAAL,CAAqBnC,SAArB,IAAA,oEAAA;;QAIA2C,UAAU,CAAC,IAAA,CAAK8C,iBAAL,CAAuBzF,SAAxB,EAAmC,EAAnC,CAAV;QAEA,IAAI,IAAA,CAAKwF,SAAL,KAAmB,IAAvB,EAA6B;YAC3BW,YAAY,CAAC,IAAA,CAAKX,SAAN,CAAZ;YACA,IAAA,CAAKA,SAAL,GAAiB,IAAjB;;;IAIIhD,WAAW,GAAA;QACjB,MAAM,EAAC+C,kBAAAA,KAAsB,IAA7B;QACA,MAAM,EAACzC,OAAAA,KAAW,IAAA,CAAKd,KAAvB;QAEA,IAAIuD,kBAAJ,EAAwB;YACtB,IAAA,CAAKD,SAAL,GAAiB,IAAjB,CADsB,CAAA,uEAAA;YAItB,IAAA,CAAKG,iBAAL,CAAuBpU,GAAvB,CAA2BuP,SAAS,CAACwF,KAArC,EAA4CtF,eAA5C,EAA6D;gBAC3DuF,OAAO,EAAE;aADX,EAJsB,CAAA,8CAAA;YAStB,IAAA,CAAKV,mBAAL,GATsB,CAAA,gDAAA;YAYtB,IAAA,CAAKF,iBAAL,CAAuBpU,GAAvB,CACEuP,SAAS,CAAC0F,eADZ,EAEE,IAAA,CAAKX,mBAFP;YAKA7C,OAAO,CAACyC,kBAAD,CAAP;;;IAIIpB,UAAU,CAAC1S,KAAD,EAAA;;QAChB,MAAM,EAAC6T,SAAD,EAAYC,kBAAZ,EAAgCvD,KAAAA,KAAS,IAA/C;QACA,MAAM,EACJsC,MADI,EAEJzQ,OAAO,EAAE,EAAC+R,oBAAAA,OACR5D,KAHJ;QAKA,IAAI,CAACuD,kBAAL,EAAyB;YACvB;;QAGF,MAAMlB,WAAW,GAAA,CAAA,4BAAGtP,sNAAmB,EAACtD,KAAD,CAAtB,KAAA,OAAA,wBAAiCwC,kBAAlD;QACA,MAAMuM,KAAK,OAAGgD,2MAAmB,EAAC+B,kBAAD,EAAqBlB,WAArB,CAAjC;QAEA,IAAI,CAACiB,SAAD,IAAcM,oBAAlB,EAAwC;;YAEtC,IAAIV,iBAAiB,CAACU,oBAAD,CAArB,EAA6C;gBAC3C,IAAIrF,mBAAmB,CAACC,KAAD,EAAQoF,oBAAoB,CAACW,SAA7B,CAAvB,EAAgE;oBAC9D,OAAO,IAAA,CAAKjE,YAAL,EAAP;;gBAGF;;YAGF,IAAIyC,oBAAoB,CAACa,oBAAD,CAAxB,EAAgD;gBAC9C,IACEA,oBAAoB,CAACW,SAArB,IAAkC,IAAlC,IACAhG,mBAAmB,CAACC,KAAD,EAAQoF,oBAAoB,CAACW,SAA7B,CAFrB,EAGE;oBACA,OAAO,IAAA,CAAKjE,YAAL,EAAP;;gBAEF,IAAI/B,mBAAmB,CAACC,KAAD,EAAQoF,oBAAoB,CAACY,QAA7B,CAAvB,EAA+D;oBAC7D,OAAO,IAAA,CAAKhE,WAAL,EAAP;;gBAGF;;;QAIJ,IAAI/Q,KAAK,CAACgV,UAAV,EAAsB;YACpBhV,KAAK,CAACoP,cAAN;;QAGFyD,MAAM,CAACD,WAAD,CAAN;;IAGMhB,SAAS,GAAA;QACf,MAAM,EAACkB,KAAAA,KAAS,IAAA,CAAKvC,KAArB;QAEA,IAAA,CAAKwC,MAAL;QACAD,KAAK;;IAGCjC,YAAY,GAAA;QAClB,MAAM,EAACmC,QAAAA,KAAY,IAAA,CAAKzC,KAAxB;QAEA,IAAA,CAAKwC,MAAL;QACAC,QAAQ;;IAGFiB,aAAa,CAACjU,KAAD,EAAA;QACnB,IAAIA,KAAK,CAACgQ,IAAN,KAAeV,YAAY,CAACM,GAAhC,EAAqC;YACnC,IAAA,CAAKiB,YAAL;;;IAIIqD,mBAAmB,GAAA;;QACzB,CAAA,wBAAA,IAAA,CAAK3I,QAAL,CAAc0J,YAAd,EAAA,KAAA,OAAA,KAAA,IAAA,sBAA8BC,eAA9B;;;AC/NJ,MAAMvB,MAAM,GAAyB;IACnCS,IAAI,EAAE;QAACC,IAAI,EAAE;KADsB;IAEnCxE,GAAG,EAAE;QAACwE,IAAI,EAAE;;AAFuB,CAArC;AASA,MAAac,sBAAsBzB;IACjC9F,YAAY2C,KAAAA,CAAAA;QACV,MAAM,EAACvQ,KAAAA,KAASuQ,KAAhB,EAAA,uEAAA;;QAGA,MAAMqD,cAAc,OAAGhJ,mNAAgB,EAAC5K,KAAK,CAAC+F,MAAP,CAAvC;QAEA,KAAA,CAAMwK,KAAN,EAAaoD,MAAb,EAAqBC,cAArB;;;AAPSuB,cAUJlC,UAAAA,GAAa;IAClB;QACExE,SAAS,EAAE,eADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAACyE,WAAW,EAAEnT,KAAAA;gBACd,EAACkT,YAAAA;YAED,IAAI,CAAClT,KAAK,CAACoV,SAAP,IAAoBpV,KAAK,CAACqV,MAAN,KAAiB,CAAzC,EAA4C;gBAC1C,OAAO,KAAP;;YAGFnC,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAAClT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;ACnBtB,MAAM2T,QAAM,GAAyB;IACnCS,IAAI,EAAE;QAACC,IAAI,EAAE;KADsB;IAEnCxE,GAAG,EAAE;QAACwE,IAAI,EAAE;;AAFuB,CAArC;AAKA,IAAKiB,WAAL;AAAA,CAAA,SAAKA,WAAAA;IACHA,WAAAA,CAAAA,WAAAA,CAAAA,aAAAA,GAAAA,EAAA,GAAA,YAAA;AACD,CAFD,EAAKA,WAAW,IAAA,CAAXA,WAAW,GAAA,CAAA,CAAA,CAAhB;AAQA,MAAaC,oBAAoB7B;IAC/B9F,YAAY2C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb,MAAqB/I,mNAAgB,EAAC2F,KAAK,CAACvQ,KAAN,CAAY+F,MAAb,CAArC;;;AAFSwP,YAKJtC,UAAAA,GAAa;IAClB;QACExE,SAAS,EAAE,aADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAACyE,WAAW,EAAEnT,KAAAA;gBACd,EAACkT,YAAAA;YAED,IAAIlT,KAAK,CAACqV,MAAN,KAAiBC,WAAW,CAACE,UAAjC,EAA6C;gBAC3C,OAAO,KAAP;;YAGFtC,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAAClT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAZJ,CADkB;CAAA;AClBtB,MAAM2T,QAAM,GAAyB;IACnCS,IAAI,EAAE;QAACC,IAAI,EAAE;KADsB;IAEnCxE,GAAG,EAAE;QAACwE,IAAI,EAAE;;AAFuB,CAArC;AASA,MAAaoB,oBAAoB/B;IAC/B9F,YAAY2C,KAAAA,CAAAA;QACV,KAAA,CAAMA,KAAN,EAAaoD,QAAb;;IAuBU,OAAL+B,KAAK,GAAA;;;;QAIV7K,MAAM,CAAC8D,gBAAP,CAAwBgF,QAAM,CAACS,IAAP,CAAYC,IAApC,EAA0CpS,IAA1C,EAAgD;YAC9C2S,OAAO,EAAE,KADqC;YAE9CN,OAAO,EAAE;SAFX;QAKA,OAAO,SAASqB,QAAT;YACL9K,MAAM,CAAC2D,mBAAP,CAA2BmF,QAAM,CAACS,IAAP,CAAYC,IAAvC,EAA6CpS,IAA7C;SADF,EAAA,0EAAA;;;;QAMA,SAASA,IAAT,IAAA;;;AAxCSwT,YAKJxC,UAAAA,GAAa;IAClB;QACExE,SAAS,EAAE,cADb;QAEEC,OAAO,EAAE,CAAA,MAAA;gBACP,EAACyE,WAAW,EAAEnT,KAAAA;gBACd,EAACkT,YAAAA;YAED,MAAM,EAAC0C,OAAAA,KAAW5V,KAAlB;YAEA,IAAI4V,OAAO,CAACvR,MAAR,GAAiB,CAArB,EAAwB;gBACtB,OAAO,KAAP;;YAGF6O,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAG;gBAAClT;aAAJ,CAAZ;YAEA,OAAO,IAAP;;IAdJ,CADkB;CAAA;ICfV6V,mBAAZ;AAAA,CAAA,SAAYA,mBAAAA;IACVA,mBAAAA,CAAAA,mBAAAA,CAAAA,UAAAA,GAAAA,EAAA,GAAA,SAAA;IACAA,mBAAAA,CAAAA,mBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAHD,EAAYA,mBAAmB,IAAA,CAAnBA,mBAAmB,GAAA,CAAA,CAAA,CAA/B;AAmCA,IAAYC,cAAZ;AAAA,CAAA,SAAYA,cAAAA;IACVA,cAAAA,CAAAA,cAAAA,CAAAA,YAAAA,GAAAA,EAAA,GAAA,WAAA;IACAA,cAAAA,CAAAA,cAAAA,CAAAA,oBAAAA,GAAAA,EAAA,GAAA,mBAAA;AACD,CAHD,EAAYA,cAAc,IAAA,CAAdA,cAAc,GAAA,CAAA,CAAA,CAA1B;AAUA,SAAgBC,gBAAAA,IAAAA;QAAgB,EAC9BtJ,YAD8B,EAE9B2G,SAAS,GAAGyC,mBAAmB,CAACG,OAFF,EAG9BC,SAH8B,EAI9BC,YAJ8B,EAK9BC,OAL8B,EAM9BC,QAAQ,GAAG,CANmB,EAO9BC,KAAK,GAAGP,cAAc,CAACQ,SAPO,EAQ9B1P,kBAR8B,EAS9BuG,mBAT8B,EAU9BoJ,uBAV8B,EAW9BxH,KAX8B,EAY9BlC,SAAAA;IAEA,MAAM2J,YAAY,GAAGC,eAAe,CAAC;QAAC1H,KAAD;QAAQ2H,QAAQ,EAAE,CAACP;KAApB,CAApC;IACA,MAAM,CAACQ,qBAAD,EAAwBC,uBAAxB,CAAA,OAAmDC,8MAAW,EAApE;IACA,MAAMC,WAAW,OAAGC,yOAAM,EAAc;QAACpU,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAAxB,CAA1B;IACA,MAAMoU,eAAe,OAAGD,yOAAM,EAAkB;QAACpU,CAAC,EAAE,CAAJ;QAAOC,CAAC,EAAE;KAA5B,CAA9B;IACA,MAAMQ,IAAI,OAAG5B,0OAAO,EAAC;QACnB,OAAQ4R,SAAR;YACE,KAAKyC,mBAAmB,CAACG,OAAzB;gBACE,OAAOpP,kBAAkB,GACrB;oBACElD,GAAG,EAAEkD,kBAAkB,CAAChE,CAD1B;oBAEEuD,MAAM,EAAES,kBAAkB,CAAChE,CAF7B;oBAGEY,IAAI,EAAEoD,kBAAkB,CAACjE,CAH3B;oBAIEsD,KAAK,EAAEW,kBAAkB,CAACjE,CAAAA;iBALP,GAOrB,IAPJ;YAQF,KAAKkT,mBAAmB,CAACoB,aAAzB;gBACE,OAAOf,YAAP;;KAZc,EAcjB;QAAC9C,SAAD;QAAY8C,YAAZ;QAA0BtP,kBAA1B;KAdiB,CAApB;IAeA,MAAMsQ,kBAAkB,OAAGH,yOAAM,EAAiB,IAAjB,CAAjC;IACA,MAAMI,UAAU,OAAGxX,8OAAW,EAAC;QAC7B,MAAM4M,eAAe,GAAG2K,kBAAkB,CAAC5F,OAA3C;QAEA,IAAI,CAAC/E,eAAL,EAAsB;YACpB;;QAGF,MAAMvB,UAAU,GAAG8L,WAAW,CAACxF,OAAZ,CAAoB3O,CAApB,GAAwBqU,eAAe,CAAC1F,OAAhB,CAAwB3O,CAAnE;QACA,MAAMwI,SAAS,GAAG2L,WAAW,CAACxF,OAAZ,CAAoB1O,CAApB,GAAwBoU,eAAe,CAAC1F,OAAhB,CAAwB1O,CAAlE;QAEA2J,eAAe,CAACkG,QAAhB,CAAyBzH,UAAzB,EAAqCG,SAArC;KAV4B,EAW3B,EAX2B,CAA9B;IAYA,MAAMiM,yBAAyB,OAAG5V,0OAAO,EACvC,IACE6U,KAAK,KAAKP,cAAc,CAACQ,SAAzB,GACI,CAAC;eAAGnJ,mBAAJ;SAAA,CAAyBkK,OAAzB,EADJ,GAEIlK,mBAJiC,EAKvC;QAACkJ,KAAD;QAAQlJ,mBAAR;KALuC,CAAzC;QAQA/N,4OAAS,EACP;QACE,IAAI,CAAC+W,OAAD,IAAY,CAAChJ,mBAAmB,CAAC9I,MAAjC,IAA2C,CAACjB,IAAhD,EAAsD;YACpDwT,uBAAuB;YACvB;;QAGF,KAAK,MAAMrK,eAAX,IAA8B6K,yBAA9B,CAAyD;YACvD,IAAI,CAAAnB,SAAS,IAAA,IAAT,GAAA,KAAA,IAAAA,SAAS,CAAG1J,eAAH,CAAT,MAAiC,KAArC,EAA4C;gBAC1C;;YAGF,MAAM9G,KAAK,GAAG0H,mBAAmB,CAAC9E,OAApB,CAA4BkE,eAA5B,CAAd;YACA,MAAMC,mBAAmB,GAAG+J,uBAAuB,CAAC9Q,KAAD,CAAnD;YAEA,IAAI,CAAC+G,mBAAL,EAA0B;gBACxB;;YAGF,MAAM,EAACG,SAAD,EAAYC,KAAAA,KAASN,0BAA0B,CACnDC,eADmD,EAEnDC,mBAFmD,EAGnDpJ,IAHmD,EAInDqJ,YAJmD,EAKnDI,SALmD,CAArD;YAQA,KAAK,MAAMiB,IAAX,IAAmB;gBAAC,GAAD;gBAAM,GAAN;aAAnB,CAAwC;gBACtC,IAAI,CAAC0I,YAAY,CAAC1I,IAAD,CAAZ,CAAmBnB,SAAS,CAACmB,IAAD,CAA5B,CAAL,EAAuD;oBACrDlB,KAAK,CAACkB,IAAD,CAAL,GAAc,CAAd;oBACAnB,SAAS,CAACmB,IAAD,CAAT,GAAkB,CAAlB;;;YAIJ,IAAIlB,KAAK,CAACjK,CAAN,GAAU,CAAV,IAAeiK,KAAK,CAAChK,CAAN,GAAU,CAA7B,EAAgC;gBAC9BgU,uBAAuB;gBAEvBM,kBAAkB,CAAC5F,OAAnB,GAA6B/E,eAA7B;gBACAoK,qBAAqB,CAACQ,UAAD,EAAaf,QAAb,CAArB;gBAEAU,WAAW,CAACxF,OAAZ,GAAsB1E,KAAtB;gBACAoK,eAAe,CAAC1F,OAAhB,GAA0B3E,SAA1B;gBAEA;;;QAIJmK,WAAW,CAACxF,OAAZ,GAAsB;YAAC3O,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;SAAhC;QACAoU,eAAe,CAAC1F,OAAhB,GAA0B;YAAC3O,CAAC,EAAE,CAAJ;YAAOC,CAAC,EAAE;SAApC;QACAgU,uBAAuB;KAjDlB,EAoDP;QACEnK,YADF;QAEE0K,UAFF;QAGElB,SAHF;QAIEW,uBAJF;QAKET,OALF;QAMEC,QANF;QAQEkB,IAAI,CAACC,SAAL,CAAenU,IAAf,CARF;QAUEkU,IAAI,CAACC,SAAL,CAAef,YAAf,CAVF;QAWEG,qBAXF;QAYExJ,mBAZF;QAaEiK,yBAbF;QAcEb,uBAdF;QAgBEe,IAAI,CAACC,SAAL,CAAe1K,SAAf,CAhBF;KApDO,CAAT;AAuED;AAOD,MAAM2K,mBAAmB,GAAiB;IACxC7U,CAAC,EAAE;QAAC,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;KADd;IAExCpK,CAAC,EAAE;QAAC,CAACyI,SAAS,CAACyB,QAAX,CAAA,EAAsB,KAAvB;QAA8B,CAACzB,SAAS,CAAC2B,OAAX,CAAA,EAAqB;;AAFd,CAA1C;AAKA,SAASyJ,eAAT,CAAA,KAAA;QAAyB,EACvB1H,KADuB,EAEvB2H,QAAAA;IAKA,MAAMe,aAAa,OAAGC,8MAAW,EAAC3I,KAAD,CAAjC;IAEA,WAAO4I,8MAAW,GACfC,cAAD;QACE,IAAIlB,QAAQ,IAAI,CAACe,aAAb,IAA8B,CAACG,cAAnC,EAAmD;;YAEjD,OAAOJ,mBAAP;;QAGF,MAAM7K,SAAS,GAAG;YAChBhK,CAAC,EAAEK,IAAI,CAAC6U,IAAL,CAAU9I,KAAK,CAACpM,CAAN,GAAU8U,aAAa,CAAC9U,CAAlC,CADa;YAEhBC,CAAC,EAAEI,IAAI,CAAC6U,IAAL,CAAU9I,KAAK,CAACnM,CAAN,GAAU6U,aAAa,CAAC7U,CAAlC;SAFL,EAAA,0EAAA;QAMA,OAAO;YACLD,CAAC,EAAE;gBACD,CAAC0I,SAAS,CAACyB,QAAX,CAAA,EACE8K,cAAc,CAACjV,CAAf,CAAiB0I,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAChK,CAAV,KAAgB,CAAC,CAF1D;gBAGD,CAAC0I,SAAS,CAAC2B,OAAX,CAAA,EACE4K,cAAc,CAACjV,CAAf,CAAiB0I,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAChK,CAAV,KAAgB;aALtD;YAOLC,CAAC,EAAE;gBACD,CAACyI,SAAS,CAACyB,QAAX,CAAA,EACE8K,cAAc,CAAChV,CAAf,CAAiByI,SAAS,CAACyB,QAA3B,CAAA,IAAwCH,SAAS,CAAC/J,CAAV,KAAgB,CAAC,CAF1D;gBAGD,CAACyI,SAAS,CAAC2B,OAAX,CAAA,EACE4K,cAAc,CAAChV,CAAf,CAAiByI,SAAS,CAAC2B,OAA3B,CAAA,IAAuCL,SAAS,CAAC/J,CAAV,KAAgB;;SAX7D;KAbc,EA4BhB;QAAC8T,QAAD;QAAW3H,KAAX;QAAkB0I,aAAlB;KA5BgB,CAAlB;AA8BD;SCjOeK,cACdC,cAAAA,EACAxX,EAAAA;IAEA,MAAMyX,aAAa,GAAGzX,EAAE,KAAK,IAAP,GAAcwX,cAAc,CAACjT,GAAf,CAAmBvE,EAAnB,CAAd,GAAuC8P,SAA7D;IACA,MAAMjH,IAAI,GAAG4O,aAAa,GAAGA,aAAa,CAAC5O,IAAd,CAAmBkI,OAAtB,GAAgC,IAA1D;IAEA,WAAOqG,8MAAW,GACfM,UAAD;;QACE,IAAI1X,EAAE,KAAK,IAAX,EAAiB;YACf,OAAO,IAAP;;;;QAMF,OAAA,CAAA,OAAO6I,IAAP,IAAA,OAAOA,IAAP,GAAe6O,UAAf,KAAA,OAAA,OAA6B,IAA7B;KATc,EAWhB;QAAC7O,IAAD;QAAO7I,EAAP;KAXgB,CAAlB;AAaD;SCjBe2X,qBACd5V,OAAAA,EACA6V,mBAAAA;IAKA,WAAO3W,0OAAO,EACZ,IACEc,OAAO,CAACgD,MAAR,CAAmC,CAACC,WAAD,EAAcpD,MAAd;YACjC,MAAM,EAACA,MAAM,EAAEiW,MAAAA,KAAUjW,MAAzB;YAEA,MAAMkW,gBAAgB,GAAGD,MAAM,CAACnF,UAAP,CAAkBqF,GAAlB,EAAuBlF,SAAD,GAAA,CAAgB;oBAC7D3E,SAAS,EAAE2E,SAAS,CAAC3E,SADwC;oBAE7DC,OAAO,EAAEyJ,mBAAmB,CAAC/E,SAAS,CAAC1E,OAAX,EAAoBvM,MAApB;iBAFiB,CAAtB,CAAzB;YAKA,OAAO,CAAC;mBAAGoD,WAAJ,EAAiB;mBAAG8S,gBAApB;aAAP;SARF,EASG,EATH,CAFU,EAYZ;QAAC/V,OAAD;QAAU6V,mBAAV;KAZY,CAAd;AAcD;IChBWI,iBAAZ;AAAA,CAAA,SAAYA,iBAAAA;IACVA,iBAAAA,CAAAA,iBAAAA,CAAAA,SAAAA,GAAAA,EAAA,GAAA,QAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,iBAAAA,GAAAA,EAAA,GAAA,gBAAA;IACAA,iBAAAA,CAAAA,iBAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;AACD,CAJD,EAAYA,iBAAiB,IAAA,CAAjBA,iBAAiB,GAAA,CAAA,CAAA,CAA7B;AAMA,IAAYC,kBAAZ;AAAA,CAAA,SAAYA,kBAAAA;IACVA,kBAAAA,CAAAA,YAAA,GAAA,WAAA;AACD,CAFD,EAAYA,kBAAkB,IAAA,CAAlBA,kBAAkB,GAAA,CAAA,CAAA,CAA9B;AAYA,MAAMC,YAAY,GAAA,WAAA,GAAY,IAAIC,GAAJ,EAA9B;AAEA,SAAgBC,sBACdC,UAAAA,EAAAA,IAAAA;QACA,EAACC,QAAD,EAAWC,YAAX,EAAyBC,MAAAA;IAEzB,MAAM,CAACC,KAAD,EAAQC,QAAR,CAAA,OAAoBxZ,2OAAQ,EAA4B,IAA5B,CAAlC;IACA,MAAM,EAACyZ,SAAD,EAAY3L,OAAZ,EAAqB4L,QAAAA,KAAYJ,MAAvC;IACA,MAAMK,aAAa,OAAGrC,yOAAM,EAAC6B,UAAD,CAA5B;IACA,MAAMlC,QAAQ,GAAG2C,UAAU,EAA3B;IACA,MAAMC,WAAW,OAAGC,iNAAc,EAAC7C,QAAD,CAAlC;IACA,MAAM8C,0BAA0B,OAAG7Z,8OAAW,EAC5C,SAAC8Z,GAAD;YAACA,QAAAA,KAAAA,GAAAA;YAAAA,MAA0B,EAAA;;QACzB,IAAIH,WAAW,CAAChI,OAAhB,EAAyB;YACvB;;QAGF2H,QAAQ,EAAEpX,KAAD;YACP,IAAIA,KAAK,KAAK,IAAd,EAAoB;gBAClB,OAAO4X,GAAP;;YAGF,OAAO5X,KAAK,CAAC6X,MAAN,CAAaD,GAAG,CAAClX,MAAJ,EAAYhC,EAAD,GAAQ,CAACsB,KAAK,CAACqI,QAAN,CAAe3J,EAAf,CAApB,CAAb,CAAP;SALM,CAAR;KAN0C,EAc5C;QAAC+Y,WAAD;KAd4C,CAA9C;IAgBA,MAAMvF,SAAS,OAAGgD,yOAAM,EAAwB,IAAxB,CAAxB;IACA,MAAMrS,cAAc,OAAGiT,8MAAW,GAC/BgC,aAAD;QACE,IAAIjD,QAAQ,IAAI,CAACmC,QAAjB,EAA2B;YACzB,OAAOJ,YAAP;;QAGF,IACE,CAACkB,aAAD,IACAA,aAAa,KAAKlB,YADlB,IAEAW,aAAa,CAAC9H,OAAd,KAA0BsH,UAF1B,IAGAI,KAAK,IAAI,IAJX,EAKE;YACA,MAAMV,GAAG,GAAY,IAAII,GAAJ,EAArB;YAEA,KAAK,IAAI5X,SAAT,IAAsB8X,UAAtB,CAAkC;gBAChC,IAAI,CAAC9X,SAAL,EAAgB;oBACd;;gBAGF,IACEkY,KAAK,IACLA,KAAK,CAAC3U,MAAN,GAAe,CADf,IAEA,CAAC2U,KAAK,CAAC9O,QAAN,CAAepJ,SAAS,CAACP,EAAzB,CAFD,IAGAO,SAAS,CAACsC,IAAV,CAAekO,OAJjB,EAKE;;oBAEAgH,GAAG,CAACsB,GAAJ,CAAQ9Y,SAAS,CAACP,EAAlB,EAAsBO,SAAS,CAACsC,IAAV,CAAekO,OAArC;oBACA;;gBAGF,MAAMlI,IAAI,GAAGtI,SAAS,CAACsI,IAAV,CAAekI,OAA5B;gBACA,MAAMlO,IAAI,GAAGgG,IAAI,GAAG,IAAIuE,IAAJ,CAASJ,OAAO,CAACnE,IAAD,CAAhB,EAAwBA,IAAxB,CAAH,GAAmC,IAApD;gBAEAtI,SAAS,CAACsC,IAAV,CAAekO,OAAf,GAAyBlO,IAAzB;gBAEA,IAAIA,IAAJ,EAAU;oBACRkV,GAAG,CAACsB,GAAJ,CAAQ9Y,SAAS,CAACP,EAAlB,EAAsB6C,IAAtB;;;YAIJ,OAAOkV,GAAP;;QAGF,OAAOqB,aAAP;KA3C8B,EA6ChC;QAACf,UAAD;QAAaI,KAAb;QAAoBH,QAApB;QAA8BnC,QAA9B;QAAwCnJ,OAAxC;KA7CgC,CAAlC;QAgDAnO,4OAAS,EAAC;QACRga,aAAa,CAAC9H,OAAd,GAAwBsH,UAAxB;KADO,EAEN;QAACA,UAAD;KAFM,CAAT;QAIAxZ,4OAAS,EACP;QACE,IAAIsX,QAAJ,EAAc;YACZ;;QAGF8C,0BAA0B;KANrB,EASP;QAACX,QAAD;QAAWnC,QAAX;KATO,CAAT;QAYAtX,4OAAS,EACP;QACE,IAAI4Z,KAAK,IAAIA,KAAK,CAAC3U,MAAN,GAAe,CAA5B,EAA+B;YAC7B4U,QAAQ,CAAC,IAAD,CAAR;;KAHG,EAOP;QAAC3B,IAAI,CAACC,SAAL,CAAeyB,KAAf,CAAD;KAPO,CAAT;QAUA5Z,4OAAS,EACP;QACE,IACEsX,QAAQ,IACR,OAAOwC,SAAP,KAAqB,QADrB,IAEAnF,SAAS,CAACzC,OAAV,KAAsB,IAHxB,EAIE;YACA;;QAGFyC,SAAS,CAACzC,OAAV,GAAoBJ,UAAU,CAAC;YAC7BsI,0BAA0B;YAC1BzF,SAAS,CAACzC,OAAV,GAAoB,IAApB;SAF4B,EAG3B4H,SAH2B,CAA9B;KAVK,EAgBP;QAACA,SAAD;QAAYxC,QAAZ;QAAsB8C,0BAAtB,EAAkD;WAAGV,YAArD;KAhBO,CAAT;IAmBA,OAAO;QACLpU,cADK;QAEL8U,0BAFK;QAGLK,kBAAkB,EAAEb,KAAK,IAAI;KAH/B;;;IAMA,SAASK,UAAT;QACE,OAAQF,QAAR;YACE,KAAKZ,iBAAiB,CAACuB,MAAvB;gBACE,OAAO,KAAP;YACF,KAAKvB,iBAAiB,CAACwB,cAAvB;gBACE,OAAOlB,QAAP;YACF;gBACE,OAAO,CAACA,QAAR;;;AAGP;SCpKemB,gBAIdnY,KAAAA,EACAoY,SAAAA;IAEA,WAAOtC,8MAAW,GACfgC,aAAD;QACE,IAAI,CAAC9X,KAAL,EAAY;YACV,OAAO,IAAP;;QAGF,IAAI8X,aAAJ,EAAmB;YACjB,OAAOA,aAAP;;QAGF,OAAO,OAAOM,SAAP,KAAqB,UAArB,GAAkCA,SAAS,CAACpY,KAAD,CAA3C,GAAqDA,KAA5D;KAVc,EAYhB;QAACoY,SAAD;QAAYpY,KAAZ;KAZgB,CAAlB;AAcD;SCtBeqY,eACd9Q,IAAAA,EACAmE,OAAAA;IAEA,OAAOyM,eAAe,CAAC5Q,IAAD,EAAOmE,OAAP,CAAtB;AACD;ACAD;;;IAIA,SAAgB4M,oBAAAA,IAAAA;QAAoB,EAACC,QAAD,EAAW1D,QAAAA;IAC7C,MAAM2D,eAAe,OAAGC,2MAAQ,EAACF,QAAD,CAAhC;IACA,MAAMG,gBAAgB,OAAG/Y,0OAAO,EAAC;QAC/B,IACEkV,QAAQ,IACR,OAAO7L,MAAP,KAAkB,MAElB,KAHA,IAEA,OAAOA,MAAM,CAAC2P,gBAAd,KAAmC,WAHrC;YAKE,OAAOnK,SAAP;;;;QAGF,MAAM,AAACmK;KATuB,EAY7B;QAACH,eAAD;QAAkB3D,QAAlB;KAZ6B,CAAhC;QAcAtX,4OAAS,EAAC;QACR,OAAO,IAAMmb,gBAAN,IAAA,OAAA,KAAA,IAAMA,gBAAgB,CAAEE,UAAlB,EAAb;KADO,EAEN;QAACF,gBAAD;KAFM,CAAT;IAIA,OAAOA,gBAAP;AACD;ACzBD;;;IAIA,SAAgBG,kBAAAA,IAAAA;QAAkB,EAACN,QAAD,EAAW1D,QAAAA;IAC3C,MAAMiE,YAAY,OAAGL,2MAAQ,EAACF,QAAD,CAA7B;IACA,MAAMQ,cAAc,OAAGpZ,0OAAO,EAC5B;QACE,IACEkV,QAAQ,IACR,OAAO7L,MAAP,KAAkB,MAElB,KAHA,IAEA,OAAOA,MAAM,CAACgQ,cAAd,KAAiC,WAHnC;YAKE,OAAOxK,SAAP;;;;QAGF,MAAM,AAACwK;KAVmB,EAe5B;QAACnE,QAAD;KAf4B,CAA9B;QAkBAtX,4OAAS,EAAC;QACR,OAAO,IAAMwb,cAAN,IAAA,OAAA,KAAA,IAAMA,cAAc,CAAEH,UAAhB,EAAb;KADO,EAEN;QAACG,cAAD;KAFM,CAAT;IAIA,OAAOA,cAAP;AACD;AC5BD,SAASE,cAAT,CAAwBnS,OAAxB;IACE,OAAO,IAAIgF,IAAJ,CAASjF,aAAa,CAACC,OAAD,CAAtB,EAAiCA,OAAjC,CAAP;AACD;AAED,SAAgBoS,QACdpS,OAAAA,EACA4E,OAAAA,EACAyN,YAAAA;QADAzN,YAAAA,KAAAA,GAAAA;QAAAA,UAAgDuN;;IAGhD,MAAM,CAAC1X,IAAD,EAAO6X,WAAP,CAAA,OAAsBC,6OAAU,EAACC,OAAD,EAAU,IAAV,CAAtC;IAEA,MAAMZ,gBAAgB,GAAGJ,mBAAmB,CAAC;QAC3CC,QAAQ,EAACgB,OAAD;YACN,IAAI,CAACzS,OAAL,EAAc;gBACZ;;YAGF,KAAK,MAAM0S,MAAX,IAAqBD,OAArB,CAA8B;gBAC5B,MAAM,EAACrb,IAAD,EAAOgG,MAAAA,KAAUsV,MAAvB;gBAEA,IACEtb,IAAI,KAAK,WAAT,IACAgG,MAAM,YAAYuV,WADlB,IAEAvV,MAAM,CAACwV,QAAP,CAAgB5S,OAAhB,CAHF,EAIE;oBACAsS,WAAW;oBACX;;;;KAfoC,CAA5C;IAoBA,MAAML,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEa;KAAZ,CAAxC;QAEAO,4NAAyB,EAAC;QACxBP,WAAW;QAEX,IAAItS,OAAJ,EAAa;YACXiS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEa,OAAhB,CAAwB9S,OAAxB;YACA4R,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEkB,OAAlB,CAA0BlQ,QAAQ,CAACmQ,IAAnC,EAAyC;gBACvCC,SAAS,EAAE,IAD4B;gBAEvCC,OAAO,EAAE;aAFX;SAFF,MAMO;YACLhB,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;YACAF,gBAAgB,IAAA,IAAhB,GAAA,KAAA,IAAAA,gBAAgB,CAAEE,UAAlB;;KAXqB,EAatB;QAAC9R,OAAD;KAbsB,CAAzB;IAeA,OAAOvF,IAAP;;;IAEA,SAAS+X,OAAT,CAAiBU,WAAjB;QACE,IAAI,CAAClT,OAAL,EAAc;YACZ,OAAO,IAAP;;QAGF,IAAIA,OAAO,CAACmT,WAAR,KAAwB,KAA5B,EAAmC;YAAA,IAAA;;;YAGjC,OAAA,CAAA,OAAOD,WAAP,IAAA,OAAOA,WAAP,GAAsBb,YAAtB,KAAA,OAAA,OAAsC,IAAtC;;QAGF,MAAMe,OAAO,GAAGxO,OAAO,CAAC5E,OAAD,CAAvB;QAEA,IAAI2O,IAAI,CAACC,SAAL,CAAesE,WAAf,MAAgCvE,IAAI,CAACC,SAAL,CAAewE,OAAf,CAApC,EAA6D;YAC3D,OAAOF,WAAP;;QAGF,OAAOE,OAAP;;AAEH;SCzEeC,aAAa5Y,IAAAA;IAC3B,MAAM6Y,WAAW,GAAGjC,eAAe,CAAC5W,IAAD,CAAnC;IAEA,OAAO+D,YAAY,CAAC/D,IAAD,EAAO6Y,WAAP,CAAnB;AACD;ACJD,MAAMxD,cAAY,GAAc,EAAhC;AAEA,SAAgByD,uBAAuB9S,IAAAA;IACrC,MAAM+S,YAAY,OAAGpF,yOAAM,EAAC3N,IAAD,CAA3B;IAEA,MAAMgT,SAAS,OAAGzE,8MAAW,GAC1BgC,aAAD;QACE,IAAI,CAACvQ,IAAL,EAAW;YACT,OAAOqP,cAAP;;QAGF,IACEkB,aAAa,IACbA,aAAa,KAAKlB,cADlB,IAEArP,IAFA,IAGA+S,YAAY,CAAC7K,OAHb,IAIAlI,IAAI,CAACiB,UAAL,KAAoB8R,YAAY,CAAC7K,OAAb,CAAqBjH,UAL3C,EAME;YACA,OAAOsP,aAAP;;QAGF,OAAO/P,sBAAsB,CAACR,IAAD,CAA7B;KAhByB,EAkB3B;QAACA,IAAD;KAlB2B,CAA7B;QAqBAhK,4OAAS,EAAC;QACR+c,YAAY,CAAC7K,OAAb,GAAuBlI,IAAvB;KADO,EAEN;QAACA,IAAD;KAFM,CAAT;IAIA,OAAOgT,SAAP;AACD;SCvBeC,iBAAiBC,QAAAA;IAC/B,MAAM,CACJC,iBADI,EAEJC,oBAFI,CAAA,OAGF/c,2OAAQ,EAA2B,IAA3B,CAHZ;IAIA,MAAMgd,YAAY,OAAG1F,yOAAM,EAACuF,QAAD,CAA3B,EAAA,4CAAA;IAGA,MAAMI,YAAY,OAAG/c,8OAAW,GAAEK,KAAD;QAC/B,MAAMiK,gBAAgB,GAAGO,oBAAoB,CAACxK,KAAK,CAAC+F,MAAP,CAA7C;QAEA,IAAI,CAACkE,gBAAL,EAAuB;YACrB;;QAGFuS,oBAAoB,EAAED,iBAAD;YACnB,IAAI,CAACA,iBAAL,EAAwB;gBACtB,OAAO,IAAP;;YAGFA,iBAAiB,CAAC3C,GAAlB,CACE3P,gBADF,EAEEmB,oBAAoB,CAACnB,gBAAD,CAFtB;YAKA,OAAO,IAAIyO,GAAJ,CAAQ6D,iBAAR,CAAP;SAVkB,CAApB;KAP8B,EAmB7B,EAnB6B,CAAhC;QAqBAnd,4OAAS,EAAC;QACR,MAAMud,gBAAgB,GAAGF,YAAY,CAACnL,OAAtC;QAEA,IAAIgL,QAAQ,KAAKK,gBAAjB,EAAmC;YACjCC,OAAO,CAACD,gBAAD,CAAP;YAEA,MAAME,OAAO,GAAGP,QAAQ,CACrBhE,GADa,EACR3P,OAAD;gBACH,MAAMmU,iBAAiB,GAAGtS,oBAAoB,CAAC7B,OAAD,CAA9C;gBAEA,IAAImU,iBAAJ,EAAuB;oBACrBA,iBAAiB,CAACnO,gBAAlB,CAAmC,QAAnC,EAA6C+N,YAA7C,EAA2D;wBACzDpI,OAAO,EAAE;qBADX;oBAIA,OAAO;wBACLwI,iBADK;wBAEL1R,oBAAoB,CAAC0R,iBAAD,CAFf;qBAAP;;gBAMF,OAAO,IAAP;aAfY,EAiBbva,MAjBa,EAmBVuD,KADF,GAKKA,KAAK,IAAI,IAvBF,CAAhB;YA0BA0W,oBAAoB,CAACK,OAAO,CAACxY,MAAR,GAAiB,IAAIqU,GAAJ,CAAQmE,OAAR,CAAjB,GAAoC,IAArC,CAApB;YAEAJ,YAAY,CAACnL,OAAb,GAAuBgL,QAAvB;;QAGF,OAAO;YACLM,OAAO,CAACN,QAAD,CAAP;YACAM,OAAO,CAACD,gBAAD,CAAP;SAFF;;;QAKA,SAASC,OAAT,CAAiBN,QAAjB;YACEA,QAAQ,CAACrc,OAAT,EAAkB0I,OAAD;gBACf,MAAMmU,iBAAiB,GAAGtS,oBAAoB,CAAC7B,OAAD,CAA9C;gBAEAmU,iBAAiB,IAAA,IAAjB,GAAA,KAAA,IAAAA,iBAAiB,CAAEtO,mBAAnB,CAAuC,QAAvC,EAAiDkO,YAAjD;aAHF;;KA3CK,EAiDN;QAACA,YAAD;QAAeJ,QAAf;KAjDM,CAAT;IAmDA,WAAO9a,0OAAO,EAAC;QACb,IAAI8a,QAAQ,CAACjY,MAAb,EAAqB;YACnB,OAAOkY,iBAAiB,GACpBQ,KAAK,CAACC,IAAN,CAAWT,iBAAiB,CAACU,MAAlB,EAAX,EAAuC3X,MAAvC,CACE,CAACkC,GAAD,EAAMoL,WAAN,OAAsBhT,sMAAG,EAAC4H,GAAD,EAAMoL,WAAN,CAD3B,EAEEpQ,kBAFF,CADoB,GAKpB0K,gBAAgB,CAACoP,QAAD,CALpB;;QAQF,OAAO9Z,kBAAP;KAVY,EAWX;QAAC8Z,QAAD;QAAWC,iBAAX;KAXW,CAAd;AAYD;SCpGeW,sBACdrP,aAAAA,EACAiL,YAAAA;QAAAA,iBAAAA,KAAAA,GAAAA;QAAAA,eAAsB,EAAA;;IAEtB,MAAMqE,oBAAoB,OAAGpG,yOAAM,EAAqB,IAArB,CAAnC;QAEA3X,4OAAS,EACP;QACE+d,oBAAoB,CAAC7L,OAArB,GAA+B,IAA/B;KAFK,EAKPwH,YALO,CAAT;QAQA1Z,4OAAS,EAAC;QACR,MAAMge,gBAAgB,GAAGvP,aAAa,KAAKrL,kBAA3C;QAEA,IAAI4a,gBAAgB,IAAI,CAACD,oBAAoB,CAAC7L,OAA9C,EAAuD;YACrD6L,oBAAoB,CAAC7L,OAArB,GAA+BzD,aAA/B;;QAGF,IAAI,CAACuP,gBAAD,IAAqBD,oBAAoB,CAAC7L,OAA9C,EAAuD;YACrD6L,oBAAoB,CAAC7L,OAArB,GAA+B,IAA/B;;KARK,EAUN;QAACzD,aAAD;KAVM,CAAT;IAYA,OAAOsP,oBAAoB,CAAC7L,OAArB,OACH+L,2MAAQ,EAACxP,aAAD,EAAgBsP,oBAAoB,CAAC7L,OAArC,CADL,GAEH9O,kBAFJ;AAGD;SC7Be8a,eAAehb,OAAAA;QAC7BlD,4OAAS,EACP;QACE,IAAI,CAACqL,4MAAL,EAAgB;YACd;;QAGF,MAAM8S,WAAW,GAAGjb,OAAO,CAACgW,GAAR,EAAY;YAAA,IAAC,EAACnW,MAAAA,EAAF,GAAA;YAAA,OAAcA,MAAM,CAACuT,KAArB,IAAA,OAAA,KAAA,IAAcvT,MAAM,CAACuT,KAAP,EAAd;SAAZ,CAApB;QAEA,OAAO;YACL,KAAK,MAAMC,QAAX,IAAuB4H,WAAvB,CAAoC;gBAClC5H,QAAQ,IAAA,IAAR,GAAA,KAAA,IAAAA,QAAQ;;SAFZ;KARK;IAgBPrT,OAAO,CAACgW,GAAR,EAAY;QAAA,IAAC,EAACnW,MAAAA,EAAF,GAAA;QAAA,OAAcA,MAAd;KAAZ,CAhBO,CAAT;AAkBD;SCXeqb,sBACdhe,SAAAA,EACAe,EAAAA;IAEA,WAAOiB,0OAAO,EAAC;QACb,OAAOhC,SAAS,CAAC8F,MAAV,CACL,CAACkC,GAAD,EAAA;gBAAM,EAACiH,SAAD,EAAYC,OAAAA;YAChBlH,GAAG,CAACiH,SAAD,CAAH,IAAkBzO,KAAD;gBACf0O,OAAO,CAAC1O,KAAD,EAAQO,EAAR,CAAP;aADF;YAIA,OAAOiH,GAAP;SANG,EAQL,CAAA,CARK,CAAP;KADY,EAWX;QAAChI,SAAD;QAAYe,EAAZ;KAXW,CAAd;AAYD;SCzBekd,cAAc9U,OAAAA;IAC5B,WAAOnH,0OAAO,EAAC,IAAOmH,OAAO,GAAGK,mBAAmB,CAACL,OAAD,CAAtB,GAAkC,IAAjD,EAAwD;QACpEA,OADoE;KAAxD,CAAd;AAGD;ACED,MAAM8P,cAAY,GAAW,EAA7B;AAEA,SAAgBiF,SACdpB,QAAAA,EACA/O,OAAAA;QAAAA,YAAAA,KAAAA,GAAAA;QAAAA,UAA4C7E;;IAE5C,MAAM,CAACiV,YAAD,CAAA,GAAiBrB,QAAvB;IACA,MAAMsB,UAAU,GAAGH,aAAa,CAC9BE,YAAY,OAAG9U,4MAAS,EAAC8U,YAAD,CAAZ,GAA6B,IADX,CAAhC;IAGA,MAAM,CAACE,KAAD,EAAQC,YAAR,CAAA,OAAwB5C,6OAAU,EAACC,OAAD,EAAU1C,cAAV,CAAxC;IACA,MAAMmC,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAE0D;KAAZ,CAAxC;IAEA,IAAIxB,QAAQ,CAACjY,MAAT,GAAkB,CAAlB,IAAuBwZ,KAAK,KAAKpF,cAArC,EAAmD;QACjDqF,YAAY;;QAGdtC,4NAAyB,EAAC;QACxB,IAAIc,QAAQ,CAACjY,MAAb,EAAqB;YACnBiY,QAAQ,CAACrc,OAAT,EAAkB0I,OAAD,GAAaiS,cAAb,IAAA,OAAA,KAAA,IAAaA,cAAc,CAAEa,OAAhB,CAAwB9S,OAAxB,CAA9B;SADF,MAEO;YACLiS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;YACAqD,YAAY;;KALS,EAOtB;QAACxB,QAAD;KAPsB,CAAzB;IASA,OAAOuB,KAAP;;;IAEA,SAAS1C,OAAT;QACE,IAAI,CAACmB,QAAQ,CAACjY,MAAd,EAAsB;YACpB,OAAOoU,cAAP;;QAGF,OAAO6D,QAAQ,CAAChE,GAAT,EAAc3P,OAAD,GAClB2C,0BAA0B,CAAC3C,OAAD,CAA1B,GACKiV,UADL,GAEI,IAAIjQ,IAAJ,CAASJ,OAAO,CAAC5E,OAAD,CAAhB,EAA2BA,OAA3B,CAHC,CAAP;;AAMH;SC/CeoV,kBACd3U,IAAAA;IAEA,IAAI,CAACA,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,IAAIA,IAAI,CAAC4U,QAAL,CAAc3Z,MAAd,GAAuB,CAA3B,EAA8B;QAC5B,OAAO+E,IAAP;;IAEF,MAAM6U,UAAU,GAAG7U,IAAI,CAAC4U,QAAL,CAAc,CAAd,CAAnB;IAEA,WAAO7T,gNAAa,EAAC8T,UAAD,CAAb,GAA4BA,UAA5B,GAAyC7U,IAAhD;AACD;SCHe8U,wBAAAA,IAAAA;QAAwB,EACtC3Q,OAAAA;IAEA,MAAM,CAACnK,IAAD,EAAO+a,OAAP,CAAA,OAAkB1e,2OAAQ,EAAoB,IAApB,CAAhC;IACA,MAAMkb,YAAY,OAAGhb,8OAAW,GAC7Bkd,OAAD;QACE,KAAK,MAAM,EAAC9W,MAAAA,EAAZ,IAAuB8W,OAAvB,CAAgC;YAC9B,QAAI1S,gNAAa,EAACpE,MAAD,CAAjB,EAA2B;gBACzBoY,OAAO,EAAE/a,IAAD;oBACN,MAAM2Y,OAAO,GAAGxO,OAAO,CAACxH,MAAD,CAAvB;oBAEA,OAAO3C,IAAI,GACP;wBAAC,GAAGA,IAAJ;wBAAUK,KAAK,EAAEsY,OAAO,CAACtY,KAAzB;wBAAgCE,MAAM,EAAEoY,OAAO,CAACpY,MAAAA;qBADzC,GAEPoY,OAFJ;iBAHK,CAAP;gBAOA;;;KAXwB,EAe9B;QAACxO,OAAD;KAf8B,CAAhC;IAiBA,MAAMqN,cAAc,GAAGF,iBAAiB,CAAC;QAACN,QAAQ,EAAEO;KAAZ,CAAxC;IACA,MAAMyD,gBAAgB,OAAGze,8OAAW,GACjCgJ,OAAD;QACE,MAAMS,IAAI,GAAG2U,iBAAiB,CAACpV,OAAD,CAA9B;QAEAiS,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEH,UAAhB;QAEA,IAAIrR,IAAJ,EAAU;YACRwR,cAAc,IAAA,IAAd,GAAA,KAAA,IAAAA,cAAc,CAAEa,OAAhB,CAAwBrS,IAAxB;;QAGF+U,OAAO,CAAC/U,IAAI,GAAGmE,OAAO,CAACnE,IAAD,CAAV,GAAmB,IAAxB,CAAP;KAVgC,EAYlC;QAACmE,OAAD;QAAUqN,cAAV;KAZkC,CAApC;IAcA,MAAM,CAACyD,OAAD,EAAUC,MAAV,CAAA,OAAoBC,6MAAU,EAACH,gBAAD,CAApC;IAEA,WAAO5c,0OAAO,EACZ,IAAA,CAAO;YACL6c,OADK;YAELjb,IAFK;YAGLkb;SAHF,CADY,EAMZ;QAAClb,IAAD;QAAOib,OAAP;QAAgBC,MAAhB;KANY,CAAd;AAQD;AC9CM,MAAME,cAAc,GAAG;IAC5B;QAACrc,MAAM,EAAEgT,aAAT;QAAwB/S,OAAO,EAAE,CAAA;IAAjC,CAD4B;IAE5B;QAACD,MAAM,EAAEmO,cAAT;QAAyBlO,OAAO,EAAE,CAAA;IAAlC,CAF4B;CAAvB;AAKA,MAAMqc,WAAW,GAAY;IAACnN,OAAO,EAAE,CAAA;AAAV,CAA7B;AAEA,MAAMoN,6BAA6B,GAAyC;IACjFve,SAAS,EAAE;QACToN,OAAO,EAAExE;KAFsE;IAIjF4V,SAAS,EAAE;QACTpR,OAAO,EAAExE,8BADA;QAEToQ,QAAQ,EAAEZ,iBAAiB,CAACqG,aAFnB;QAGT1F,SAAS,EAAEV,kBAAkB,CAACqG,SAAAA;KAPiD;IASjFC,WAAW,EAAE;QACXvR,OAAO,EAAE7E;;AAVsE,CAA5E;MCdMqW,+BAA+BrG;IAI1C5T,GAAG,CAACvE,EAAD,EAAA;;QACD,OAAOA,EAAE,IAAI,IAAN,GAAA,CAAA,aAAa,KAAA,CAAMuE,GAAN,CAAUvE,EAAV,CAAb,KAAA,OAAA,aAA8B8P,SAA9B,GAA0CA,SAAjD;;IAGF2O,OAAO,GAAA;QACL,OAAOjC,KAAK,CAACC,IAAN,CAAW,IAAA,CAAKC,MAAL,EAAX,CAAP;;IAGFgC,UAAU,GAAA;QACR,OAAO,IAAA,CAAKD,OAAL,GAAezc,MAAf,EAAsB;YAAA,IAAC,EAACmU,QAAAA,EAAF,GAAA;YAAA,OAAgB,CAACA,QAAjB;SAAtB,CAAP;;IAGFwI,UAAU,CAAC3e,EAAD,EAAA;;QACR,OAAA,CAAA,wBAAA,CAAA,YAAO,IAAA,CAAKuE,GAAL,CAASvE,EAAT,CAAP,KAAA,OAAA,KAAA,IAAO,UAAc6I,IAAd,CAAmBkI,OAA1B,KAAA,OAAA,wBAAqCjB,SAArC;;;ACfG,MAAM8O,oBAAoB,GAA4B;IAC3DC,cAAc,EAAE,IAD2C;IAE3D9e,MAAM,EAAE,IAFmD;IAG3D8Q,UAAU,EAAE,IAH+C;IAI3DiO,cAAc,EAAE,IAJ2C;IAK3Dlb,UAAU,EAAE,IAL+C;IAM3Dmb,iBAAiB,EAAE,IANwC;IAO3DvH,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAP2C;IAQ3DhU,cAAc,EAAA,WAAA,GAAE,IAAIgU,GAAJ,EAR2C;IAS3D/T,mBAAmB,EAAA,WAAA,GAAE,IAAIoa,sBAAJ,EATsC;IAU3Dte,IAAI,EAAE,IAVqD;IAW3Dqe,WAAW,EAAE;QACXT,OAAO,EAAE;YACP/M,OAAO,EAAE;SAFA;QAIXlO,IAAI,EAAE,IAJK;QAKXkb,MAAM,EAAErc;KAhBiD;IAkB3DkL,mBAAmB,EAAE,EAlBsC;IAmB3DoJ,uBAAuB,EAAE,EAnBkC;IAoB3DgJ,sBAAsB,EAAEb,6BApBmC;IAqB3DlF,0BAA0B,EAAEvX,IArB+B;IAsB3D2b,UAAU,EAAE,IAtB+C;IAuB3D/D,kBAAkB,EAAE;AAvBuC,CAAtD;AA0BA,MAAM2F,sBAAsB,GAA8B;IAC/DJ,cAAc,EAAE,IAD+C;IAE/DnM,UAAU,EAAE,EAFmD;IAG/D3S,MAAM,EAAE,IAHuD;IAI/D+e,cAAc,EAAE,IAJ+C;IAK/DI,iBAAiB,EAAE;QACjBtf,SAAS,EAAE;KANkD;IAQ/DL,QAAQ,EAAEmC,IARqD;IAS/D8V,cAAc,EAAA,WAAA,GAAE,IAAIW,GAAJ,EAT+C;IAU/DjY,IAAI,EAAE,IAVyD;IAW/D+Y,0BAA0B,EAAEvX;AAXmC,CAA1D;AAcA,MAAMyd,eAAe,GAAA,WAAA,OAAG3gB,gPAAa,EAC1CygB,sBAD0C,CAArC;AAIA,MAAMG,aAAa,GAAA,WAAA,OAAG5gB,gPAAa,EACxCogB,oBADwC,CAAnC;SC/CSS;IACd,OAAO;QACLzf,SAAS,EAAE;YACTG,MAAM,EAAE,IADC;YAETwT,kBAAkB,EAAE;gBAACnR,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;aAFrB;YAGTid,KAAK,EAAE,IAAInH,GAAJ,EAHE;YAIToH,SAAS,EAAE;gBAACnd,CAAC,EAAE,CAAJ;gBAAOC,CAAC,EAAE;;SALlB;QAOL+b,SAAS,EAAE;YACT/F,UAAU,EAAE,IAAImG,sBAAJ;;KARhB;AAWD;AAED,SAAgB5D,QAAQ4E,KAAAA,EAAcC,MAAAA;IACpC,OAAQA,MAAM,CAACjgB,IAAf;QACE,KAAKiC,MAAM,CAACuS,SAAZ;YACE,OAAO;gBACL,GAAGwL,KADE;gBAEL5f,SAAS,EAAE;oBACT,GAAG4f,KAAK,CAAC5f,SADA;oBAET2T,kBAAkB,EAAEkM,MAAM,CAAClM,kBAFlB;oBAGTxT,MAAM,EAAE0f,MAAM,CAAC1f,MAAAA;;aALnB;QAQF,KAAK0B,MAAM,CAACie,QAAZ;YACE,IAAI,CAACF,KAAK,CAAC5f,SAAN,CAAgBG,MAArB,EAA6B;gBAC3B,OAAOyf,KAAP;;YAGF,OAAO;gBACL,GAAGA,KADE;gBAEL5f,SAAS,EAAE;oBACT,GAAG4f,KAAK,CAAC5f,SADA;oBAET2f,SAAS,EAAE;wBACTnd,CAAC,EAAEqd,MAAM,CAACpN,WAAP,CAAmBjQ,CAAnB,GAAuBod,KAAK,CAAC5f,SAAN,CAAgB2T,kBAAhB,CAAmCnR,CADpD;wBAETC,CAAC,EAAEod,MAAM,CAACpN,WAAP,CAAmBhQ,CAAnB,GAAuBmd,KAAK,CAAC5f,SAAN,CAAgB2T,kBAAhB,CAAmClR,CAAAA;;;aANnE;QAUF,KAAKZ,MAAM,CAACke,OAAZ;QACA,KAAKle,MAAM,CAACme,UAAZ;YACE,OAAO;gBACL,GAAGJ,KADE;gBAEL5f,SAAS,EAAE;oBACT,GAAG4f,KAAK,CAAC5f,SADA;oBAETG,MAAM,EAAE,IAFC;oBAGTwT,kBAAkB,EAAE;wBAACnR,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;qBAHrB;oBAITkd,SAAS,EAAE;wBAACnd,CAAC,EAAE,CAAJ;wBAAOC,CAAC,EAAE;;;aANzB;QAUF,KAAKZ,MAAM,CAACoe,iBAAZ;YAA+B;gBAC7B,MAAM,EAACzX,OAAAA,KAAWqX,MAAlB;gBACA,MAAM,EAACzf,EAAAA,KAAMoI,OAAb;gBACA,MAAMiQ,UAAU,GAAG,IAAImG,sBAAJ,CAA2BgB,KAAK,CAACpB,SAAN,CAAgB/F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAerZ,EAAf,EAAmBoI,OAAnB;gBAEA,OAAO;oBACL,GAAGoX,KADE;oBAELpB,SAAS,EAAE;wBACT,GAAGoB,KAAK,CAACpB,SADA;wBAET/F;;iBAJJ;;QASF,KAAK5W,MAAM,CAACqe,oBAAZ;YAAkC;gBAChC,MAAM,EAAC9f,EAAD,EAAK0N,GAAL,EAAUyI,QAAAA,KAAYsJ,MAA5B;gBACA,MAAMrX,OAAO,GAAGoX,KAAK,CAACpB,SAAN,CAAgB/F,UAAhB,CAA2B9T,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAO8R,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAImG,sBAAJ,CAA2BgB,KAAK,CAACpB,SAAN,CAAgB/F,UAA3C,CAAnB;gBACAA,UAAU,CAACgB,GAAX,CAAerZ,EAAf,EAAmB;oBACjB,GAAGoI,OADc;oBAEjB+N;iBAFF;gBAKA,OAAO;oBACL,GAAGqJ,KADE;oBAELpB,SAAS,EAAE;wBACT,GAAGoB,KAAK,CAACpB,SADA;wBAET/F;;iBAJJ;;QASF,KAAK5W,MAAM,CAACse,mBAAZ;YAAiC;gBAC/B,MAAM,EAAC/f,EAAD,EAAK0N,GAAAA,KAAO+R,MAAlB;gBACA,MAAMrX,OAAO,GAAGoX,KAAK,CAACpB,SAAN,CAAgB/F,UAAhB,CAA2B9T,GAA3B,CAA+BvE,EAA/B,CAAhB;gBAEA,IAAI,CAACoI,OAAD,IAAYsF,GAAG,KAAKtF,OAAO,CAACsF,GAAhC,EAAqC;oBACnC,OAAO8R,KAAP;;gBAGF,MAAMnH,UAAU,GAAG,IAAImG,sBAAJ,CAA2BgB,KAAK,CAACpB,SAAN,CAAgB/F,UAA3C,CAAnB;gBACAA,UAAU,CAAC/Y,MAAX,CAAkBU,EAAlB;gBAEA,OAAO;oBACL,GAAGwf,KADE;oBAELpB,SAAS,EAAE;wBACT,GAAGoB,KAAK,CAACpB,SADA;wBAET/F;;iBAJJ;;QASF;YAAS;gBACP,OAAOmH,KAAP;;;AAGL;SCzGeQ,aAAAA,IAAAA;QAAa,EAAC7J,QAAAA;IAC5B,MAAM,EAACpW,MAAD,EAAS8e,cAAT,EAAyBrH,cAAAA,SAAkB5Y,6OAAU,EAACugB,eAAD,CAA3D;IACA,MAAMc,sBAAsB,OAAG9I,8MAAW,EAAC0H,cAAD,CAA1C;IACA,MAAMqB,gBAAgB,OAAG/I,8MAAW,EAACpX,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAApC,EAAA,+CAAA;QAGAnB,4OAAS,EAAC;QACR,IAAIsX,QAAJ,EAAc;YACZ;;QAGF,IAAI,CAAC0I,cAAD,IAAmBoB,sBAAnB,IAA6CC,gBAAgB,IAAI,IAArE,EAA2E;YACzE,IAAI,KAAClP,kNAAe,EAACiP,sBAAD,CAApB,EAA8C;gBAC5C;;YAGF,IAAIjV,QAAQ,CAACmV,aAAT,KAA2BF,sBAAsB,CAACza,MAAtD,EAA8D;;gBAE5D;;YAGF,MAAMiS,aAAa,GAAGD,cAAc,CAACjT,GAAf,CAAmB2b,gBAAnB,CAAtB;YAEA,IAAI,CAACzI,aAAL,EAAoB;gBAClB;;YAGF,MAAM,EAAC3E,aAAD,EAAgBjK,IAAAA,KAAQ4O,aAA9B;YAEA,IAAI,CAAC3E,aAAa,CAAC/B,OAAf,IAA0B,CAAClI,IAAI,CAACkI,OAApC,EAA6C;gBAC3C;;YAGFqP,qBAAqB,CAAC;gBACpB,KAAK,MAAMhY,OAAX,IAAsB;oBAAC0K,aAAa,CAAC/B,OAAf;oBAAwBlI,IAAI,CAACkI,OAA7B;iBAAtB,CAA6D;oBAC3D,IAAI,CAAC3I,OAAL,EAAc;wBACZ;;oBAGF,MAAMiY,aAAa,OAAGC,yNAAsB,EAAClY,OAAD,CAA5C;oBAEA,IAAIiY,aAAJ,EAAmB;wBACjBA,aAAa,CAACE,KAAd;wBACA;;;aAVe,CAArB;;KA3BK,EA0CN;QACD1B,cADC;QAED1I,QAFC;QAGDqB,cAHC;QAID0I,gBAJC;QAKDD,sBALC;KA1CM,CAAT;IAkDA,OAAO,IAAP;AACD;SClEeO,eACdC,SAAAA,EAAAA,IAAAA;QACA,EAACla,SAAD,EAAY,GAAGma;IAEf,OAAOD,SAAS,IAAA,IAAT,IAAAA,SAAS,CAAE3c,MAAX,GACH2c,SAAS,CAAC1b,MAAV,CAA4B,CAACC,WAAD,EAAc8B,QAAd;QAC1B,OAAOA,QAAQ,CAAC;YACdP,SAAS,EAAEvB,WADG;YAEd,GAAG0b,IAAAA;SAFU,CAAf;KADF,EAKGna,SALH,CADG,GAOHA,SAPJ;AAQD;SCVeoa,0BACdnI,MAAAA;IAEA,WAAOvX,0OAAO,EACZ,IAAA,CAAO;YACLrB,SAAS,EAAE;gBACT,GAAGue,6BAA6B,CAACve,SADxB;gBAET,GAAG4Y,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE5Y,SAAX;aAHG;YAKLwe,SAAS,EAAE;gBACT,GAAGD,6BAA6B,CAACC,SADxB;gBAET,GAAG5F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE4F,SAAX;aAPG;YASLG,WAAW,EAAE;gBACX,GAAGJ,6BAA6B,CAACI,WADtB;gBAEX,GAAG/F,MAAH,IAAA,OAAA,KAAA,IAAGA,MAAM,CAAE+F,WAAX;;SAXJ,CADY,EAgBZ;QAAC/F,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAE5Y,SAAT;QAAoB4Y,MAApB,IAAA,OAAA,KAAA,IAAoBA,MAAM,CAAE4F,SAA5B;QAAuC5F,MAAvC,IAAA,OAAA,KAAA,IAAuCA,MAAM,CAAE+F,WAA/C;KAhBY,CAAd;AAkBD;SCXeqC,iCAAAA,IAAAA;QAAiC,EAC/C/P,UAD+C,EAE/C7D,OAF+C,EAG/C0O,WAH+C,EAI/ClD,MAAM,GAAG,IAAA;IAET,MAAMqI,WAAW,OAAGrK,yOAAM,EAAC,KAAD,CAA1B;IACA,MAAM,EAACpU,CAAD,EAAIC,CAAAA,KAAK,OAAOmW,MAAP,KAAkB,SAAlB,GAA8B;QAACpW,CAAC,EAAEoW,MAAJ;QAAYnW,CAAC,EAAEmW;KAA7C,GAAuDA,MAAtE;QAEAyC,4NAAyB,EAAC;QACxB,MAAM9E,QAAQ,GAAG,CAAC/T,CAAD,IAAM,CAACC,CAAxB;QAEA,IAAI8T,QAAQ,IAAI,CAACtF,UAAjB,EAA6B;YAC3BgQ,WAAW,CAAC9P,OAAZ,GAAsB,KAAtB;YACA;;QAGF,IAAI8P,WAAW,CAAC9P,OAAZ,IAAuB,CAAC2K,WAA5B,EAAyC;;;YAGvC;;QAIF,MAAM7S,IAAI,GAAGgI,UAAH,IAAA,OAAA,KAAA,IAAGA,UAAU,CAAEhI,IAAZ,CAAiBkI,OAA9B;QAEA,IAAI,CAAClI,IAAD,IAASA,IAAI,CAAC0S,WAAL,KAAqB,KAAlC,EAAyC;;;YAGvC;;QAGF,MAAM1Y,IAAI,GAAGmK,OAAO,CAACnE,IAAD,CAApB;QACA,MAAMiY,SAAS,GAAGla,YAAY,CAAC/D,IAAD,EAAO6Y,WAAP,CAA9B;QAEA,IAAI,CAACtZ,CAAL,EAAQ;YACN0e,SAAS,CAAC1e,CAAV,GAAc,CAAd;;QAGF,IAAI,CAACC,CAAL,EAAQ;YACNye,SAAS,CAACze,CAAV,GAAc,CAAd;;QAIFwe,WAAW,CAAC9P,OAAZ,GAAsB,IAAtB;QAEA,IAAItO,IAAI,CAAC+J,GAAL,CAASsU,SAAS,CAAC1e,CAAnB,IAAwB,CAAxB,IAA6BK,IAAI,CAAC+J,GAAL,CAASsU,SAAS,CAACze,CAAnB,IAAwB,CAAzD,EAA4D;YAC1D,MAAM2H,uBAAuB,GAAGD,0BAA0B,CAAClB,IAAD,CAA1D;YAEA,IAAImB,uBAAJ,EAA6B;gBAC3BA,uBAAuB,CAACkI,QAAxB,CAAiC;oBAC/B/O,GAAG,EAAE2d,SAAS,CAACze,CADgB;oBAE/BY,IAAI,EAAE6d,SAAS,CAAC1e,CAAAA;iBAFlB;;;KAzCmB,EA+CtB;QAACyO,UAAD;QAAazO,CAAb;QAAgBC,CAAhB;QAAmBqZ,WAAnB;QAAgC1O,OAAhC;KA/CsB,CAAzB;AAgDD;ACgDM,MAAM+T,sBAAsB,GAAA,WAAA,OAAGviB,gPAAa,EAAY;IAC7D,GAAGyD,kBAD0D;IAE7DyE,MAAM,EAAE,CAFqD;IAG7DC,MAAM,EAAE;AAHqD,CAAZ,CAA5C;AAMP,IAAKqa,MAAL;AAAA,CAAA,SAAKA,MAAAA;IACHA,MAAAA,CAAAA,MAAAA,CAAAA,gBAAAA,GAAAA,EAAA,GAAA,eAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,eAAAA,GAAAA,EAAA,GAAA,cAAA;IACAA,MAAAA,CAAAA,MAAAA,CAAAA,cAAAA,GAAAA,EAAA,GAAA,aAAA;AACD,CAJD,EAAKA,MAAM,IAAA,CAANA,MAAM,GAAA,CAAA,CAAA,CAAX;AAMA,MAAaC,UAAU,GAAA,WAAA,OAAGC,uOAAI,EAAC,SAASD,UAAT,CAAA,IAAA;;QAAoB,EACjDjhB,EADiD,EAEjDmhB,aAFiD,EAGjDvK,UAAU,GAAG,IAHoC,EAIjD6G,QAJiD,EAKjD1b,OAAO,GAAGkc,cALuC,EAMjDmD,kBAAkB,GAAGnb,gBAN4B,EAOjDob,SAPiD,EAQjDZ,SARiD,EASjD,GAAGzQ;IAEH,MAAMsR,KAAK,OAAG3G,6OAAU,EAACC,OAAD,EAAU9K,SAAV,EAAqBuP,eAArB,CAAxB;IACA,MAAM,CAACG,KAAD,EAAQjgB,QAAR,CAAA,GAAoB+hB,KAA1B;IACA,MAAM,CAACC,oBAAD,EAAuBC,uBAAvB,CAAA,GACJxiB,qBAAqB,EADvB;IAEA,MAAM,CAACyiB,MAAD,EAASC,SAAT,CAAA,OAAsBxiB,2OAAQ,EAAS8hB,MAAM,CAACW,aAAhB,CAApC;IACA,MAAMC,aAAa,GAAGH,MAAM,KAAKT,MAAM,CAACa,WAAxC;IACA,MAAM,EACJjiB,SAAS,EAAE,EAACG,MAAM,EAAE+hB,QAAT,EAAmBxC,KAAK,EAAE9H,cAA1B,EAA0C+H,SAAAA,EADjD,EAEJnB,SAAS,EAAE,EAAC/F,UAAU,EAAEjU,mBAAAA,OACtBob,KAHJ;IAIA,MAAM3W,IAAI,GAAGiZ,QAAQ,GAAGtK,cAAc,CAACjT,GAAf,CAAmBud,QAAnB,CAAH,GAAkC,IAAvD;IACA,MAAMC,WAAW,OAAGvL,yOAAM,EAA4B;QACpDwL,OAAO,EAAE,IAD2C;QAEpDC,UAAU,EAAE;KAFY,CAA1B;IAIA,MAAMliB,MAAM,OAAGkB,0OAAO,EACpB;QAAA,IAAA;QAAA,OACE6gB,QAAQ,IAAI,IAAZ,GACI;YACE9hB,EAAE,EAAE8hB,QADN;;YAGExe,IAAI,EAAA,CAAA,aAAEuF,IAAF,IAAA,OAAA,KAAA,IAAEA,IAAI,CAAEvF,IAAR,KAAA,OAAA,aAAgB4a,WAHtB;YAIErb,IAAI,EAAEkf;SALZ,GAOI,IARN;KADoB,EAUpB;QAACD,QAAD;QAAWjZ,IAAX;KAVoB,CAAtB;IAYA,MAAMqZ,SAAS,OAAG1L,yOAAM,EAA0B,IAA1B,CAAxB;IACA,MAAM,CAAC2L,YAAD,EAAeC,eAAf,CAAA,OAAkCljB,2OAAQ,EAAwB,IAAxB,CAAhD;IACA,MAAM,CAAC2f,cAAD,EAAiBwD,iBAAjB,CAAA,OAAsCnjB,2OAAQ,EAAe,IAAf,CAApD;IACA,MAAMojB,WAAW,OAAGtJ,iNAAc,EAAChJ,KAAD,EAAQ9N,MAAM,CAACwa,MAAP,CAAc1M,KAAd,CAAR,CAAlC;IACA,MAAMuS,sBAAsB,OAAGzhB,8MAAW,EAAA,kBAAmBd,EAAnB,CAA1C;IACA,MAAMwiB,0BAA0B,OAAGvhB,0OAAO,EACxC,IAAMmD,mBAAmB,CAACsa,UAApB,EADkC,EAExC;QAACta,mBAAD;KAFwC,CAA1C;IAIA,MAAM4a,sBAAsB,GAAG2B,yBAAyB,CAACU,SAAD,CAAxD;IACA,MAAM,EAACld,cAAD,EAAiB8U,0BAAjB,EAA6CK,kBAAAA,KACjDlB,qBAAqB,CAACoK,0BAAD,EAA6B;QAChDlK,QAAQ,EAAEsJ,aADsC;QAEhDrJ,YAAY,EAAE;YAACgH,SAAS,CAACnd,CAAX;YAAcmd,SAAS,CAACld,CAAxB;SAFkC;QAGhDmW,MAAM,EAAEwG,sBAAsB,CAACZ,SAAAA;KAHZ,CADvB;IAMA,MAAMvN,UAAU,GAAG0G,aAAa,CAACC,cAAD,EAAiBsK,QAAjB,CAAhC;IACA,MAAMW,qBAAqB,OAAGxhB,0OAAO,EACnC,IAAO4d,cAAc,OAAG9b,sNAAmB,EAAC8b,cAAD,CAAtB,GAAyC,IAD3B,EAEnC;QAACA,cAAD;KAFmC,CAArC;IAIA,MAAM6D,iBAAiB,GAAGC,sBAAsB,EAAhD;IACA,MAAMC,qBAAqB,GAAGjJ,cAAc,CAC1C9I,UAD0C,EAE1CmO,sBAAsB,CAACpf,SAAvB,CAAiCoN,OAFS,CAA5C;IAKA4T,gCAAgC,CAAC;QAC/B/P,UAAU,EAAEiR,QAAQ,GAAGtK,cAAc,CAACjT,GAAf,CAAmBud,QAAnB,CAAH,GAAkC,IADvB;QAE/BtJ,MAAM,EAAEkK,iBAAiB,CAACG,uBAFK;QAG/BnH,WAAW,EAAEkH,qBAHkB;QAI/B5V,OAAO,EAAEgS,sBAAsB,CAACpf,SAAvB,CAAiCoN,OAAAA;KAJZ,CAAhC;IAOA,MAAM8R,cAAc,GAAGtE,OAAO,CAC5B3J,UAD4B,EAE5BmO,sBAAsB,CAACpf,SAAvB,CAAiCoN,OAFL,EAG5B4V,qBAH4B,CAA9B;IAKA,MAAM7D,iBAAiB,GAAGvE,OAAO,CAC/B3J,UAAU,GAAGA,UAAU,CAACiS,aAAd,GAA8B,IADT,CAAjC;IAGA,MAAMC,aAAa,OAAGvM,yOAAM,EAAgB;QAC1CqI,cAAc,EAAE,IAD0B;QAE1C9e,MAAM,EAAE,IAFkC;QAG1C8Q,UAH0C;QAI1C3M,aAAa,EAAE,IAJ2B;QAK1CN,UAAU,EAAE,IAL8B;QAM1CO,cAN0C;QAO1CqT,cAP0C;QAQ1CwL,YAAY,EAAE,IAR4B;QAS1CC,gBAAgB,EAAE,IATwB;QAU1C7e,mBAV0C;QAW1ClE,IAAI,EAAE,IAXoC;QAY1C0M,mBAAmB,EAAE,EAZqB;QAa1CsW,uBAAuB,EAAE;KAbC,CAA5B;IAeA,MAAMC,QAAQ,GAAG/e,mBAAmB,CAACua,UAApB,CAAA,CAAA,wBACfoE,aAAa,CAAChS,OAAd,CAAsB7Q,IADP,KAAA,OAAA,KAAA,IACf,sBAA4BF,EADb,CAAjB;IAGA,MAAMue,WAAW,GAAGZ,uBAAuB,CAAC;QAC1C3Q,OAAO,EAAEgS,sBAAsB,CAACT,WAAvB,CAAmCvR,OAAAA;KADH,CAA3C,EAAA,oDAAA;IAKA,MAAMgW,YAAY,GAAA,CAAA,wBAAGzE,WAAW,CAACT,OAAZ,CAAoB/M,OAAvB,KAAA,OAAA,wBAAkCF,UAApD;IACA,MAAMoS,gBAAgB,GAAGrB,aAAa,GAAA,CAAA,oBAClCrD,WAAW,CAAC1b,IADsB,KAAA,OAAA,oBACdic,cADc,GAElC,IAFJ;IAGA,MAAMsE,eAAe,GAAGnQ,OAAO,CAC7BsL,WAAW,CAACT,OAAZ,CAAoB/M,OAApB,IAA+BwN,WAAW,CAAC1b,IADd,CAA/B,EAAA,wEAAA;;IAKA,MAAMwgB,aAAa,GAAG5H,YAAY,CAAC2H,eAAe,GAAG,IAAH,GAAUtE,cAA1B,CAAlC,EAAA,2CAAA;IAGA,MAAMzB,UAAU,GAAGH,aAAa,CAC9B8F,YAAY,OAAG1a,4MAAS,EAAC0a,YAAD,CAAZ,GAA6B,IADX,CAAhC,EAAA,gDAAA;IAKA,MAAMpW,mBAAmB,GAAG+O,sBAAsB,CAChDiG,aAAa,GAAGuB,QAAH,IAAA,OAAGA,QAAH,GAAetS,UAAf,GAA4B,IADO,CAAlD;IAGA,MAAMmF,uBAAuB,GAAGmH,QAAQ,CAACvQ,mBAAD,CAAxC,EAAA,kBAAA;IAGA,MAAM0W,iBAAiB,GAAG9C,cAAc,CAACC,SAAD,EAAY;QAClDla,SAAS,EAAE;YACTnE,CAAC,EAAEmd,SAAS,CAACnd,CAAV,GAAcihB,aAAa,CAACjhB,CADtB;YAETC,CAAC,EAAEkd,SAAS,CAACld,CAAV,GAAcghB,aAAa,CAAChhB,CAFtB;YAGTqE,MAAM,EAAE,CAHC;YAITC,MAAM,EAAE;SALwC;QAOlDkY,cAPkD;QAQlD9e,MARkD;QASlD+e,cATkD;QAUlDC,iBAVkD;QAWlDkE,gBAXkD;QAYlD/iB,IAAI,EAAE6iB,aAAa,CAAChS,OAAd,CAAsB7Q,IAZsB;QAalDqjB,eAAe,EAAEhF,WAAW,CAAC1b,IAbqB;QAclD+J,mBAdkD;QAelDoJ,uBAfkD;QAgBlDqH;KAhBsC,CAAxC;IAmBA,MAAMhX,kBAAkB,GAAGoc,qBAAqB,OAC5CpjB,sMAAG,EAACojB,qBAAD,EAAwBlD,SAAxB,CADyC,GAE5C,IAFJ;IAIA,MAAMjS,aAAa,GAAGwO,gBAAgB,CAAClP,mBAAD,CAAtC,EAAA,2DAAA;IAEA,MAAM4W,gBAAgB,GAAG7G,qBAAqB,CAACrP,aAAD,CAA9C,EAAA,oFAAA;IAEA,MAAMmW,qBAAqB,GAAG9G,qBAAqB,CAACrP,aAAD,EAAgB;QACjEwR,cADiE;KAAhB,CAAnD;IAIA,MAAMoE,uBAAuB,OAAG7jB,sMAAG,EAACikB,iBAAD,EAAoBE,gBAApB,CAAnC;IAEA,MAAMtf,aAAa,GAAG+e,gBAAgB,GAClC9b,eAAe,CAAC8b,gBAAD,EAAmBK,iBAAnB,CADmB,GAElC,IAFJ;IAIA,MAAM1f,UAAU,GACd7D,MAAM,IAAImE,aAAV,GACIkd,kBAAkB,CAAC;QACjBrhB,MADiB;QAEjBmE,aAFiB;QAGjBC,cAHiB;QAIjBC,mBAAmB,EAAEoe,0BAJJ;QAKjBnc;KALgB,CADtB,GAQI,IATN;IAUA,MAAMqd,MAAM,GAAG/f,iBAAiB,CAACC,UAAD,EAAa,IAAb,CAAhC;IACA,MAAM,CAAC1D,IAAD,EAAOyjB,OAAP,CAAA,OAAkBzkB,2OAAQ,EAAc,IAAd,CAAhC,EAAA,iEAAA;;IAIA,MAAM0kB,gBAAgB,GAAGR,eAAe,GACpCE,iBADoC,OAEpCjkB,sMAAG,EAACikB,iBAAD,EAAoBG,qBAApB,CAFP;IAIA,MAAMld,SAAS,GAAGD,WAAW,CAC3Bsd,gBAD2B,EAAA,CAAA,aAE3B1jB,IAF2B,IAAA,OAAA,KAAA,IAE3BA,IAAI,CAAE2C,IAFqB,KAAA,OAAA,aAEb,IAFa,EAG3Bic,cAH2B,CAA7B;IAMA,MAAM+E,iBAAiB,OAAGzkB,8OAAW,EACnC,CACEK,KADF,EAAA;YAEE,EAACmC,MAAM,EAAEiW,MAAT,EAAiBhW,OAAAA;QAEjB,IAAIqgB,SAAS,CAACnR,OAAV,IAAqB,IAAzB,EAA+B;YAC7B;;QAGF,MAAMF,UAAU,GAAG2G,cAAc,CAACjT,GAAf,CAAmB2d,SAAS,CAACnR,OAA7B,CAAnB;QAEA,IAAI,CAACF,UAAL,EAAiB;YACf;;QAGF,MAAMgO,cAAc,GAAGpf,KAAK,CAACmT,WAA7B;QAEA,MAAMkR,cAAc,GAAG,IAAIjM,MAAJ,CAAW;YAChC9X,MAAM,EAAEmiB,SAAS,CAACnR,OADc;YAEhCF,UAFgC;YAGhCpR,KAAK,EAAEof,cAHyB;YAIhChd,OAJgC;;;YAOhCoP,OAAO,EAAE8R,aAPuB;YAQhCjS,OAAO,EAACyC,kBAAD;gBACL,MAAMvT,EAAE,GAAGkiB,SAAS,CAACnR,OAArB;gBAEA,IAAI/Q,EAAE,IAAI,IAAV,EAAgB;oBACd;;gBAGF,MAAMyX,aAAa,GAAGD,cAAc,CAACjT,GAAf,CAAmBvE,EAAnB,CAAtB;gBAEA,IAAI,CAACyX,aAAL,EAAoB;oBAClB;;gBAGF,MAAM,EAAC3X,WAAAA,KAAewiB,WAAW,CAACvR,OAAlC;gBACA,MAAMtR,KAAK,GAAmB;oBAC5BM,MAAM,EAAE;wBAACC,EAAD;wBAAKsD,IAAI,EAAEmU,aAAa,CAACnU,IAAzB;wBAA+BT,IAAI,EAAEkf;;iBAD/C;oBAIAgC,iQAAuB,EAAC;oBACtBjkB,WAAW,IAAA,IAAX,GAAA,KAAA,IAAAA,WAAW,CAAGL,KAAH,CAAX;oBACAiiB,SAAS,CAACV,MAAM,CAACgD,YAAR,CAAT;oBACAzkB,QAAQ,CAAC;wBACPC,IAAI,EAAEiC,MAAM,CAACuS,SADN;wBAEPT,kBAFO;wBAGPxT,MAAM,EAAEC;qBAHF,CAAR;oBAKAuhB,oBAAoB,CAAC;wBAAC/hB,IAAI,EAAE,aAAP;wBAAsBC;qBAAvB,CAApB;iBARqB,CAAvB;aA1B8B;YAqChC6S,MAAM,EAACD,WAAD;gBACJ9S,QAAQ,CAAC;oBACPC,IAAI,EAAEiC,MAAM,CAACie,QADN;oBAEPrN;iBAFM,CAAR;aAtC8B;YA2ChCE,KAAK,EAAE0R,aAAa,CAACxiB,MAAM,CAACke,OAAR,CA3CY;YA4ChClN,QAAQ,EAAEwR,aAAa,CAACxiB,MAAM,CAACme,UAAR;SA5CF,CAAvB;YA+CAmE,iQAAuB,EAAC;YACtB3B,eAAe,CAAC0B,cAAD,CAAf;YACAzB,iBAAiB,CAAC5iB,KAAK,CAACmT,WAAP,CAAjB;SAFqB,CAAvB;QAKA,SAASqR,aAAT,CAAuBzkB,IAAvB;YACE,OAAO,eAAe2O,OAAf;gBACL,MAAM,EAACpO,MAAD,EAAS6D,UAAT,EAAqB1D,IAArB,EAA2BgjB,uBAAAA,KAC/BH,aAAa,CAAChS,OADhB;gBAEA,IAAItR,KAAK,GAAwB,IAAjC;gBAEA,IAAIM,MAAM,IAAImjB,uBAAd,EAAuC;oBACrC,MAAM,EAACgB,UAAAA,KAAc5B,WAAW,CAACvR,OAAjC;oBAEAtR,KAAK,GAAG;wBACNof,cADM;wBAEN9e,MAAM,EAAEA,MAFF;wBAGN6D,UAHM;wBAIN4K,KAAK,EAAE0U,uBAJD;wBAKNhjB;qBALF;oBAQA,IAAIV,IAAI,KAAKiC,MAAM,CAACke,OAAhB,IAA2B,OAAOuE,UAAP,KAAsB,UAArD,EAAiE;wBAC/D,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,OAAR,CAAgBH,UAAU,CAACzkB,KAAD,CAA1B,CAA3B;wBAEA,IAAI0kB,YAAJ,EAAkB;4BAChB3kB,IAAI,GAAGiC,MAAM,CAACme,UAAd;;;;gBAKNsC,SAAS,CAACnR,OAAV,GAAoB,IAApB;oBAEAgT,iQAAuB,EAAC;oBACtBxkB,QAAQ,CAAC;wBAACC;qBAAF,CAAR;oBACAkiB,SAAS,CAACV,MAAM,CAACW,aAAR,CAAT;oBACAgC,OAAO,CAAC,IAAD,CAAP;oBACAvB,eAAe,CAAC,IAAD,CAAf;oBACAC,iBAAiB,CAAC,IAAD,CAAjB;oBAEA,MAAMnU,SAAS,GACb1O,IAAI,KAAKiC,MAAM,CAACke,OAAhB,GAA0B,WAA1B,GAAwC,cAD1C;oBAGA,IAAIlgB,KAAJ,EAAW;wBACT,MAAM0O,OAAO,GAAGmU,WAAW,CAACvR,OAAZ,CAAoB7C,SAApB,CAAhB;wBAEAC,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO,CAAG1O,KAAH,CAAP;wBACA8hB,oBAAoB,CAAC;4BAAC/hB,IAAI,EAAE0O,SAAP;4BAAkBzO;yBAAnB,CAApB;;iBAdmB,CAAvB;aA3BF;;KAtE+B,EAsHnC;QAAC+X,cAAD;KAtHmC,CAArC;IAyHA,MAAM8M,iCAAiC,OAAGllB,8OAAW,EACnD,CACE+O,OADF,EAEEvM,MAFF;QAIE,OAAO,CAACnC,KAAD,EAAQM,MAAR;YACL,MAAM6S,WAAW,GAAGnT,KAAK,CAACmT,WAA1B;YACA,MAAM2R,mBAAmB,GAAG/M,cAAc,CAACjT,GAAf,CAAmBxE,MAAnB,CAA5B;YAEA,IAEEmiB,SAAS,CAACnR,OAAV,KAAsB,IAAtB,IAAA,sBAAA;YAEA,CAACwT,mBAFD,IAAA,kCAAA;YAIA3R,WAAW,CAAC4R,MAJZ,IAKA5R,WAAW,CAAC6R,gBAPd,EAQE;gBACA;;YAGF,MAAMC,iBAAiB,GAAG;gBACxB3kB,MAAM,EAAEwkB;aADV;YAGA,MAAMI,cAAc,GAAGxW,OAAO,CAC5B1O,KAD4B,EAE5BmC,MAAM,CAACC,OAFqB,EAG5B6iB,iBAH4B,CAA9B;YAMA,IAAIC,cAAc,KAAK,IAAvB,EAA6B;gBAC3B/R,WAAW,CAAC4R,MAAZ,GAAqB;oBACnBI,UAAU,EAAEhjB,MAAM,CAACA,MAAAA;iBADrB;gBAIAsgB,SAAS,CAACnR,OAAV,GAAoBhR,MAApB;gBACA8jB,iBAAiB,CAACpkB,KAAD,EAAQmC,MAAR,CAAjB;;SA/BJ;KALiD,EAwCnD;QAAC4V,cAAD;QAAiBqM,iBAAjB;KAxCmD,CAArD;IA2CA,MAAMnR,UAAU,GAAGiF,oBAAoB,CACrC5V,OADqC,EAErCuiB,iCAFqC,CAAvC;IAKAvH,cAAc,CAAChb,OAAD,CAAd;QAEAkZ,4NAAyB,EAAC;QACxB,IAAI6D,cAAc,IAAI2C,MAAM,KAAKT,MAAM,CAACgD,YAAxC,EAAsD;YACpDtC,SAAS,CAACV,MAAM,CAACa,WAAR,CAAT;;KAFqB,EAItB;QAAC/C,cAAD;QAAiB2C,MAAjB;KAJsB,CAAzB;QAMA5iB,4OAAS,EACP;QACE,MAAM,EAACqC,UAAAA,KAAcohB,WAAW,CAACvR,OAAjC;QACA,MAAM,EAAChR,MAAD,EAAS8e,cAAT,EAAyBjb,UAAzB,EAAqC1D,IAAAA,KAAQ6iB,aAAa,CAAChS,OAAjE;QAEA,IAAI,CAAChR,MAAD,IAAW,CAAC8e,cAAhB,EAAgC;YAC9B;;QAGF,MAAMpf,KAAK,GAAkB;YAC3BM,MAD2B;YAE3B8e,cAF2B;YAG3Bjb,UAH2B;YAI3B4K,KAAK,EAAE;gBACLpM,CAAC,EAAE8gB,uBAAuB,CAAC9gB,CADtB;gBAELC,CAAC,EAAE6gB,uBAAuB,CAAC7gB,CAAAA;aANF;YAQ3BnC;SARF;YAWA6jB,iQAAuB,EAAC;YACtB7iB,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGzB,KAAH,CAAV;YACA8hB,oBAAoB,CAAC;gBAAC/hB,IAAI,EAAE,YAAP;gBAAqBC;aAAtB,CAApB;SAFqB,CAAvB;KApBK,EA0BP;QAACyjB,uBAAuB,CAAC9gB,CAAzB;QAA4B8gB,uBAAuB,CAAC7gB,CAApD;KA1BO,CAAT;QA6BAxD,4OAAS,EACP;QACE,MAAM,EACJkB,MADI,EAEJ8e,cAFI,EAGJjb,UAHI,EAIJQ,mBAJI,EAKJ8e,uBAAAA,KACEH,aAAa,CAAChS,OANlB;QAQA,IACE,CAAChR,MAAD,IACAmiB,SAAS,CAACnR,OAAV,IAAqB,IADrB,IAEA,CAAC8N,cAFD,IAGA,CAACqE,uBAJH,EAKE;YACA;;QAGF,MAAM,EAACjjB,UAAAA,KAAcqiB,WAAW,CAACvR,OAAjC;QACA,MAAM8T,aAAa,GAAGzgB,mBAAmB,CAACG,GAApB,CAAwBmf,MAAxB,CAAtB;QACA,MAAMxjB,IAAI,GACR2kB,aAAa,IAAIA,aAAa,CAAChiB,IAAd,CAAmBkO,OAApC,GACI;YACE/Q,EAAE,EAAE6kB,aAAa,CAAC7kB,EADpB;YAEE6C,IAAI,EAAEgiB,aAAa,CAAChiB,IAAd,CAAmBkO,OAF3B;YAGEzN,IAAI,EAAEuhB,aAAa,CAACvhB,IAHtB;YAIE6S,QAAQ,EAAE0O,aAAa,CAAC1O,QAAAA;SAL9B,GAOI,IARN;QASA,MAAM1W,KAAK,GAAkB;YAC3BM,MAD2B;YAE3B8e,cAF2B;YAG3Bjb,UAH2B;YAI3B4K,KAAK,EAAE;gBACLpM,CAAC,EAAE8gB,uBAAuB,CAAC9gB,CADtB;gBAELC,CAAC,EAAE6gB,uBAAuB,CAAC7gB,CAAAA;aANF;YAQ3BnC;SARF;YAWA6jB,iQAAuB,EAAC;YACtBJ,OAAO,CAACzjB,IAAD,CAAP;YACAD,UAAU,IAAA,IAAV,GAAA,KAAA,IAAAA,UAAU,CAAGR,KAAH,CAAV;YACA8hB,oBAAoB,CAAC;gBAAC/hB,IAAI,EAAE,YAAP;gBAAqBC;aAAtB,CAApB;SAHqB,CAAvB;KAzCK,EAgDP;QAACikB,MAAD;KAhDO,CAAT;QAmDAzI,4NAAyB,EAAC;QACxB8H,aAAa,CAAChS,OAAd,GAAwB;YACtB8N,cADsB;YAEtB9e,MAFsB;YAGtB8Q,UAHsB;YAItB3M,aAJsB;YAKtBN,UALsB;YAMtBO,cANsB;YAOtBqT,cAPsB;YAQtBwL,YARsB;YAStBC,gBATsB;YAUtB7e,mBAVsB;YAWtBlE,IAXsB;YAYtB0M,mBAZsB;YAatBsW;SAbF;QAgBAnB,WAAW,CAAChR,OAAZ,GAAsB;YACpBiR,OAAO,EAAEiB,gBADW;YAEpBhB,UAAU,EAAE/d;SAFd;KAjBuB,EAqBtB;QACDnE,MADC;QAED8Q,UAFC;QAGDjN,UAHC;QAIDM,aAJC;QAKDsT,cALC;QAMDwL,YANC;QAODC,gBAPC;QAQD9e,cARC;QASDC,mBATC;QAUDlE,IAVC;QAWD0M,mBAXC;QAYDsW,uBAZC;KArBsB,CAAzB;IAoCA1N,eAAe,CAAC;QACd,GAAGkN,iBADW;QAEdlU,KAAK,EAAE+Q,SAFO;QAGd5J,YAAY,EAAEzR,aAHA;QAIdmC,kBAJc;QAKduG,mBALc;QAMdoJ;KANa,CAAf;IASA,MAAM8O,aAAa,OAAG7jB,0OAAO,EAAC;QAC5B,MAAMgQ,OAAO,GAA4B;YACvClR,MADuC;YAEvC8Q,UAFuC;YAGvCiO,cAHuC;YAIvCD,cAJuC;YAKvCjb,UALuC;YAMvCmb,iBANuC;YAOvCR,WAPuC;YAQvC/G,cARuC;YASvCpT,mBATuC;YAUvCD,cAVuC;YAWvCjE,IAXuC;YAYvC+Y,0BAZuC;YAavCrM,mBAbuC;YAcvCoJ,uBAduC;YAevCgJ,sBAfuC;YAgBvC1F,kBAhBuC;YAiBvC+D;SAjBF;QAoBA,OAAOpM,OAAP;KArB2B,EAsB1B;QACDlR,MADC;QAED8Q,UAFC;QAGDiO,cAHC;QAIDD,cAJC;QAKDjb,UALC;QAMDmb,iBANC;QAODR,WAPC;QAQD/G,cARC;QASDpT,mBATC;QAUDD,cAVC;QAWDjE,IAXC;QAYD+Y,0BAZC;QAaDrM,mBAbC;QAcDoJ,uBAdC;QAeDgJ,sBAfC;QAgBD1F,kBAhBC;QAiBD+D,UAjBC;KAtB0B,CAA7B;IA0CA,MAAM0H,eAAe,OAAG9jB,0OAAO,EAAC;QAC9B,MAAMgQ,OAAO,GAA8B;YACzC4N,cADyC;YAEzCnM,UAFyC;YAGzC3S,MAHyC;YAIzC+e,cAJyC;YAKzCI,iBAAiB,EAAE;gBACjBtf,SAAS,EAAE2iB;aAN4B;YAQzChjB,QARyC;YASzCiY,cATyC;YAUzCtX,IAVyC;YAWzC+Y;SAXF;QAcA,OAAOhI,OAAP;KAf6B,EAgB5B;QACD4N,cADC;QAEDnM,UAFC;QAGD3S,MAHC;QAID+e,cAJC;QAKDvf,QALC;QAMDgjB,sBANC;QAOD/K,cAPC;QAQDtX,IARC;QASD+Y,0BATC;KAhB4B,CAA/B;IA4BA,OACE7X,0OAAAA,CAAAA,aAAA,CAAC7C,iBAAiB,CAACymB,QAAnB,EAAA;QAA4B1jB,KAAK,EAAEkgB;KAAnC,EACEpgB,0OAAAA,CAAAA,aAAA,CAAC+d,eAAe,CAAC6F,QAAjB,EAAA;QAA0B1jB,KAAK,EAAEyjB;KAAjC,EACE3jB,0OAAAA,CAAAA,aAAA,CAACge,aAAa,CAAC4F,QAAf,EAAA;QAAwB1jB,KAAK,EAAEwjB;KAA/B,EACE1jB,0OAAAA,CAAAA,aAAA,CAAC2f,sBAAsB,CAACiE,QAAxB,EAAA;QAAiC1jB,KAAK,EAAEiF;KAAxC,EACGkX,QADH,CADF,CADF,EAMErc,0OAAAA,CAAAA,aAAA,CAAC4e,YAAD,EAAA;QAAc7J,QAAQ,EAAE,CAAAgL,aAAa,IAAA,IAAb,GAAA,KAAA,IAAAA,aAAa,CAAE8D,YAAf,MAAgC;KAAxD,CANF,CADF,EASE7jB,0OAAAA,CAAAA,aAAA,CAACf,aAAD,EAAA;QAAA,GACM8gB,aAAAA;QACJ3gB,uBAAuB,EAAE+hB;KAF3B,CATF,CADF;;;IAiBA,SAASI,sBAAT;QACE,MAAMuC,8BAA8B,GAClC,CAAA/C,YAAY,IAAA,IAAZ,GAAA,KAAA,IAAAA,YAAY,CAAElS,iBAAd,MAAoC,KADtC;QAEA,MAAMkV,0BAA0B,GAC9B,OAAOvO,UAAP,KAAsB,QAAtB,GACIA,UAAU,CAAChB,OAAX,KAAuB,KAD3B,GAEIgB,UAAU,KAAK,KAHrB;QAIA,MAAMhB,OAAO,GACXgM,aAAa,IACb,CAACsD,8BADD,IAEA,CAACC,0BAHH;QAKA,IAAI,OAAOvO,UAAP,KAAsB,QAA1B,EAAoC;YAClC,OAAO;gBACL,GAAGA,UADE;gBAELhB;aAFF;;QAMF,OAAO;YAACA;SAAR;;AAEH,CAtlB6B,CAAvB;ACjGP,MAAMwP,WAAW,GAAA,WAAA,OAAG5mB,gPAAa,EAAM,IAAN,CAAjC;AAEA,MAAM6mB,WAAW,GAAG,QAApB;AAEA,MAAMC,SAAS,GAAG,WAAlB;AAEA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3BvlB,EAD2B,EAE3BsD,IAF2B,EAG3B6S,QAAQ,GAAG,KAHgB,EAI3BqP,UAAAA;IAEA,MAAM9X,GAAG,OAAG5M,8MAAW,EAACwkB,SAAD,CAAvB;IACA,MAAM,EACJ5S,UADI,EAEJmM,cAFI,EAGJ9e,MAHI,EAIJ+e,cAJI,EAKJI,iBALI,EAMJ1H,cANI,EAOJtX,IAAAA,SACEtB,6OAAU,EAACugB,eAAD,CARd;IASA,MAAM,EACJsG,IAAI,GAAGJ,WADH,EAEJK,eAAe,GAAG,WAFd,EAGJC,QAAQ,GAAG,CAAA,KACTH,UAJE,IAAA,OAIFA,UAJE,GAIY,CAAA,CAJlB;IAKA,MAAMI,UAAU,GAAG,CAAA7lB,MAAM,IAAA,IAAN,GAAA,KAAA,IAAAA,MAAM,CAAEC,EAAR,MAAeA,EAAlC;IACA,MAAMuG,SAAS,OAAqB3H,6OAAU,EAC5CgnB,UAAU,GAAG7E,sBAAH,GAA4BqE,WADM,CAA9C;IAGA,MAAM,CAACvc,IAAD,EAAOgd,UAAP,CAAA,OAAqB7H,6MAAU,EAArC;IACA,MAAM,CAAClL,aAAD,EAAgBgT,mBAAhB,CAAA,OAAuC9H,6MAAU,EAAvD;IACA,MAAM/e,SAAS,GAAGge,qBAAqB,CAACvK,UAAD,EAAa1S,EAAb,CAAvC;IACA,MAAM+lB,OAAO,OAAG/M,iNAAc,EAAC1V,IAAD,CAA9B;QAEA2X,4NAAyB,EACvB;QACEzD,cAAc,CAAC6B,GAAf,CAAmBrZ,EAAnB,EAAuB;YAACA,EAAD;YAAK0N,GAAL;YAAU7E,IAAV;YAAgBiK,aAAhB;YAA+BxP,IAAI,EAAEyiB;SAA5D;QAEA,OAAO;YACL,MAAMld,IAAI,GAAG2O,cAAc,CAACjT,GAAf,CAAmBvE,EAAnB,CAAb;YAEA,IAAI6I,IAAI,IAAIA,IAAI,CAAC6E,GAAL,KAAaA,GAAzB,EAA8B;gBAC5B8J,cAAc,CAAClY,MAAf,CAAsBU,EAAtB;;SAJJ;KAJqB,EAavB;QAACwX,cAAD;QAAiBxX,EAAjB;KAbuB,CAAzB;IAgBA,MAAMgmB,kBAAkB,OAAwB/kB,0OAAO,EACrD,IAAA,CAAO;YACLwkB,IADK;YAELE,QAFK;YAGL,iBAAiBxP,QAHZ;YAIL,gBAAgByP,UAAU,IAAIH,IAAI,KAAKJ,WAAvB,GAAqC,IAArC,GAA4CvV,SAJvD;YAKL,wBAAwB4V,eALnB;YAML,oBAAoBxG,iBAAiB,CAACtf,SAAAA;SANxC,CADqD,EASrD;QACEuW,QADF;QAEEsP,IAFF;QAGEE,QAHF;QAIEC,UAJF;QAKEF,eALF;QAMExG,iBAAiB,CAACtf,SANpB;KATqD,CAAvD;IAmBA,OAAO;QACLG,MADK;QAEL8e,cAFK;QAGLC,cAHK;QAIL0G,UAAU,EAAEQ,kBAJP;QAKLJ,UALK;QAML3mB,SAAS,EAAEkX,QAAQ,GAAGrG,SAAH,GAAe7Q,SAN7B;QAOL4J,IAPK;QAQL3I,IARK;QASL2lB,UATK;QAULC,mBAVK;QAWLvf;KAXF;AAaD;SCrHe0f;IACd,WAAOrnB,6OAAU,EAACwgB,aAAD,CAAjB;AACD;AC2BD,MAAMkG,WAAS,GAAG,WAAlB;AAEA,MAAMY,2BAA2B,GAAG;IAClCC,OAAO,EAAE;AADyB,CAApC;AAIA,SAAgBC,aAAAA,IAAAA;QAAa,EAC3B9iB,IAD2B,EAE3B6S,QAAQ,GAAG,KAFgB,EAG3BnW,EAH2B,EAI3BqmB,oBAAAA;IAEA,MAAM3Y,GAAG,OAAG5M,8MAAW,EAACwkB,WAAD,CAAvB;IACA,MAAM,EAACvlB,MAAD,EAASR,QAAT,EAAmBW,IAAnB,EAAyB+Y,0BAAAA,SAA8Bra,6OAAU,EACrEugB,eADqE,CAAvE;IAGA,MAAMmH,QAAQ,OAAG9P,yOAAM,EAAC;QAACL;KAAF,CAAvB;IACA,MAAMoQ,uBAAuB,OAAG/P,yOAAM,EAAC,KAAD,CAAtC;IACA,MAAM3T,IAAI,OAAG2T,yOAAM,EAAoB,IAApB,CAAnB;IACA,MAAMgQ,UAAU,OAAGhQ,yOAAM,EAAwB,IAAxB,CAAzB;IACA,MAAM,EACJL,QAAQ,EAAEsQ,sBADN,EAEJC,qBAFI,EAGJP,OAAO,EAAEQ,qBAAAA,KACP;QACF,GAAGT,2BADD;QAEF,GAAGG,oBAAAA;KANL;IAQA,MAAMnN,GAAG,OAAGF,iNAAc,EAAC0N,qBAAD,IAAA,OAACA,qBAAD,GAA0B1mB,EAA1B,CAA1B;IACA,MAAMoa,YAAY,OAAGhb,8OAAW,EAC9B;QACE,IAAI,CAACmnB,uBAAuB,CAACxV,OAA7B,EAAsC;;;YAGpCwV,uBAAuB,CAACxV,OAAxB,GAAkC,IAAlC;YACA;;QAGF,IAAIyV,UAAU,CAACzV,OAAX,IAAsB,IAA1B,EAAgC;YAC9BoD,YAAY,CAACqS,UAAU,CAACzV,OAAZ,CAAZ;;QAGFyV,UAAU,CAACzV,OAAX,GAAqBJ,UAAU,CAAC;YAC9BsI,0BAA0B,CACxBuD,KAAK,CAACoK,OAAN,CAAc1N,GAAG,CAACnI,OAAlB,IAA6BmI,GAAG,CAACnI,OAAjC,GAA2C;gBAACmI,GAAG,CAACnI,OAAL;aADnB,CAA1B;YAGAyV,UAAU,CAACzV,OAAX,GAAqB,IAArB;SAJ6B,EAK5B4V,qBAL4B,CAA/B;KAb4B,EAqB9B;QAACA,qBAAD;KArB8B,CAAhC;IAuBA,MAAMtM,cAAc,GAAGF,iBAAiB,CAAC;QACvCN,QAAQ,EAAEO,YAD6B;QAEvCjE,QAAQ,EAAEsQ,sBAAsB,IAAI,CAAC1mB;KAFC,CAAxC;IAIA,MAAM8d,gBAAgB,OAAGze,8OAAW,EAClC,CAACynB,UAAD,EAAiCC,eAAjC;QACE,IAAI,CAACzM,cAAL,EAAqB;YACnB;;QAGF,IAAIyM,eAAJ,EAAqB;YACnBzM,cAAc,CAAC0M,SAAf,CAAyBD,eAAzB;YACAP,uBAAuB,CAACxV,OAAxB,GAAkC,KAAlC;;QAGF,IAAI8V,UAAJ,EAAgB;YACdxM,cAAc,CAACa,OAAf,CAAuB2L,UAAvB;;KAZ8B,EAelC;QAACxM,cAAD;KAfkC,CAApC;IAiBA,MAAM,CAACyD,OAAD,EAAU+H,UAAV,CAAA,OAAwB7H,6MAAU,EAACH,gBAAD,CAAxC;IACA,MAAMkI,OAAO,OAAG/M,iNAAc,EAAC1V,IAAD,CAA9B;QAEAzE,4OAAS,EAAC;QACR,IAAI,CAACwb,cAAD,IAAmB,CAACyD,OAAO,CAAC/M,OAAhC,EAAyC;YACvC;;QAGFsJ,cAAc,CAACH,UAAf;QACAqM,uBAAuB,CAACxV,OAAxB,GAAkC,KAAlC;QACAsJ,cAAc,CAACa,OAAf,CAAuB4C,OAAO,CAAC/M,OAA/B;KAPO,EAQN;QAAC+M,OAAD;QAAUzD,cAAV;KARM,CAAT;QAUAY,4NAAyB,EACvB;QACE1b,QAAQ,CAAC;YACPC,IAAI,EAAEiC,MAAM,CAACoe,iBADN;YAEPzX,OAAO,EAAE;gBACPpI,EADO;gBAEP0N,GAFO;gBAGPyI,QAHO;gBAIPtN,IAAI,EAAEiV,OAJC;gBAKPjb,IALO;gBAMPS,IAAI,EAAEyiB;;SARF,CAAR;QAYA,OAAO,IACLxmB,QAAQ,CAAC;gBACPC,IAAI,EAAEiC,MAAM,CAACse,mBADN;gBAEPrS,GAFO;gBAGP1N;aAHM,CADV;KAdqB,EAsBvB;QAACA,EAAD;KAtBuB,CAAzB;QAyBAnB,4OAAS,EAAC;QACR,IAAIsX,QAAQ,KAAKmQ,QAAQ,CAACvV,OAAT,CAAiBoF,QAAlC,EAA4C;YAC1C5W,QAAQ,CAAC;gBACPC,IAAI,EAAEiC,MAAM,CAACqe,oBADN;gBAEP9f,EAFO;gBAGP0N,GAHO;gBAIPyI;aAJM,CAAR;YAOAmQ,QAAQ,CAACvV,OAAT,CAAiBoF,QAAjB,GAA4BA,QAA5B;;KATK,EAWN;QAACnW,EAAD;QAAK0N,GAAL;QAAUyI,QAAV;QAAoB5W,QAApB;KAXM,CAAT;IAaA,OAAO;QACLQ,MADK;QAEL8C,IAFK;QAGLmkB,MAAM,EAAE,CAAA9mB,IAAI,IAAA,IAAJ,GAAA,KAAA,IAAAA,IAAI,CAAEF,EAAN,MAAaA,EAHhB;QAIL6I,IAAI,EAAEiV,OAJD;QAKL5d,IALK;QAML2lB;KANF;AAQD;SCrJeoB,iBAAAA,IAAAA;QAAiB,EAACC,SAAD,EAAYzJ,QAAAA;IAC3C,MAAM,CACJ0J,cADI,EAEJC,iBAFI,CAAA,OAGFloB,2OAAQ,EAA4B,IAA5B,CAHZ;IAIA,MAAM,CAACkJ,OAAD,EAAUif,UAAV,CAAA,OAAwBnoB,2OAAQ,EAAqB,IAArB,CAAtC;IACA,MAAMooB,gBAAgB,OAAGnQ,8MAAW,EAACsG,QAAD,CAApC;IAEA,IAAI,CAACA,QAAD,IAAa,CAAC0J,cAAd,IAAgCG,gBAApC,EAAsD;QACpDF,iBAAiB,CAACE,gBAAD,CAAjB;;QAGFrM,4NAAyB,EAAC;QACxB,IAAI,CAAC7S,OAAL,EAAc;YACZ;;QAGF,MAAMsF,GAAG,GAAGyZ,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAEzZ,GAA5B;QACA,MAAM1N,EAAE,GAAGmnB,cAAH,IAAA,OAAA,KAAA,IAAGA,cAAc,CAAEnX,KAAhB,CAAsBhQ,EAAjC;QAEA,IAAI0N,GAAG,IAAI,IAAP,IAAe1N,EAAE,IAAI,IAAzB,EAA+B;YAC7BonB,iBAAiB,CAAC,IAAD,CAAjB;YACA;;QAGFhD,OAAO,CAACC,OAAR,CAAgB6C,SAAS,CAAClnB,EAAD,EAAKoI,OAAL,CAAzB,EAAwCmf,IAAxC,CAA6C;YAC3CH,iBAAiB,CAAC,IAAD,CAAjB;SADF;KAbuB,EAgBtB;QAACF,SAAD;QAAYC,cAAZ;QAA4B/e,OAA5B;KAhBsB,CAAzB;IAkBA,OACEhH,0OAAAA,CAAAA,aAAA,CAAA,0OAAA,CAAA,QAAA,EAAA,IAAA,EACGqc,QADH,EAEG0J,cAAc,OAAGK,+OAAY,EAACL,cAAD,EAAiB;QAACM,GAAG,EAAEJ;KAAvB,CAAf,GAAqD,IAFtE,CADF;AAMD;ACzCD,MAAMK,gBAAgB,GAAc;IAClCtlB,CAAC,EAAE,CAD+B;IAElCC,CAAC,EAAE,CAF+B;IAGlCqE,MAAM,EAAE,CAH0B;IAIlCC,MAAM,EAAE;AAJ0B,CAApC;AAOA,SAAgBghB,yBAAAA,IAAAA;QAAyB,EAAClK,QAAAA;IACxC,OACErc,0OAAAA,CAAAA,aAAA,CAAC+d,eAAe,CAAC6F,QAAjB,EAAA;QAA0B1jB,KAAK,EAAE2d;KAAjC,EACE7d,0OAAAA,CAAAA,aAAA,CAAC2f,sBAAsB,CAACiE,QAAxB,EAAA;QAAiC1jB,KAAK,EAAEomB;KAAxC,EACGjK,QADH,CADF,CADF;AAOD;ACAD,MAAMmK,UAAU,GAAwB;IACtC7e,QAAQ,EAAE,OAD4B;IAEtC8e,WAAW,EAAE;AAFyB,CAAxC;AAKA,MAAMC,iBAAiB,IAAsBjJ,cAAD;IAC1C,MAAMkJ,mBAAmB,OAAG/W,kNAAe,EAAC6N,cAAD,CAA3C;IAEA,OAAOkJ,mBAAmB,GAAG,sBAAH,GAA4BjY,SAAtD;AACD,CAJD;AAMO,MAAMkY,iBAAiB,GAAA,WAAA,OAAGC,6OAAU,EACzC,CAAA,MAYER,GAZF;QACE,EACES,EADF,EAEErJ,cAFF,EAGEvY,WAHF,EAIEmX,QAJF,EAKE0K,SALF,EAMEtlB,IANF,EAOEulB,KAPF,EAQE7hB,SARF,EASE8hB,UAAU,GAAGP,iBAAAA;IAIf,IAAI,CAACjlB,IAAL,EAAW;QACT,OAAO,IAAP;;IAGF,MAAMylB,sBAAsB,GAAGhiB,WAAW,GACtCC,SADsC,GAEtC;QACE,GAAGA,SADL;QAEEG,MAAM,EAAE,CAFV;QAGEC,MAAM,EAAE;KALd;IAOA,MAAM4hB,MAAM,GAAoC;QAC9C,GAAGX,UAD2C;QAE9C1kB,KAAK,EAAEL,IAAI,CAACK,KAFkC;QAG9CE,MAAM,EAAEP,IAAI,CAACO,MAHiC;QAI9CD,GAAG,EAAEN,IAAI,CAACM,GAJoC;QAK9CF,IAAI,EAAEJ,IAAI,CAACI,IALmC;QAM9CsD,SAAS,EAAEiiB,sMAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBJ,sBAAvB,CANmC;QAO9CtlB,eAAe,EACbsD,WAAW,IAAIuY,cAAf,GACIjc,0BAA0B,CACxBic,cADwB,EAExBhc,IAFwB,CAD9B,GAKIiN,SAbwC;QAc9CuY,UAAU,EACR,OAAOA,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAACxJ,cAAD,CADd,GAEIwJ,UAjBwC;QAkB9C,GAAGD,KAAAA;KAlBL;IAqBA,OAAOhnB,0OAAK,CAACunB,aAAN,CACLT,EADK,EAEL;QACEC,SADF;QAEEC,KAAK,EAAEG,MAFT;QAGEd;KALG,EAOLhK,QAPK,CAAP;AASD,CAxDwC,CAApC;MCwDMmL,+BAA+B,IAC1C/mB,OAD6C,IAEhB;YAAC,EAAC9B,MAAD,EAASwe,WAAAA;QACvC,MAAMsK,cAAc,GAA2B,CAAA,CAA/C;QACA,MAAM,EAACN,MAAD,EAASJ,SAAAA,KAAatmB,OAA5B;QAEA,IAAI0mB,MAAJ,IAAA,QAAIA,MAAM,CAAExoB,MAAZ,EAAoB;YAClB,KAAK,MAAM,CAAC2N,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACoa,OAAP,CAAeiM,MAAM,CAACxoB,MAAtB,CAA3B,CAA0D;gBACxD,IAAIuB,KAAK,KAAKwO,SAAd,EAAyB;oBACvB;;gBAGF+Y,cAAc,CAACnb,GAAD,CAAd,GAAsB3N,MAAM,CAAC8I,IAAP,CAAYuf,KAAZ,CAAkBU,gBAAlB,CAAmCpb,GAAnC,CAAtB;gBACA3N,MAAM,CAAC8I,IAAP,CAAYuf,KAAZ,CAAkBW,WAAlB,CAA8Brb,GAA9B,EAAmCpM,KAAnC;;;QAIJ,IAAIinB,MAAJ,IAAA,QAAIA,MAAM,CAAEhK,WAAZ,EAAyB;YACvB,KAAK,MAAM,CAAC7Q,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACoa,OAAP,CAAeiM,MAAM,CAAChK,WAAtB,CAA3B,CAA+D;gBAC7D,IAAIjd,KAAK,KAAKwO,SAAd,EAAyB;oBACvB;;gBAGFyO,WAAW,CAAC1V,IAAZ,CAAiBuf,KAAjB,CAAuBW,WAAvB,CAAmCrb,GAAnC,EAAwCpM,KAAxC;;;QAIJ,IAAI6mB,SAAJ,IAAA,QAAIA,SAAS,CAAEpoB,MAAf,EAAuB;YACrBA,MAAM,CAAC8I,IAAP,CAAYmgB,SAAZ,CAAsB3pB,GAAtB,CAA0B8oB,SAAS,CAACpoB,MAApC;;QAGF,IAAIooB,SAAJ,IAAA,QAAIA,SAAS,CAAE5J,WAAf,EAA4B;YAC1BA,WAAW,CAAC1V,IAAZ,CAAiBmgB,SAAjB,CAA2B3pB,GAA3B,CAA+B8oB,SAAS,CAAC5J,WAAzC;;QAGF,OAAO,SAASlC,OAAT;YACL,KAAK,MAAM,CAAC3O,GAAD,EAAMpM,KAAN,CAAX,IAA2BY,MAAM,CAACoa,OAAP,CAAeuM,cAAf,CAA3B,CAA2D;gBACzD9oB,MAAM,CAAC8I,IAAP,CAAYuf,KAAZ,CAAkBW,WAAlB,CAA8Brb,GAA9B,EAAmCpM,KAAnC;;YAGF,IAAI6mB,SAAJ,IAAA,QAAIA,SAAS,CAAEpoB,MAAf,EAAuB;gBACrBA,MAAM,CAAC8I,IAAP,CAAYmgB,SAAZ,CAAsBC,MAAtB,CAA6Bd,SAAS,CAACpoB,MAAvC;;SANJ;IASD,CA5CM;AA8CP,MAAMmpB,uBAAuB,IAAqB;IAAA,IAAC,EACjD3iB,SAAS,EAAE,EAACyb,OAAD,EAAUmH,KAAAA,IAD2B,GAAA;IAAA,OAE5C;QACJ;YACE5iB,SAAS,EAAEiiB,sMAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuB1G,OAAvB;SAFT;QAIJ;YACEzb,SAAS,EAAEiiB,sMAAG,CAACC,SAAJ,CAAcC,QAAd,CAAuBS,KAAvB;SALT;KAF4C;AAAA,CAAlD;AAWA,MAAaC,iCAAiC,GAAmC;IAC/EC,QAAQ,EAAE,GADqE;IAE/EC,MAAM,EAAE,MAFuE;IAG/EC,SAAS,EAAEL,uBAHoE;IAI/EM,WAAW,EAAA,WAAA,GAAEZ,+BAA+B,CAAC;QAC3CL,MAAM,EAAE;YACNxoB,MAAM,EAAE;gBACN0pB,OAAO,EAAE;;;KAH6B;AAJmC,CAA1E;AAaP,SAAgBC,iBAAAA,KAAAA;QAAiB,EAC/BlR,MAD+B,EAE/BhB,cAF+B,EAG/BpT,mBAH+B,EAI/B4a,sBAAAA;IAEA,WAAOjF,2MAAQ,EAAY,CAAC/Z,EAAD,EAAK6I,IAAL;QACzB,IAAI2P,MAAM,KAAK,IAAf,EAAqB;YACnB;;QAGF,MAAMmR,eAAe,GAA8BnS,cAAc,CAACjT,GAAf,CAAmBvE,EAAnB,CAAnD;QAEA,IAAI,CAAC2pB,eAAL,EAAsB;YACpB;;QAGF,MAAM9Y,UAAU,GAAG8Y,eAAe,CAAC9gB,IAAhB,CAAqBkI,OAAxC;QAEA,IAAI,CAACF,UAAL,EAAiB;YACf;;QAGF,MAAM+Y,cAAc,GAAGpM,iBAAiB,CAAC3U,IAAD,CAAxC;QAEA,IAAI,CAAC+gB,cAAL,EAAqB;YACnB;;QAEF,MAAM,EAACrjB,SAAAA,SAAa+B,4MAAS,EAACO,IAAD,CAAT,CAAgBN,gBAAhB,CAAiCM,IAAjC,CAApB;QACA,MAAMnB,eAAe,GAAGN,cAAc,CAACb,SAAD,CAAtC;QAEA,IAAI,CAACmB,eAAL,EAAsB;YACpB;;QAGF,MAAMwf,SAAS,GACb,OAAO1O,MAAP,KAAkB,UAAlB,GACIA,MADJ,GAEIqR,0BAA0B,CAACrR,MAAD,CAHhC;QAKAzL,sBAAsB,CACpB8D,UADoB,EAEpBmO,sBAAsB,CAACpf,SAAvB,CAAiCoN,OAFb,CAAtB;QAKA,OAAOka,SAAS,CAAC;YACfnnB,MAAM,EAAE;gBACNC,EADM;gBAENsD,IAAI,EAAEqmB,eAAe,CAACrmB,IAFhB;gBAGNuF,IAAI,EAAEgI,UAHA;gBAINhO,IAAI,EAAEmc,sBAAsB,CAACpf,SAAvB,CAAiCoN,OAAjC,CAAyC6D,UAAzC;aALO;YAOf2G,cAPe;YAQf+G,WAAW,EAAE;gBACX1V,IADW;gBAEXhG,IAAI,EAAEmc,sBAAsB,CAACT,WAAvB,CAAmCvR,OAAnC,CAA2C4c,cAA3C;aAVO;YAYfxlB,mBAZe;YAaf4a,sBAbe;YAcfzY,SAAS,EAAEmB;SAdG,CAAhB;KAvCa,CAAf;AAwDD;AAED,SAASmiB,0BAAT,CACEhoB,OADF;IAGE,MAAM,EAACwnB,QAAD,EAAWC,MAAX,EAAmBE,WAAnB,EAAgCD,SAAAA,KAAa;QACjD,GAAGH,iCAD8C;QAEjD,GAAGvnB,OAAAA;KAFL;IAKA,QAAO;YAAC,EAAC9B,MAAD,EAASwe,WAAT,EAAsBhY,SAAtB,EAAiC,GAAGujB;QAC1C,IAAI,CAACT,QAAL,EAAe;;YAEb;;QAGF,MAAM7a,KAAK,GAAG;YACZpM,CAAC,EAAEmc,WAAW,CAAC1b,IAAZ,CAAiBI,IAAjB,GAAwBlD,MAAM,CAAC8C,IAAP,CAAYI,IAD3B;YAEZZ,CAAC,EAAEkc,WAAW,CAAC1b,IAAZ,CAAiBM,GAAjB,GAAuBpD,MAAM,CAAC8C,IAAP,CAAYM,GAAAA;SAFxC;QAKA,MAAM4mB,KAAK,GAAG;YACZrjB,MAAM,EACJH,SAAS,CAACG,MAAV,KAAqB,CAArB,GACK3G,MAAM,CAAC8C,IAAP,CAAYK,KAAZ,GAAoBqD,SAAS,CAACG,MAA/B,GAAyC6X,WAAW,CAAC1b,IAAZ,CAAiBK,KAD9D,GAEI,CAJM;YAKZyD,MAAM,EACJJ,SAAS,CAACI,MAAV,KAAqB,CAArB,GACK5G,MAAM,CAAC8C,IAAP,CAAYO,MAAZ,GAAqBmD,SAAS,CAACI,MAAhC,GAA0C4X,WAAW,CAAC1b,IAAZ,CAAiBO,MAD/D,GAEI;SARR;QAUA,MAAM4mB,cAAc,GAAG;YACrB5nB,CAAC,EAAEmE,SAAS,CAACnE,CAAV,GAAcoM,KAAK,CAACpM,CADF;YAErBC,CAAC,EAAEkE,SAAS,CAAClE,CAAV,GAAcmM,KAAK,CAACnM,CAFF;YAGrB,GAAG0nB,KAAAA;SAHL;QAMA,MAAME,kBAAkB,GAAGV,SAAS,CAAC;YACnC,GAAGO,IADgC;YAEnC/pB,MAFmC;YAGnCwe,WAHmC;YAInChY,SAAS,EAAE;gBAACyb,OAAO,EAAEzb,SAAV;gBAAqB4iB,KAAK,EAAEa;;SAJL,CAApC;QAOA,MAAM,CAACE,aAAD,CAAA,GAAkBD,kBAAxB;QACA,MAAME,YAAY,GAAGF,kBAAkB,CAACA,kBAAkB,CAACnmB,MAAnB,GAA4B,CAA7B,CAAvC;QAEA,IAAIiT,IAAI,CAACC,SAAL,CAAekT,aAAf,MAAkCnT,IAAI,CAACC,SAAL,CAAemT,YAAf,CAAtC,EAAoE;;YAElE;;QAGF,MAAM9N,OAAO,GAAGmN,WAAH,IAAA,OAAA,KAAA,IAAGA,WAAW,CAAG;YAACzpB,MAAD;YAASwe,WAAT;YAAsB,GAAGuL,IAAAA;SAA5B,CAA3B;QACA,MAAM5C,SAAS,GAAG3I,WAAW,CAAC1V,IAAZ,CAAiBuhB,OAAjB,CAAyBH,kBAAzB,EAA6C;YAC7DZ,QAD6D;YAE7DC,MAF6D;YAG7De,IAAI,EAAE;SAHU,CAAlB;QAMA,OAAO,IAAIjG,OAAJ,EAAaC,OAAD;YACjB6C,SAAS,CAACoD,QAAV,GAAqB;gBACnBjO,OAAO,IAAA,IAAP,GAAA,KAAA,IAAAA,OAAO;gBACPgI,OAAO;aAFT;SADK,CAAP;KAjDF;AAwDD;AC9RD,IAAI3W,GAAG,GAAG,CAAV;AAEA,SAAgB6c,OAAOvqB,EAAAA;IACrB,WAAOiB,0OAAO,EAAC;QACb,IAAIjB,EAAE,IAAI,IAAV,EAAgB;YACd;;QAGF0N,GAAG;QACH,OAAOA,GAAP;KANY,EAOX;QAAC1N,EAAD;KAPW,CAAd;AAQD;MCaYwqB,WAAW,GAAA,WAAA,GAAGppB,0OAAK,CAAC8f,IAAN,EACzB;QAAC,EACC5a,WAAW,GAAG,KADf,EAECmX,QAFD,EAGCgN,aAAa,EAAEC,mBAHhB,EAICtC,KAJD,EAKCC,UALD,EAMC5H,SAND,EAOCkK,cAAc,GAAG,KAPlB,EAQCxC,SARD,EASCyC,MAAM,GAAG,GAAA;IAET,MAAM,EACJ/L,cADI,EAEJ9e,MAFI,EAGJ+e,cAHI,EAIJC,iBAJI,EAKJvH,cALI,EAMJpT,mBANI,EAOJma,WAPI,EAQJre,IARI,EASJ8e,sBATI,EAUJpS,mBAVI,EAWJoJ,uBAXI,EAYJqH,UAAAA,KACE4I,aAAa,EAbjB;IAcA,MAAM1f,SAAS,OAAG3H,6OAAU,EAACmiB,sBAAD,CAA5B;IACA,MAAMrT,GAAG,GAAG6c,MAAM,CAACxqB,MAAD,IAAA,OAAA,KAAA,IAACA,MAAM,CAAEC,EAAT,CAAlB;IACA,MAAM6qB,iBAAiB,GAAGrK,cAAc,CAACC,SAAD,EAAY;QAClD5B,cADkD;QAElD9e,MAFkD;QAGlD+e,cAHkD;QAIlDC,iBAJkD;QAKlDkE,gBAAgB,EAAE1E,WAAW,CAAC1b,IALoB;QAMlD3C,IANkD;QAOlDqjB,eAAe,EAAEhF,WAAW,CAAC1b,IAPqB;QAQlD+J,mBARkD;QASlDoJ,uBATkD;QAUlDzP,SAVkD;QAWlD8W;KAXsC,CAAxC;IAaA,MAAM3B,WAAW,GAAGjC,eAAe,CAACqF,cAAD,CAAnC;IACA,MAAM2L,aAAa,GAAGf,gBAAgB,CAAC;QACrClR,MAAM,EAAEkS,mBAD6B;QAErClT,cAFqC;QAGrCpT,mBAHqC;QAIrC4a;KAJoC,CAAtC,EAAA,4FAAA;;IAQA,MAAMyI,GAAG,GAAG/L,WAAW,GAAG6C,WAAW,CAACR,MAAf,GAAwBjO,SAA/C;IAEA,OACE1O,0OAAAA,CAAAA,aAAA,CAACumB,wBAAD,EAAA,IAAA,EACEvmB,0OAAAA,CAAAA,aAAA,CAAC6lB,gBAAD,EAAA;QAAkBC,SAAS,EAAEuD;KAA7B,EACG1qB,MAAM,IAAI2N,GAAV,GACCtM,0OAAAA,CAAAA,aAAA,CAAC4mB,iBAAD,EAAA;QACEta,GAAG,EAAEA;QACL1N,EAAE,EAAED,MAAM,CAACC,EAAAA;QACXynB,GAAG,EAAEA;QACLS,EAAE,EAAEyC;QACJ9L,cAAc,EAAEA;QAChBvY,WAAW,EAAEA;QACb6hB,SAAS,EAAEA;QACXE,UAAU,EAAEA;QACZxlB,IAAI,EAAE6Y;QACN0M,KAAK,EAAE;YACLwC,MADK;YAEL,GAAGxC,KAAAA;;QAEL7hB,SAAS,EAAEskB;KAdb,EAgBGpN,QAhBH,CADD,GAmBG,IApBN,CADF,CADF;AA0BD,CA9EwB,CAApB", "debugId": null}}]}