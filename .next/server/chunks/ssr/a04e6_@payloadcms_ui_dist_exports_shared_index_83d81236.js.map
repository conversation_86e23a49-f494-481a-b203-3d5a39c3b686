{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/elements/Translation/index.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/elements/withMergedProps/index.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/elements/WithServerSideProps/index.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/fields/mergeFieldStyles.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/forms/Form/reduceToSerializableFields.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/graphics/Icon/index.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/graphics/Logo/index.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/providers/TableColumns/buildColumnState/filterFields.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/providers/TableColumns/getInitialColumns.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/abortAndIgnore.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/api.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/findLocaleFromCode.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/formatAdminURL.ts", "file:///home/<USER>/git/payload-test/node_modules/node_modules/.pnpm/%40date-fns%2Btz%401.2.0/node_modules/%40date-fns/tz/tzOffset/index.js", "file:///home/<USER>/git/payload-test/node_modules/node_modules/.pnpm/%40date-fns%2Btz%401.2.0/node_modules/%40date-fns/tz/date/mini.js", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/formatDocTitle/formatDateTitle.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/formatDocTitle/index.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/formatDocTitle/formatLexicalDocTitle.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/formatDocTitle/formatRelationshipTitle.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/groupNavItems.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/handleBackToDashboard.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/handleGoBack.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/handleTakeOver.tsx", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/hasSavePermission.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/isClientUserObject.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/isEditing.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/utilities/sanitizeID.ts", "file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/ui/src/exports/shared/index.ts"], "sourcesContent": ["import type { ClientTranslationKeys, TFunction } from '@payloadcms/translations'\n\nimport * as React from 'react'\n\nconst RecursiveTranslation: React.FC<{\n  elements?: Record<string, React.FC<{ children: React.ReactNode }>>\n  translationString: string\n}> = ({ elements, translationString }) => {\n  const regex = /(<[^>]+>.*?<\\/[^>]+>)/g\n  const sections = translationString.split(regex)\n\n  return (\n    <span>\n      {sections.map((section, index) => {\n        if (elements && section.startsWith('<') && section.endsWith('>')) {\n          const elementKey = section[1]\n          const Element = elements[elementKey]\n\n          if (Element) {\n            const regex = new RegExp(`<${elementKey}>(.*?)<\\/${elementKey}>`, 'g')\n            const children = section.replace(regex, (_, group) => group)\n\n            return (\n              <Element key={index}>\n                <RecursiveTranslation translationString={children} />\n              </Element>\n            )\n          }\n        }\n\n        return section\n      })}\n    </span>\n  )\n}\n\nexport type TranslationProps = {\n  elements?: Record<string, React.FC<{ children: React.ReactNode }>>\n  i18nKey: ClientTranslationKeys\n  t: TFunction\n  variables?: Record<string, unknown>\n}\n\nexport const Translation: React.FC<TranslationProps> = ({ elements, i18nKey, t, variables }) => {\n  const stringWithVariables = t(i18nKey, variables || {})\n\n  if (!elements) {\n    return stringWithVariables\n  }\n\n  return <RecursiveTranslation elements={elements} translationString={stringWithVariables} />\n}\n", "import { isReactServerComponentOrFunction, serverProps } from 'payload/shared'\nimport React from 'react'\n\n/**\n * Creates a higher-order component (HOC) that merges predefined properties (`toMergeIntoProps`)\n * with any properties passed to the resulting component.\n *\n * Use this when you want to pre-specify some props for a component, while also allowing users to\n * pass in their own props. The HOC ensures the passed props and predefined props are combined before\n * rendering the original component.\n *\n * @example\n * const PredefinedComponent = getMergedPropsComponent({\n *   Component: OriginalComponent,\n *   toMergeIntoProps: { someExtraValue: 5 }\n * });\n * // Using <PredefinedComponent customProp=\"value\" /> will result in\n * // <OriginalComponent customProp=\"value\" someExtraValue={5} />\n *\n * @returns A higher-order component with combined properties.\n *\n * @param Component - The original component to wrap.\n * @param sanitizeServerOnlyProps - If true, server-only props will be removed from the merged props. @default true if the component is not a server component, false otherwise.\n * @param toMergeIntoProps - The properties to merge into the passed props.\n */\nexport function withMergedProps<ToMergeIntoProps, CompleteReturnProps>({\n  Component,\n  sanitizeServerOnlyProps,\n  toMergeIntoProps,\n}: {\n  Component: React.FC<CompleteReturnProps>\n  sanitizeServerOnlyProps?: boolean\n  toMergeIntoProps: ToMergeIntoProps\n}): React.FC<CompleteReturnProps> {\n  if (sanitizeServerOnlyProps === undefined) {\n    sanitizeServerOnlyProps = !isReactServerComponentOrFunction(Component)\n  }\n  // A wrapper around the args.Component to inject the args.toMergeArgs as props, which are merged with the passed props\n  const MergedPropsComponent: React.FC<CompleteReturnProps> = (passedProps) => {\n    const mergedProps = simpleMergeProps(passedProps, toMergeIntoProps) as CompleteReturnProps\n\n    if (sanitizeServerOnlyProps) {\n      serverProps.forEach((prop) => {\n        delete mergedProps[prop]\n      })\n    }\n\n    return <Component {...mergedProps} />\n  }\n\n  return MergedPropsComponent\n}\n\nfunction simpleMergeProps(props, toMerge) {\n  return { ...props, ...toMerge }\n}\n", "import type { WithServerSidePropsComponent } from 'payload'\n\nimport { isReactServerComponentOrFunction } from 'payload/shared'\nimport React from 'react'\n\nexport const WithServerSideProps: WithServerSidePropsComponent = ({\n  Component,\n  serverOnlyProps,\n  ...rest\n}) => {\n  if (Component) {\n    const WithServerSideProps: React.FC = (passedProps) => {\n      const propsWithServerOnlyProps = {\n        ...passedProps,\n        ...(isReactServerComponentOrFunction(Component) ? (serverOnlyProps ?? {}) : {}),\n      }\n\n      return <Component {...propsWithServerOnlyProps} />\n    }\n\n    return WithServerSideProps(rest)\n  }\n\n  return null\n}\n", "import type { ClientField } from 'payload'\n\nexport const mergeFieldStyles = (\n  field: ClientField | Omit<ClientField, 'type'>,\n): React.CSSProperties => ({\n  ...(field?.admin?.style || {}),\n  ...(field?.admin?.width\n    ? {\n        '--field-width': field.admin.width,\n      }\n    : {\n        flex: '1 1 auto',\n      }),\n  // allow flex overrides to still take precedence over the fallback\n  ...(field?.admin?.style?.flex\n    ? {\n        flex: field.admin.style.flex,\n      }\n    : {}),\n})\n", "import { type FormField, type FormState } from 'payload'\n\ntype BlacklistedKeys = 'customComponents' | 'validate'\nconst blacklistedKeys: BlacklistedKeys[] = ['validate', 'customComponents']\n\nconst sanitizeField = (incomingField: FormField): FormField => {\n  const field = { ...incomingField } // shallow copy, as we only need to remove top-level keys\n\n  for (const key of blacklistedKeys) {\n    delete field[key]\n  }\n\n  return field\n}\n\n/**\n * Takes in FormState and removes fields that are not serializable.\n * Returns FormState without blacklisted keys.\n */\nexport const reduceToSerializableFields = (\n  fields: FormState,\n): {\n  [key: string]: Omit<FormField, BlacklistedKeys>\n} => {\n  const result: Record<string, Omit<FormField, BlacklistedKeys>> = {}\n\n  for (const key in fields) {\n    result[key] = sanitizeField(fields[key])\n  }\n\n  return result\n}\n", "import React from 'react'\n\nexport const PayloadIcon: React.FC<{\n  fill?: string\n}> = ({ fill: fillFromProps }) => {\n  const fill = fillFromProps || 'var(--theme-elevation-1000)'\n\n  return (\n    <svg\n      className=\"graphic-icon\"\n      height=\"100%\"\n      viewBox=\"0 0 25 25\"\n      width=\"100%\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path\n        d=\"M11.8673 21.2336L4.40922 16.9845C4.31871 16.9309 4.25837 16.8355 4.25837 16.7282V10.1609C4.25837 10.0477 4.38508 9.97616 4.48162 10.0298L13.1404 14.9642C13.2611 15.0358 13.412 14.9464 13.412 14.8093V11.6091C13.412 11.4839 13.3456 11.3647 13.2309 11.2992L2.81624 5.36353C2.72573 5.30989 2.60505 5.30989 2.51454 5.36353L1.15085 6.14422C1.06034 6.19786 1 6.29321 1 6.40048V18.5995C1 18.7068 1.06034 18.8021 1.15085 18.8558L11.8491 24.9583C11.9397 25.0119 12.0603 25.0119 12.1509 24.9583L21.1355 19.8331C21.2562 19.7616 21.2562 19.5948 21.1355 19.5232L18.3357 17.9261C18.2211 17.8605 18.0883 17.8605 17.9737 17.9261L12.175 21.2336C12.0845 21.2872 11.9638 21.2872 11.8733 21.2336H11.8673Z\"\n        fill={fill}\n      />\n      <path\n        d=\"M22.8491 6.13827L12.1508 0.0417218C12.0603 -0.0119135 11.9397 -0.0119135 11.8491 0.0417218L6.19528 3.2658C6.0746 3.33731 6.0746 3.50418 6.19528 3.57569L8.97092 5.16091C9.08557 5.22647 9.21832 5.22647 9.33296 5.16091L11.8672 3.71872C11.9578 3.66508 12.0784 3.66508 12.1689 3.71872L19.627 7.96782C19.7175 8.02146 19.7778 8.11681 19.7778 8.22408V14.8212C19.7778 14.9464 19.8442 15.0656 19.9589 15.1311L22.7345 16.7104C22.8552 16.7819 23.006 16.6925 23.006 16.5554V6.40048C23.006 6.29321 22.9457 6.19786 22.8552 6.14423L22.8491 6.13827Z\"\n        fill={fill}\n      />\n    </svg>\n  )\n}\n", "import React from 'react'\n\nconst css = `\n  .graphic-logo path {\n    fill: var(--theme-elevation-1000);\n  }\n`\n\nexport const PayloadLogo: React.FC = () => (\n  <svg\n    className=\"graphic-logo\"\n    fill=\"none\"\n    height=\"43.5\"\n    id=\"b\"\n    viewBox=\"0 0 193.38 43.5\"\n    width=\"193.38\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <style>{css}</style>\n    <g id=\"c\">\n      <path d=\"M18.01,35.63l-12.36-7.13c-.15-.09-.25-.25-.25-.43v-11.02c0-.19.21-.31.37-.22l14.35,8.28c.2.12.45-.03.45-.26v-5.37c0-.21-.11-.41-.3-.52L3.01,9c-.15-.09-.35-.09-.5,0l-2.26,1.31c-.15.09-.25.25-.25.43v20.47c0,.18.1.34.25.43l17.73,10.24c.15.09.35.09.5,0l14.89-8.6c.2-.12.2-.4,0-.52l-4.64-2.68c-.19-.11-.41-.11-.6,0l-9.61,5.55c-.15.09-.35.09-.5,0Z\" />\n      <path d=\"M36.21,10.3L18.48.07c-.15-.09-.35-.09-.5,0l-9.37,5.41c-.2.12-.2.4,0,.52l4.6,2.66c.19.11.41.11.6,0l4.2-2.42c.15-.09.35-.09.5,0l12.36,7.13c.15.09.25.25.25.43v11.07c0,.21.11.41.3.52l4.6,2.65c.2.12.45-.03.45-.26V10.74c0-.18-.1-.34-.25-.43Z\" />\n      <g id=\"d\">\n        <path d=\"M193.38,9.47c0,1.94-1.48,3.32-3.3,3.32s-3.31-1.39-3.31-3.32,1.49-3.31,3.31-3.31,3.3,1.39,3.3,3.31ZM192.92,9.47c0-1.68-1.26-2.88-2.84-2.88s-2.84,1.2-2.84,2.88,1.26,2.89,2.84,2.89,2.84-1.2,2.84-2.89ZM188.69,11.17v-3.51h1.61c.85,0,1.35.39,1.35,1.15,0,.53-.3.86-.67,1.02l.79,1.35h-.89l-.72-1.22h-.64v1.22h-.82ZM190.18,9.31c.46,0,.64-.16.64-.5s-.19-.49-.64-.49h-.67v.99h.67Z\" />\n        <path d=\"M54.72,24.84v10.93h-5.4V6.1h12.26c7.02,0,11.1,3.2,11.1,9.39s-4.07,9.35-11.06,9.35h-6.9,0ZM61.12,20.52c4.07,0,6.11-1.66,6.11-5.03s-2.04-5.03-6.11-5.03h-6.4v10.06h6.4Z\" />\n        <path d=\"M85.94,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.18-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM85.73,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z\" />\n        <path d=\"M90.39,14.66h5.4l5.86,15.92h.08l5.57-15.92h5.28l-8.23,21.49c-2,5.28-4.45,7.32-8.89,7.36-.71,0-1.7-.08-2.45-.21v-4.03c.62.13.96.13,1.41.13,2.16,0,3.07-.75,4.2-3.66l-8.23-21.07h0Z\" />\n        <path d=\"M113.46,35.77V6.1h5.32v29.67h-5.32Z\" />\n        <path d=\"M130.79,36.27c-6.23,0-10.68-4.2-10.68-11.05s4.45-11.05,10.68-11.05,10.68,4.24,10.68,11.05-4.45,11.05-10.68,11.05ZM130.79,32.32c3.41,0,5.36-2.66,5.36-7.11s-1.95-7.11-5.36-7.11-5.36,2.7-5.36,7.11,1.91,7.11,5.36,7.11Z\" />\n        <path d=\"M156.19,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.19-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM155.98,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z\" />\n        <path d=\"M178.5,32.41c-1.04,2.12-3.58,3.87-6.78,3.87-5.53,0-9.31-4.49-9.31-11.05s3.78-11.05,9.31-11.05c3.28,0,5.69,1.83,6.69,3.95V6.1h5.32v29.67h-5.24v-3.37h0ZM178.55,24.84c0-4.11-1.95-6.78-5.32-6.78s-5.45,2.83-5.45,7.15,2,7.15,5.45,7.15,5.32-2.66,5.32-6.78v-.75h0Z\" />\n      </g>\n    </g>\n  </svg>\n)\n", "import type { ClientField, Field } from 'payload'\n\nimport { fieldIsHiddenOrDisabled, fieldIsID } from 'payload/shared'\n\n/**\n * Filters fields that are hidden, disabled, or have `disableListColumn` set to `true`.\n * Recurses through `tabs` and any container with `.fields` (e.g., `row`, `group`, `collapsible`).\n */\nexport const filterFields = <T extends ClientField | Field>(incomingFields: T[]): T[] => {\n  const shouldSkipField = (field: T): boolean =>\n    (field.type !== 'ui' && fieldIsHiddenOrDisabled(field) && !fieldIsID(field)) ||\n    field?.admin?.disableListColumn === true\n\n  return (incomingFields ?? []).reduce<T[]>((acc, field) => {\n    if (shouldSkipField(field)) {\n      return acc\n    }\n\n    // handle tabs\n    if (field.type === 'tabs' && 'tabs' in field) {\n      const formattedField: T = {\n        ...field,\n        tabs: field.tabs.map((tab) => ({\n          ...tab,\n          fields: filterFields(tab.fields as T[]),\n        })),\n      }\n      acc.push(formattedField)\n      return acc\n    }\n\n    // handle fields with subfields (row, group, collapsible, etc.)\n    if ('fields' in field && Array.isArray(field.fields)) {\n      const formattedField: T = {\n        ...field,\n        fields: filterFields(field.fields as T[]),\n      }\n      acc.push(formattedField)\n      return acc\n    }\n\n    // leaf\n    acc.push(field)\n    return acc\n  }, [])\n}\n", "import type { ClientField, CollectionConfig, CollectionPreferences, Field } from 'payload'\n\nimport { fieldAffectsData } from 'payload/shared'\n\nconst getRemainingColumns = <T extends ClientField[] | Field[]>(\n  fields: T,\n  useAsTitle: string,\n): CollectionPreferences['columns'] =>\n  fields?.reduce((remaining, field) => {\n    if (fieldAffectsData(field) && field.name === useAsTitle) {\n      return remaining\n    }\n\n    if (!fieldAffectsData(field) && 'fields' in field) {\n      return [...remaining, ...getRemainingColumns(field.fields, useAsTitle)]\n    }\n\n    if (field.type === 'tabs' && 'tabs' in field) {\n      return [\n        ...remaining,\n        ...field.tabs.reduce(\n          (tabFieldColumns, tab) => [\n            ...tabFieldColumns,\n            ...('name' in tab ? [tab.name] : getRemainingColumns(tab.fields, useAsTitle)),\n          ],\n          [],\n        ),\n      ]\n    }\n\n    return [...remaining, field.name]\n  }, [])\n\n/**\n * Returns the initial columns to display in the table based on the following criteria:\n * 1. If `defaultColumns` is set in the collection config, use those columns\n * 2. Otherwise take `useAtTitle, if set, and the next 3 fields that are not hidden or disabled\n */\nexport const getInitialColumns = <T extends ClientField[] | Field[]>(\n  fields: T,\n  useAsTitle: CollectionConfig['admin']['useAsTitle'],\n  defaultColumns: CollectionConfig['admin']['defaultColumns'],\n): CollectionPreferences['columns'] => {\n  let initialColumns = []\n\n  if (Array.isArray(defaultColumns) && defaultColumns.length >= 1) {\n    initialColumns = defaultColumns\n  } else {\n    if (useAsTitle) {\n      initialColumns.push(useAsTitle)\n    }\n\n    const remainingColumns = getRemainingColumns(fields, useAsTitle)\n\n    initialColumns = initialColumns.concat(remainingColumns)\n    initialColumns = initialColumns.slice(0, 4)\n  }\n\n  return initialColumns.map((column) => ({\n    accessor: column,\n    active: true,\n  }))\n}\n", "export function abortAndIgnore(abortController: AbortController) {\n  if (abortController) {\n    try {\n      abortController.abort()\n    } catch (_err) {\n      // swallow error\n    }\n  }\n}\n\n/**\n * Use this function when an effect is triggered multiple times over and you want to cancel the previous effect.\n * It will abort the previous effect and create a new AbortController for the next effect.\n * Important: You must also _reset_ the `abortControllerRef` after the effect is done, otherwise the next effect will be aborted immediately.\n * For example, run `abortControllerRef.current = null` in a `finally` block or after an awaited promise.\n * @param abortControllerRef\n * @returns {AbortController}\n */\nexport function handleAbortRef(\n  abortControllerRef: React.RefObject<AbortController>,\n): AbortController {\n  const newController = new AbortController()\n\n  if (abortControllerRef.current) {\n    try {\n      abortControllerRef.current.abort()\n    } catch (_err) {\n      // swallow error\n    }\n  }\n\n  abortControllerRef.current = newController\n\n  return newController\n}\n", "import * as qs from 'qs-esm'\n\ntype GetOptions = {\n  params?: Record<string, unknown>\n} & RequestInit\n\nexport const requests = {\n  delete: (url: string, options: RequestInit = { headers: {} }): Promise<Response> => {\n    const headers = options && options.headers ? { ...options.headers } : {}\n\n    const formattedOptions: RequestInit = {\n      ...options,\n      credentials: 'include',\n      headers: {\n        ...headers,\n      },\n      method: 'delete',\n    }\n\n    return fetch(url, formattedOptions)\n  },\n\n  get: (url: string, options: GetOptions = { headers: {} }): Promise<Response> => {\n    let query = ''\n    if (options.params) {\n      query = qs.stringify(options.params, { addQueryPrefix: true })\n    }\n    return fetch(`${url}${query}`, {\n      credentials: 'include',\n      ...options,\n    })\n  },\n\n  patch: (url: string, options: RequestInit = { headers: {} }): Promise<Response> => {\n    const headers = options && options.headers ? { ...options.headers } : {}\n\n    const formattedOptions: RequestInit = {\n      ...options,\n      credentials: 'include',\n      headers: {\n        ...headers,\n      },\n      method: 'PATCH',\n    }\n\n    return fetch(url, formattedOptions)\n  },\n\n  post: (url: string, options: RequestInit = { headers: {} }): Promise<Response> => {\n    const headers = options && options.headers ? { ...options.headers } : {}\n\n    const formattedOptions: RequestInit = {\n      ...options,\n      credentials: 'include',\n      headers: {\n        ...headers,\n      },\n      method: 'post',\n    }\n\n    return fetch(`${url}`, formattedOptions)\n  },\n\n  put: (url: string, options: RequestInit = { headers: {} }): Promise<Response> => {\n    const headers = options && options.headers ? { ...options.headers } : {}\n\n    const formattedOptions: RequestInit = {\n      ...options,\n      credentials: 'include',\n      headers: {\n        ...headers,\n      },\n      method: 'put',\n    }\n\n    return fetch(url, formattedOptions)\n  },\n}\n", "import type { Locale, SanitizedLocalizationConfig } from 'payload'\n/*\n return the Locale for given locale code, else return null\n*/\nexport const findLocaleFromCode = (\n  localizationConfig: SanitizedLocalizationConfig,\n  locale: string,\n): Locale | null => {\n  if (!localizationConfig?.locales || localizationConfig.locales.length === 0) {\n    return null\n  }\n\n  return localizationConfig.locales.find((el) => el?.code === locale)\n}\n", "/** Will read the `routes.admin` config and appropriately handle `\"/\"` admin paths */\nexport { formatAdminURL } from 'payload/shared'\n", "const offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nexport function tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-GB\", {\n      timeZone,\n      hour: \"numeric\",\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split('GMT')[1] || '';\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +values[0];\n  const minutes = +(values[1] || 0);\n  return offsetCache[cacheStr] = hours > 0 ? hours * 60 + minutes : hours * 60 - minutes;\n}", "import { tzOffset } from \"../tzOffset/index.js\";\nexport class TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN(tzOffset(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    return -tzOffset(this.timeZone, this);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCMinutes(date.internal.getUTCMinutes() - date.getTimezoneOffset());\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const offset = tzOffset(date.timeZone, date);\n\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postOffset = tzOffset(date.timeZone, date);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newOffset = tzOffset(date.timeZone, date);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}", "import type { I18n, I18nClient } from '@payloadcms/translations'\n\nimport { TZDateMini as TZDate } from '@date-fns/tz/date/mini'\nimport { format, formatDistanceToNow, transpose } from 'date-fns'\n\nexport type FormatDateArgs = {\n  date: Date | number | string | undefined\n  i18n: I18n<unknown, unknown> | I18nClient<unknown>\n  pattern: string\n  timezone?: string\n}\n\nexport const formatDate = ({ date, i18n, pattern, timezone }: FormatDateArgs): string => {\n  const theDate = new TZDate(new Date(date))\n\n  if (timezone) {\n    const DateWithOriginalTz = TZDate.tz(timezone)\n\n    const modifiedDate = theDate.withTimeZone(timezone)\n\n    // Transpose the date to the selected timezone\n    const dateWithTimezone = transpose(modifiedDate, DateWithOriginalTz)\n\n    // Transpose the date to the user's timezone - this is necessary because the react-datepicker component insists on displaying the date in the user's timezone\n    return i18n.dateFNS\n      ? format(dateWithTimezone, pattern, { locale: i18n.dateFNS })\n      : `${i18n.t('general:loading')}...`\n  }\n\n  return i18n.dateFNS\n    ? format(theDate, pattern, { locale: i18n.dateFNS })\n    : `${i18n.t('general:loading')}...`\n}\n\ntype FormatTimeToNowArgs = {\n  date: Date | number | string | undefined\n  i18n: I18n<unknown, unknown> | I18nClient<unknown>\n}\n\nexport const formatTimeToNow = ({ date, i18n }: FormatTimeToNowArgs): string => {\n  const theDate = typeof date === 'string' ? new Date(date) : date\n  return i18n?.dateFNS\n    ? formatDistanceToNow(theDate, { locale: i18n.dateFNS })\n    : `${i18n.t('general:loading')}...`\n}\n", "import type { I18n } from '@payloadcms/translations'\nimport type {\n  ClientCollectionConfig,\n  ClientGlobalConfig,\n  SanitizedConfig,\n  TypeWithID,\n} from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\n\nimport { formatDate } from './formatDateTitle.js'\nimport { formatLexicalDocTitle, isSerializedLexicalEditor } from './formatLexicalDocTitle.js'\nimport { formatRelationshipTitle } from './formatRelationshipTitle.js'\n\nexport const formatDocTitle = ({\n  collectionConfig,\n  data,\n  dateFormat: dateFormatFromConfig,\n  fallback,\n  globalConfig,\n  i18n,\n}: {\n  collectionConfig?: ClientCollectionConfig\n  data: TypeWithID\n  dateFormat: SanitizedConfig['admin']['dateFormat']\n  fallback?: object | string\n  globalConfig?: ClientGlobalConfig\n  i18n: I18n<any, any>\n}): string => {\n  let title: string\n\n  if (collectionConfig) {\n    const useAsTitle = collectionConfig?.admin?.useAsTitle\n\n    if (useAsTitle) {\n      title = data?.[useAsTitle] as string\n\n      if (title) {\n        const fieldConfig = collectionConfig.fields.find(\n          (f) => 'name' in f && f.name === useAsTitle,\n        )\n\n        const isDate = fieldConfig?.type === 'date'\n        const isRelationship = fieldConfig?.type === 'relationship'\n\n        if (isDate) {\n          const dateFormat =\n            ('date' in fieldConfig.admin && fieldConfig?.admin?.date?.displayFormat) ||\n            dateFormatFromConfig\n\n          title = formatDate({ date: title, i18n, pattern: dateFormat }) || title\n        }\n\n        if (isRelationship) {\n          const formattedRelationshipTitle = formatRelationshipTitle(data[useAsTitle])\n          title = formattedRelationshipTitle\n        }\n      }\n    }\n  }\n\n  if (globalConfig) {\n    title = getTranslation(globalConfig?.label, i18n) || globalConfig?.slug\n  }\n\n  // richtext lexical case. We convert the first child of root to plain text\n  if (title && isSerializedLexicalEditor(title)) {\n    title = formatLexicalDocTitle(title.root.children?.[0]?.children || [], '')\n  }\n\n  if (!title && isSerializedLexicalEditor(fallback)) {\n    title = formatLexicalDocTitle(fallback.root.children?.[0]?.children || [], '')\n  }\n\n  if (!title) {\n    title = typeof fallback === 'string' ? fallback : `[${i18n.t('general:untitled')}]`\n  }\n\n  return title\n}\n", "type SerializedLexicalEditor = {\n  root: {\n    children: Array<{ children?: Array<{ type: string }>; type: string }>\n  }\n}\n\nexport function isSerializedLexicalEditor(value: unknown): value is SerializedLexicalEditor {\n  return typeof value === 'object' && 'root' in value\n}\n\nexport function formatLexicalDocTitle(\n  editorState: Array<{ children?: Array<{ type: string }>; type: string }>,\n  textContent: string,\n): string {\n  for (const node of editorState) {\n    if ('text' in node && node.text) {\n      textContent += node.text as string\n    } else {\n      if (!('children' in node)) {\n        textContent += `[${node.type}]`\n      }\n    }\n    if ('children' in node && node.children) {\n      textContent += formatLexicalDocTitle(node.children as Array<{ type: string }>, textContent)\n    }\n  }\n  return textContent\n}\n", "export const formatRelationshipTitle = (data): string => {\n  if (Array.isArray(data)) {\n    return data\n      .map((item) => {\n        if (typeof item === 'object' && item !== null) {\n          return item.id\n        }\n        return String(item)\n      })\n      .filter(Boolean)\n      .join(', ')\n  }\n\n  if (typeof data === 'object' && data !== null) {\n    return data.id || ''\n  }\n\n  return String(data)\n}\n", "import type { I18nClient } from '@payloadcms/translations'\nimport type {\n  SanitizedCollectionConfig,\n  SanitizedGlobalConfig,\n  SanitizedPermissions,\n  StaticLabel,\n} from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\n\nexport enum EntityType {\n  collection = 'collections',\n  global = 'globals',\n}\n\nexport type EntityToGroup =\n  | {\n      entity: SanitizedCollectionConfig\n      type: EntityType.collection\n    }\n  | {\n      entity: SanitizedGlobalConfig\n      type: EntityType.global\n    }\n\nexport type NavGroupType = {\n  entities: {\n    label: StaticLabel\n    slug: string\n    type: EntityType\n  }[]\n  label: string\n}\n\nexport function groupNavItems(\n  entities: EntityToGroup[],\n  permissions: SanitizedPermissions,\n  i18n: I18nClient,\n): NavGroupType[] {\n  const result = entities.reduce(\n    (groups, entityToGroup) => {\n      // Skip entities where admin.group is explicitly false\n      if (entityToGroup.entity?.admin?.group === false) {\n        return groups\n      }\n\n      if (permissions?.[entityToGroup.type.toLowerCase()]?.[entityToGroup.entity.slug]?.read) {\n        const translatedGroup = getTranslation(entityToGroup.entity.admin.group, i18n)\n\n        const labelOrFunction =\n          'labels' in entityToGroup.entity\n            ? entityToGroup.entity.labels.plural\n            : entityToGroup.entity.label\n\n        const label =\n          typeof labelOrFunction === 'function'\n            ? labelOrFunction({ i18n, t: i18n.t })\n            : labelOrFunction\n\n        if (entityToGroup.entity.admin.group) {\n          const existingGroup = groups.find(\n            (group) => getTranslation(group.label, i18n) === translatedGroup,\n          ) as NavGroupType\n\n          let matchedGroup: NavGroupType = existingGroup\n\n          if (!existingGroup) {\n            matchedGroup = { entities: [], label: translatedGroup }\n            groups.push(matchedGroup)\n          }\n\n          matchedGroup.entities.push({\n            slug: entityToGroup.entity.slug,\n            type: entityToGroup.type,\n            label,\n          })\n        } else {\n          const defaultGroup = groups.find((group) => {\n            return getTranslation(group.label, i18n) === i18n.t(`general:${entityToGroup.type}`)\n          }) as NavGroupType\n          defaultGroup.entities.push({\n            slug: entityToGroup.entity.slug,\n            type: entityToGroup.type,\n            label,\n          })\n        }\n      }\n\n      return groups\n    },\n    [\n      {\n        entities: [],\n        label: i18n.t('general:collections'),\n      },\n      {\n        entities: [],\n        label: i18n.t('general:globals'),\n      },\n    ],\n  )\n\n  return result.filter((group) => group.entities.length > 0)\n}\n", "import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime.js'\n\nimport { formatAdminURL } from 'payload/shared'\n\ntype BackToDashboardProps = {\n  adminRoute: string\n  router: AppRouterInstance\n}\n\nexport const handleBackToDashboard = ({ adminRoute, router }: BackToDashboardProps) => {\n  const redirectRoute = formatAdminURL({\n    adminRoute,\n    path: '/',\n  })\n  router.push(redirectRoute)\n}\n", "import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime.js'\n\nimport { formatAdminURL } from 'payload/shared'\n\ntype GoBackProps = {\n  adminRoute: string\n  collectionSlug: string\n  router: AppRouterInstance\n}\n\nexport const handleGoBack = ({ adminRoute, collectionSlug, router }: GoBackProps) => {\n  const redirectRoute = formatAdminURL({\n    adminRoute,\n    path: collectionSlug ? `/collections/${collectionSlug}` : '/',\n  })\n  router.push(redirectRoute)\n}\n", "import type { ClientUser } from 'payload'\n\nexport interface HandleTakeOverParams {\n  clearRouteCache?: () => void\n  collectionSlug?: string\n  documentLockStateRef: React.RefObject<{\n    hasShownLockedModal: boolean\n    isLocked: boolean\n    user: ClientUser | number | string\n  }>\n  globalSlug?: string\n  id: number | string\n  isLockingEnabled: boolean\n  isWithinDoc: boolean\n  setCurrentEditor: (value: React.SetStateAction<ClientUser | number | string>) => void\n  setIsReadOnlyForIncomingUser?: (value: React.SetStateAction<boolean>) => void\n  updateDocumentEditor: (\n    docID: number | string,\n    slug: string,\n    user: ClientUser | number | string,\n  ) => Promise<void>\n  user: ClientUser | number | string\n}\n\nexport const handleTakeOver = async ({\n  id,\n  clearRouteCache,\n  collectionSlug,\n  documentLockStateRef,\n  globalSlug,\n  isLockingEnabled,\n  isWithinDoc,\n  setCurrentEditor,\n  setIsReadOnlyForIncomingUser,\n  updateDocumentEditor,\n  user,\n}: HandleTakeOverParams): Promise<void> => {\n  if (!isLockingEnabled) {\n    return\n  }\n\n  try {\n    // Call updateDocumentEditor to update the document's owner to the current user\n    await updateDocumentEditor(id, collectionSlug ?? globalSlug, user)\n\n    if (!isWithinDoc) {\n      documentLockStateRef.current.hasShownLockedModal = true\n    }\n\n    // Update the locked state to reflect the current user as the owner\n    documentLockStateRef.current = {\n      hasShownLockedModal: documentLockStateRef.current?.hasShownLockedModal,\n      isLocked: true,\n      user,\n    }\n    setCurrentEditor(user)\n\n    // If this is a takeover within the document, ensure the document is editable\n    if (isWithinDoc && setIsReadOnlyForIncomingUser) {\n      setIsReadOnlyForIncomingUser(false)\n    }\n\n    // Need to clear the route cache to refresh the page and update readOnly state for server rendered components\n    if (clearRouteCache) {\n      clearRouteCache()\n    }\n  } catch (error) {\n    // eslint-disable-next-line no-console\n    console.error('Error during document takeover:', error)\n  }\n}\n", "import type {\n  SanitizedCollectionPermission,\n  SanitizedDocumentPermissions,\n  SanitizedGlobalPermission,\n} from 'payload'\n\nexport const hasSavePermission = (args: {\n  /*\n   * Pass either `collectionSlug` or `globalSlug`\n   */\n  collectionSlug?: string\n  docPermissions: SanitizedDocumentPermissions\n  /*\n   * Pass either `collectionSlug` or `globalSlug`\n   */\n  globalSlug?: string\n  isEditing: boolean\n}) => {\n  const { collectionSlug, docPermissions, globalSlug, isEditing } = args\n\n  if (collectionSlug) {\n    return Boolean(\n      (isEditing && docPermissions?.update) ||\n        (!isEditing && (docPermissions as SanitizedCollectionPermission)?.create),\n    )\n  }\n\n  if (globalSlug) {\n    return Boolean((docPermissions as SanitizedGlobalPermission)?.update)\n  }\n\n  return false\n}\n", "import type { ClientUser } from 'payload'\n\nexport const isClientUserObject = (user): user is ClientUser => {\n  return user && typeof user === 'object'\n}\n", "export const isEditing = ({\n  id,\n  collectionSlug,\n  globalSlug,\n}: {\n  collectionSlug?: string\n  globalSlug?: string\n  id?: number | string\n}): boolean => Boolean(globalSlug || (collectionSlug && !!id))\n", "export function sanitizeID(id: number | string): number | string {\n  if (id === undefined) {\n    return id\n  }\n\n  if (typeof id === 'number') {\n    return id\n  }\n\n  return decodeURIComponent(id)\n}\n", "export { Translation } from '../../elements/Translation/index.js'\nexport { withMergedProps } from '../../elements/withMergedProps/index.js' // cannot be within a 'use client', thus we export this from shared\nexport { WithServerSideProps } from '../../elements/WithServerSideProps/index.js'\nexport { mergeFieldStyles } from '../../fields/mergeFieldStyles.js'\nexport { reduceToSerializableFields } from '../../forms/Form/reduceToSerializableFields.js'\nexport { PayloadIcon } from '../../graphics/Icon/index.js'\nexport { PayloadLogo } from '../../graphics/Logo/index.js'\n// IMPORTANT: the shared.ts file CANNOT contain any Server Components _that import client components_.\nexport { filterFields } from '../../providers/TableColumns/buildColumnState/filterFields.js'\nexport { getInitialColumns } from '../../providers/TableColumns/getInitialColumns.js'\nexport { abortAndIgnore, handleAbortRef } from '../../utilities/abortAndIgnore.js'\nexport { requests } from '../../utilities/api.js'\nexport { findLocaleFromCode } from '../../utilities/findLocaleFromCode.js'\nexport { formatAdminURL } from '../../utilities/formatAdminURL.js'\nexport { formatDate } from '../../utilities/formatDocTitle/formatDateTitle.js'\nexport { formatDocTitle } from '../../utilities/formatDocTitle/index.js'\nexport {\n  type EntityToGroup,\n  EntityType,\n  groupNavItems,\n  type NavGroupType,\n} from '../../utilities/groupNavItems.js'\nexport { handleBackToDashboard } from '../../utilities/handleBackToDashboard.js'\nexport { handleGoBack } from '../../utilities/handleGoBack.js'\nexport { handleTakeOver } from '../../utilities/handleTakeOver.js'\nexport { hasSavePermission } from '../../utilities/hasSavePermission.js'\nexport { isClientUserObject } from '../../utilities/isClientUserObject.js'\nexport { isEditing } from '../../utilities/isEditing.js'\nexport { sanitizeID } from '../../utilities/sanitizeID.js'\n/**\n * @deprecated\n * The `mergeListSearchAndWhere` function is deprecated.\n * Import this from `payload/shared` instead.\n */\nexport { mergeListSearchAndWhere } from 'payload/shared'\n"], "names": ["RecursiveTranslation", "elements", "translationString", "regex", "sections", "split", "_jsx", "map", "section", "index", "startsWith", "endsWith", "elementKey", "Element", "RegExp", "children", "replace", "_", "group", "Translation", "i18nKey", "t", "variables", "stringWithVariables", "isReactServerComponentOrFunction", "serverProps", "withMergedProps", "Component", "sanitizeServerOnlyProps", "toMergeIntoProps", "undefined", "passedProps", "mergedProps", "simpleMergeProps", "for<PERSON>ach", "prop", "_jsx", "props", "toMerge", "isReactServerComponentOrFunction", "WithServerSideProps", "Component", "serverOnlyProps", "rest", "passedProps", "propsWithServerOnlyProps", "_jsx", "mergeFieldStyles", "field", "admin", "style", "width", "flex", "blacklisted<PERSON><PERSON><PERSON>", "sanitizeField", "incomingField", "field", "key", "reduceToSerializableFields", "fields", "result", "PayloadIcon", "fill", "fillFromProps", "_jsxs", "className", "height", "viewBox", "width", "xmlns", "_jsx", "d", "css", "PayloadLogo", "_jsxs", "className", "fill", "height", "id", "viewBox", "width", "xmlns", "_jsx", "d", "fieldIsHiddenOrDisabled", "fieldIsID", "filterFields", "incomingFields", "shouldSkipField", "field", "type", "admin", "disableListColumn", "reduce", "acc", "formattedField", "tabs", "map", "tab", "fields", "push", "Array", "isArray", "fieldAffectsData", "getRemainingColumns", "fields", "useAsTitle", "reduce", "remaining", "field", "name", "type", "tabs", "tabFieldColumns", "tab", "getInitialColumns", "defaultColumns", "initialColumns", "Array", "isArray", "length", "push", "remainingColumns", "concat", "slice", "map", "column", "accessor", "active", "abortAndIgnore", "abortController", "abort", "handleAbortRef", "abortControllerRef", "newController", "AbortController", "current", "qs", "requests", "delete", "url", "options", "headers", "formattedOptions", "credentials", "method", "fetch", "get", "query", "params", "stringify", "addQueryPrefix", "patch", "post", "put", "findLocaleFromCode", "localizationConfig", "locale", "locales", "length", "find", "el", "code", "formatAdminURL", "offsetFormatCache", "offsetCache", "tzOffset", "timeZone", "date", "offsetStr", "calcOffset", "captures", "offsetRe", "cacheStr", "values", "hours", "minutes", "TZDateMini", "_TZDateMini", "args", "tzOffset", "adjustToSystemTZ", "syncToInternal", "tz", "timeZone", "time", "date", "re", "method", "utcMethod", "syncFromInternal", "offset", "prevHour", "systemOffset", "prevHourSystemOffset", "systemDSTChange", "dstShift", "offsetDiff", "postOffset", "postOffsetDiff", "offsetChanged", "postDiff", "newOffset", "offsetChange", "format", "formatDistanceToNow", "transpose", "formatDate", "date", "i18n", "pattern", "timezone", "theDate", "TZDate", "Date", "DateWithOriginalTz", "tz", "modifiedDate", "withTimeZone", "dateWithTimezone", "dateFNS", "locale", "t", "getTranslation", "isSerializedLexicalEditor", "value", "formatLexicalDocTitle", "editorState", "textContent", "node", "text", "type", "children", "formatRelationshipTitle", "data", "Array", "isArray", "map", "item", "id", "String", "filter", "Boolean", "join", "formatDocTitle", "collectionConfig", "data", "dateFormat", "dateFormatFromConfig", "fallback", "globalConfig", "i18n", "title", "useAsTitle", "admin", "fieldConfig", "fields", "find", "f", "name", "isDate", "type", "isRelationship", "date", "displayFormat", "formatDate", "pattern", "formatRelationshipTitle", "getTranslation", "label", "slug", "isSerializedLexicalEditor", "formatLexicalDocTitle", "root", "children", "t", "getTranslation", "EntityType", "groupNavItems", "entities", "permissions", "i18n", "reduce", "groups", "entityToGroup", "entity", "admin", "group", "type", "toLowerCase", "slug", "read", "translatedGroup", "labelOrFunction", "labels", "plural", "label", "t", "existingGroup", "find", "matchedGroup", "push", "filter", "length", "formatAdminURL", "handleBackToDashboard", "adminRoute", "router", "redirectRoute", "path", "push", "formatAdminURL", "handleGoBack", "adminRoute", "collectionSlug", "router", "redirectRoute", "path", "push", "handleTakeOver", "id", "clearRouteCache", "collectionSlug", "documentLockStateRef", "globalSlug", "isLockingEnabled", "isWithinDoc", "setCurrentEditor", "setIsReadOnlyForIncomingUser", "updateDocumentEditor", "user", "current", "hasShownLockedModal", "isLocked", "error", "console", "hasSavePermission", "args", "collectionSlug", "docPermissions", "globalSlug", "isEditing", "Boolean", "update", "create", "isClientUserObject", "user", "isEditing", "id", "collectionSlug", "globalSlug", "Boolean", "sanitizeID", "id", "undefined", "decodeURIComponent", "mergeListSearchAndWhere"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAuB;ACFvB,OAASwB,oCAAAA,EAAkCC,eAAAA,MAAmB,iBAC9D,MAAkB;;AMClB,OAAS2D,2BAAAA,GAAyBC,aAAAA,OAAiB;AGFnD,UAAYoD,MAAQ;AECpB,OAAS0B,kBAAAA,OAAsB;AGE/B,OAASyC,UAAAA,EAAQC,uBAAAA,GAAqBC,aAAAA,OAAiB;;ACKvD,OAASiB,kBAAAA,OAAsB;AW0B/B,OAASqI,2BAAAA,OAA+B;;;A3B9BxC,IAAMpW,IAGDA,CAAC,EAAEC,UAAAA,CAAAA,EAAUC,mBAAAA,CAAiB,EAAA,GAAE;IACnC,IAAMC,IAAQ,0BACRC,IAAWF,EAAkBG,KAAAA,CAAMF,CAAA;IAEzC,WACEG,wPAAAA,EAAC,QAAA;QAAA,UACEF,EAASG,GAAAA,CAAI,CAACC,GAASC,IAAA;YACtB,IAAIR,KAAYO,EAAQE,UAAAA,CAAW,GAAA,KAAQF,EAAQG,QAAAA,CAAS,GAAA,GAAM;gBAChE,IAAMC,IAAaJ,CAAAA,CAAQ,CAAA,CAAA,EACrBK,IAAUZ,CAAAA,CAASW,CAAA,CAAA;gBAEzB,IAAIC,GAAS;oBACX,IAAMV,IAAQ,IAAIW,OAAO,CAAA,CAAA,EAAIF,CAAA,CAAA,QAAA,EAAsBA,CAAA,CAAA,CAAA,CAAA,EAAe,GAAA,GAC5DG,IAAWP,EAAQQ,OAAAA,CAAQb,GAAO,CAACc,GAAGC,IAAUA,CAAA;oBAEtD,WACEZ,wPAAAA,EAACO,GAAA;wBAAA,UACCP,4PAAAA,EAACN,GAAA;4BAAqBE,mBAAmBa;wBAAAA;oBAAAA,GAD7BN,CAAA;gBAIlB;YACF;YAEA,OAAOD;QACT,CAAA;IAAA;AAGN,GASaW,IAA0CA,CAAC,EAAElB,UAAAA,CAAAA,EAAUmB,SAAAA,CAAAA,EAASC,GAAAA,CAAAA,EAAGC,WAAAA,CAAS,EAAA,GAAE;IACzF,IAAMC,IAAsBF,EAAED,GAASE,KAAa,CAAC,CAAA;IAErD,OAAKrB,QAIEK,wPAAAA,EAACN,GAAA;QAAqBC,UAAUA;QAAUC,mBAAmBqB;IAAAA,KAH3DA;AAIX;;;;AC1BO,SAASG,EAAuD,EACrEC,WAAAA,CAAAA,EACAC,yBAAAA,CAAAA,EACAC,kBAAAA,CAAgB,EAAA,CAKjB;IACC,OAAID,MAA4BE,KAAAA,KAAAA,CAC9BF,IAA0B,CAACJ,kOAAAA,EAAiCG,CAAA,CAAA,IAGDI,GAAA;QAC3D,IAAMC,IAAcC,EAAiBF,GAAaF,CAAA;QAElD,OAAID,KACFH,2LAAAA,CAAYS,OAAAA,EAASC,GAAA;YACnB,OAAOH,CAAAA,CAAYG,CAAA;QACrB,CAAA,OAGKC,wPAAAA,EAACT,GAAA;YAAW,GAAGK,CAAAA;QAAAA;IACxB;AAGF;AAEA,SAASC,EAAiBI,CAAAA,EAAOC,CAAAA,CAAO;IACtC,OAAO;QAAE,GAAGD,CAAAA;QAAO,GAAGC,CAAQ;;AAChC,yCCrDA,OAASC,oCAAAA,MAAwC,iBACjD,MAAkB;;;;AAEX,IAAMC,IAAoDA,CAAC,EAChEC,WAAAA,CAAAA,EACAC,iBAAAA,CAAAA,EACA,GAAGC,CAAA,EAAA,GAECF,IAAAA,EACqCG,GAAA;QACrC,IAAMC,IAA2B;YAC/B,GAAGD,CAAAA;YACH,OAAIL,8NAAAA,EAAiCE,CAAA,IAAcC,KAAmB,CAAC,IAAK,CAAC;QAC/E;QAEA,WAAOI,wPAAAA,EAACL,GAAA;YAAW,GAAGI,CAAAA;QAAAA;IACxB,CAAA,EAE2BF,CAAA,IAGtB;ACrBF,IAAMI,IACXC,KAAAA,CACyB;QACzB,GAAIA,GAAOC,OAAOC,SAAS,CAAC,CAAA;QAC5B,GAAIF,GAAOC,OAAOE,QACd;YACE,iBAAiBH,EAAMC,KAAAA,CAAME;QAC/B,IACA;YACEC,MAAM;QACR,CAAA;QAEJ,GAAIJ,GAAOC,OAAOC,OAAOE,OACrB;YACEA,MAAMJ,EAAMC,KAAAA,CAAMC,KAAAA,CAAME;QAC1B,IACA,CAAC,CACP;KAAA;AChBA,IAAMC,IAAqC;IAAC;IAAY,kBAAA;CAAA,EAElDC,KAAiBC,GAAA;IACrB,IAAMC,IAAQ;QAAE,GAAGD;IAAc;IAEjC,KAAA,IAAWE,KAAOJ,EAChB,OAAOG,CAAAA,CAAMC,CAAA,CAAA;IAGf,OAAOD;AACT,GAMaE,KACXC,GAAA;IAIA,IAAMC,IAA2D,CAAC;IAElE,IAAA,IAAWH,KAAOE,EAChBC,CAAAA,CAAOH,CAAA,CAAA,GAAOH,EAAcK,CAAAA,CAAOF,CAAA,CAAI;IAGzC,OAAOG;AACT,oDC/BA,MAAkB;;;AAEX,IAAMC,IAERA,CAAC,EAAEC,MAAMC,CAAa,EAAA,GAAE;IAC3B,IAAMD,IAAOC,KAAiB;IAE9B,WACEC,yPAAAA,EAAC,OAAA;QACCC,WAAU;QACVC,QAAO;QACPC,SAAQ;QACRC,OAAM;QACNC,OAAM;QAAA,UAAA;gBAENC,wPAAAA,EAAC,QAAA;gBACCC,GAAE;gBACFT,MAAMA;YAAAA;gBAERQ,wPAAAA,EAAC,QAAA;gBACCC,GAAE;gBACFT,MAAMA;YAAAA;SAAAA;IAAAA;AAId,oDCzBA,MAAkB;;;AAElB,IAAMU,IAAM,CAAA;;;;GAMCC,IAAwBA,QACnCC,yPAAAA,EAAC,OAAA;QACCC,WAAU;QACVC,MAAK;QACLC,QAAO;QACPC,IAAG;QACHC,SAAQ;QACRC,OAAM;QACNC,OAAM;QAAA,UAAA;gBAENC,wPAAAA,EAAC,SAAA;gBAAA,UAAOV;YAAAA;gBACRE,yPAAAA,EAAC,KAAA;gBAAEI,IAAG;gBAAA,UAAA;wBACJI,wPAAAA,EAAC,QAAA;wBAAKC,GAAE;oBAAA;wBACRD,wPAAAA,EAAC,QAAA;wBAAKC,GAAE;oBAAA;oBACRT,6PAAAA,EAAC,KAAA;wBAAEI,IAAG;wBAAA,UAAA;gCACJI,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;gCACRD,wPAAAA,EAAC,QAAA;gCAAKC,GAAE;4BAAA;yBAAA;oBAAA;iBAAA;YAAA;SAAA;IAAA;;ACtBT,IAAMG,KAA+CC,GAAA;IAC1D,IAAMC,KAAmBC,IACvBA,EAAOC,IAAAA,KAAS,QAAQN,qNAAAA,EAAwBK,CAAA,KAAU,KAACJ,mMAAAA,EAAUI,CAAA,KACrEA,GAAOE,OAAOC,sBAAsB,CAAA;IAEtC,OAAA,CAAQL,KAAkB,CAAA,CAAA,EAAIM,MAAAA,CAAY,CAACC,GAAKL,IAAA;QAC9C,IAAID,EAAgBC,CAAA,GAClB,OAAOK;QAIT,IAAIL,EAAMC,IAAAA,KAAS,UAAU,UAAUD,GAAO;YAC5C,IAAMM,IAAoB;gBACxB,GAAGN,CAAAA;gBACHO,MAAMP,EAAMO,IAAAA,CAAKC,GAAAA,EAAKC,IAAAA,CAAS;wBAC7B,GAAGA,CAAAA;wBACHC,QAAQb,EAAaY,EAAIC,MAAM;oBACjC,CAAA,CAAA;YACF;YACAL,OAAAA,EAAIM,IAAAA,CAAKL,CAAA,GACFD;QACT;QAGA,IAAI,YAAYL,KAASY,MAAMC,OAAAA,CAAQb,EAAMU,MAAM,GAAG;YACpD,IAAMJ,IAAoB;gBACxB,GAAGN,CAAAA;gBACHU,QAAQb,EAAaG,EAAMU,MAAM;YACnC;YACAL,OAAAA,EAAIM,IAAAA,CAAKL,CAAA,GACFD;QACT;QAGAA,OAAAA,EAAIM,IAAAA,CAAKX,CAAA,GACFK;IACT,GAAG,CAAA,CAAE;AACP,EC3CA,OAASS,oBAAAA,MAAwB;;AAEjC,IAAMC,IAAsBA,CAC1BC,GACAC,IAEAD,GAAQE,OAAO,CAACC,GAAWC,QACrBN,0MAAAA,EAAiBM,CAAA,KAAUA,EAAMC,IAAAA,KAASJ,IACrCE,IAGL,KAACL,0MAAAA,EAAiBM,CAAA,KAAU,YAAYA,IACnC,CAAA;eAAID,EAAA;eAAcJ,EAAoBK,EAAMJ,MAAAA,EAAQC,CAAA,CAAA;SAAA,GAGzDG,EAAME,IAAAA,KAAS,UAAU,UAAUF,IAC9B,CAAA;eACFD,EAAA;eACAC,EAAMG,IAAAA,CAAKL,MAAAA,CACZ,CAACM,GAAiBC,IAAQ,CAAA;uBACrBD,EAAA;uBACC,UAAUC,IAAM;wBAACA,EAAIJ,IAAI;qBAAA,GAAIN,EAAoBU,EAAIT,MAAAA,EAAQC,CAAA,CAAA;iBAAA,EAEnE,CAAA,CAAE,CAAA;SAAA,GAKD,CAAA;eAAIE;YAAWC,EAAMC,IAAI;SAAA,EAC/B,CAAA,CAAE,GAOMK,KAAoBA,CAC/BV,GACAC,GACAU,IAAA;IAEA,IAAIC,IAAiB,CAAA,CAAA;IAErB,IAAIC,MAAMC,OAAAA,CAAQH,CAAA,KAAmBA,EAAeI,MAAAA,IAAU,GAC5DH,IAAiBD;SACZ;QACDV,KACFW,EAAeI,IAAAA,CAAKf,CAAA;QAGtB,IAAMgB,IAAmBlB,EAAoBC,GAAQC,CAAA;QAErDW,IAAiBA,EAAeM,MAAAA,CAAOD,CAAA,GACvCL,IAAiBA,EAAeO,KAAAA,CAAM,GAAG,CAAA;IAC3C;IAEA,OAAOP,EAAeQ,GAAAA,CAAKC,KAAAA,CAAY;YACrCC,UAAUD;YACVE,QAAQ,CAAA;QACV,CAAA,CAAA;AACF;AC9DO,SAASC,GAAeC,CAAAA,CAAgC;IAC7D,IAAIA,GACF,IAAI;QACFA,EAAgBC,KAAAA,CAAK;IACvB,EAAA,OAAe,CACb;AAGN;AAUO,SAASC,GACdC,CAAAA,CAAoD;IAEpD,IAAMC,IAAgB,IAAIC;IAE1B,IAAIF,EAAmBG,OAAAA,EACrB,IAAI;QACFH,EAAmBG,OAAAA,CAAQL,KAAAA,CAAK;IAClC,EAAA,OAAe,CACb;IAIJE,OAAAA,EAAmBG,OAAAA,GAAUF,GAEtBA;AACT;;AC5BO,IAAMI,KAAW;IACtBC,QAAQA,CAACC,GAAaC,IAAuB;QAAEC,SAAS,CAAC;IAAE,CAAA,GAAC;QAC1D,IAAMA,IAAUD,KAAWA,EAAQC,OAAAA,GAAU;YAAE,GAAGD,EAAQC;QAAQ,IAAI,CAAC,GAEjEC,IAAgC;YACpC,GAAGF,CAAAA;YACHG,aAAa;YACbF,SAAS;gBACP,GAAGA;YACL;YACAG,QAAQ;QACV;QAEA,OAAOC,MAAMN,GAAKG,CAAA;IACpB;IAEAI,KAAKA,CAACP,GAAaC,IAAsB;QAAEC,SAAS,CAAC;IAAE,CAAA,GAAC;QACtD,IAAIM,IAAQ;QACZ,OAAIP,EAAQQ,MAAAA,IAAAA,CACVD,IAAWE,oLAAAA,CAAUT,EAAQQ,MAAAA,EAAQ;YAAEE,gBAAgB,CAAA;QAAK,CAAA,CAAA,GAEvDL,MAAM,GAAGN,CAAA,GAAMQ,CAAA,EAAA,EAAS;YAC7BJ,aAAa;YACb,GAAGH;QACL,CAAA;IACF;IAEAW,OAAOA,CAACZ,GAAaC,IAAuB;QAAEC,SAAS,CAAC;IAAE,CAAA,GAAC;QACzD,IAAMA,IAAUD,KAAWA,EAAQC,OAAAA,GAAU;YAAE,GAAGD,EAAQC;QAAQ,IAAI,CAAC,GAEjEC,IAAgC;YACpC,GAAGF,CAAAA;YACHG,aAAa;YACbF,SAAS;gBACP,GAAGA;YACL;YACAG,QAAQ;QACV;QAEA,OAAOC,MAAMN,GAAKG,CAAA;IACpB;IAEAU,MAAMA,CAACb,GAAaC,IAAuB;QAAEC,SAAS,CAAC;IAAE,CAAA,GAAC;QACxD,IAAMA,IAAUD,KAAWA,EAAQC,OAAAA,GAAU;YAAE,GAAGD,EAAQC;QAAQ,IAAI,CAAC,GAEjEC,IAAgC;YACpC,GAAGF,CAAAA;YACHG,aAAa;YACbF,SAAS;gBACP,GAAGA;YACL;YACAG,QAAQ;QACV;QAEA,OAAOC,MAAM,GAAGN,CAAA,EAAA,EAAOG,CAAA;IACzB;IAEAW,KAAKA,CAACd,GAAaC,IAAuB;QAAEC,SAAS,CAAC;IAAE,CAAA,GAAC;QACvD,IAAMA,IAAUD,KAAWA,EAAQC,OAAAA,GAAU;YAAE,GAAGD,EAAQC;QAAQ,IAAI,CAAC,GAEjEC,IAAgC;YACpC,GAAGF,CAAAA;YACHG,aAAa;YACbF,SAAS;gBACP,GAAGA;YACL;YACAG,QAAQ;QACV;QAEA,OAAOC,MAAMN,GAAKG,CAAA;IACpB;AACF;ACzEO,IAAMY,KAAqBA,CAChCC,GACAC,IAEI,CAACD,GAAoBE,WAAWF,EAAmBE,OAAAA,CAAQC,MAAAA,KAAW,IACjE,OAGFH,EAAmBE,OAAAA,CAAQE,IAAAA,EAAMC,IAAOA,GAAIC,SAASL,CAAA;;AEZ9D,IAAMO,KAAoB,CAAC,GACrBC,IAAc,CAAC;AAed,SAASC,EAASC,CAAAA,EAAUC,CAAAA,CAAM;IACvC,IAAI;QAMF,IAAMC,IAAAA,CALSL,EAAAA,CAAkBG,CAAQ,CAAA,KAAM,IAAI,KAAK,cAAA,CAAe,SAAS;YAC9E,UAAAA;YACA,MAAM;YACN,cAAc;QAChB,CAAC,EAAE,MAAA,EACsBC,CAAI,EAAE,KAAA,CAAM,KAAK,CAAA,CAAE,CAAC,CAAA,IAAK;QAClD,OAAIC,KAAaJ,IAAoBA,CAAAA,CAAYI,CAAS,CAAA,GACnDC,EAAWD,GAAWA,EAAU,KAAA,CAAM,GAAG,CAAC;IACnD,EAAA,OAAQ;QAGN,IAAIF,KAAYF,GAAa,OAAOA,CAAAA,CAAYE,CAAQ,CAAA;QACxD,IAAMI,IAAWJ,GAAU,MAAMK,EAAQ;QACzC,OAAID,IAAiBD,EAAWH,GAAUI,EAAS,KAAA,CAAM,CAAC,CAAC,IACpD;IACT;AACF;AACA,IAAMC,KAAW;AACjB,SAASF,EAAWG,CAAAA,EAAUC,CAAAA,CAAQ;IACpC,IAAMC,IAAQ,CAACD,CAAAA,CAAO,CAAC,CAAA,EACjBE,IAAU,CAAA,CAAEF,CAAAA,CAAO,CAAC,CAAA,IAAK,CAAA;IAC/B,OAAOT,CAAAA,CAAYQ,CAAQ,CAAA,GAAIE,IAAQ,IAAIA,IAAQ,KAAKC,IAAUD,IAAQ,KAAKC;AACjF;ACvCO,IAAMC,IAAN,MAAMC,UAAmB,IAAK;IAGnC,YAAA,GAAeC,CAAAA,CAAM;QACnB,KAAA,CAAM,GACFA,EAAK,MAAA,GAAS,KAAK,OAAOA,CAAAA,CAAKA,EAAK,MAAA,GAAS,CAAC,CAAA,IAAM,YAAA,CACtD,IAAA,CAAK,QAAA,GAAWA,EAAK,GAAA,CAAI,CAAA,GAE3B,IAAA,CAAK,QAAA,GAAW,IAAI,MAChB,MAAMC,EAAS,IAAA,CAAK,QAAA,EAAU,IAAI,CAAC,IACrC,IAAA,CAAK,OAAA,CAAQ,GAAG,IAEXD,EAAK,MAAA,GAEC,OAAOA,CAAAA,CAAK,CAAC,CAAA,IAAM,YAAA,CAAaA,EAAK,MAAA,KAAW,KAAKA,EAAK,MAAA,KAAW,KAAK,OAAOA,CAAAA,CAAK,CAAC,CAAA,IAAM,QAAA,IACtG,IAAA,CAAK,OAAA,CAAQA,CAAAA,CAAK,CAAC,CAAC,IACX,OAAOA,CAAAA,CAAK,CAAC,CAAA,IAAM,WAC5B,IAAA,CAAK,OAAA,CAAQ,CAAC,IAAI,KAAKA,CAAAA,CAAK,CAAC,CAAC,CAAC,IACtBA,CAAAA,CAAK,CAAC,CAAA,YAAa,OAC5B,IAAA,CAAK,OAAA,CAAQ,CAACA,CAAAA,CAAK,CAAC,CAAC,IAAA,CAErB,IAAA,CAAK,OAAA,CAAQ,CAAC,IAAI,KAAK,GAAGA,CAAI,CAAC,GAC/BE,EAAiB,IAAA,EAAM,GAAG,GAC1BC,EAAe,IAAI,CAAA,IAVnB,IAAA,CAAK,OAAA,CAAQ,KAAK,GAAA,CAAI,CAAC;IAa7B;IACA,OAAO,GAAGC,CAAAA,EAAAA,GAAOJ,CAAAA,EAAM;QACrB,OAAOA,EAAK,MAAA,GAAS,IAAID,EAAW,GAAGC,GAAMI,CAAE,IAAI,IAAIL,EAAW,KAAK,GAAA,CAAI,GAAGK,CAAE;IAClF;IAMA,aAAaC,CAAAA,EAAU;QACrB,OAAO,IAAIN,EAAW,CAAC,IAAA,EAAMM,CAAQ;IACvC;IACA,oBAAoB;QAClB,OAAO,CAACJ,EAAS,IAAA,CAAK,QAAA,EAAU,IAAI;IACtC;IAMA,QAAQK,CAAAA,EAAM;QACZ,OAAA,KAAK,SAAA,CAAU,OAAA,CAAQ,KAAA,CAAM,IAAA,EAAM,SAAS,GAC5CH,EAAe,IAAI,GACZ,CAAC;IACV;IAMA,CAAC,OAAO,GAAA,CAAI,mBAAmB,CAAC,CAAA,CAAEI,CAAAA,EAAM;QACtC,OAAO,IAAIR,EAAW,CAAC,IAAI,KAAKQ,CAAI,GAAG,IAAA,CAAK,QAAQ;IACtD;AAGF,GAGMC,IAAK;AACX,OAAO,mBAAA,CAAoB,KAAK,SAAS,EAAE,OAAA,EAAQC,GAAU;IAC3D,IAAI,CAACD,EAAG,IAAA,CAAKC,CAAM,GAAG;IACtB,IAAMC,IAAYD,EAAO,OAAA,CAAQD,GAAI,OAAO;IAEvCV,EAAW,SAAA,CAAUY,CAAS,CAAA,IAAA,CAC/BD,EAAO,UAAA,CAAW,KAAK,IAEzBX,EAAW,SAAA,CAAUW,CAAM,CAAA,GAAI,UAAY;QACzC,OAAO,IAAA,CAAK,QAAA,CAASC,CAAS,CAAA,CAAE;IAClC,IAAA,CAGAZ,EAAW,SAAA,CAAUW,CAAM,CAAA,GAAI,UAAY;QACzC,OAAA,KAAK,SAAA,CAAUC,CAAS,CAAA,CAAE,KAAA,CAAM,IAAA,CAAK,QAAA,EAAU,SAAS,GACxDC,GAAiB,IAAI,GACd,CAAC;IACV,GAGAb,EAAW,SAAA,CAAUY,CAAS,CAAA,GAAI,UAAY;QAC5C,OAAA,KAAK,SAAA,CAAUA,CAAS,CAAA,CAAE,KAAA,CAAM,IAAA,EAAM,SAAS,GAC/CP,EAAe,IAAI,GACZ,CAAC;IACV,CAAA,CAAA;AAEJ,CAAC;AAOD,SAASA,EAAeI,CAAAA,CAAM;IAC5BA,EAAK,QAAA,CAAS,OAAA,CAAQ,CAACA,CAAI,GAC3BA,EAAK,QAAA,CAAS,aAAA,CAAcA,EAAK,QAAA,CAAS,aAAA,CAAc,IAAIA,EAAK,iBAAA,CAAkB,CAAC;AACtF;AAQA,SAASI,GAAiBJ,CAAAA,CAAM;IAE9B,KAAK,SAAA,CAAU,WAAA,CAAY,IAAA,CAAKA,GAAMA,EAAK,QAAA,CAAS,cAAA,CAAe,GAAGA,EAAK,QAAA,CAAS,WAAA,CAAY,GAAGA,EAAK,QAAA,CAAS,UAAA,CAAW,CAAC,GAC7H,KAAK,SAAA,CAAU,QAAA,CAAS,IAAA,CAAKA,GAAMA,EAAK,QAAA,CAAS,WAAA,CAAY,GAAGA,EAAK,QAAA,CAAS,aAAA,CAAc,GAAGA,EAAK,QAAA,CAAS,aAAA,CAAc,GAAGA,EAAK,QAAA,CAAS,kBAAA,CAAmB,CAAC,GAGhKL,EAAiBK,CAAI;AACvB;AAQA,SAASL,EAAiBK,CAAAA,CAAM;IAE9B,IAAMK,IAASX,EAASM,EAAK,QAAA,EAAUA,CAAI,GA2BrCM,IAAW,IAAI,KAAK,CAACN,CAAI;IAG/BM,EAAS,WAAA,CAAYA,EAAS,WAAA,CAAY,IAAI,CAAC;IAG/C,IAAMC,IAAe,CAAC,IAAI,KAAK,CAACP,CAAI,EAAE,iBAAA,CAAkB,GAClDQ,IAAuB,CAAC,IAAI,KAAK,CAACF,CAAQ,EAAE,iBAAA,CAAkB,GAC9DG,IAAkBF,IAAeC,GAEjCE,IAAW,KAAK,SAAA,CAAU,QAAA,CAAS,KAAA,CAAMV,CAAI,MAAMA,EAAK,QAAA,CAAS,WAAA,CAAY;IAG/ES,KAAmBC,KAAUV,EAAK,QAAA,CAAS,aAAA,CAAcA,EAAK,QAAA,CAAS,aAAA,CAAc,IAAIS,CAAe;IAU5G,IAAME,IAAaJ,IAAeF;IAC9BM,KAAY,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKX,GAAM,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKA,CAAI,IAAIW,CAAU;IAM5G,IAAMC,IAAalB,EAASM,EAAK,QAAA,EAAUA,CAAI,GAEzCa,IADmB,CAAC,IAAI,KAAK,CAACb,CAAI,EAAE,iBAAA,CAAkB,IAClBY,GACpCE,IAAgBF,MAAeP,GAC/BU,IAAWF,IAAiBF;IAClC,IAAIG,KAAiBC,GAAU;QAC7B,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKf,GAAM,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKA,CAAI,IAAIe,CAAQ;QAK1F,IAAMC,IAAYtB,EAASM,EAAK,QAAA,EAAUA,CAAI,GACxCiB,IAAeL,IAAaI;QAC9BC,KAAAA,CACFjB,EAAK,QAAA,CAAS,aAAA,CAAcA,EAAK,QAAA,CAAS,aAAA,CAAc,IAAIiB,CAAY,GACxE,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKjB,GAAM,KAAK,SAAA,CAAU,aAAA,CAAc,IAAA,CAAKA,CAAI,IAAIiB,CAAY,CAAA;IAElG;AAGF;;AC/LO,IAAMI,IAAaA,CAAC,EAAEC,MAAAA,CAAAA,EAAMC,MAAAA,CAAAA,EAAMC,SAAAA,CAAAA,EAASC,UAAAA,CAAQ,EAAA,GAAkB;IAC1E,IAAMC,IAAU,IAAIC,EAAO,IAAIC,KAAKN,CAAA,CAAA;IAEpC,IAAIG,GAAU;QACZ,IAAMI,IAAqBF,EAAOG,EAAAA,CAAGL,CAAA,GAE/BM,IAAeL,EAAQM,YAAAA,CAAaP,CAAA,GAGpCQ,QAAmBb,+KAAAA,EAAUW,GAAcF,CAAA;QAGjD,OAAON,EAAKW,OAAAA,OACRhB,yLAAAA,EAAOe,GAAkBT,GAAS;YAAEW,QAAQZ,EAAKW;QAAQ,CAAA,IACzD,GAAGX,EAAKa,CAAAA,CAAE,iBAAA,CAAA,CAAA,GAAA;IAChB;IAEA,OAAOb,EAAKW,OAAAA,OACRhB,yLAAAA,EAAOQ,GAASF,GAAS;QAAEW,QAAQZ,EAAKW;IAAQ,CAAA,IAChD,GAAGX,EAAKa,CAAAA,CAAE,iBAAA,CAAA,CAAA,GAAA;AAChB;;AE1BO,SAASE,EAA0BC,CAAAA,CAAc;IACtD,OAAO,OAAOA,KAAU,YAAY,UAAUA;AAChD;AAEO,SAASC,EACdC,CAAAA,EACAC,CAAAA,CAAmB;IAEnB,KAAA,IAAWC,KAAQF,EACb,UAAUE,KAAQA,EAAKC,IAAAA,GACzBF,KAAeC,EAAKC,IAAAA,GAEd,cAAcD,KAAAA,CAClBD,KAAe,CAAA,CAAA,EAAIC,EAAKE,IAAI,CAAA,CAAA,CAAA,GAG5B,cAAcF,KAAQA,EAAKG,QAAAA,IAAAA,CAC7BJ,KAAeF,EAAsBG,EAAKG,QAAAA,EAAqCJ,CAAA,CAAA;IAGnF,OAAOA;AACT;AC3BO,IAAMK,KAA2BC,IAClCC,MAAMC,OAAAA,CAAQF,CAAA,IACTA,EACJG,GAAAA,EAAKC,IACA,OAAOA,KAAS,YAAYA,MAAS,OAChCA,EAAKC,EAAAA,GAEPC,OAAOF,CAAA,CAChB,EACCG,MAAAA,CAAOC,OAAA,EACPC,IAAAA,CAAK,IAAA,IAGN,OAAOT,KAAS,YAAYA,MAAS,OAChCA,EAAKK,EAAAA,IAAM,KAGbC,OAAON,CAAA;AFHT,IAAMU,KAAiBA,CAAC,EAC7BC,kBAAAA,CAAAA,EACAC,MAAAA,CAAAA,EACAC,YAAYC,CAAAA,EACZC,UAAAA,CAAAA,EACAC,cAAAA,CAAAA,EACAC,MAAAA,CAAI,EAAA,GAQL;IACC,IAAIC;IAEJ,IAAIP,GAAkB;QACpB,IAAMQ,IAAaR,GAAkBS,OAAOD;QAE5C,IAAIA,KAAAA,CACFD,IAAQN,GAAAA,CAAOO,CAAA,CAAA,EAEXD,CAAAA,GAAO;YACT,IAAMG,IAAcV,EAAiBW,MAAAA,CAAOC,IAAAA,CACzCC,KAAM,UAAUA,KAAKA,EAAEC,IAAAA,KAASN,CAAA,GAG7BO,IAASL,GAAaM,SAAS,QAC/BC,IAAiBP,GAAaM,SAAS;YAE7C,IAAID,GAAQ;gBACV,IAAMb,IACJ,UAAWQ,EAAYD,KAAAA,IAASC,GAAaD,OAAOS,MAAMC,iBAC1DhB;gBAEFI,IAAQa,EAAW;oBAAEF,MAAMX;oBAAOD,MAAAA;oBAAMe,SAASnB;gBAAW,CAAA,KAAMK;YACpE;YAEIU,KAAAA,CAEFV,IADmCe,EAAwBrB,CAAAA,CAAKO,CAAA,CAAW,CAAA;QAG/E;IAEJ;IAEA,OAAIH,KAAAA,CACFE,QAAQgB,+NAAAA,EAAelB,GAAcmB,OAAOlB,CAAA,KAASD,GAAcoB,IAAAA,GAIjElB,KAASmB,EAA0BnB,CAAA,KAAA,CACrCA,IAAQoB,EAAsBpB,EAAMqB,IAAAA,CAAKC,QAAAA,EAAAA,CAAW,CAAA,CAAA,EAAIA,YAAY,CAAA,CAAA,EAAI,EAAA,CAAA,GAGtE,CAACtB,KAASmB,EAA0BtB,CAAA,KAAA,CACtCG,IAAQoB,EAAsBvB,EAASwB,IAAAA,CAAKC,QAAAA,EAAAA,CAAW,CAAA,CAAA,EAAIA,YAAY,CAAA,CAAA,EAAI,EAAA,CAAA,GAGxEtB,KAAAA,CACHA,IAAQ,OAAOH,KAAa,WAAWA,IAAW,CAAA,CAAA,EAAIE,EAAKwB,CAAAA,CAAE,kBAAA,CAAA,CAAA,CAAA,CAAA,GAGxDvB;AACT,EGvEA,OAASwB,kBAAAA,MAAsB;;AAExB,IAAAC,KAAK,SAAAA,CAAAA,CAAA;IAAA,OAAA,EAAA,UAAA,GAAA,eAAA,EAAA,MAAA,GAAA,WAAAA;AAAAA,EAAAA,CAAAA;AAwBL,SAASC,GACdC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,CAAgB;IAiEhB,OA/DeF,EAASG,MAAAA,CACtB,CAACC,GAAQC,IAAA;QAEP,IAAIA,EAAcC,MAAAA,EAAQC,OAAOC,UAAU,CAAA,GACzC,OAAOJ;QAGT,IAAIH,GAAAA,CAAcI,EAAcI,IAAAA,CAAKC,WAAAA,CAAW,CAAA,CAAA,EAAA,CAAML,EAAcC,MAAAA,CAAOK,IAAI,CAAA,EAAGC,MAAM;YACtF,IAAMC,QAAkBhB,+NAAAA,EAAeQ,EAAcC,MAAAA,CAAOC,KAAAA,CAAMC,KAAAA,EAAON,CAAA,GAEnEY,IACJ,YAAYT,EAAcC,MAAAA,GACtBD,EAAcC,MAAAA,CAAOS,MAAAA,CAAOC,MAAAA,GAC5BX,EAAcC,MAAAA,CAAOW,KAAAA,EAErBA,IACJ,OAAOH,KAAoB,aACvBA,EAAgB;gBAAEZ,MAAAA;gBAAMgB,GAAGhB,EAAKgB;YAAE,CAAA,IAClCJ;YAEN,IAAIT,EAAcC,MAAAA,CAAOC,KAAAA,CAAMC,KAAAA,EAAO;gBACpC,IAAMW,IAAgBf,EAAOgB,IAAAA,EAC1BZ,QAAUX,+NAAAA,EAAeW,EAAMS,KAAAA,EAAOf,CAAA,MAAUW,CAAA,GAG/CQ,IAA6BF;gBAE5BA,KAAAA,CACHE,IAAe;oBAAErB,UAAU,CAAA,CAAA;oBAAIiB,OAAOJ;gBAAgB,GACtDT,EAAOkB,IAAAA,CAAKD,CAAA,CAAA,GAGdA,EAAarB,QAAAA,CAASsB,IAAAA,CAAK;oBACzBX,MAAMN,EAAcC,MAAAA,CAAOK,IAAAA;oBAC3BF,MAAMJ,EAAcI,IAAAA;oBACpBQ,OAAAA;gBACF,CAAA;YACF,OACuBb,EAAOgB,IAAAA,EAAMZ,QACzBX,+NAAAA,EAAeW,EAAMS,KAAAA,EAAOf,CAAA,MAAUA,EAAKgB,CAAAA,CAAE,CAAA,QAAA,EAAWb,EAAcI,IAAI,EAAE,CACrF,EACaT,QAAAA,CAASsB,IAAAA,CAAK;gBACzBX,MAAMN,EAAcC,MAAAA,CAAOK,IAAAA;gBAC3BF,MAAMJ,EAAcI,IAAAA;gBACpBQ,OAAAA;YACF,CAAA;QAEJ;QAEA,OAAOb;IACT,GACA;QACE;YACEJ,UAAU,CAAA,CAAA;YACViB,OAAOf,EAAKgB,CAAAA,CAAE,qBAAA;QAChB;QACA;YACElB,UAAU,CAAA,CAAA;YACViB,OAAOf,EAAKgB,CAAAA,CAAE,iBAAA;QAChB,CAAA;KACD,EAGWK,MAAAA,EAAQf,IAAUA,EAAMR,QAAAA,CAASwB,MAAAA,GAAS,CAAA;AAC1D,CCrGA,OAASC,kBAAAA,OAAsB;;AAOxB,IAAMC,KAAwBA,CAAC,EAAEC,YAAAA,CAAAA,EAAYC,QAAAA,CAAM,EAAA,GAAwB;IAChF,IAAMC,QAAgBJ,0MAAAA,EAAe;QACnCE,YAAAA;QACAG,MAAM;IACR,CAAA;IACAF,EAAOG,IAAAA,CAAKF,CAAA;AACd,ECbA,OAASG,kBAAAA,OAAsB;;AAQxB,IAAMC,KAAeA,CAAC,EAAEC,YAAAA,CAAAA,EAAYC,gBAAAA,CAAAA,EAAgBC,QAAAA,CAAM,EAAA,GAAe;IAC9E,IAAMC,QAAgBL,0MAAAA,EAAe;QACnCE,YAAAA;QACAI,MAAMH,IAAiB,CAAA,aAAA,EAAgBA,CAAA,EAAA,GAAmB;IAC5D,CAAA;IACAC,EAAOG,IAAAA,CAAKF,CAAA;AACd;ACQO,IAAMG,KAAiB,OAAO,EACnCC,IAAAA,CAAAA,EACAC,iBAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACAC,sBAAAA,CAAAA,EACAC,YAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,aAAAA,CAAAA,EACAC,kBAAAA,CAAAA,EACAC,8BAAAA,CAAAA,EACAC,sBAAAA,CAAAA,EACAC,MAAAA,CAAI,EAAA,GACiB;IACrB,IAAKL,GAIL,IAAI;QAEF,MAAMI,EAAqBT,GAAIE,KAAkBE,GAAYM,CAAA,GAExDJ,KAAAA,CACHH,EAAqBQ,OAAAA,CAAQC,mBAAAA,GAAsB,CAAA,CAAA,GAIrDT,EAAqBQ,OAAAA,GAAU;YAC7BC,qBAAqBT,EAAqBQ,OAAAA,EAASC;YACnDC,UAAU,CAAA;YACVH,MAAAA;QACF,GACAH,EAAiBG,CAAA,GAGbJ,KAAeE,KACjBA,EAA6B,CAAA,CAAA,GAI3BP,KACFA,EAAA;IAEJ,EAAA,OAASa,GAAO;QAEdC,QAAQD,KAAAA,CAAM,mCAAmCA,CAAA;IACnD;AACF;AChEO,IAAME,MAAqBC,GAAA;IAYhC,IAAM,EAAEC,gBAAAA,CAAAA,EAAgBC,gBAAAA,CAAAA,EAAgBC,YAAAA,CAAAA,EAAYC,WAAAA,CAAS,EAAA,GAAKJ;IAElE,OAAIC,IACKI,CAAAA,CAAAA,CACLD,KAAcF,GAAgBI,UAC3B,CAACF,KAAcF,GAAkDK,MAAAA,IAIpEJ,IACKE,CAAAA,CAASH,GAA8CI,SAGzD,CAAA;AACT;AC9BO,IAAME,MAAsBC,IAC1BA,KAAQ,OAAOA,KAAS;ACH1B,IAAMC,KAAYA,CAAC,EACxBC,IAAAA,CAAAA,EACAC,gBAAAA,CAAAA,EACAC,YAAAA,CAAU,EAAA,GAKGC,CAAAA,CAAAA,CAAQD,KAAeD,KAAoBD,CAAAA;ACRnD,SAASI,GAAWC,CAAAA,CAAmB;IAK5C,OAJIA,MAAOC,KAAAA,KAIP,OAAOD,KAAO,WACTA,IAGFE,mBAAmBF,CAAA;AAC5B", "debugId": null}}]}