module.exports = [
"[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatDistance.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatDistance",
    ()=>formatDistance
]);
function declension(scheme, count) {
    // scheme for count=1 exists
    if (scheme.one !== undefined && count === 1) {
        return scheme.one;
    }
    const rem10 = count % 10;
    const rem100 = count % 100;
    // 1, 21, 31, ...
    if (rem10 === 1 && rem100 !== 11) {
        return scheme.singularNominative.replace("{{count}}", String(count));
    // 2, 3, 4, 22, 23, 24, 32 ...
    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {
        return scheme.singularGenitive.replace("{{count}}", String(count));
    // 5, 6, 7, 8, 9, 10, 11, ...
    } else {
        return scheme.pluralGenitive.replace("{{count}}", String(count));
    }
}
function buildLocalizeTokenFn(scheme) {
    return (count, options)=>{
        if (options?.addSuffix) {
            if (options.comparison && options.comparison > 0) {
                if (scheme.future) {
                    return declension(scheme.future, count);
                } else {
                    return "через " + declension(scheme.regular, count);
                }
            } else {
                if (scheme.past) {
                    return declension(scheme.past, count);
                } else {
                    return declension(scheme.regular, count) + " назад";
                }
            }
        } else {
            return declension(scheme.regular, count);
        }
    };
}
const formatDistanceLocale = {
    lessThanXSeconds: buildLocalizeTokenFn({
        regular: {
            one: "меньше секунды",
            singularNominative: "меньше {{count}} секунды",
            singularGenitive: "меньше {{count}} секунд",
            pluralGenitive: "меньше {{count}} секунд"
        },
        future: {
            one: "меньше, чем через секунду",
            singularNominative: "меньше, чем через {{count}} секунду",
            singularGenitive: "меньше, чем через {{count}} секунды",
            pluralGenitive: "меньше, чем через {{count}} секунд"
        }
    }),
    xSeconds: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} секунда",
            singularGenitive: "{{count}} секунды",
            pluralGenitive: "{{count}} секунд"
        },
        past: {
            singularNominative: "{{count}} секунду назад",
            singularGenitive: "{{count}} секунды назад",
            pluralGenitive: "{{count}} секунд назад"
        },
        future: {
            singularNominative: "через {{count}} секунду",
            singularGenitive: "через {{count}} секунды",
            pluralGenitive: "через {{count}} секунд"
        }
    }),
    halfAMinute: (_count, options)=>{
        if (options?.addSuffix) {
            if (options.comparison && options.comparison > 0) {
                return "через полминуты";
            } else {
                return "полминуты назад";
            }
        }
        return "полминуты";
    },
    lessThanXMinutes: buildLocalizeTokenFn({
        regular: {
            one: "меньше минуты",
            singularNominative: "меньше {{count}} минуты",
            singularGenitive: "меньше {{count}} минут",
            pluralGenitive: "меньше {{count}} минут"
        },
        future: {
            one: "меньше, чем через минуту",
            singularNominative: "меньше, чем через {{count}} минуту",
            singularGenitive: "меньше, чем через {{count}} минуты",
            pluralGenitive: "меньше, чем через {{count}} минут"
        }
    }),
    xMinutes: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} минута",
            singularGenitive: "{{count}} минуты",
            pluralGenitive: "{{count}} минут"
        },
        past: {
            singularNominative: "{{count}} минуту назад",
            singularGenitive: "{{count}} минуты назад",
            pluralGenitive: "{{count}} минут назад"
        },
        future: {
            singularNominative: "через {{count}} минуту",
            singularGenitive: "через {{count}} минуты",
            pluralGenitive: "через {{count}} минут"
        }
    }),
    aboutXHours: buildLocalizeTokenFn({
        regular: {
            singularNominative: "около {{count}} часа",
            singularGenitive: "около {{count}} часов",
            pluralGenitive: "около {{count}} часов"
        },
        future: {
            singularNominative: "приблизительно через {{count}} час",
            singularGenitive: "приблизительно через {{count}} часа",
            pluralGenitive: "приблизительно через {{count}} часов"
        }
    }),
    xHours: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} час",
            singularGenitive: "{{count}} часа",
            pluralGenitive: "{{count}} часов"
        }
    }),
    xDays: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} день",
            singularGenitive: "{{count}} дня",
            pluralGenitive: "{{count}} дней"
        }
    }),
    aboutXWeeks: buildLocalizeTokenFn({
        regular: {
            singularNominative: "около {{count}} недели",
            singularGenitive: "около {{count}} недель",
            pluralGenitive: "около {{count}} недель"
        },
        future: {
            singularNominative: "приблизительно через {{count}} неделю",
            singularGenitive: "приблизительно через {{count}} недели",
            pluralGenitive: "приблизительно через {{count}} недель"
        }
    }),
    xWeeks: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} неделя",
            singularGenitive: "{{count}} недели",
            pluralGenitive: "{{count}} недель"
        }
    }),
    aboutXMonths: buildLocalizeTokenFn({
        regular: {
            singularNominative: "около {{count}} месяца",
            singularGenitive: "около {{count}} месяцев",
            pluralGenitive: "около {{count}} месяцев"
        },
        future: {
            singularNominative: "приблизительно через {{count}} месяц",
            singularGenitive: "приблизительно через {{count}} месяца",
            pluralGenitive: "приблизительно через {{count}} месяцев"
        }
    }),
    xMonths: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} месяц",
            singularGenitive: "{{count}} месяца",
            pluralGenitive: "{{count}} месяцев"
        }
    }),
    aboutXYears: buildLocalizeTokenFn({
        regular: {
            singularNominative: "около {{count}} года",
            singularGenitive: "около {{count}} лет",
            pluralGenitive: "около {{count}} лет"
        },
        future: {
            singularNominative: "приблизительно через {{count}} год",
            singularGenitive: "приблизительно через {{count}} года",
            pluralGenitive: "приблизительно через {{count}} лет"
        }
    }),
    xYears: buildLocalizeTokenFn({
        regular: {
            singularNominative: "{{count}} год",
            singularGenitive: "{{count}} года",
            pluralGenitive: "{{count}} лет"
        }
    }),
    overXYears: buildLocalizeTokenFn({
        regular: {
            singularNominative: "больше {{count}} года",
            singularGenitive: "больше {{count}} лет",
            pluralGenitive: "больше {{count}} лет"
        },
        future: {
            singularNominative: "больше, чем через {{count}} год",
            singularGenitive: "больше, чем через {{count}} года",
            pluralGenitive: "больше, чем через {{count}} лет"
        }
    }),
    almostXYears: buildLocalizeTokenFn({
        regular: {
            singularNominative: "почти {{count}} год",
            singularGenitive: "почти {{count}} года",
            pluralGenitive: "почти {{count}} лет"
        },
        future: {
            singularNominative: "почти через {{count}} год",
            singularGenitive: "почти через {{count}} года",
            pluralGenitive: "почти через {{count}} лет"
        }
    })
};
const formatDistance = (token, count, options)=>{
    return formatDistanceLocale[token](count, options);
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatLong.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatLong",
    ()=>formatLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildFormatLongFn.js [app-rsc] (ecmascript)");
;
const dateFormats = {
    full: "EEEE, d MMMM y 'г.'",
    long: "d MMMM y 'г.'",
    medium: "d MMM y 'г.'",
    short: "dd.MM.y"
};
const timeFormats = {
    full: "H:mm:ss zzzz",
    long: "H:mm:ss z",
    medium: "H:mm:ss",
    short: "H:mm"
};
const dateTimeFormats = {
    any: "{{date}}, {{time}}"
};
const formatLong = {
    date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateFormats,
        defaultWidth: "full"
    }),
    time: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: timeFormats,
        defaultWidth: "full"
    }),
    dateTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateTimeFormats,
        defaultWidth: "any"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/isSameWeek.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "isSameWeek",
    ()=>isSameWeek
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$_lib$2f$normalizeDates$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/_lib/normalizeDates.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/startOfWeek.js [app-rsc] (ecmascript)");
;
;
function isSameWeek(laterDate, earlierDate, options) {
    const [laterDate_, earlierDate_] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$_lib$2f$normalizeDates$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["normalizeDates"])(options?.in, laterDate, earlierDate);
    return +(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["startOfWeek"])(laterDate_, options) === +(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["startOfWeek"])(earlierDate_, options);
}
const __TURBOPACK__default__export__ = isSameWeek;
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatRelative.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatRelative",
    ()=>formatRelative
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$isSameWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/isSameWeek.js [app-rsc] (ecmascript)");
;
const accusativeWeekdays = [
    "воскресенье",
    "понедельник",
    "вторник",
    "среду",
    "четверг",
    "пятницу",
    "субботу"
];
function lastWeek(day) {
    const weekday = accusativeWeekdays[day];
    switch(day){
        case 0:
            return "'в прошлое " + weekday + " в' p";
        case 1:
        case 2:
        case 4:
            return "'в прошлый " + weekday + " в' p";
        case 3:
        case 5:
        case 6:
            return "'в прошлую " + weekday + " в' p";
    }
}
function thisWeek(day) {
    const weekday = accusativeWeekdays[day];
    if (day === 2 /* Tue */ ) {
        return "'во " + weekday + " в' p";
    } else {
        return "'в " + weekday + " в' p";
    }
}
function nextWeek(day) {
    const weekday = accusativeWeekdays[day];
    switch(day){
        case 0:
            return "'в следующее " + weekday + " в' p";
        case 1:
        case 2:
        case 4:
            return "'в следующий " + weekday + " в' p";
        case 3:
        case 5:
        case 6:
            return "'в следующую " + weekday + " в' p";
    }
}
const formatRelativeLocale = {
    lastWeek: (date, baseDate, options)=>{
        const day = date.getDay();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$isSameWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSameWeek"])(date, baseDate, options)) {
            return thisWeek(day);
        } else {
            return lastWeek(day);
        }
    },
    yesterday: "'вчера в' p",
    today: "'сегодня в' p",
    tomorrow: "'завтра в' p",
    nextWeek: (date, baseDate, options)=>{
        const day = date.getDay();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$isSameWeek$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isSameWeek"])(date, baseDate, options)) {
            return thisWeek(day);
        } else {
            return nextWeek(day);
        }
    },
    other: "P"
};
const formatRelative = (token, date, baseDate, options)=>{
    const format = formatRelativeLocale[token];
    if (typeof format === "function") {
        return format(date, baseDate, options);
    }
    return format;
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/localize.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "localize",
    ()=>localize
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildLocalizeFn.js [app-rsc] (ecmascript)");
;
const eraValues = {
    narrow: [
        "до н.э.",
        "н.э."
    ],
    abbreviated: [
        "до н. э.",
        "н. э."
    ],
    wide: [
        "до нашей эры",
        "нашей эры"
    ]
};
const quarterValues = {
    narrow: [
        "1",
        "2",
        "3",
        "4"
    ],
    abbreviated: [
        "1-й кв.",
        "2-й кв.",
        "3-й кв.",
        "4-й кв."
    ],
    wide: [
        "1-й квартал",
        "2-й квартал",
        "3-й квартал",
        "4-й квартал"
    ]
};
const monthValues = {
    narrow: [
        "Я",
        "Ф",
        "М",
        "А",
        "М",
        "И",
        "И",
        "А",
        "С",
        "О",
        "Н",
        "Д"
    ],
    abbreviated: [
        "янв.",
        "фев.",
        "март",
        "апр.",
        "май",
        "июнь",
        "июль",
        "авг.",
        "сент.",
        "окт.",
        "нояб.",
        "дек."
    ],
    wide: [
        "январь",
        "февраль",
        "март",
        "апрель",
        "май",
        "июнь",
        "июль",
        "август",
        "сентябрь",
        "октябрь",
        "ноябрь",
        "декабрь"
    ]
};
const formattingMonthValues = {
    narrow: [
        "Я",
        "Ф",
        "М",
        "А",
        "М",
        "И",
        "И",
        "А",
        "С",
        "О",
        "Н",
        "Д"
    ],
    abbreviated: [
        "янв.",
        "фев.",
        "мар.",
        "апр.",
        "мая",
        "июн.",
        "июл.",
        "авг.",
        "сент.",
        "окт.",
        "нояб.",
        "дек."
    ],
    wide: [
        "января",
        "февраля",
        "марта",
        "апреля",
        "мая",
        "июня",
        "июля",
        "августа",
        "сентября",
        "октября",
        "ноября",
        "декабря"
    ]
};
const dayValues = {
    narrow: [
        "В",
        "П",
        "В",
        "С",
        "Ч",
        "П",
        "С"
    ],
    short: [
        "вс",
        "пн",
        "вт",
        "ср",
        "чт",
        "пт",
        "сб"
    ],
    abbreviated: [
        "вск",
        "пнд",
        "втр",
        "срд",
        "чтв",
        "птн",
        "суб"
    ],
    wide: [
        "воскресенье",
        "понедельник",
        "вторник",
        "среда",
        "четверг",
        "пятница",
        "суббота"
    ]
};
const dayPeriodValues = {
    narrow: {
        am: "ДП",
        pm: "ПП",
        midnight: "полн.",
        noon: "полд.",
        morning: "утро",
        afternoon: "день",
        evening: "веч.",
        night: "ночь"
    },
    abbreviated: {
        am: "ДП",
        pm: "ПП",
        midnight: "полн.",
        noon: "полд.",
        morning: "утро",
        afternoon: "день",
        evening: "веч.",
        night: "ночь"
    },
    wide: {
        am: "ДП",
        pm: "ПП",
        midnight: "полночь",
        noon: "полдень",
        morning: "утро",
        afternoon: "день",
        evening: "вечер",
        night: "ночь"
    }
};
const formattingDayPeriodValues = {
    narrow: {
        am: "ДП",
        pm: "ПП",
        midnight: "полн.",
        noon: "полд.",
        morning: "утра",
        afternoon: "дня",
        evening: "веч.",
        night: "ночи"
    },
    abbreviated: {
        am: "ДП",
        pm: "ПП",
        midnight: "полн.",
        noon: "полд.",
        morning: "утра",
        afternoon: "дня",
        evening: "веч.",
        night: "ночи"
    },
    wide: {
        am: "ДП",
        pm: "ПП",
        midnight: "полночь",
        noon: "полдень",
        morning: "утра",
        afternoon: "дня",
        evening: "вечера",
        night: "ночи"
    }
};
const ordinalNumber = (dirtyNumber, options)=>{
    const number = Number(dirtyNumber);
    const unit = options?.unit;
    let suffix;
    if (unit === "date") {
        suffix = "-е";
    } else if (unit === "week" || unit === "minute" || unit === "second") {
        suffix = "-я";
    } else {
        suffix = "-й";
    }
    return number + suffix;
};
const localize = {
    ordinalNumber,
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: eraValues,
        defaultWidth: "wide"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: quarterValues,
        defaultWidth: "wide",
        argumentCallback: (quarter)=>quarter - 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: monthValues,
        defaultWidth: "wide",
        formattingValues: formattingMonthValues,
        defaultFormattingWidth: "wide"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayValues,
        defaultWidth: "wide"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayPeriodValues,
        defaultWidth: "any",
        formattingValues: formattingDayPeriodValues,
        defaultFormattingWidth: "wide"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/match.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "match",
    ()=>match
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchFn.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js [app-rsc] (ecmascript)");
;
;
const matchOrdinalNumberPattern = /^(\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;
const parseOrdinalNumberPattern = /\d+/i;
const matchEraPatterns = {
    narrow: /^((до )?н\.?\s?э\.?)/i,
    abbreviated: /^((до )?н\.?\s?э\.?)/i,
    wide: /^(до нашей эры|нашей эры|наша эра)/i
};
const parseEraPatterns = {
    any: [
        /^д/i,
        /^н/i
    ]
};
const matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,
    wide: /^[1234](-?[ыои]?й?)? квартал/i
};
const parseQuarterPatterns = {
    any: [
        /1/i,
        /2/i,
        /3/i,
        /4/i
    ]
};
const matchMonthPatterns = {
    narrow: /^[яфмаисонд]/i,
    abbreviated: /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\.?/i,
    wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i
};
const parseMonthPatterns = {
    narrow: [
        /^я/i,
        /^ф/i,
        /^м/i,
        /^а/i,
        /^м/i,
        /^и/i,
        /^и/i,
        /^а/i,
        /^с/i,
        /^о/i,
        /^н/i,
        /^я/i
    ],
    any: [
        /^я/i,
        /^ф/i,
        /^мар/i,
        /^ап/i,
        /^ма[йя]/i,
        /^июн/i,
        /^июл/i,
        /^ав/i,
        /^с/i,
        /^о/i,
        /^н/i,
        /^д/i
    ]
};
const matchDayPatterns = {
    narrow: /^[впсч]/i,
    short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\.?/i,
    abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,
    wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i
};
const parseDayPatterns = {
    narrow: [
        /^в/i,
        /^п/i,
        /^в/i,
        /^с/i,
        /^ч/i,
        /^п/i,
        /^с/i
    ],
    any: [
        /^в[ос]/i,
        /^п[он]/i,
        /^в/i,
        /^ср/i,
        /^ч/i,
        /^п[ят]/i,
        /^с[уб]/i
    ]
};
const matchDayPeriodPatterns = {
    narrow: /^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,
    abbreviated: /^([дп]п|полн\.?|полд\.?|утр[оа]|день|дня|веч\.?|ноч[ьи])/i,
    wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i
};
const parseDayPeriodPatterns = {
    any: {
        am: /^дп/i,
        pm: /^пп/i,
        midnight: /^полн/i,
        noon: /^полд/i,
        morning: /^у/i,
        afternoon: /^д[ен]/i,
        evening: /^в/i,
        night: /^н/i
    }
};
const match = {
    ordinalNumber: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchPatternFn"])({
        matchPattern: matchOrdinalNumberPattern,
        parsePattern: parseOrdinalNumberPattern,
        valueCallback: (value)=>parseInt(value, 10)
    }),
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchEraPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseEraPatterns,
        defaultParseWidth: "any"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchQuarterPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseQuarterPatterns,
        defaultParseWidth: "any",
        valueCallback: (index)=>index + 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchMonthPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseMonthPatterns,
        defaultParseWidth: "any"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPatterns,
        defaultParseWidth: "any"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPeriodPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPeriodPatterns,
        defaultParseWidth: "any"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/ru.js [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "ru",
    ()=>ru
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatDistance.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatLong.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/formatRelative.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$localize$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/localize.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$match$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/ru/_lib/match.js [app-rsc] (ecmascript)");
;
;
;
;
;
const ru = {
    code: "ru",
    formatDistance: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDistance"],
    formatLong: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatLong"],
    formatRelative: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatRelative"],
    localize: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$localize$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["localize"],
    match: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$ru$2f$_lib$2f$match$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["match"],
    options: {
        weekStartsOn: 1 /* Monday */ ,
        firstWeekContainsDate: 1
    }
};
const __TURBOPACK__default__export__ = ru;
}),
];

//# sourceMappingURL=a04e6_date-fns_069ce7f5._.js.map