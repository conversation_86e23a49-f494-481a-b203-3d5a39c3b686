{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/Nav/NavHamburger/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/Nav/NavHamburger/index.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON>er, useNav } from '@payloadcms/ui'\nimport React from 'react'\n\n/**\n * @internal\n */\nexport const NavHamburger: React.FC<{\n  baseClass?: string\n}> = ({ baseClass }) => {\n  const { navOpen, setNavOpen } = useNav()\n\n  return (\n    <button\n      className={`${baseClass}__mobile-close`}\n      onClick={() => {\n        setNavOpen(false)\n      }}\n      tabIndex={!navOpen ? -1 : undefined}\n      type=\"button\"\n    >\n      <Hamburger isActive />\n    </button>\n  )\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "useNav", "React", "NavHamburger", "t0", "$", "baseClass", "navOpen", "setNavOpen", "t1", "t2", "t3", "undefined", "t4", "_jsx", "className", "onClick", "tabIndex", "type", "children", "isActive"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,SAAS,EAAEC,MAAM,QAAQ;AAClC,OAAOC,KAAA,MAAW;AAFlB;;;;;AAOO,MAAMC,YAAA,IAERC,EAAA;IAAA,MAAAC,CAAA,OAAAN,0MAAA,EAAA;IAAC,MAAA,EAAAO,SAAAA,EAAA,GAAAF,EAAa;IACjB,MAAA,EAAAG,OAAA,EAAAC,UAAAA,EAAA,OAAgCP,+NAAA;IAIjB,MAAAQ,EAAA,GAAA,GAAG,OAAAH,SAAA,EAAA,eAAyB;IAAA,IAAAI,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAG,UAAA,EAAA;QAC9BE,EAAA,GAAAA,CAAA;YACPF,UAAA,CAAA,KAAW;QAAA;QACbH,CAAA,CAAA,EAAA,GAAAG,UAAA;QAAAH,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IACU,MAAAM,EAAA,GAAA,CAACJ,OAAA,GAAA,CAAA,IAAAK,SAAe;IAAA,IAAAC,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAI,EAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,EAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,EAAA,EAAA;QAL5BE,EAAA,OAAAC,uMAAA,EAAC,UAAA;YAAAC,SAAA,EACYN,EAA4B;YAAAO,OAAA,EAC9BN,EAET;YAAAO,QAAA,EACUN,EAAgB;YAAAO,IAAA,EACrB;YAAAC,QAAA,MAELL,uMAAA,EAAAd,kOAAA,EAAA;gBAAAoB,QAAA,EAAA;YAAA,C;;;;;;;;;WARFP,E;CAWJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/Nav/NavWrapper/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/Nav/NavWrapper/index.tsx"], "sourcesContent": ["'use client'\nimport { useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\n/**\n * @internal\n */\nexport const NavWrapper: React.FC<{\n  baseClass?: string\n  children: React.ReactNode\n}> = (props) => {\n  const { baseClass, children } = props\n\n  const { hydrated, navOpen, navRef, shouldAnimate } = useNav()\n\n  return (\n    <aside\n      className={[\n        baseClass,\n        navOpen && `${baseClass}--nav-open`,\n        shouldAnimate && `${baseClass}--nav-animate`,\n        hydrated && `${baseClass}--nav-hydrated`,\n      ]\n        .filter(Boolean)\n        .join(' ')}\n      inert={!navOpen ? true : undefined}\n    >\n      <div className={`${baseClass}__scroll`} ref={navRef}>\n        {children}\n      </div>\n    </aside>\n  )\n}\n"], "names": ["c", "_c", "useNav", "React", "NavWrapper", "props", "$", "baseClass", "children", "hydrated", "navOpen", "navRef", "shouldAnimate", "t0", "t1", "t2", "t3", "filter", "Boolean", "t4", "join", "t5", "undefined", "t6", "t7", "_jsx", "className", "inert", "ref"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,MAAM,QAAQ;AACvB,OAAOC,KAAA,MAAW;AAFlB;;;;;AASO,MAAMC,UAAA,IAGRC,KAAA;IAAA,MAAAC,CAAA,OAAAL,0MAAA,EAAA;IACH,MAAA,EAAAM,SAAA,EAAAC,QAAAA,EAAA,GAAgCH,KAAA;IAEhC,MAAA,EAAAI,QAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAAA,EAAA,OAAqDV,+NAAA;IAM/C,MAAAW,EAAA,GAAAH,OAAA,IAAW,GAAG,OAAAH,SAAA,EAAA,WAAqB;IACnC,MAAAO,EAAA,GAAAF,aAAA,IAAiB,GAAG,OAAAL,SAAA,EAAA,cAAwB;IAC5C,MAAAQ,EAAA,GAAAN,QAAA,IAAY,GAAG,OAAAF,SAAA,EAAA,eAAyB;IAAA,IAAAS,EAAA;IAAA,IAAAV,CAAA,CAAA,EAAA,KAAAC,SAAA,IAAAD,CAAA,CAAA,EAAA,KAAAO,EAAA,IAAAP,CAAA,CAAA,EAAA,KAAAQ,EAAA,IAAAR,CAAA,CAAA,EAAA,KAAAS,EAAA,EAAA;QAJ/BC,EAAA,GAAA;YACTT,SAAA;YACAM,EAAmC;YACnCC,EAA4C;YAC5CC,EAAwC;SAAA,CAAAE,MAAA,CAAAC,OAEhC;QAAAZ,CAAA,CAAA,EAAA,GAAAC,SAAA;QAAAD,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAS,EAAA;QAAAT,CAAA,CAAA,EAAA,GAAAU,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAV,CAAA,CAAA,EAAA;IAAA;IANC,MAAAa,EAAA,GAAAH,EAMD,CAAAI,IAAA,CACF;IACD,MAAAC,EAAA,GAAA,CAACX,OAAA,GAAA,OAAAY,SAAiB;IAET,MAAAC,EAAA,GAAA,GAAG,OAAAhB,SAAA,EAAA,SAAmB;IAAA,IAAAiB,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAK,MAAA,IAAAL,CAAA,CAAA,EAAA,KAAAa,EAAA,IAAAb,CAAA,CAAA,EAAA,KAAAe,EAAA,IAAAf,CAAA,CAAA,EAAA,KAAAiB,EAAA,EAAA;QAXxCC,EAAA,OAAAC,uMAAA,EAAC,SAAA;YAAAC,SAAA,EACYP,EAOH;YAAAQ,KAAA,EACDN,EAAkB;YAAAb,QAAA,MAEzBiB,uMAAA,EAAC,OAAA;gBAAAC,SAAA,EAAeH,EAAsB;gBAAAK,GAAA,EAAOjB,MAAA;gBAAAH;YAAA,C;;;;;;;;;;;WAX/CgB,E;CAgBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/Nav/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/Nav/index.client.tsx"], "sourcesContent": ["'use client'\n\nimport type { groupNavItems } from '@payloadcms/ui/shared'\nimport type { NavPreferences } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { BrowseByFolderButton, Link, NavGroup, useConfig, useTranslation } from '@payloadcms/ui'\nimport { EntityType } from '@payloadcms/ui/shared'\nimport { usePathname } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\nimport React, { Fragment } from 'react'\n\nconst baseClass = 'nav'\n\n/**\n * @internal\n */\nexport const DefaultNavClient: React.FC<{\n  groups: ReturnType<typeof groupNavItems>\n  navPreferences: NavPreferences\n}> = ({ groups, navPreferences }) => {\n  const pathname = usePathname()\n\n  const {\n    config: {\n      admin: {\n        routes: { browseByFolder: foldersRoute },\n      },\n      folders,\n      routes: { admin: adminRoute },\n    },\n  } = useConfig()\n\n  const { i18n } = useTranslation()\n\n  const folderURL = formatAdminURL({\n    adminRoute,\n    path: foldersRoute,\n  })\n\n  const viewingRootFolderView = pathname.startsWith(folderURL)\n\n  return (\n    <Fragment>\n      {folders && folders.browseByFolder && <BrowseByFolderButton active={viewingRootFolderView} />}\n      {groups.map(({ entities, label }, key) => {\n        return (\n          <NavGroup isOpen={navPreferences?.groups?.[label]?.open} key={key} label={label}>\n            {entities.map(({ slug, type, label }, i) => {\n              let href: string\n              let id: string\n\n              if (type === EntityType.collection) {\n                href = formatAdminURL({ adminRoute, path: `/collections/${slug}` })\n                id = `nav-${slug}`\n              }\n\n              if (type === EntityType.global) {\n                href = formatAdminURL({ adminRoute, path: `/globals/${slug}` })\n                id = `nav-global-${slug}`\n              }\n\n              const isActive =\n                pathname.startsWith(href) && ['/', undefined].includes(pathname[href.length])\n\n              const Label = (\n                <>\n                  {isActive && <div className={`${baseClass}__link-indicator`} />}\n                  <span className={`${baseClass}__link-label`}>{getTranslation(label, i18n)}</span>\n                </>\n              )\n\n              // If the URL matches the link exactly\n              if (pathname === href) {\n                return (\n                  <div className={`${baseClass}__link`} id={id} key={i}>\n                    {Label}\n                  </div>\n                )\n              }\n\n              return (\n                <Link className={`${baseClass}__link`} href={href} id={id} key={i} prefetch={false}>\n                  {Label}\n                </Link>\n              )\n            })}\n          </NavGroup>\n        )\n      })}\n    </Fragment>\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "BrowseByFolderButton", "Link", "NavGroup", "useConfig", "useTranslation", "EntityType", "usePathname", "formatAdminURL", "React", "Fragment", "baseClass", "DefaultNavClient", "t0", "$", "groups", "navPreferences", "pathname", "config", "t1", "admin", "t2", "folders", "routes", "t3", "t4", "browseByFolder", "foldersRoute", "adminRoute", "i18n", "t5", "folderURL", "path", "viewingRootFolderView", "startsWith", "t6", "t7", "key", "entities", "label", "_jsx", "isOpen", "open", "children", "map", "t8", "i", "slug", "type", "label_0", "href", "id", "collection", "global", "isActive", "undefined", "includes", "length", "Label", "_jsxs", "_Fragment", "className", "prefetch", "active"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAKA,SAASC,cAAc,QAAQ;AAC/B,SAASC,oBAAoB,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;;AAChF,SAASC,UAAU,QAAQ;AAC3B,SAASC,WAAW,QAAQ;AAC5B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,IAASC,QAAQ,QAAQ;AAVhC;;;;;;;;;AAYA,MAAMC,SAAA,GAAY;AAKX,MAAMC,gBAAA,IAGRC,EAAA;IAAA,MAAAC,CAAA,OAAAf,0MAAA,EAAA;IAAC,MAAA,EAAAgB,MAAA,EAAAC,cAAAA,EAAA,GAAAH,EAA0B;IAC9B,MAAAI,QAAA,OAAiBV,8KAAA;IAEjB,MAAA,EAAAW,MAAA,EAAAC,EAAAA,EAAA,OAQIf,gQAAA;IAPM,MAAA,EAAAgB,KAAA,EAAAC,EAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,EAAAA,EAAA,GAAAL,EAMP;IALQ,MAAA,EAAAI,MAAA,EAAAE,EAAAA,EAAA,GAAAJ,EAEN;IADS,MAAA,EAAAK,cAAA,EAAAC,YAAAA,EAAA,GAAAF,EAAgC;IAGlC,MAAA,EAAAL,KAAA,EAAAQ,UAAAA,EAAA,GAAAJ,EAAqB;IAIjC,MAAA,EAAAK,IAAAA,EAAA,OAAiBxB,0QAAA;IAAA,IAAAyB,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAc,UAAA,IAAAd,CAAA,CAAA,EAAA,KAAAQ,OAAA,IAAAR,CAAA,CAAA,EAAA,KAAAa,YAAA,IAAAb,CAAA,CAAA,EAAA,KAAAC,MAAA,IAAAD,CAAA,CAAA,EAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,EAAA,sEAAAE,cAAA,CAAAD,MAAA,KAAAD,CAAA,CAAA,EAAA,KAAAG,QAAA,EAAA;QAEjB,MAAAc,SAAA,OAAkBvB,6MAAA,EAAA;YAAAoB,UAAA;YAAAI,IAAA,EAEVL;QAAA,CACR;QAEA,MAAAM,qBAAA,GAA8BhB,QAAA,CAAAiB,UAAA,CAAoBH,SAAA;QAAA,IAAAI,EAAA;QAAA,IAAArB,CAAA,CAAA,EAAA,KAAAc,UAAA,IAAAd,CAAA,CAAA,EAAA,KAAAe,IAAA,IAAAf,CAAA,CAAA,GAAA,MAAAE,cAAA,iEAAAD,MAAA,KAAAD,CAAA,CAAA,GAAA,KAAAG,QAAA,EAAA;YAKlCkB,EAAA,GAAAA,CAAAC,EAAA,EAAAC,GAAA;;gBAAC,MAAA,EAAAC,QAAA,EAAAC,KAAAA,EAAA,GAAAH,EAAmB;gBAAA,WAE5BI,uMAAA,EAAArC,iOAAA,EAAA;oBAAAsC,MAAA,EAAkBzB,cAAA,2FAAAD,MAAA,iHAAA,CAAyBwB,KAAA,CAAA,8FAAAG,IAAA;oBAAAH,KAAA;oBAAAI,QAAA,EACxCL,QAAA,CAAAM,GAAA,CAAA,CAAAC,EAAA,EAAAC,CAAA;wBAAc,MAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAT,KAAA,EAAAU,OAAAA,EAAA,GAAAJ,EAAqB;wBAC9BK,GAAA,CAAAA,IAAA;wBACAC,GAAA,CAAAA,EAAA;wBAAA,IAEAH,IAAA,KAAA1C,mOAAA,CAAA8C,UAA8B,EAAA;4BAChCF,IAAA,CAAAA,CAAA,KAAOA,6MAAAA,EAAAA;gCAAAA;gCAAAA,KAAA,CAAmCA,gBAAgB,OAAAH,IAAA;4BAAM,CAAC;4BACjEI,EAAA,CAAAA,CAAA,CAAKA,OAAO,CAAM,MAANJ,IAAA;wBAAZ;wBAAA,IAGEC,IAAA,KAAA1C,mOAAA,CAAA+C,MAA0B,EAAA;4BAC5BH,IAAA,CAAAA,CAAA,KAAOA,6MAAAA,EAAAA;gCAAAA;gCAAAA,KAAA,CAAmCA,YAAY,OAAAH,IAAA;4BAAM,CAAC;4BAC7DI,EAAA,CAAAA,CAAA,CAAKA,cAAc,CAAM,MAANJ,IAAA;wBAAnB;wBAGF,MAAAO,QAAA,GACErC,QAAA,CAAAiB,UAAA,CAAoBgB,IAAA,KAAS;4BAAC;4BAAAK,SAAA;yBAAA,CAAAC,QAAA,CAAyBvC,QAAQ,CAACiC,IAAA,CAAAO,MAAA,CAAY;wBAE9E,MAAAC,KAAA,OACEC,wMAAA,EAAAC,4MAAA,EAAA;4BAAAjB,QAAA,EAAA;gCACGW,QAAA,IAAYd,2MAAA,EAAC,OAAA;oCAAAqB,SAAA,EAAe,GAAA,OAAAlD,SAAA,EAAA;gCAA8B,C;oCAC3D6B,uMAAA,EAAC,QAAA;oCAAAqB,SAAA,EAAgB,GAAA,OAAAlD,SAAA,EAAA,aAA0B;oCAAAgC,QAAA,MAAG3C,kOAAA,EAAeuC,OAAA,EAAOV,IAAA;gCAAA,C;6B;;4BAKpEZ,QAAA,KAAaiC,IAAA,EAAA;4BAAA,WAEbV,uMAAA,EAAC,OAAA;gCAAAqB,SAAA,EAAe,GAAA,OAAAlD,AAAoB,SAApB,EAAA;gCAAoBwC,EAAA;gCAAAR,QAAA,EACjCe;4BAAA,GADgDZ,CAAA;wBAAA;wBAAA,WAOrDN,uMAAA,EAAAtC,6NAAA,EAAA;4BAAA2D,SAAA,EAAiB,GAAA,OAAAlD,SAAA,EAAA,OAAoB;4BAAAuC,IAAA;4BAAAC,EAAA;4BAAAW,QAAA,EAAA;4BAAAnB,QAAA,EAClCe;wBAAA,GAD6DZ,CAAA;oBAAA,CAIpE;gBAAA,GAvC4DT,GAAA;YAAA;YA0ClEvB,CAAA,CAAA,EAAA,GAAAc,UAAA;YAAAd,CAAA,CAAA,EAAA,GAAAe,IAAA;YAAAf,CAAA,CAAA,GAAA,mEAAAE,cAAA,CAAAD,MAAA;YAAAD,CAAA,CAAA,GAAA,GAAAG,QAAA;YAAAH,CAAA,CAAA,GAAA,GAAAqB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAArB,CAAA,CAAA,GAAA;QAAA;QA9CFgB,EAAA,OAAA6B,wMAAA,EAAAjD,mMAAA,EAAA;YAAAiC,QAAA,EAAA;gBACGrB,OAAA,IAAWA,OAAA,CAAAI,cAAsB,QAAIc,uMAAA,EAAAvC,6OAAA,EAAA;oBAAA8D,MAAA,EAA8B9B;gBAAA,C;gBACnElB,MAAA,CAAA6B,GAAA,CAAWT,EA4CZ;aAAA;QAAA,C;;;;;;;;;;;;WA9CFL,E;CAiDJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/templates/Default/NavHamburger/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/templates/Default/NavHamburger/index.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const NavHamburger: React.FC = () => {\n  const { navOpen } = useNav()\n  return <Hamburger closeIcon=\"collapse\" isActive={navOpen} />\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "useNav", "React", "NavHamburger", "$", "navOpen", "t0", "_jsx", "closeIcon", "isActive"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,SAAS,EAAEC,MAAM,QAAQ;AAClC,OAAOC,KAAA,MAAW;AAFlB;;;;;AAIO,MAAMC,YAAA,GAAyBA,CAAA;IAAA,MAAAC,CAAA,OAAAL,0MAAA,EAAA;IACpC,MAAA,EAAAM,OAAAA,EAAA,OAAoBJ,+NAAA;IAAA,IAAAK,EAAA;IAAA,IAAAF,CAAA,CAAA,EAAA,KAAAC,OAAA,EAAA;QACbC,EAAA,OAAAC,uMAAA,EAAAP,kOAAA,EAAA;YAAAQ,SAAA,EAAqB;YAAAC,QAAA,EAAqBJ;QAAA,C;;;;;;WAA1CC,E;CACT", "ignoreList": [], "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/templates/Default/Wrapper/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/templates/Default/Wrapper/index.tsx"], "sourcesContent": ["'use client'\nimport { useNav } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nexport const Wrapper: React.FC<{\n  baseClass?: string\n  children?: React.ReactNode\n  className?: string\n}> = (props) => {\n  const { baseClass, children, className } = props\n  const { hydrated, navOpen, shouldAnimate } = useNav()\n\n  return (\n    <div\n      className={[\n        baseClass,\n        className,\n        navOpen && `${baseClass}--nav-open`,\n        shouldAnimate && `${baseClass}--nav-animate`,\n        hydrated && `${baseClass}--nav-hydrated`,\n      ]\n        .filter(Boolean)\n        .join(' ')}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": ["c", "_c", "useNav", "React", "Wrapper", "props", "$", "baseClass", "children", "className", "hydrated", "navOpen", "shouldAnimate", "t0", "t1", "t2", "t3", "filter", "Boolean", "t4", "join", "t5", "_jsx"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,MAAM,QAAQ;AACvB,OAAOC,KAAA,MAAW;AAFlB;;;;;AAMO,MAAMC,OAAA,IAIRC,KAAA;IAAA,MAAAC,CAAA,OAAAL,0MAAA,EAAA;IACH,MAAA,EAAAM,SAAA,EAAAC,QAAA,EAAAC,SAAAA,EAAA,GAA2CJ,KAAA;IAC3C,MAAA,EAAAK,QAAA,EAAAC,OAAA,EAAAC,aAAAA,EAAA,OAA6CV,+NAAA;IAOvC,MAAAW,EAAA,GAAAF,OAAA,IAAW,GAAG,OAAAJ,SAAA,EAAA,WAAqB;IACnC,MAAAO,EAAA,GAAAF,aAAA,IAAiB,GAAG,OAAAL,SAAA,EAAA,cAAwB;IAC5C,MAAAQ,EAAA,GAAAL,QAAA,IAAY,GAAG,OAAAH,QAAyB,CAAzB,EAAA;IAAyB,IAAAS,EAAA;IAAA,IAAAV,CAAA,CAAA,EAAA,KAAAC,SAAA,IAAAD,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAO,EAAA,IAAAP,CAAA,CAAA,EAAA,KAAAQ,EAAA,IAAAR,CAAA,CAAA,EAAA,KAAAS,EAAA,EAAA;QAL/BC,EAAA,GAAA;YACTT,SAAA;YACAE,SAAA;YACAI,EAAmC;YACnCC,EAA4C;YAC5CC,EAAwC;SAAA,CAAAE,MAAA,CAAAC,OAEhC;QAAAZ,CAAA,CAAA,EAAA,GAAAC,SAAA;QAAAD,CAAA,CAAA,EAAA,GAAAG,SAAA;QAAAH,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAS,EAAA;QAAAT,CAAA,CAAA,EAAA,GAAAU,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAV,CAAA,CAAA,EAAA;IAAA;IAPC,MAAAa,EAAA,GAAAH,EAOD,CAAAI,IAAA,CACF;IAAA,IAAAC,EAAA;IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAa,EAAA,EAAA;QATVE,EAAA,OAAAC,uMAAA,EAAC,OAAA;YAAAb,SAAA,EACYU,EAQH;YAAAX;QAAA,C;;;;;;;WATVa,E;CAcJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/DocumentHeader/Tabs/ShouldRenderTabs.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/DocumentHeader/Tabs/ShouldRenderTabs.tsx"], "sourcesContent": ["'use client'\nimport type React from 'react'\n\nimport { useDocumentInfo } from '@payloadcms/ui'\n\nexport const ShouldRenderTabs: React.FC<{\n  children: React.ReactNode\n}> = ({ children }) => {\n  const { id: idFromContext, collectionSlug, globalSlug } = useDocumentInfo()\n\n  const id = idFromContext !== 'create' ? idFromContext : null\n\n  // Don't show tabs when creating new documents\n  if ((collectionSlug && id) || globalSlug) {\n    return children\n  }\n\n  return null\n}\n"], "names": ["useDocumentInfo", "ShouldRenderTabs", "t0", "children", "id", "idFromContext", "collectionSlug", "globalSlug"], "mappings": ";;;;AAGA,SAASA,eAAe,QAAQ;AAHhC;;AAKO,MAAMC,gBAAA,IAERC,EAAA;IAAC,MAAA,EAAAC,QAAAA,EAAA,GAAAD,EAAY;IAChB,MAAA,EAAAE,EAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,UAAAA,EAAA,OAA0DP,wOAAA;IAE1D,MAAAI,EAAA,GAAWC,aAAA,KAAkB,WAAWA,aAAA,GAAA,IAAgB;IAAA,IAGpDC,cAAC,IAAkBF,EAAA,IAAOG,UAAA,EAAA;QAAA,OACrBJ,QAAA;IAAA;IAAA,OAAA;AAAA,CAIX", "ignoreList": [], "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/DocumentHeader/Tabs/Tab/TabLink.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/DocumentHeader/Tabs/Tab/TabLink.tsx"], "sourcesContent": ["'use client'\nimport type { SanitizedConfig } from 'payload'\n\nimport { Button } from '@payloadcms/ui'\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\nimport React from 'react'\n\nexport const DocumentTabLink: React.FC<{\n  adminRoute: SanitizedConfig['routes']['admin']\n  ariaLabel?: string\n  baseClass: string\n  children?: React.ReactNode\n  href: string\n  isActive?: boolean\n  newTab?: boolean\n}> = ({\n  adminRoute,\n  ariaLabel,\n  baseClass,\n  children,\n  href: hrefFromProps,\n  isActive: isActiveFromProps,\n  newTab,\n}) => {\n  const pathname = usePathname()\n  const params = useParams()\n\n  const searchParams = useSearchParams()\n\n  const locale = searchParams.get('locale')\n\n  const [entityType, entitySlug, segmentThree, segmentFour, ...rest] = params.segments || []\n  const isCollection = entityType === 'collections'\n\n  let docPath = formatAdminURL({\n    adminRoute,\n    path: `/${isCollection ? 'collections' : 'globals'}/${entitySlug}`,\n  })\n\n  if (isCollection) {\n    if (segmentThree === 'trash' && segmentFour) {\n      docPath += `/trash/${segmentFour}`\n    } else if (segmentThree) {\n      docPath += `/${segmentThree}`\n    }\n  }\n\n  const href = `${docPath}${hrefFromProps}`\n  // separated the two so it doesn't break checks against pathname\n  const hrefWithLocale = `${href}${locale ? `?locale=${locale}` : ''}`\n\n  const isActive =\n    (href === docPath && pathname === docPath) ||\n    (href !== docPath && pathname.startsWith(href)) ||\n    isActiveFromProps\n\n  return (\n    <Button\n      aria-label={ariaLabel}\n      buttonStyle=\"tab\"\n      className={[baseClass, isActive && `${baseClass}--active`].filter(Boolean).join(' ')}\n      disabled={isActive}\n      el={!isActive || href !== pathname ? 'link' : 'div'}\n      margin={false}\n      newTab={newTab}\n      size=\"medium\"\n      to={!isActive || href !== pathname ? hrefWithLocale : undefined}\n    >\n      {children}\n    </Button>\n  )\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "useParams", "usePathname", "useSearchParams", "formatAdminURL", "React", "DocumentTabLink", "t0", "$", "adminRoute", "aria<PERSON><PERSON><PERSON>", "baseClass", "children", "href", "hrefFromProps", "isActive", "isActiveFromProps", "newTab", "pathname", "params", "searchParams", "locale", "get", "entityType", "entitySlug", "segmentThree", "segmentFour", "segments", "isCollection", "t1", "t2", "path", "<PERSON><PERSON><PERSON><PERSON>", "hrefWithLocale", "t3", "startsWith", "_jsx", "buttonStyle", "className", "filter", "Boolean", "join", "disabled", "el", "margin", "size", "to", "undefined"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,MAAM,QAAQ;AACvB,SAASC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ;AACxD,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AANlB;;;;;;;AAQO,MAAMC,eAAA,IAQRC,EAAA;IAAA,MAAAC,CAAA,OAAAT,0MAAA,EAAA;IAAC,MAAA,EAAAU,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,QAAA,EAAAC,IAAA,EAAAC,aAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,MAAAA,EAAA,GAAAV,EAQL;IACC,MAAAW,QAAA,OAAiBhB,8KAAA;IACjB,MAAAiB,MAAA,OAAelB,4KAAA;IAEf,MAAAmB,YAAA,OAAqBjB,kLAAA;IAErB,MAAAkB,MAAA,GAAeD,YAAA,CAAAE,GAAA,CAAiB;IAEhC,MAAA,CAAAC,UAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,WAAA,CAAA,GAAqEP,MAAA,CAAAQ,QAAA,IAAA,EAAqB;IAC1F,MAAAC,YAAA,GAAqBL,UAAA,KAAe;IAI5B,MAAAM,EAAA,GAAA,WAAID,YAAA,GAAe,gBAAgB,WAAA,KAAa,CAAY,MAAZJ,UAAA;IAAY,IAAAM,EAAA;IAAA,IAAAtB,CAAA,CAAA,EAAA,KAAAC,UAAA,IAAAD,CAAA,CAAA,EAAA,KAAAqB,EAAA,EAAA;QAFtDC,EAAA,OAAA1B,6MAAA,EAAA;YAAAK,UAAA;YAAAsB,IAAA,EAENF;QAA4D,CACpE;QAAArB,CAAA,CAAA,EAAA,GAAAC,UAAA;QAAAD,CAAA,CAAA,EAAA,GAAAqB,EAAA;QAAArB,CAAA,CAAA,EAAA,GAAAsB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAtB,CAAA,CAAA,EAAA;IAAA;IAHA,IAAAwB,OAAA,GAAcF,EAGd;IAAA,IAEIF,YAAA,EAAA;QAAA,IACEH,YAAA,KAAiB,WAAWC,WAAA,EAAA;YAC9BM,OAAA,GAAAA,OAAA,GAAW,UAAU,CAAa,MAAbN,WAAA;QAAa,OAAA;YAAA,IACzBD,YAAA,EAAA;gBACTO,OAAA,GAAAA,OAAA,GAAW,IAAI,CAAc,MAAdP,YAAA;YAAc;QAAA;IAAA;IAIjC,MAAAZ,IAAA,GAAa,UAAGmB,OAAA,EAAU,CAAe,MAAflB,aAAA;IAE1B,MAAAmB,cAAA,GAAuB,GAAUZ,MAAA,CAAPR,IAAA,EAAsC,CAAI,eAA1B,WAAW,CAAQ,MAARQ,MAAA,IAAW;IAAI,IAAAa,EAAA;IAAA,IAAA1B,CAAA,CAAA,EAAA,KAAAE,SAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAI,QAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAwB,OAAA,IAAAxB,CAAA,CAAA,EAAA,KAAAK,IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAyB,cAAA,IAAAzB,CAAA,CAAA,EAAA,KAAAQ,iBAAA,IAAAR,CAAA,CAAA,GAAA,KAAAS,MAAA,IAAAT,CAAA,CAAA,GAAA,KAAAU,QAAA,EAAA;QAEpE,MAAAH,QAAA,GACEF,IAAC,KAASmB,OAAA,IAAWd,QAAA,KAAac,OAAA,IACjCnB,IAAA,KAASmB,OAAA,IAAWd,QAAA,CAAAiB,UAAA,CAAoBtB,IAAA,KACzCG,iBAAA;QAGAkB,EAAA,OAAAE,uMAAA,EAAApC,+NAAA,EAAA;YAAA,cACcU,SAAA;YAAA2B,WAAA,EACA;YAAAC,SAAA,EACD;gBAAC3B,SAAA;gBAAWI,QAAA,IAAY,GAAG,OAAAJ,EAAmB,OAAnB,EAAA;aAAmB,CAAA4B,MAAA,CAAAC,OAAS,EAAAC,IAAA,CAAc;YAAAC,QAAA,EACtE3B,QAAA;YAAA4B,EAAA,EACN,CAAC5B,QAAA,IAAYF,IAAA,KAASK,QAAA,GAAW,SAAS;YAAA0B,MAAA,EAAA;YAAA3B,MAAA;YAAA4B,IAAA,EAGzC;YAAAC,EAAA,EACD,CAAC/B,QAAA,IAAYF,IAAA,KAASK,QAAA,GAAWe,cAAA,GAAAc,SAAiB;YAAAnC;QAAA,C;;;;;;;;;;;;;;WATxDsB,E;CAcJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/DocumentHeader/Tabs/tabs/VersionsPill/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/DocumentHeader/Tabs/tabs/VersionsPill/index.tsx"], "sourcesContent": ["'use client'\nimport { useDocumentInfo } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'pill-version-count'\n\nexport const VersionsPill: React.FC = () => {\n  const { versionCount } = useDocumentInfo()\n\n  if (!versionCount) {\n    return null\n  }\n\n  return <span className={baseClass}>{versionCount}</span>\n}\n"], "names": ["c", "_c", "useDocumentInfo", "React", "baseClass", "VersionsPill", "$", "versionCount", "t0", "_jsx", "className", "children"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,eAAe,QAAQ;AAChC,OAAOC,KAAA,MAAW;AAFlB;;;;;AAMA,MAAMC,SAAA,GAAY;AAEX,MAAMC,YAAA,GAAyBA,CAAA;IAAA,MAAAC,CAAA,OAAAL,0MAAA,EAAA;IACpC,MAAA,EAAAM,YAAAA,EAAA,GAAyBL,4OAAA;IAAA,IAAA,CAEpBK,YAAA,EAAA;QAAA,OAAA;IAAA;IAAA,IAAAC,EAAA;IAAA,IAAAF,CAAA,CAAA,EAAA,KAAAC,YAAA,EAAA;QAIEC,EAAA,OAAAC,uMAAA,EAAC,QAAA;YAAAC,SAAA,EAAAN,SAAA;YAAAO,QAAA,EAA4BJ;QAAA,C;;;;;;WAA7BC,E;CACT", "ignoreList": [], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Edit/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Edit/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { DocumentViewClientProps } from 'payload'\n\nimport { DefaultEditView } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const EditView: React.FC<DocumentViewClientProps> = (props) => {\n  return <DefaultEditView {...props} />\n}\n"], "names": ["DefaultEditView", "React", "EditView", "props", "_jsx"], "mappings": ";;;;;AAIA,SAASA,eAAe,QAAQ;AAChC,OAAOC,KAAA,MAAW;AALlB;;;;AAOO,MAAMC,QAAA,IAA+CC,KAAA;IAC1D,OAAA,WAAA,OAAOC,uMAAA,EAACJ,wOAAA,EAAA;QAAiB,GAAGG,KAAAA;;AAC9B", "ignoreList": [], "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Account/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Account/index.client.tsx"], "sourcesContent": ["'use client'\nimport { type StepNavItem, useStepNav, useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const AccountClient: React.FC = () => {\n  const { setStepNav } = useStepNav()\n  const { t } = useTranslation()\n\n  React.useEffect(() => {\n    const nav: StepNavItem[] = []\n\n    nav.push({\n      label: t('authentication:account'),\n      url: '/account',\n    })\n\n    setStepNav(nav)\n  }, [setStepNav, t])\n\n  return null\n}\n"], "names": ["c", "_c", "useStepNav", "useTranslation", "React", "AccountClient", "$", "setStepNav", "t", "t0", "t1", "nav", "push", "label", "url", "useEffect"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;AACA,SAA2BC,UAAU,EAAEC,cAAc,QAAQ;;AAC7D,OAAOC,KAAA,MAAW;AAFlB;;;;AAIO,MAAMC,aAAA,GAA0BA,CAAA;IAAA,MAAAC,CAAA,OAAAL,0MAAA,EAAA;IACrC,MAAA,EAAAM,UAAAA,EAAA,OAAuBL,mOAAA;IACvB,MAAA,EAAAM,CAAAA,EAAA,OAAcL,0QAAA;IAAA,IAAAM,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAC,UAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,CAAA,EAAA;QAEEC,EAAA,GAAAA,CAAA;YACd,MAAAE,GAAA,GAAA,EAAA;YAEAA,GAAA,CAAAC,IAAA,CAAA;gBAAAC,KAAA,EACSL,CAAA,CAAE;gBAAAM,GAAA,EACJ;YAAA,CACP;YAEAP,UAAA,CAAWI,GAAA;QAAA;QACVD,EAAA,GAAA;YAACH,UAAA;YAAYC,CAAA;SAAA;QAAEF,CAAA,CAAA,EAAA,GAAAC,UAAA;QAAAD,CAAA,CAAA,EAAA,GAAAE,CAAA;QAAAF,CAAA,CAAA,EAAA,GAAAG,EAAA;QAAAH,CAAA,CAAA,EAAA,GAAAI,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAH,CAAA,CAAA,EAAA;QAAAI,EAAA,GAAAJ,CAAA,CAAA,EAAA;IAAA;IATlBF,kMAAA,CAAAW,SAAA,CAAgBN,EAShB,EAAGC,EAAe;IAAA,OAAA;AAAA,CAGpB", "ignoreList": [], "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Account/ResetPreferences/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Account/ResetPreferences/index.tsx"], "sourcesContent": ["'use client'\nimport type { TypedUser } from 'payload'\n\nimport { Button, ConfirmationModal, toast, useModal, useTranslation } from '@payloadcms/ui'\nimport * as qs from 'qs-esm'\nimport { Fragment, useCallback } from 'react'\n\nconst confirmResetModalSlug = 'confirm-reset-modal'\n\nexport const ResetPreferences: React.FC<{\n  readonly apiRoute: string\n  readonly user?: TypedUser\n}> = ({ apiRoute, user }) => {\n  const { openModal } = useModal()\n  const { t } = useTranslation()\n\n  const handleResetPreferences = useCallback(async () => {\n    if (!user) {\n      return\n    }\n\n    const stringifiedQuery = qs.stringify(\n      {\n        depth: 0,\n        where: {\n          user: {\n            id: {\n              equals: user.id,\n            },\n          },\n        },\n      },\n      { addQueryPrefix: true },\n    )\n\n    try {\n      const res = await fetch(`${apiRoute}/payload-preferences${stringifiedQuery}`, {\n        credentials: 'include',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        method: 'DELETE',\n      })\n\n      const json = await res.json()\n      const message = json.message\n\n      if (res.ok) {\n        toast.success(message)\n      } else {\n        toast.error(message)\n      }\n    } catch (_err) {\n      // swallow error\n    }\n  }, [apiRoute, user])\n\n  return (\n    <Fragment>\n      <div>\n        <Button buttonStyle=\"secondary\" onClick={() => openModal(confirmResetModalSlug)}>\n          {t('general:resetPreferences')}\n        </Button>\n      </div>\n      <ConfirmationModal\n        body={t('general:resetPreferencesDescription')}\n        confirmingLabel={t('general:resettingPreferences')}\n        heading={t('general:resetPreferences')}\n        modalSlug={confirmResetModalSlug}\n        onConfirm={handleResetPreferences}\n      />\n    </Fragment>\n  )\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "ConfirmationModal", "toast", "useModal", "useTranslation", "qs", "Fragment", "useCallback", "confirmResetModalSlug", "ResetPreferences", "t0", "$", "apiRoute", "user", "openModal", "t", "t1", "stringified<PERSON><PERSON>y", "stringify", "depth", "where", "id", "equals", "addQueryPrefix", "res", "fetch", "credentials", "headers", "method", "json", "message", "ok", "success", "error", "t2", "_err", "handleResetPreferences", "t3", "_jsxs", "children", "_jsx", "buttonStyle", "onClick", "body", "<PERSON><PERSON><PERSON><PERSON>", "heading", "modalSlug", "onConfirm"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,cAAc,QAAQ;;AAC3E,YAAYC,EAAA,MAAQ;AACpB,SAASC,QAAQ,EAAEC,WAAW,QAAQ;AALtC;;;;;;AAOA,MAAMC,qBAAA,GAAwB;AAEvB,MAAMC,gBAAA,IAGRC,EAAA;IAAA,MAAAC,CAAA,OAAAZ,0MAAA,EAAA;IAAC,MAAA,EAAAa,QAAA,EAAAC,IAAAA,EAAA,GAAAH,EAAkB;IACtB,MAAA,EAAAI,SAAAA,EAAA,OAAsBX,iOAAA;IACtB,MAAA,EAAAY,CAAAA,EAAA,OAAcX,0QAAA;IAAA,IAAAY,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAC,QAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,IAAA,EAAA;QAE6BG,EAAA,GAAA,MAAAA,CAAA;YAAA,IAAA,CACpCH,IAAA,EAAA;gBAAA;YAAA;YAIL,MAAAI,gBAAA,GAAyBZ,EAAA,CAAAa,oLAAA,CAAA;gBAAAC,KAAA,EAAA;gBAAAC,KAAA,EAAA;oBAAAP,IAAA,EAAA;wBAAAQ,EAAA,EAAA;4BAAAC,MAAA,EAMPT,IAAA,CAAAQ,EAAAA;wBAAA;oBAAA;gBAAA;YAAA,GAAA;gBAAAE,cAAA,EAAA;YAAA,CAKO;;YAAA,IAAA;gBAIvB,MAAAC,GAAA,GAAA,MAAkBC,KAAA,CAAM,UAAGb,QAAA,EAAA,wBAA+B,CAAkB,MAAlBK,gBAAA,GAAkB;oBAAAS,WAAA,EAC7D;oBAAAC,OAAA,EAAA;wBAAA,gBAEK;oBAAA;oBAAAC,MAAA,EAEV;gBAAA,CACV;gBAEA,MAAAC,IAAA,GAAA,MAAmBL,GAAA,CAAAK,IAAA,CAAQ;gBAC3B,MAAAC,OAAA,GAAgBD,IAAA,CAAAC,OAAA;gBAAY,IAExBN,GAAA,CAAAO,EAAA,EAAA;oBACF7B,8NAAA,CAAA8B,OAAA,CAAcF,OAAA;gBAAA,OAAA;oBAEd5B,8NAAA,CAAA+B,KAAA,CAAYH,OAAA;gBAAA;YAAA,EAAA,OAAAI,EAAA,EAAA;gBAEPC,KAAA,CAAAA,IAAA,CAAAA,CAAA,CAAAA,EAAA;YAAA;QAAA;QAGXxB,CAAA,CAAA,EAAA,GAAAC,QAAA;QAAAD,CAAA,CAAA,EAAA,GAAAE,IAAA;QAAAF,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAvCA,MAAAyB,sBAAA,GAA+BpB,EAuCZ;IAAA,IAAAkB,EAAA;IAAA,IAAAvB,CAAA,CAAA,EAAA,KAAAG,SAAA,EAAA;QAK4BoB,EAAA,GAAAA,CAAA,GAAMpB,SAAA,CAAAN,qBAAU;QAAAG,CAAA,CAAA,EAAA,GAAAG,SAAA;QAAAH,CAAA,CAAA,EAAA,GAAAuB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAvB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAA0B,EAAA;IAAA,IAAA1B,CAAA,CAAA,EAAA,KAAAyB,sBAAA,IAAAzB,CAAA,CAAA,EAAA,KAAAI,CAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAuB,EAAA,EAAA;QAF7DG,EAAA,OAAAC,wMAAA,EAAAhC,mMAAA,EAAA;YAAAiC,QAAA,EAAA;oBACEC,uMAAA,EAAC,OAAA;oBAAAD,QAAA,MACCC,uMAAA,EAAAxC,+NAAA,EAAA;wBAAAyC,WAAA,EAAoB;wBAAAC,OAAA,EAAqBR,EAAgB;wBAAAK,QAAA,EACtDxB,CAAA,CAAE;oBAAA,C;;oBAGPyB,uMAAA,EAAAvC,0OAAA,EAAA;oBAAA0C,IAAA,EACQ5B,CAAA,CAAE;oBAAA6B,eAAA,EACS7B,CAAA,CAAE;oBAAA8B,OAAA,EACV9B,CAAA,CAAE;oBAAA+B,SAAA,EAAAtC,qBAAA;oBAAAuC,SAAA,EAEAX;gBAAA,C;a;;;;;;;;;WAXfC,E;CAeJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Account/ToggleTheme/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Account/ToggleTheme/index.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>GroupField, useTheme, useTranslation } from '@payloadcms/ui'\nimport React, { useCallback } from 'react'\n\nexport const ToggleTheme: React.FC = () => {\n  const { autoMode, setTheme, theme } = useTheme()\n  const { t } = useTranslation()\n\n  const onChange = useCallback(\n    (newTheme) => {\n      setTheme(newTheme)\n    },\n    [setTheme],\n  )\n\n  return (\n    <RadioGroupField\n      disableModifyingForm={true}\n      field={{\n        name: 'theme',\n        label: t('general:adminTheme'),\n        options: [\n          {\n            label: t('general:automatic'),\n            value: 'auto',\n          },\n          {\n            label: t('general:light'),\n            value: 'light',\n          },\n          {\n            label: t('general:dark'),\n            value: 'dark',\n          },\n        ],\n      }}\n      onChange={onChange}\n      path=\"theme\"\n      value={autoMode ? 'auto' : theme}\n    />\n  )\n}\n"], "names": ["c", "_c", "RadioGroupField", "useTheme", "useTranslation", "React", "useCallback", "ToggleTheme", "$", "autoMode", "setTheme", "theme", "t", "t0", "newTheme", "onChange", "t1", "_jsx", "disableModifyingForm", "field", "name", "label", "options", "value", "path"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAEA,SAASC,eAAe,EAAEC,QAAQ,EAAEC,cAAc,QAAQ;;;AAC1D,OAAOC,KAAA,IAASC,WAAW,QAAQ;AAHnC;;;;;AAKO,MAAMC,WAAA,GAAwBA,CAAA;IAAA,MAAAC,CAAA,OAAAP,0MAAA,EAAA;IACnC,MAAA,EAAAQ,QAAA,EAAAC,QAAA,EAAAC,KAAAA,EAAA,GAAsCR,kQAAA;IACtC,MAAA,EAAAS,CAAAA,EAAA,OAAcR,0QAAA;IAAA,IAAAS,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAE,QAAA,EAAA;QAGZG,EAAA,IAAAC,QAAA;YACEJ,QAAA,CAASI,QAAA;QAAA;QACXN,CAAA,CAAA,EAAA,GAAAE,QAAA;QAAAF,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAHF,MAAAO,QAAA,GAAiBF,EAIL;IAAA,IAAAG,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAC,QAAA,IAAAD,CAAA,CAAA,EAAA,KAAAO,QAAA,IAAAP,CAAA,CAAA,EAAA,KAAAI,CAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAG,KAAA,EAAA;QAIVK,EAAA,GAAAC,2MAAA,EAAAf,wOAAA,EAAA;YAAAgB,oBAAA,EAAA;YAAAC,KAAA,EAAA;gBAAAC,IAAA,EAGU;gBAAAC,KAAA,EACCT,CAAA,CAAE;gBAAAU,OAAA,EAAA;oBAAA;wBAAAD,KAAA,EAGET,CAAA,CAAE;wBAAAW,KAAA,EACF;oBAAA;oBAAA;wBAAAF,KAAA,EAGAT,CAAA,CAAE;wBAAAW,KAAA,EACF;oBAAA;oBAAA;wBAAAF,KAAA,EAGAT,CAAA,CAAE;wBAAAW,KAAA,EACF;oBAAA;iBAAA;YAAA;YAAAR,QAAA;YAAAS,IAAA,EAKR;YAAAD,KAAA,EACEd,QAAA,GAAW,SAASE;QAAA,C;;;;;;;;;WAtB7BK,E;CAyBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Account/Settings/LanguageSelector.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Account/Settings/LanguageSelector.tsx"], "sourcesContent": ["'use client'\nimport type { AcceptedLanguages } from '@payloadcms/translations'\nimport type { ReactSelectOption } from '@payloadcms/ui'\nimport type { LanguageOptions } from 'payload'\n\nimport { ReactSelect, useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const LanguageSelector: React.FC<{\n  languageOptions: LanguageOptions\n}> = (props) => {\n  const { languageOptions } = props\n\n  const { i18n, switchLanguage } = useTranslation()\n\n  return (\n    <ReactSelect\n      inputId=\"language-select\"\n      isClearable={false}\n      onChange={async (option: ReactSelectOption<AcceptedLanguages>) => {\n        await switchLanguage(option.value)\n      }}\n      options={languageOptions}\n      value={languageOptions.find((language) => language.value === i18n.language)}\n    />\n  )\n}\n"], "names": ["c", "_c", "ReactSelect", "useTranslation", "React", "LanguageSelector", "props", "$", "languageOptions", "i18n", "switchLanguage", "t0", "option", "value", "t1", "t2", "language", "_jsx", "inputId", "isClearable", "onChange", "options", "find"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAKA,SAASC,WAAW,EAAEC,cAAc,QAAQ;;AAC5C,OAAOC,KAAA,MAAW;AANlB;;;;;AAQO,MAAMC,gBAAA,IAERC,KAAA;IAAA,MAAAC,CAAA,OAAAN,0MAAA,EAAA;IACH,MAAA,EAAAO,eAAAA,EAAA,GAA4BF,KAAA;IAE5B,MAAA,EAAAG,IAAA,EAAAC,cAAAA,EAAA,OAAiCP,0QAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAG,cAAA,EAAA;QAMnBC,EAAA,GAAA,OAAAC,MAAA;YAAA,MACFF,cAAA,CAAeE,MAAA,CAAAC,KAAY;QAAA;QACnCN,CAAA,CAAA,EAAA,GAAAG,cAAA;QAAAH,CAAA,CAAA,EAAA,GAAAI,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAJ,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAE,IAAA,IAAAF,CAAA,CAAA,EAAA,KAAAC,eAAA,IAAAD,CAAA,CAAA,EAAA,KAAAI,EAAA,EAAA;QAAA,IAAAI,EAAA;QAAA,IAAAR,CAAA,CAAA,EAAA,KAAAE,IAAA,EAAA;YAE4BM,EAAA,GAAAC,QAAA,IAAcA,QAAA,CAAAH,KAAA,KAAmBJ,IAAA,CAAAO,QAAa;YAAAT,CAAA,CAAA,EAAA,GAAAE,IAAA;YAAAF,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;QAAA;QAP5EO,EAAA,OAAAG,uMAAA,EAAAf,oOAAA,EAAA;YAAAgB,OAAA,EACU;YAAAC,WAAA,EAAA;YAAAC,QAAA,EAEET,EAEV;YAAAU,OAAA,EACSb,eAAA;YAAAK,KAAA,EACFL,eAAA,CAAAc,IAAA,CAAqBP,EAA8C;QAAA,C;;;;;;;;WAP5ED,E;CAUJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/CreateFirstUser/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/CreateFirstUser/index.client.tsx"], "sourcesContent": ["'use client'\nimport type { Form<PERSON><PERSON>, UserWithToken } from '@payloadcms/ui'\nimport type {\n  DocumentPreferences,\n  FormState,\n  LoginWithUsernameOptions,\n  SanitizedDocumentPermissions,\n} from 'payload'\n\nimport {\n  ConfirmPasswordField,\n  EmailAndUsernameFields,\n  Form,\n  FormSubmit,\n  PasswordField,\n  RenderFields,\n  useAuth,\n  useConfig,\n  useServerFunctions,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { abortAndIgnore, handleAbortRef } from '@payloadcms/ui/shared'\nimport React, { useEffect } from 'react'\n\nexport const CreateFirstUserClient: React.FC<{\n  docPermissions: SanitizedDocumentPermissions\n  docPreferences: DocumentPreferences\n  initialState: FormState\n  loginWithUsername?: false | LoginWithUsernameOptions\n  userSlug: string\n}> = ({ docPermissions, docPreferences, initialState, loginWithUsername, userSlug }) => {\n  const {\n    config: {\n      routes: { admin, api: apiRoute },\n      serverURL,\n    },\n    getEntityConfig,\n  } = useConfig()\n\n  const { getFormState } = useServerFunctions()\n\n  const { t } = useTranslation()\n  const { setUser } = useAuth()\n\n  const abortOnChangeRef = React.useRef<AbortController>(null)\n\n  const collectionConfig = getEntityConfig({ collectionSlug: userSlug })\n\n  const onChange: FormProps['onChange'][0] = React.useCallback(\n    async ({ formState: prevFormState, submitted }) => {\n      const controller = handleAbortRef(abortOnChangeRef)\n\n      const response = await getFormState({\n        collectionSlug: userSlug,\n        docPermissions,\n        docPreferences,\n        formState: prevFormState,\n        operation: 'create',\n        schemaPath: userSlug,\n        signal: controller.signal,\n        skipValidation: !submitted,\n      })\n\n      abortOnChangeRef.current = null\n\n      if (response && response.state) {\n        return response.state\n      }\n    },\n    [userSlug, getFormState, docPermissions, docPreferences],\n  )\n\n  const handleFirstRegister = (data: UserWithToken) => {\n    setUser(data)\n  }\n\n  useEffect(() => {\n    const abortOnChange = abortOnChangeRef.current\n\n    return () => {\n      abortAndIgnore(abortOnChange)\n    }\n  }, [])\n\n  return (\n    <Form\n      action={`${serverURL}${apiRoute}/${userSlug}/first-register`}\n      initialState={{\n        ...initialState,\n        'confirm-password': {\n          ...initialState['confirm-password'],\n          valid: initialState['confirm-password']['valid'] || false,\n          value: initialState['confirm-password']['value'] || '',\n        },\n      }}\n      method=\"POST\"\n      onChange={[onChange]}\n      onSuccess={handleFirstRegister}\n      redirect={admin}\n      validationOperation=\"create\"\n    >\n      <EmailAndUsernameFields\n        className=\"emailAndUsername\"\n        loginWithUsername={loginWithUsername}\n        operation=\"create\"\n        readOnly={false}\n        t={t}\n      />\n      <PasswordField\n        autoComplete=\"off\"\n        field={{\n          name: 'password',\n          label: t('authentication:newPassword'),\n          required: true,\n        }}\n        path=\"password\"\n      />\n      <ConfirmPasswordField />\n      <RenderFields\n        fields={collectionConfig.fields}\n        forceRender\n        parentIndexPath=\"\"\n        parentPath=\"\"\n        parentSchemaPath={userSlug}\n        permissions={true}\n        readOnly={false}\n      />\n      <FormSubmit size=\"large\">{t('general:create')}</FormSubmit>\n    </Form>\n  )\n}\n"], "names": ["ConfirmPasswordField", "EmailAndUsernameFields", "Form", "FormSubmit", "PasswordField", "RenderFields", "useAuth", "useConfig", "useServerFunctions", "useTranslation", "abortAndIgnore", "handleAbortRef", "React", "useEffect", "CreateFirstUserClient", "docPermissions", "docPreferences", "initialState", "loginWithUsername", "userSlug", "config", "routes", "admin", "api", "apiRoute", "serverURL", "getEntityConfig", "getFormState", "t", "setUser", "abortOnChangeRef", "useRef", "collectionConfig", "collectionSlug", "onChange", "useCallback", "formState", "prevFormState", "submitted", "controller", "response", "operation", "schemaPath", "signal", "skipValidation", "current", "state", "handleFirstRegister", "data", "abortOnChange", "_jsxs", "action", "valid", "value", "method", "onSuccess", "redirect", "validationOperation", "_jsx", "className", "readOnly", "autoComplete", "field", "name", "label", "required", "path", "fields", "forceRender", "parentIndexPath", "parentPath", "parentSchemaPath", "permissions", "size"], "mappings": ";;;;;AASA,SACEA,oBAAoB,EACpBC,sBAAsB,EACtBC,IAAI,EACJC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,OAAO,EACPC,SAAS,EACTC,kBAAkB,EAClBC,cAAc,QACT;;;AACP,SAASC,cAAc,EAAEC,cAAc,QAAQ;AAC/C,OAAOC,KAAA,IAASC,SAAS,QAAQ;AAtBjC;;;;;AAwBO,MAAMC,qBAAA,GAMRA;QAAC,EAAEC,cAAc,EAAEC,cAAc,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,QAAAA,EAAU;IACjF,MAAM,EACJC,MAAA,EAAQ,EACNC,MAAA,EAAQ,EAAEC,KAAK,EAAEC,GAAA,EAAKC,QAAAA,EAAU,EAChCC,SAAAA,EACD,EACDC,eAAAA,EACD,OAAGnB,gQAAA;IAEJ,MAAM,EAAEoB,YAAAA,EAAc,OAAGnB,2OAAA;IAEzB,MAAM,EAAEoB,CAAAA,EAAG,GAAGnB,8QAAA;IACd,MAAM,EAAEoB,OAAAA,EAAS,OAAGvB,gOAAA;IAEpB,MAAMwB,gBAAA,GAAmBlB,kMAAA,CAAMmB,MAAM,CAAkB;IAEvD,MAAMC,gBAAA,GAAmBN,eAAA,CAAgB;QAAEO,cAAA,EAAgBd;IAAS;IAEpE,MAAMe,QAAA,GAAqCtB,kMAAA,CAAMuB,WAAW;uDAC1D;gBAAO,EAAEC,SAAA,EAAWC,aAAa,EAAEC,SAAAA,EAAW;YAC5C,MAAMC,UAAA,OAAa5B,uOAAA,EAAemB,gBAAA;YAElC,MAAMU,QAAA,GAAW,MAAMb,YAAA,CAAa;gBAClCM,cAAA,EAAgBd,QAAA;gBAChBJ,cAAA;gBACAC,cAAA;gBACAoB,SAAA,EAAWC,aAAA;gBACXI,SAAA,EAAW;gBACXC,UAAA,EAAYvB,QAAA;gBACZwB,MAAA,EAAQJ,UAAA,CAAWI,MAAM;gBACzBC,cAAA,EAAgB,CAACN;YACnB;YAEAR,gBAAA,CAAiBe,OAAO,GAAG;YAE3B,IAAIL,QAAA,IAAYA,QAAA,CAASM,KAAK,EAAE;gBAC9B,OAAON,QAAA,CAASM,KAAK;YACvB;QACF;sDACA;QAAC3B,QAAA;QAAUQ,YAAA;QAAcZ,cAAA;QAAgBC,cAAA;KAAe;IAG1D,MAAM+B,mBAAA,GAAuBC,IAAA;QAC3BnB,OAAA,CAAQmB,IAAA;IACV;QAEAnC,oMAAA;2CAAU;YACR,MAAMoC,aAAA,GAAgBnB,gBAAA,CAAiBe,OAAO;YAE9C;mDAAO;oBACLnC,2OAAA,EAAeuC,aAAA;gBACjB;;QACF;0CAAG,EAAE;IAEL,OAAA,WAAA,OACEC,wMAAA,EAAChD,6NAAA,EAAA;QACCiD,MAAA,EAAQ,UAAG1B,SAAA,EAAwBN,OAAZK,CAAY,OAAZ,EAAA,KAAY,iBAAA,gBAAyB;QAC5DP,YAAA,EAAc;YACZ,GAAGA,YAAY;YACf,oBAAoB;gBAClB,GAAGA,YAAY,CAAC,mBAAmB;gBACnCmC,KAAA,EAAOnC,YAAY,CAAC,mBAAmB,CAAC,QAAQ,IAAI;gBACpDoC,KAAA,EAAOpC,YAAY,CAAC,mBAAmB,CAAC,QAAQ,IAAI;YACtD;QACF;QACAqC,MAAA,EAAO;QACPpB,QAAA,EAAU;YAACA,QAAA;SAAS;QACpBqB,SAAA,EAAWR,mBAAA;QACXS,QAAA,EAAUlC,KAAA;QACVmC,mBAAA,EAAoB;;8BAEpBC,uMAAA,EAACzD,+OAAA,EAAA;gBACC0D,SAAA,EAAU;gBACVzC,iBAAA,EAAmBA,iBAAA;gBACnBuB,SAAA,EAAU;gBACVmB,QAAA,EAAU;gBACVhC,CAAA,EAAGA;;8BAEL8B,uMAAA,EAACtD,sOAAA,EAAA;gBACCyD,YAAA,EAAa;gBACbC,KAAA,EAAO;oBACLC,IAAA,EAAM;oBACNC,KAAA,EAAOpC,CAAA,CAAE;oBACTqC,QAAA,EAAU;gBACZ;gBACAC,IAAA,EAAK;;8BAEPR,uMAAA,EAAC1D,6OAAA,EAAA,CAAA;Y,W,OACD0D,uMAAA,EAACrD,qOAAA,EAAA;gBACC8D,MAAA,EAAQnC,gBAAA,CAAiBmC,MAAM;gBAC/BC,WAAW,EAAA;gBACXC,eAAA,EAAgB;gBAChBC,UAAA,EAAW;gBACXC,gBAAA,EAAkBpD,QAAA;gBAClBqD,WAAA,EAAa;gBACbZ,QAAA,EAAU;;8BAEZF,uMAAA,EAACvD,mOAAA,EAAA;gBAAWsE,IAAA,EAAK;0BAAS7C,CAAA,CAAE;;;;AAGlC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/NotFound/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/NotFound/index.client.tsx"], "sourcesContent": ["'use client'\nimport { <PERSON><PERSON>, <PERSON><PERSON>, useConfig, useStepNav, useTranslation } from '@payloadcms/ui'\nimport React, { useEffect } from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'not-found'\n\nexport const NotFoundClient: React.FC<{\n  marginTop?: 'large'\n}> = (props) => {\n  const { marginTop = 'large' } = props\n\n  const { setStepNav } = useStepNav()\n  const { t } = useTranslation()\n\n  const {\n    config: {\n      routes: { admin: adminRoute },\n    },\n  } = useConfig()\n\n  useEffect(() => {\n    setStepNav([\n      {\n        label: t('general:notFound'),\n      },\n    ])\n  }, [setStepNav, t])\n\n  return (\n    <div\n      className={[baseClass, marginTop && `${baseClass}--margin-top-${marginTop}`]\n        .filter(Boolean)\n        .join(' ')}\n    >\n      <Gutter className={`${baseClass}__wrap`}>\n        <div className={`${baseClass}__content`}>\n          <h1>{t('general:nothingFound')}</h1>\n          <p>{t('general:sorryNotFound')}</p>\n        </div>\n        <Button className={`${baseClass}__button`} el=\"link\" size=\"large\" to={adminRoute}>\n          {t('general:backToDashboard')}\n        </Button>\n      </Gutter>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "<PERSON><PERSON>", "useConfig", "useStepNav", "useTranslation", "React", "useEffect", "baseClass", "NotFoundClient", "props", "$", "marginTop", "t0", "undefined", "setStepNav", "t", "config", "t1", "routes", "t2", "admin", "adminRoute", "t3", "t4", "label", "t5", "t6", "filter", "Boolean", "t7", "join", "t8", "_jsx", "className", "children", "_jsxs", "el", "size", "to"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,QAAQ;;;AACtE,OAAOC,KAAA,IAASC,SAAS,QAAQ;AAFjC;;;;;AAMA,MAAMC,SAAA,GAAY;AAEX,MAAMC,cAAA,IAERC,KAAA;IAAA,MAAAC,CAAA,OAAAX,0MAAA,EAAA;IACH,MAAA,EAAAY,SAAA,EAAAC,EAAAA,EAAA,GAAgCH,KAAA;IAAxB,MAAAE,SAAA,GAAAC,EAAmB,KAAAC,SAAA,GAAP,OAAO,GAAnBD,EAAmB;IAE3B,MAAA,EAAAE,UAAAA,EAAA,OAAuBX,mOAAA;IACvB,MAAA,EAAAY,CAAAA,EAAA,OAAcX,0QAAA;IAEd,MAAA,EAAAY,MAAA,EAAAC,EAAAA,EAAA,OAIIf,gQAAA;IAHM,MAAA,EAAAgB,MAAA,EAAAC,EAAAA,EAAA,GAAAF,EAEP;IADS,MAAA,EAAAG,KAAA,EAAAC,UAAAA,EAAA,GAAAF,EAAqB;IAAA,IAAAG,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAI,UAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,CAAA,EAAA;QAIvBO,EAAA,GAAAA,CAAA;YACRR,UAAA,CAAA;gBAAA;oBAAAU,KAAA,EAEWT,CAAA,CAAE;gBAAA;aAEZ;QAAA;QACAQ,EAAA,GAAA;YAACT,UAAA;YAAYC,CAAA;SAAA;QAAEL,CAAA,CAAA,EAAA,GAAAI,UAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,CAAA;QAAAL,CAAA,CAAA,EAAA,GAAAY,EAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAa,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAZ,CAAA,CAAA,EAAA;QAAAa,EAAA,GAAAb,CAAA,CAAA,EAAA;IAAA;QANlBJ,oMAAA,EAAUgB,EAMV,EAAGC,EAAe;IAIS,MAAAE,EAAA,GAAAd,SAAA,IAAa,UAAAJ,SAAA,EAAA,iBAA4B,CAAW,MAAXI,SAAA;IAAW,IAAAe,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAe,EAAA,EAAA;QAAhEC,EAAA,GAAA;YAAAnB,SAAA;YAAYkB,EAAoD;SAAA,CAAAE,MAAA,CAAAC,OACjE;QAAAlB,CAAA,CAAA,EAAA,GAAAe,EAAA;QAAAf,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IADC,MAAAmB,EAAA,GAAAH,EACD,CAAAI,IAAA,CACF;IAAA,IAAAC,EAAA;IAAA,IAAArB,CAAA,CAAA,EAAA,KAAAW,UAAA,IAAAX,CAAA,CAAA,EAAA,KAAAK,CAAA,IAAAL,CAAA,CAAA,EAAA,KAAAmB,EAAA,EAAA;QAHVE,EAAA,OAAAC,uMAAA,EAAC,OAAA;YAAAC,SAAA,EACYJ,EAEH;YAAAK,QAAA,MAERC,wMAAA,EAAAlC,+NAAA,EAAA;gBAAAgC,SAAA,EAAmB,GAAA,OAAA1B,SAAA,EAAA,OAAoB;gBAAA2B,QAAA,EAAA;wBACrCC,wMAAA,EAAC,OAAA;wBAAAF,SAAA,EAAe,GAAA,OAAA1B,GAAuB,MAAvB,EAAA;wBAAuB2B,QAAA,EAAA;gCACrCF,uMAAA,EAAC,MAAA;gCAAAE,QAAA,EAAInB,CAAA,CAAE;4BAAA,C;gCACPiB,uMAAA,EAAC,KAAA;gCAAAE,QAAA,EAAGnB,CAAA,CAAE;4BAAA,C;yB;;wBAERiB,uMAAA,EAAAhC,+NAAA,EAAA;wBAAAiC,SAAA,EAAmB,GAAA,OAAA1B,EAAsB,OAAtB,EAAA;wBAAsB6B,EAAA,EAAK;wBAAAC,IAAA,EAAY;wBAAAC,EAAA,EAAYjB,UAAA;wBAAAa,QAAA,EACnEnB,CAAA,CAAE;oBAAA,C;iB;;;;;;;;;;WAXTgB,E;CAgBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/API/LocaleSelector/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/API/LocaleSelector/index.tsx"], "sourcesContent": ["'use client'\nimport { SelectField, useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nexport const LocaleSelector: React.FC<{\n  readonly localeOptions: {\n    label: Record<string, string> | string\n    value: string\n  }[]\n  readonly onChange: (value: string) => void\n}> = ({ localeOptions, onChange }) => {\n  const { t } = useTranslation()\n\n  return (\n    <SelectField\n      field={{\n        name: 'locale',\n        label: t('general:locale'),\n        options: localeOptions,\n      }}\n      onChange={(value: string) => onChange(value)}\n      path=\"locale\"\n    />\n  )\n}\n"], "names": ["c", "_c", "SelectField", "useTranslation", "React", "LocaleSelector", "t0", "$", "localeOptions", "onChange", "t", "t1", "t2", "value", "_jsx", "field", "name", "label", "options", "path"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,WAAW,EAAEC,cAAc,QAAQ;;AAC5C,OAAOC,KAAA,MAAW;AAFlB;;;;;AAIO,MAAMC,cAAA,IAMRC,EAAA;IAAA,MAAAC,CAAA,OAAAN,0MAAA,EAAA;IAAC,MAAA,EAAAO,aAAA,EAAAC,QAAAA,EAAA,GAAAH,EAA2B;IAC/B,MAAA,EAAAI,CAAAA,EAAA,OAAcP,0QAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAC,aAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,CAAA,EAAA;QAAA,IAAAE,EAAA;QAAA,IAAAL,CAAA,CAAA,EAAA,KAAAE,QAAA,EAAA;YASAG,EAAA,IAAAC,KAAA,GAAmBJ,QAAA,CAASI,KAAA;YAAAN,CAAA,CAAA,EAAA,GAAAE,QAAA;YAAAF,CAAA,CAAA,EAAA,GAAAK,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;QAAA;QANxCI,EAAA,OAAAG,uMAAA,EAAAZ,oOAAA,EAAA;YAAAa,KAAA,EAAA;gBAAAC,IAAA,EAEU;gBAAAC,KAAA,EACCP,CAAA,CAAE;gBAAAQ,OAAA,EACAV;YAAA;YAAAC,QAAA,EAEDG,EAA4B;YAAAO,IAAA,EACjC;QAAA,C;;;;;;;;WAPPR,E;CAUJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/API/RenderJSON/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/API/RenderJSON/index.tsx"], "sourcesContent": ["'use client'\nimport { ChevronIcon } from '@payloadcms/ui'\nimport * as React from 'react'\n\nimport './index.scss'\n\nconst chars = {\n  leftCurlyBracket: '\\u007B',\n  leftSquareBracket: '\\u005B',\n  rightCurlyBracket: '\\u007D',\n  rightSquareBracket: '\\u005D',\n}\n\nconst baseClass = 'query-inspector'\n\nconst Bracket = ({\n  type,\n  comma = false,\n  position,\n}: {\n  comma?: boolean\n  position: 'end' | 'start'\n  type: 'array' | 'object'\n}) => {\n  const rightBracket = type === 'object' ? chars.rightCurlyBracket : chars.rightSquareBracket\n  const leftBracket = type === 'object' ? chars.leftCurlyBracket : chars.leftSquareBracket\n  const bracketToRender = position === 'end' ? rightBracket : leftBracket\n\n  return (\n    <span className={`${baseClass}__bracket ${baseClass}__bracket--position-${position}`}>\n      {bracketToRender}\n      {position === 'end' && comma ? ',' : null}\n    </span>\n  )\n}\n\ntype Args = {\n  isEmpty?: boolean\n  object: any[] | Record<string, any>\n  objectKey?: string\n  parentType?: 'array' | 'object'\n  trailingComma?: boolean\n}\n\nexport const RenderJSON = ({\n  isEmpty = false,\n  object,\n  objectKey,\n  parentType = 'object',\n  trailingComma = false,\n}: Args) => {\n  const objectKeys = object ? Object.keys(object) : []\n  const objectLength = objectKeys.length\n  const [isOpen, setIsOpen] = React.useState<boolean>(true)\n  const isNested = parentType === 'object' || parentType === 'array'\n  return (\n    <li className={isNested ? `${baseClass}__row-line--nested` : ''}>\n      <button\n        aria-label=\"toggle\"\n        className={`${baseClass}__list-toggle ${isEmpty ? `${baseClass}__list-toggle--empty` : ''}`}\n        onClick={() => setIsOpen(!isOpen)}\n        type=\"button\"\n      >\n        {isEmpty ? null : (\n          <ChevronIcon\n            className={`${baseClass}__toggle-row-icon ${baseClass}__toggle-row-icon--${\n              isOpen ? 'open' : 'closed'\n            }`}\n          />\n        )}\n        <span>\n          {objectKey && `\"${objectKey}\": `}\n          <Bracket position=\"start\" type={parentType} />\n          {isEmpty ? <Bracket comma={trailingComma} position=\"end\" type={parentType} /> : null}\n        </span>\n      </button>\n\n      <ul\n        className={`${baseClass}__json-children ${isNested ? `${baseClass}__json-children--nested` : ''}`}\n      >\n        {isOpen &&\n          objectKeys.map((key, keyIndex) => {\n            let value = object[key]\n            let type = 'string'\n            const isLastKey = keyIndex === objectLength - 1\n\n            if (value === null) {\n              type = 'null'\n            } else if (value instanceof Date) {\n              type = 'date'\n              value = value.toISOString()\n            } else if (Array.isArray(value)) {\n              type = 'array'\n            } else if (typeof value === 'object') {\n              type = 'object'\n            } else if (typeof value === 'number') {\n              type = 'number'\n            } else if (typeof value === 'boolean') {\n              type = 'boolean'\n            } else {\n              type = 'string'\n            }\n\n            if (type === 'object' || type === 'array') {\n              return (\n                <RenderJSON\n                  isEmpty={value.length === 0 || Object.keys(value).length === 0}\n                  key={`${key}-${keyIndex}`}\n                  object={value}\n                  objectKey={parentType === 'object' ? key : undefined}\n                  parentType={type}\n                  trailingComma={!isLastKey}\n                />\n              )\n            }\n\n            if (\n              type === 'date' ||\n              type === 'string' ||\n              type === 'null' ||\n              type === 'number' ||\n              type === 'boolean'\n            ) {\n              const parentHasKey = Boolean(parentType === 'object' && key)\n\n              const rowClasses = [\n                `${baseClass}__row-line`,\n                `${baseClass}__value-type--${type}`,\n                `${baseClass}__row-line--${objectKey ? 'nested' : 'top'}`,\n              ]\n                .filter(Boolean)\n                .join(' ')\n\n              return (\n                <li className={rowClasses} key={`${key}-${keyIndex}`}>\n                  {parentHasKey ? <span>{`\"${key}\": `}</span> : null}\n\n                  <span className={`${baseClass}__value`}>{JSON.stringify(value)}</span>\n                  {isLastKey ? '' : ','}\n                </li>\n              )\n            }\n          })}\n      </ul>\n\n      {!isEmpty && (\n        <span className={isNested ? `${baseClass}__bracket--nested` : ''}>\n          <Bracket comma={trailingComma} position=\"end\" type={parentType} />\n        </span>\n      )}\n    </li>\n  )\n}\n"], "names": ["c", "_c", "ChevronIcon", "React", "chars", "leftCurlyBracket", "leftSquareBracket", "rightCurlyBracket", "rightSquareBracket", "baseClass", "Bracket", "type", "comma", "position", "rightBracket", "leftBracket", "bracketToRender", "_jsxs", "className", "RenderJSON", "t0", "$", "isEmpty", "t1", "object", "object<PERSON>ey", "parentType", "t2", "trailingComma", "t3", "undefined", "objectKeys", "Object", "keys", "objectLength", "length", "isOpen", "setIsOpen", "useState", "isNested", "t4", "children", "onClick", "_jsx", "map", "key", "keyIndex", "value", "isLastKey", "Date", "toISOString", "Array", "isArray", "parentHasKey", "Boolean", "rowClasses", "filter", "join", "JSON", "stringify"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,WAAW,QAAQ;AAC5B,YAAYC,KAAA,MAAW;AAFvB;;;;;AAMA,MAAMC,KAAA,GAAQ;IACZC,gBAAA,EAAkB;IAClBC,iBAAA,EAAmB;IACnBC,iBAAA,EAAmB;IACnBC,kBAAA,EAAoB;AACtB;AAEA,MAAMC,SAAA,GAAY;AAElB,MAAMC,OAAA,GAAUA;QAAC,EACfC,IAAI,EACJC,KAAA,GAAQ,KAAK,EACbC,QAAAA,EAKD;IACC,MAAMC,YAAA,GAAeH,IAAA,KAAS,WAAWP,KAAA,CAAMG,iBAAiB,GAAGH,KAAA,CAAMI,kBAAkB;IAC3F,MAAMO,WAAA,GAAcJ,IAAA,KAAS,WAAWP,KAAA,CAAMC,gBAAgB,GAAGD,KAAA,CAAME,iBAAiB;IACxF,MAAMU,eAAA,GAAkBH,QAAA,KAAa,QAAQC,YAAA,GAAeC,WAAA;IAE5D,OAAA,WAAA,OACEE,wMAAA,EAAC,QAAA;QAAKC,SAAA,EAAW,UAAGT,SAAA,EAAA,qBAAsBA,SAAA,EAAA,wBAAgC,CAAU,MAAVI,QAAA;;YACvEG,eAAA;YACAH,QAAA,KAAa,SAASD,KAAA,GAAQ,MAAM;SAAA;;AAG3C;AAUO,MAAMO,UAAA,IAAaC,EAAA;IAAA,MAAAC,CAAA,OAAApB,0MAAA,EAAA;IAAC,MAAA,EAAAqB,OAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,aAAA,EAAAC,EAAAA,EAAA,GAAAT,EAMpB;IALL,MAAAE,OAAA,GAAAC,EAAe,KAAAO,SAAA,GAAA,QAAfP,EAAe;IAGf,MAAAG,UAAA,GAAAC,EAAqB,KAAAG,SAAA,GAAR,QAAQ,GAArBH,EAAqB;IACrB,MAAAC,aAAA,GAAAC,EAAqB,KAAAC,SAAA,GAAA,QAArBD,EAAqB;IAErB,MAAAE,UAAA,GAAmBP,MAAA,GAASQ,MAAA,CAAAC,IAAA,CAAYT,MAAA,IAAA,EAAY;IACpD,MAAAU,YAAA,GAAqBH,UAAA,CAAAI,MAAA;IACrB,MAAA,CAAAC,MAAA,EAAAC,SAAA,CAAA,GAA4BlC,KAAA,CAAAmC,6LAAA,CAAA,IAAwB;IACpD,MAAAC,QAAA,GAAiBb,UAAA,KAAe,YAAYA,UAAA,KAAe;IAAA,IAAAc,EAAA;IAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAe,MAAA,EAAA;QAM5CI,EAAA,GAAAA,CAAA,GAAMH,SAAA,CAAA,CAAWD,MAAA;QAAAf,CAAA,CAAA,EAAA,GAAAe,MAAA;QAAAf,CAAA,CAAA,EAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,EAAA;IAAA;IAAA,WAJ9BJ,wMAAA,EAAC,MAAA;QAAAC,SAAA,EAAcqB,QAAA,GAAW,GAAA,OAAA9B,SAAA,EAAA,mBAAgC,KAAG;QAAAgC,QAAA,EAAA;gBAC3DxB,wMAAA,EAAC,UAAA;gBAAA,cACY;gBAAAC,SAAA,EACA,UAAAT,SAAA,EAAA,kBAA4E,CAAI,MAAnDa,OAAA,GAAU,GAAA,OAAAb,SAAA,EAAA,qBAAkC,KAAG;gBAAIiC,OAAA,EAClFF,EAAiB;gBAAA7B,IAAA,EACrB;gBAAA8B,QAAA,EAAA;oBAEJnB,OAAA,GAAA,WACCqB,uMAAA,EAAAzC,oOAAA,EAAA;wBAAAgB,SAAA,EACa,GAAAT,OAAAA,EAAA,OAAA,EAAA,sBACT2B,MAAA,YADS,uBACS,gBAAT,SAAS;oBAClB,C;wBAGNnB,wMAAA,EAAC,QAAA;wBAAAwB,QAAA,EAAA;4BACEhB,SAAA,IAAa,IAAI,IAAc,GAAdA,SAAA,EAAA;gCAClBkB,uMAAA,EAAAjC,OAAA,EAAA;gCAAAG,QAAA,EAAkB;gCAAAF,IAAA,EAAce;4BAAA,C;4BAC/BJ,OAAA,OAAUqB,uMAAA,EAAAjC,OAAA,EAAA;gCAAAE,KAAA,EAAgBgB,aAAA;gCAAAf,QAAA,EAAwB;gCAAAF,IAAA,EAAYe;4BAAA,C,I,IAAiB;yBAAA;oBAAA,C;iB;;gBAIpFiB,uMAAA,EAAC,MAAA;gBAAAzB,SAAA,EACY,UAAAT,SAAA,EAAA,oBAAkF,CAAI,MAAvD8B,QAAA,GAAW,GAAA,OAAA9B,SAAA,EAAA,wBAAqC,KAAG;gBAAIgC,QAAA,EAEhGL,MAAA,IACCL,UAAA,CAAAa,GAAA,CAAA,CAAAC,GAAA,EAAAC,QAAA;oBACE,IAAAC,KAAA,GAAYvB,MAAM,CAACqB,GAAA,CAAA;oBACnB,IAAAlC,IAAA;oBACA,MAAAqC,SAAA,GAAkBF,QAAA,KAAaZ,YAAA,GAAA,CAAe;oBAAA,IAE1Ca,KAAA,KAAA,IAAU,EAAA;wBACZpC,IAAA,CAAAA,CAAA,CAAOA,MAAA;oBAAP,OAAA;wBAAA,IACSoC,KAAA,YAAAE,IAAiB,EAAA;4BAC1BtC,IAAA,CAAAA,CAAA,CAAOA,MAAA;4BACPoC,KAAA,CAAAA,CAAA,CAAQA,KAAA,CAAAG,WAAA,CAAiB;wBAAzB,OAAA;4BAAA,IACSC,KAAA,CAAAC,OAAA,CAAcL,KAAA,GAAA;gCACvBpC,IAAA,CAAAA,CAAA,CAAOA,OAAA;4BAAP,OAAA;gCAAA,IACS,OAAOoC,KAAA,KAAU,UAAA;oCAC1BpC,IAAA,CAAAA,CAAA,CAAOA,QAAA;gCAAP,OAAA;oCAAA,IACS,OAAOoC,KAAA,KAAU,UAAA;wCAC1BpC,IAAA,CAAAA,CAAA,CAAOA,QAAA;oCAAP,OAAA;wCAAA,IACS,OAAOoC,KAAA,KAAU,WAAA;4CAC1BpC,IAAA,CAAAA,CAAA,CAAOA,SAAA;wCAAP,OAAA;4CAEAA,IAAA,CAAAA,CAAA,CAAOA,QAAA;wCAAP;oCAAA;gCAAA;4BAAA;wBAAA;oBAAA;oBAAA,IAGEA,IAAA,KAAS,YAAYA,IAAA,KAAS,SAAA;wBAAA,OAE9BgC,2MAAA,EAAAxB,UAAA,EAAA;4BAAAG,OAAA,EACWyB,KAAA,CAAAZ,MAAA,KAAA,CAAiB,IAAKH,MAAA,CAAAC,IAAA,CAAYc,KAAA,EAAAZ,MAAA,KAAA,CAAkB;4BAAAX,MAAA,EAErDuB,KAAA;4BAAAtB,SAAA,EACGC,UAAA,KAAe,WAAWmB,GAAA,GAAAf,SAAM;4BAAAJ,UAAA,EAC/Bf,IAAA;4BAAAiB,aAAA,EAAA,CACIoB;wBAAA,GAJX,UAAGH,GAAA,EAAA,KAAO,CAAU,MAAVC,QAAA;oBAAU;oBAAA,IAU7BnC,IAAA,KAAS,UACTA,IAAA,KAAS,YACTA,IAAA,KAAS,UACTA,IAAA,KAAS,YACTA,IAAA,KAAS,WAAA;wBAET,MAAA0C,YAAA,GAAqBC,OAAA,CAAQ5B,UAAA,KAAe,YAAYmB,GAAA;wBAExD,MAAAU,UAAA,GAAmB,CACjB;4BAAA,GAAA,OAAA9C,IAAwB,EACxB,GADA,EAAA;4BACA,UAAAA,SAAA,EAAA,kBAA6B,CAAM,EACnC,IAD6BE,IAAA;4BAC7B,UAAAF,SAAA,EAAA,gBAAkD,CAAO,MAA9BgB,SAAA,GAAY,WAAW;yBAAO,CAAA+B,MAAA,CAAAF,OAEjD,EAAAG,IAAA,CACF;wBAAA,WAGNxC,wMAAA,EAAC,MAAA;4BAAAC,SAAA,EAAcqC,UAAA;4BAAAd,QAAA,EAAA;gCACZY,YAAA,OAAeV,uMAAA,EAAC,QAAA;oCAAAF,QAAA,EAAM,IAAI,OAAAI,GAAA,EAAA;gCAAQ,C,I,IAAW;oCAE9CF,uMAAA,EAAC,QAAA;oCAAAzB,SAAA,EAAgB,GAAA,OAAAT,SAAA,EAAA,QAAqB;oCAAAgC,QAAA,EAAGiB,IAAA,CAAAC,SAAA,CAAeZ,KAAA;gCAAA,C;gCACvDC,SAAA,GAAY,KAAK;6BAAA;wBAAA,GAJY,UAAGH,GAAA,EAAA,KAAO,CAAU,MAAVC,QAAA;oBAAU;gBAAA,CAQ1D;YAAA,C;YAGH,CAACxB,OAAA,QACAqB,uMAAA,EAAC,QAAA;gBAAAzB,SAAA,EAAgBqB,QAAA,GAAW,GAAA,OAAA9B,SAAA,EAAA,kBAA+B,KAAG;gBAAAgC,QAAA,MAC5DE,uMAAA,EAAAjC,OAAA,EAAA;oBAAAE,KAAA,EAAgBgB,aAAA;oBAAAf,QAAA,EAAwB;oBAAAF,IAAA,EAAYe;gBAAA,C;;;;CAK9D", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/API/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/API/index.client.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  <PERSON>boxField,\n  CopyToClipboard,\n  Form,\n  Gutter,\n  MinimizeMaximizeIcon,\n  NumberField,\n  SetDocumentStepNav,\n  toast,\n  useConfig,\n  useDocumentInfo,\n  useLocale,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { useSearchParams } from 'next/navigation.js'\n\nimport './index.scss'\n\nimport * as React from 'react'\n\nimport { LocaleSelector } from './LocaleSelector/index.js'\nimport { RenderJSON } from './RenderJSON/index.js'\n\nconst baseClass = 'query-inspector'\n\nexport const APIViewClient: React.FC = () => {\n  const { id, collectionSlug, globalSlug, initialData, isTrashed } = useDocumentInfo()\n\n  const searchParams = useSearchParams()\n  const { i18n, t } = useTranslation()\n  const { code } = useLocale()\n\n  const {\n    config: {\n      defaultDepth,\n      localization,\n      routes: { api: apiRoute },\n      serverURL,\n    },\n    getEntityConfig,\n  } = useConfig()\n\n  const collectionConfig = getEntityConfig({ collectionSlug })\n  const globalConfig = getEntityConfig({ globalSlug })\n\n  const localeOptions =\n    localization &&\n    localization.locales.map((locale) => ({ label: locale.label, value: locale.code }))\n\n  let draftsEnabled: boolean = false\n  let docEndpoint: string = ''\n\n  if (collectionConfig) {\n    draftsEnabled = Boolean(collectionConfig.versions?.drafts)\n    docEndpoint = `/${collectionSlug}/${id}`\n  }\n\n  if (globalConfig) {\n    draftsEnabled = Boolean(globalConfig.versions?.drafts)\n    docEndpoint = `/globals/${globalSlug}`\n  }\n\n  const [data, setData] = React.useState<any>(initialData)\n  const [draft, setDraft] = React.useState<boolean>(searchParams.get('draft') === 'true')\n  const [locale, setLocale] = React.useState<string>(searchParams?.get('locale') || code)\n  const [depth, setDepth] = React.useState<string>(\n    searchParams.get('depth') || defaultDepth.toString(),\n  )\n  const [authenticated, setAuthenticated] = React.useState<boolean>(true)\n  const [fullscreen, setFullscreen] = React.useState<boolean>(false)\n\n  const trashParam = typeof initialData?.deletedAt === 'string'\n\n  const params = new URLSearchParams({\n    depth,\n    draft: String(draft),\n    locale,\n    trash: trashParam ? 'true' : 'false',\n  }).toString()\n\n  const fetchURL = `${serverURL}${apiRoute}${docEndpoint}?${params}`\n\n  React.useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const res = await fetch(fetchURL, {\n          credentials: authenticated ? 'include' : 'omit',\n          headers: {\n            'Accept-Language': i18n.language,\n          },\n          method: 'GET',\n        })\n\n        try {\n          const json = await res.json()\n          setData(json)\n        } catch (error) {\n          toast.error('Error parsing response')\n          console.error(error) // eslint-disable-line no-console\n        }\n      } catch (error) {\n        toast.error('Error making request')\n        console.error(error) // eslint-disable-line no-console\n      }\n    }\n\n    void fetchData()\n  }, [i18n.language, fetchURL, authenticated])\n\n  return (\n    <Gutter\n      className={[baseClass, fullscreen && `${baseClass}--fullscreen`].filter(Boolean).join(' ')}\n      right={false}\n    >\n      <SetDocumentStepNav\n        collectionSlug={collectionSlug}\n        globalLabel={globalConfig?.label}\n        globalSlug={globalSlug}\n        id={id}\n        isTrashed={isTrashed}\n        pluralLabel={collectionConfig ? collectionConfig?.labels?.plural : undefined}\n        useAsTitle={collectionConfig ? collectionConfig?.admin?.useAsTitle : undefined}\n        view=\"API\"\n      />\n      <div className={`${baseClass}__configuration`}>\n        <div className={`${baseClass}__api-url`}>\n          <span className={`${baseClass}__label`}>\n            API URL <CopyToClipboard value={fetchURL} />\n          </span>\n          <a href={fetchURL} rel=\"noopener noreferrer\" target=\"_blank\">\n            {fetchURL}\n          </a>\n        </div>\n        <Form\n          initialState={{\n            authenticated: {\n              initialValue: authenticated || false,\n              valid: true,\n              value: authenticated || false,\n            },\n            depth: {\n              initialValue: Number(depth || 0),\n              valid: true,\n              value: Number(depth || 0),\n            },\n            draft: {\n              initialValue: draft || false,\n              valid: true,\n              value: draft || false,\n            },\n            locale: {\n              initialValue: locale,\n              valid: true,\n              value: locale,\n            },\n          }}\n        >\n          <div className={`${baseClass}__form-fields`}>\n            <div className={`${baseClass}__filter-query-checkboxes`}>\n              {draftsEnabled && (\n                <CheckboxField\n                  field={{\n                    name: 'draft',\n                    label: t('version:draft'),\n                  }}\n                  onChange={() => setDraft(!draft)}\n                  path=\"draft\"\n                />\n              )}\n              <CheckboxField\n                field={{\n                  name: 'authenticated',\n                  label: t('authentication:authenticated'),\n                }}\n                onChange={() => setAuthenticated(!authenticated)}\n                path=\"authenticated\"\n              />\n            </div>\n            {localeOptions && <LocaleSelector localeOptions={localeOptions} onChange={setLocale} />}\n            <NumberField\n              field={{\n                name: 'depth',\n                admin: {\n                  step: 1,\n                },\n                label: t('general:depth'),\n                max: 10,\n                min: 0,\n              }}\n              onChange={(value) => setDepth(value?.toString())}\n              path=\"depth\"\n            />\n          </div>\n        </Form>\n      </div>\n      <div className={`${baseClass}__results-wrapper`}>\n        <div className={`${baseClass}__toggle-fullscreen-button-container`}>\n          <button\n            aria-label=\"toggle fullscreen\"\n            className={`${baseClass}__toggle-fullscreen-button`}\n            onClick={() => setFullscreen(!fullscreen)}\n            type=\"button\"\n          >\n            <MinimizeMaximizeIcon isMinimized={!fullscreen} />\n          </button>\n        </div>\n        <div className={`${baseClass}__results`}>\n          <RenderJSON object={data} />\n        </div>\n      </div>\n    </Gutter>\n  )\n}\n"], "names": ["CheckboxField", "CopyToClipboard", "Form", "<PERSON><PERSON>", "MinimizeMaximizeIcon", "NumberField", "SetDocumentStepNav", "toast", "useConfig", "useDocumentInfo", "useLocale", "useTranslation", "useSearchParams", "React", "LocaleSelector", "RenderJSON", "baseClass", "APIViewClient", "id", "collectionSlug", "globalSlug", "initialData", "isTrashed", "searchParams", "i18n", "t", "code", "config", "defaultDepth", "localization", "routes", "api", "apiRoute", "serverURL", "getEntityConfig", "collectionConfig", "globalConfig", "localeOptions", "locales", "map", "locale", "label", "value", "draftsEnabled", "docEndpoint", "Boolean", "versions", "drafts", "data", "setData", "useState", "draft", "setDraft", "get", "setLocale", "depth", "<PERSON><PERSON><PERSON><PERSON>", "toString", "authenticated", "setAuthenticated", "fullscreen", "setFullscreen", "trashParam", "deletedAt", "params", "URLSearchParams", "String", "trash", "fetchURL", "useEffect", "fetchData", "res", "fetch", "credentials", "headers", "language", "method", "json", "error", "console", "_jsxs", "className", "filter", "join", "right", "_jsx", "globalLabel", "plural<PERSON><PERSON>l", "labels", "plural", "undefined", "useAsTitle", "admin", "view", "href", "rel", "target", "initialState", "initialValue", "valid", "Number", "field", "name", "onChange", "path", "step", "max", "min", "onClick", "type", "isMinimized", "object"], "mappings": ";;;;;AAEA,SACEA,aAAa,EACbC,eAAe,EACfC,IAAI,EACJC,MAAM,EACNC,oBAAoB,EACpBC,WAAW,EACXC,kBAAkB,EAClBC,KAAK,EACLC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,cAAc,QACT;;;AACP,SAASC,eAAe,QAAQ;AAIhC,YAAYC,KAAA,MAAW;AAEvB,SAASC,cAAc,QAAQ;AAC/B,SAASC,UAAU,QAAQ;AAvB3B;;;;;;;AAyBA,MAAMC,SAAA,GAAY;AAEX,MAAMC,aAAA,GAA0BA,CAAA;kCAgGAkB,gBAAA;IA/FrC,MAAM,EAAEjB,EAAE,EAAEC,cAAc,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAAA,EAAW,OAAGb,wOAAA;IAEnE,MAAMc,YAAA,OAAeX,kLAAA;IACrB,MAAM,EAAEY,IAAI,EAAEC,CAAAA,EAAG,GAAGd,8QAAA;IACpB,MAAM,EAAEe,IAAAA,EAAM,OAAGhB,kOAAA;IAEjB,MAAM,EACJiB,MAAA,EAAQ,EACNC,YAAY,EACZC,YAAY,EACZC,MAAA,EAAQ,EAAEC,GAAA,EAAKC,QAAAA,EAAU,EACzBC,SAAAA,EACD,EACDC,eAAAA,EACD,OAAG1B,gQAAA;IAEJ,MAAM2B,gBAAA,GAAmBD,eAAA,CAAgB;QAAEf;IAAe;IAC1D,MAAMiB,YAAA,GAAeF,eAAA,CAAgB;QAAEd;IAAW;IAElD,MAAMiB,aAAA,GACJR,YAAA,IACAA,YAAA,CAAaS,OAAO,CAACC,GAAG,EAAEC,MAAA,GAAA,CAAY;YAAEC,KAAA,EAAOD,MAAA,CAAOC,KAAK;YAAEC,KAAA,EAAOF,MAAA,CAAOd,IAAAA;QAAK,CAAA;IAElF,IAAIiB,aAAA,GAAyB;IAC7B,IAAIC,WAAA,GAAsB;IAE1B,IAAIT,gBAAA,EAAkB;;QACpBQ,aAAA,GAAgBE,OAAA,gDAAyBC,QAAQ,+DAAzBX,gBAAA,WAA2BY,MAAA;QACnDH,WAAA,GAAc,IAAsB1B,EAAA,KAAlBC,cAAA,EAAA,KAAkB,CAAI;IAC1C;IAEA,IAAIiB,YAAA,EAAc;;QAChBO,aAAA,GAAgBE,OAAA,wCAAqBC,QAAQ,2DAArBV,YAAA,WAAuBW,MAAA;QAC/CH,WAAA,GAAc,YAAY,CAAY,MAAZxB,UAAA;IAC5B;IAEA,MAAM,CAAC4B,IAAA,EAAMC,OAAA,CAAQ,GAAGpC,KAAA,CAAMqC,6LAAQ,CAAM7B,WAAA;IAC5C,MAAM,CAAC8B,KAAA,EAAOC,QAAA,CAAS,GAAGvC,KAAA,CAAMqC,6LAAQ,CAAU3B,YAAA,CAAa8B,GAAG,CAAC,aAAa;IAChF,MAAM,CAACb,QAAA,EAAQc,SAAA,CAAU,GAAGzC,KAAA,CAAMqC,6LAAQ,8DAAS3B,YAAA,CAAc8B,GAAA,CAAI,cAAa3B,IAAA;IAClF,MAAM,CAAC6B,KAAA,EAAOC,QAAA,CAAS,GAAG3C,KAAA,CAAMqC,6LAAQ,CACtC3B,YAAA,CAAa8B,GAAG,CAAC,YAAYzB,YAAA,CAAa6B,QAAQ;IAEpD,MAAM,CAACC,aAAA,EAAeC,gBAAA,CAAiB,GAAG9C,KAAA,CAAMqC,6LAAQ,CAAU;IAClE,MAAM,CAACU,UAAA,EAAYC,aAAA,CAAc,GAAGhD,KAAA,CAAMqC,6LAAQ,CAAU;IAE5D,MAAMY,UAAA,GAAa,QAAOzC,WAAA,2DAAa0C,SAAA,MAAc;IAErD,MAAMC,MAAA,GAAS,IAAIC,eAAA,CAAgB;QACjCV,KAAA;QACAJ,KAAA,EAAOe,MAAA,CAAOf,KAAA;QACdX,MAAA,EAAAA,QAAA;QACA2B,KAAA,EAAOL,UAAA,GAAa,SAAS;IAC/B,GAAGL,QAAQ;IAEX,MAAMW,QAAA,GAAW,UAAGnC,SAAA,SAAYD,QAAA,SAAWY,WAAA,EAAA,KAAe,CAAQ,MAARoB,MAAA;IAE1DnD,KAAA,CAAMwD,8LAAS;mCAAC;YACd,MAAMC,SAAA;qDAAY,MAAAA,CAAA;oBAChB,IAAI;wBACF,MAAMC,GAAA,GAAM,MAAMC,KAAA,CAAMJ,QAAA,EAAU;4BAChCK,WAAA,EAAaf,aAAA,GAAgB,YAAY;4BACzCgB,OAAA,EAAS;gCACP,mBAAmBlD,IAAA,CAAKmD,QAAAA;4BAC1B;4BACAC,MAAA,EAAQ;wBACV;wBAEA,IAAI;4BACF,MAAMC,IAAA,GAAO,MAAMN,GAAA,CAAIM,IAAI;4BAC3B5B,OAAA,CAAQ4B,IAAA;wBACV,EAAE,OAAOC,OAAA,EAAO;4BACdvE,8NAAA,CAAMuE,KAAK,CAAC;4BACZC,OAAA,CAAQD,KAAK,CAACA,OAAA,EAAO,iCAAA;;wBACvB;oBACF,EAAE,OAAOA,KAAA,EAAO;wBACdvE,8NAAA,CAAMuE,KAAK,CAAC;wBACZC,OAAA,CAAQD,KAAK,CAACA,KAAA,EAAO,iCAAA;;oBACvB;gBACF;;YAEA,KAAKR,SAAA;QACP;kCAAG;QAAC9C,IAAA,CAAKmD,QAAQ;QAAEP,QAAA;QAAUV,aAAA;KAAc;IAE3C,OAAA,WAAA,OACEsB,wMAAA,EAAC7E,+NAAA,EAAA;QACC8E,SAAA,EAAW;YAACjE,SAAA;YAAW4C,UAAA,IAAc,GAAG,OAAA5C,SAAA,EAAA,aAAuB;SAAC,CAACkE,MAAM,CAACrC,OAAA,EAASsC,IAAI,CAAC;QACtFC,KAAA,EAAO;;8BAEPC,uMAAA,EAAC/E,2OAAA,EAAA;gBACCa,cAAA,EAAgBA,cAAA;gBAChBmE,WAAA,8DAAalD,YAAA,CAAcK,KAAA;gBAC3BrB,UAAA,EAAYA,UAAA;gBACZF,EAAA,EAAIA,EAAA;gBACJI,SAAA,EAAWA,SAAA;gBACXiE,WAAA,EAAapD,gBAAA,GAAmBA,gBAAA,iGAAkBqD,MAAA,sFAAQC,MAAA,GAASC,SAAA;gBACnEC,UAAA,EAAYxD,gBAAA,mHAAqCyD,KAAA,oFAAOD,UAAA,GAAaD,SAAA;gBACrEG,IAAA,EAAK;;0BAEPb,4MAAA,EAAC,OAAA;gBAAIC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,gBAA0B;;sCAC3CgE,wMAAA,EAAC,OAAA;wBAAIC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,UAAoB;;8CACrCgE,wMAAA,EAAC,QAAA;gCAAKC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,QAAkB;;oCAAE;oC,W,OAC9BqE,uMAAA,EAACpF,wOAAA,EAAA;wCAAgByC,KAAA,EAAO0B;;;;8CAElCiB,uMAAA,EAAC,KAAA;gCAAES,IAAA,EAAM1B,QAAA;gCAAU2B,GAAA,EAAI;gCAAsBC,MAAA,EAAO;0CACjD5B;;;;sCAGLiB,uMAAA,EAACnF,6NAAA,EAAA;wBACC+F,YAAA,EAAc;4BACZvC,aAAA,EAAe;gCACbwC,YAAA,EAAcxC,aAAA,IAAiB;gCAC/ByC,KAAA,EAAO;gCACPzD,KAAA,EAAOgB,aAAA,IAAiB;4BAC1B;4BACAH,KAAA,EAAO;gCACL2C,YAAA,EAAcE,MAAA,CAAO7C,KAAA,IAAS;gCAC9B4C,KAAA,EAAO;gCACPzD,KAAA,EAAO0D,MAAA,CAAO7C,KAAA,IAAS;4BACzB;4BACAJ,KAAA,EAAO;gCACL+C,YAAA,EAAc/C,KAAA,IAAS;gCACvBgD,KAAA,EAAO;gCACPzD,KAAA,EAAOS,KAAA,IAAS;4BAClB;4BACAX,MAAA,EAAQ;gCACN0D,YAAA,EAAc1D,QAAA;gCACd2D,KAAA,EAAO;gCACPzD,KAAA,EAAOF;4BACT;wBACF;kCAEA,WAAA,OAAAwC,wMAAA,EAAC,OAAA;4BAAIC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,cAAwB;;kDACzCgE,wMAAA,EAAC,OAAA;oCAAIC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,0BAAoC;;wCACpD2B,aAAA,IAAA,WAAA,OACC0C,uMAAA,EAACrF,sOAAA,EAAA;4CACCqG,KAAA,EAAO;gDACLC,IAAA,EAAM;gDACN7D,KAAA,EAAOhB,CAAA,CAAE;4CACX;4CACA8E,QAAA,EAAUA,CAAA,GAAMnD,QAAA,CAAS,CAACD,KAAA;4CAC1BqD,IAAA,EAAK;;0DAGTnB,uMAAA,EAACrF,sOAAA,EAAA;4CACCqG,KAAA,EAAO;gDACLC,IAAA,EAAM;gDACN7D,KAAA,EAAOhB,CAAA,CAAE;4CACX;4CACA8E,QAAA,EAAUA,CAAA,GAAM5C,gBAAA,CAAiB,CAACD,aAAA;4CAClC8C,IAAA,EAAK;;;;gCAGRnE,aAAA,IAAA,WAAA,OAAiBgD,uMAAA,EAACvE,sOAAA,EAAA;oCAAeuB,aAAA,EAAeA,aAAA;oCAAekE,QAAA,EAAUjD;;kDAC1E+B,uMAAA,EAAChF,oOAAA,EAAA;oCACCgG,KAAA,EAAO;wCACLC,IAAA,EAAM;wCACNV,KAAA,EAAO;4CACLa,IAAA,EAAM;wCACR;wCACAhE,KAAA,EAAOhB,CAAA,CAAE;wCACTiF,GAAA,EAAK;wCACLC,GAAA,EAAK;oCACP;oCACAJ,QAAA,GAAW7D,KAAA,GAAUc,QAAA,CAASd,KAAA,+CAAOe,QAAA;oCACrC+C,IAAA,EAAK;;;;;;;8BAKbxB,wMAAA,EAAC,OAAA;gBAAIC,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,kBAA4B;;sCAC7CqE,uMAAA,EAAC,OAAA;wBAAIJ,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,mBAA+C;kCAChE,WAAA,OAAAqE,uMAAA,EAAC,UAAA;4BACC,cAAW;4BACXJ,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,2BAAqC;4BACnD4F,OAAA,EAASA,CAAA,GAAM/C,aAAA,CAAc,CAACD,UAAA;4BAC9BiD,IAAA,EAAK;sCAEL,WAAA,OAAAxB,uMAAA,EAACjF,6OAAA,EAAA;gCAAqB0G,WAAA,EAAa,CAAClD;;;;sCAGxCyB,uMAAA,EAAC,OAAA;wBAAIJ,SAAA,EAAW,GAAG,OAAAjE,SAAA,EAAA,UAAoB;kCACrC,WAAA,OAAAqE,uMAAA,EAACtE,8NAAA,EAAA;4BAAWgG,MAAA,EAAQ/D;;;;;;;AAK9B", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/Restore/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/Restore/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { ClientCollectionConfig, ClientGlobalConfig, SanitizedCollectionConfig } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport {\n  Button,\n  ConfirmationModal,\n  PopupList,\n  toast,\n  useConfig,\n  useModal,\n  useRouteTransition,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { requests } from '@payloadcms/ui/shared'\nimport { useRouter } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\n\nimport './index.scss'\n\nimport React, { Fragment, useCallback, useState } from 'react'\n\nconst baseClass = 'restore-version'\nconst modalSlug = 'restore-version'\n\ntype Props = {\n  className?: string\n  collectionConfig?: ClientCollectionConfig\n  globalConfig?: ClientGlobalConfig\n  label: SanitizedCollectionConfig['labels']['singular']\n  originalDocID: number | string\n  status?: string\n  versionDateFormatted: string\n  versionID: string\n}\n\nexport const Restore: React.FC<Props> = ({\n  className,\n  collectionConfig,\n  globalConfig,\n  label,\n  originalDocID,\n  status,\n  versionDateFormatted,\n  versionID,\n}) => {\n  const {\n    config: {\n      routes: { admin: adminRoute, api: apiRoute },\n      serverURL,\n    },\n  } = useConfig()\n\n  const { toggleModal } = useModal()\n  const router = useRouter()\n  const { i18n, t } = useTranslation()\n  const [draft, setDraft] = useState(false)\n  const { startRouteTransition } = useRouteTransition()\n\n  const restoreMessage = t('version:aboutToRestoreGlobal', {\n    label: getTranslation(label, i18n),\n    versionDate: versionDateFormatted,\n  })\n\n  const canRestoreAsDraft = status !== 'draft' && collectionConfig?.versions?.drafts\n\n  const handleRestore = useCallback(async () => {\n    let fetchURL = `${serverURL}${apiRoute}`\n    let redirectURL: string\n\n    if (collectionConfig) {\n      fetchURL = `${fetchURL}/${collectionConfig.slug}/versions/${versionID}?draft=${draft}`\n      redirectURL = formatAdminURL({\n        adminRoute,\n        path: `/collections/${collectionConfig.slug}/${originalDocID}`,\n      })\n    }\n\n    if (globalConfig) {\n      fetchURL = `${fetchURL}/globals/${globalConfig.slug}/versions/${versionID}?draft=${draft}`\n      redirectURL = formatAdminURL({\n        adminRoute,\n        path: `/globals/${globalConfig.slug}`,\n      })\n    }\n\n    const res = await requests.post(fetchURL, {\n      headers: {\n        'Accept-Language': i18n.language,\n      },\n    })\n\n    if (res.status === 200) {\n      const json = await res.json()\n      toast.success(json.message)\n      return startRouteTransition(() => router.push(redirectURL))\n    } else {\n      toast.error(t('version:problemRestoringVersion'))\n    }\n  }, [\n    serverURL,\n    apiRoute,\n    collectionConfig,\n    globalConfig,\n    i18n.language,\n    versionID,\n    draft,\n    adminRoute,\n    originalDocID,\n    startRouteTransition,\n    router,\n    t,\n  ])\n\n  return (\n    <Fragment>\n      <div className={[baseClass, className].filter(Boolean).join(' ')}>\n        <Button\n          buttonStyle=\"primary\"\n          className={[canRestoreAsDraft && `${baseClass}__restore-as-draft-button`]\n            .filter(Boolean)\n            .join(' ')}\n          onClick={() => toggleModal(modalSlug)}\n          size=\"xsmall\"\n          SubMenuPopupContent={\n            canRestoreAsDraft\n              ? () => (\n                  <PopupList.ButtonGroup>\n                    <PopupList.Button onClick={() => [setDraft(true), toggleModal(modalSlug)]}>\n                      {t('version:restoreAsDraft')}\n                    </PopupList.Button>\n                  </PopupList.ButtonGroup>\n                )\n              : null\n          }\n        >\n          {t('version:restoreThisVersion')}\n        </Button>\n      </div>\n      <ConfirmationModal\n        body={restoreMessage}\n        confirmingLabel={t('version:restoring')}\n        heading={t('version:confirmVersionRestoration')}\n        modalSlug={modalSlug}\n        onConfirm={handleRestore}\n      />\n    </Fragment>\n  )\n}\n"], "names": ["getTranslation", "<PERSON><PERSON>", "ConfirmationModal", "PopupList", "toast", "useConfig", "useModal", "useRouteTransition", "useTranslation", "requests", "useRouter", "formatAdminURL", "React", "Fragment", "useCallback", "useState", "baseClass", "modalSlug", "Rest<PERSON>", "className", "collectionConfig", "globalConfig", "label", "originalDocID", "status", "versionDateFormatted", "versionID", "config", "routes", "admin", "adminRoute", "api", "apiRoute", "serverURL", "toggleModal", "router", "i18n", "t", "draft", "setDraft", "startRouteTransition", "restoreMessage", "versionDate", "canRestoreAsDraft", "versions", "drafts", "handleRestore", "fetchURL", "redirectURL", "slug", "path", "res", "post", "headers", "language", "json", "success", "message", "push", "error", "_jsxs", "_jsx", "filter", "Boolean", "join", "buttonStyle", "onClick", "size", "SubMenuPopupContent", "ButtonGroup", "body", "<PERSON><PERSON><PERSON><PERSON>", "heading", "onConfirm"], "mappings": ";;;;;AAIA,SAASA,cAAc,QAAQ;AAC/B,SACEC,MAAM,EACNC,iBAAiB,EACjBC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,kBAAkB,EAClBC,cAAc,QACT;;;AACP,SAASC,QAAQ,QAAQ;AACzB,SAASC,SAAS,QAAQ;AAC1B,SAASC,cAAc,QAAQ;AAI/B,OAAOC,KAAA,IAASC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ;AArBvD;;;;;;;;AAuBA,MAAMC,SAAA,GAAY;AAClB,MAAMC,SAAA,GAAY;AAaX,MAAMC,OAAA,GAA2BA;QAAC,EACvCC,SAAS,EACTC,gBAAgB,EAChBC,YAAY,EACZC,KAAK,EACLC,aAAa,EACbC,MAAM,EACNC,oBAAoB,EACpBC,SAAAA,EACD;QAmBiDN,gBAAA;IAlBhD,MAAM,EACJO,MAAA,EAAQ,EACNC,MAAA,EAAQ,EAAEC,KAAA,EAAOC,UAAU,EAAEC,GAAA,EAAKC,QAAAA,EAAU,EAC5CC,SAAAA,EAAS,EAEZ,OAAG5B,gQAAA;IAEJ,MAAM,EAAE6B,WAAAA,EAAa,OAAG5B,iOAAA;IACxB,MAAM6B,MAAA,GAASzB,gLAAA;IACf,MAAM,EAAE0B,IAAI,EAAEC,CAAAA,EAAG,OAAG7B,0QAAA;IACpB,MAAM,CAAC8B,KAAA,EAAOC,QAAA,CAAS,OAAGxB,mMAAA,EAAS;IACnC,MAAM,EAAEyB,oBAAAA,EAAsB,GAAGjC,+OAAA;IAEjC,MAAMkC,cAAA,GAAiBJ,CAAA,CAAE,gCAAgC;QACvDf,KAAA,MAAOtB,kOAAA,EAAesB,KAAA,EAAOc,IAAA;QAC7BM,WAAA,EAAajB;IACf;IAEA,MAAMkB,iBAAA,GAAoBnB,MAAA,KAAW,+HAA6BoB,QAAA,0FAAUC,MAAA;IAE5E,MAAMC,aAAA,GAAgBhC,0MAAA;8CAAY;YAChC,IAAIiC,QAAA,GAAW,UAAGd,SAAA,EAAY,CAAU,MAAVD,QAAA;YAC9B,IAAIgB,WAAA;YAEJ,IAAI5B,gBAAA,EAAkB;gBACpB2B,QAAA,GAAW,GAAe3B,OAAZ2B,QAAA,CAAY,CAAZ,KAA8CrB,SAAA,eAAjBuB,IAAI,EAAA,gCAAa,WAAmB,CAAO,MAAPX,KAAA;gBAC/EU,WAAA,OAAcrC,6MAAA,EAAe;oBAC3BmB,UAAA;oBACAoB,IAAA,EAAM,uBAAgB9B,gBAAA,CAAiB6B,IAAI,EAAA,KAAI,OAAA1B,aAAA;gBACjD;YACF;YAEA,IAAIF,YAAA,EAAc;gBAChB0B,QAAA,GAAW,UAAGA,QAAA,EAAA,oBAAoB1B,YAAA,CAAa4B,IAAI,EAAA,qBAAavB,SAAA,EAAA,WAAmB,CAAO,MAAPY,KAAA;gBACnFU,WAAA,OAAcrC,6MAAA,EAAe;oBAC3BmB,UAAA;oBACAoB,IAAA,EAAM,YAA6B,OAAjB7B,YAAA,CAAa4B,IAAI;gBACrC;YACF;YAEA,MAAME,GAAA,GAAM,MAAM1C,iOAAA,CAAS2C,IAAI,CAACL,QAAA,EAAU;gBACxCM,OAAA,EAAS;oBACP,mBAAmBjB,IAAA,CAAKkB,QAAAA;gBAC1B;YACF;YAEA,IAAIH,GAAA,CAAI3B,MAAM,KAAK,KAAK;gBACtB,MAAM+B,IAAA,GAAO,MAAMJ,GAAA,CAAII,IAAI;gBAC3BnD,8NAAA,CAAMoD,OAAO,CAACD,IAAA,CAAKE,OAAO;gBAC1B,OAAOjB,oBAAA;0DAAqB,IAAML,MAAA,CAAOuB,IAAI,CAACV,WAAA;;YAChD,OAAO;gBACL5C,8NAAA,CAAMuD,KAAK,CAACtB,CAAA,CAAE;YAChB;QACF;6CAAG;QACDJ,SAAA;QACAD,QAAA;QACAZ,gBAAA;QACAC,YAAA;QACAe,IAAA,CAAKkB,QAAQ;QACb5B,SAAA;QACAY,KAAA;QACAR,UAAA;QACAP,aAAA;QACAiB,oBAAA;QACAL,MAAA;QACAE,CAAA;KACD;IAED,OAAA,WAAA,OACEuB,wMAAA,EAAC/C,mMAAA,EAAA;;8BACCgD,uMAAA,EAAC,OAAA;gBAAI1C,SAAA,EAAW;oBAACH,SAAA;oBAAWG,SAAA;iBAAU,CAAC2C,MAAM,CAACC,OAAA,EAASC,IAAI,CAAC;0BAC1D,WAAA,OAAAH,uMAAA,EAAC5D,+NAAA,EAAA;oBACCgE,WAAA,EAAY;oBACZ9C,SAAA,EAAW;wBAACwB,iBAAA,IAAqB,GAAG,OAAA3B,SAAA,EAAA,QAAoC;qBAAC,CACtE8C,MAAM,CAACC,OAAA,EACPC,IAAI,CAAC;oBACRE,OAAA,EAASA,CAAA,GAAMhC,WAAA,CAAYjB,SAAA;oBAC3BkD,IAAA,EAAK;oBACLC,mBAAA,EACEzB,iBAAA,GACI,IAAA,WAAA,GACEkB,2MAAA,EAAC1D,kOAAA,CAAUkE,WAAW,EAAA;sCACpB,WAAA,OAAAR,uMAAA,EAAC1D,kOAAA,CAAUF,MAAM,EAAA;gCAACiE,OAAA,EAASA,CAAA,GAAM;wCAAC3B,QAAA,CAAS;wCAAOL,WAAA,CAAYjB,SAAA;qCAAW;0CACtEoB,CAAA,CAAE;;6BAIT;8BAGLA,CAAA,CAAE;;;8BAGPwB,uMAAA,EAAC3D,0OAAA,EAAA;gBACCoE,IAAA,EAAM7B,cAAA;gBACN8B,eAAA,EAAiBlC,CAAA,CAAE;gBACnBmC,OAAA,EAASnC,CAAA,CAAE;gBACXpB,SAAA,EAAWA,SAAA;gBACXwD,SAAA,EAAW3B;;;;AAInB", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/SelectComparison/VersionDrawer/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/SelectComparison/VersionDrawer/index.tsx"], "sourcesContent": ["'use client'\nimport {\n  Drawer,\n  LoadingOverlay,\n  toast,\n  useDocumentInfo,\n  useEditDepth,\n  useModal,\n  useServerFunctions,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { useSearchParams } from 'next/navigation.js'\n\nimport './index.scss'\n\nimport React, { useCallback, useEffect, useId, useMemo, useRef, useState } from 'react'\n\nexport const baseClass = 'version-drawer'\nexport const formatVersionDrawerSlug = ({\n  depth,\n  uuid,\n}: {\n  depth: number\n  uuid: string // supply when creating a new document and no id is available\n}) => `version-drawer_${depth}_${uuid}`\n\nexport const VersionDrawerContent: React.FC<{\n  collectionSlug?: string\n  docID?: number | string\n  drawerSlug: string\n  globalSlug?: string\n}> = (props) => {\n  const { collectionSlug, docID, drawerSlug, globalSlug } = props\n  const { isTrashed } = useDocumentInfo()\n  const { closeModal } = useModal()\n  const searchParams = useSearchParams()\n  const prevSearchParams = useRef(searchParams)\n\n  const { renderDocument } = useServerFunctions()\n\n  const [DocumentView, setDocumentView] = useState<React.ReactNode>(undefined)\n  const [isLoading, setIsLoading] = useState(true)\n  const hasRenderedDocument = useRef(false)\n  const { t } = useTranslation()\n\n  const getDocumentView = useCallback(\n    (docID?: number | string) => {\n      const fetchDocumentView = async () => {\n        setIsLoading(true)\n\n        try {\n          const isGlobal = Boolean(globalSlug)\n          const entitySlug = collectionSlug ?? globalSlug\n\n          const result = await renderDocument({\n            collectionSlug: entitySlug,\n            docID,\n            drawerSlug,\n            paramsOverride: {\n              segments: [\n                isGlobal ? 'globals' : 'collections',\n                entitySlug,\n                ...(isTrashed ? ['trash'] : []),\n                isGlobal ? undefined : String(docID),\n                'versions',\n              ].filter(Boolean),\n            },\n            redirectAfterDelete: false,\n            redirectAfterDuplicate: false,\n            searchParams: Object.fromEntries(searchParams.entries()),\n            versions: {\n              disableGutter: true,\n              useVersionDrawerCreatedAtCell: true,\n            },\n          })\n\n          if (result?.Document) {\n            setDocumentView(result.Document)\n            setIsLoading(false)\n          }\n        } catch (error) {\n          toast.error(error?.message || t('error:unspecific'))\n          closeModal(drawerSlug)\n          // toast.error(data?.errors?.[0].message || t('error:unspecific'))\n        }\n      }\n\n      void fetchDocumentView()\n    },\n    [\n      closeModal,\n      collectionSlug,\n      drawerSlug,\n      globalSlug,\n      isTrashed,\n      renderDocument,\n      searchParams,\n      t,\n    ],\n  )\n\n  useEffect(() => {\n    if (!hasRenderedDocument.current || prevSearchParams.current !== searchParams) {\n      prevSearchParams.current = searchParams\n      getDocumentView(docID)\n      hasRenderedDocument.current = true\n    }\n  }, [docID, getDocumentView, searchParams])\n\n  if (isLoading) {\n    return <LoadingOverlay />\n  }\n\n  return DocumentView\n}\nexport const VersionDrawer: React.FC<{\n  collectionSlug?: string\n  docID?: number | string\n  drawerSlug: string\n  globalSlug?: string\n}> = (props) => {\n  const { collectionSlug, docID, drawerSlug, globalSlug } = props\n  const { t } = useTranslation()\n\n  return (\n    <Drawer\n      className={baseClass}\n      gutter={true}\n      slug={drawerSlug}\n      title={t('version:selectVersionToCompare')}\n    >\n      <VersionDrawerContent\n        collectionSlug={collectionSlug}\n        docID={docID}\n        drawerSlug={drawerSlug}\n        globalSlug={globalSlug}\n      />\n    </Drawer>\n  )\n}\n\nexport const useVersionDrawer = ({\n  collectionSlug,\n  docID,\n  globalSlug,\n}: {\n  collectionSlug?: string\n  docID?: number | string\n  globalSlug?: string\n}) => {\n  const drawerDepth = useEditDepth()\n  const uuid = useId()\n  const { closeModal, modalState, openModal, toggleModal } = useModal()\n  const [isOpen, setIsOpen] = useState(false)\n\n  const drawerSlug = formatVersionDrawerSlug({\n    depth: drawerDepth,\n    uuid,\n  })\n\n  useEffect(() => {\n    setIsOpen(Boolean(modalState[drawerSlug]?.isOpen))\n  }, [modalState, drawerSlug])\n\n  const toggleDrawer = useCallback(() => {\n    toggleModal(drawerSlug)\n  }, [toggleModal, drawerSlug])\n\n  const closeDrawer = useCallback(() => {\n    closeModal(drawerSlug)\n  }, [drawerSlug, closeModal])\n\n  const openDrawer = useCallback(() => {\n    openModal(drawerSlug)\n  }, [drawerSlug, openModal])\n\n  const MemoizedDrawer = useMemo(() => {\n    return () => (\n      <VersionDrawer\n        collectionSlug={collectionSlug}\n        docID={docID}\n        drawerSlug={drawerSlug}\n        globalSlug={globalSlug}\n      />\n    )\n  }, [collectionSlug, docID, drawerSlug, globalSlug])\n\n  return useMemo(\n    () => ({\n      closeDrawer,\n      Drawer: MemoizedDrawer,\n      drawerDepth,\n      drawerSlug,\n      isDrawerOpen: isOpen,\n      openDrawer,\n      toggleDrawer,\n    }),\n    [MemoizedDrawer, closeDrawer, drawerDepth, drawerSlug, isOpen, openDrawer, toggleDrawer],\n  )\n}\n"], "names": ["c", "_c", "Drawer", "LoadingOverlay", "toast", "useDocumentInfo", "useEditDepth", "useModal", "useServerFunctions", "useTranslation", "useSearchParams", "React", "useCallback", "useEffect", "useId", "useMemo", "useRef", "useState", "baseClass", "formatVersionDrawerSlug", "depth", "uuid", "VersionDrawerContent", "props", "collectionSlug", "docID", "drawerSlug", "globalSlug", "isTrashed", "closeModal", "searchParams", "prevSearchParams", "renderDocument", "DocumentView", "setDocumentView", "undefined", "isLoading", "setIsLoading", "hasRenderedDocument", "t", "getDocumentView", "fetchDocumentView", "isGlobal", "Boolean", "entitySlug", "result", "paramsOverride", "segments", "String", "filter", "redirectAfterDelete", "redirectAfterDuplicate", "Object", "fromEntries", "entries", "versions", "disable<PERSON>utter", "useVersionDrawerCreatedAtCell", "Document", "error", "message", "current", "_jsx", "VersionDrawer", "$", "t0", "className", "gutter", "slug", "title", "children", "useVersionDrawer", "<PERSON><PERSON><PERSON><PERSON>", "modalState", "openModal", "toggleModal", "isOpen", "setIsOpen", "t1", "t2", "t3", "t4", "toggle<PERSON>rawer", "t5", "closeDrawer", "t6", "openDrawer", "t7", "MemoizedDrawer", "t8", "isDrawerOpen"], "mappings": ";;;;;;;;;;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SACEC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,eAAe,EACfC,YAAY,EACZC,QAAQ,EACRC,kBAAkB,EAClBC,cAAc,QACT;;AACP,SAASC,eAAe,QAAQ;AAIhC,OAAOC,KAAA,IAASC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ;AAfhF;;;;;;AAiBO,MAAMC,SAAA,GAAY;AAClB,MAAMC,uBAAA,GAA0BA;QAAC,EACtCC,KAAK,EACLC,IAAAA,EAID;WAAK,kBAA2BA,IAAA,GAATD,KAAA,EAAA,KAAS,CAAM;;AAEhC,MAAME,oBAAA,GAKPC,KAAA;IACJ,MAAM,EAAEC,cAAc,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAAA,EAAY,GAAGJ,KAAA;IAC1D,MAAM,EAAEK,SAAAA,EAAW,GAAGvB,4OAAA;IACtB,MAAM,EAAEwB,UAAAA,EAAY,OAAGtB,iOAAA;IACvB,MAAMuB,YAAA,OAAepB,kLAAA;IACrB,MAAMqB,gBAAA,OAAmBf,iMAAA,EAAOc,YAAA;IAEhC,MAAM,EAAEE,cAAAA,EAAgB,OAAGxB,2OAAA;IAE3B,MAAM,CAACyB,YAAA,EAAcC,eAAA,CAAgB,OAAGjB,mMAAA,EAA0BkB,SAAA;IAClE,MAAM,CAACC,SAAA,EAAWC,YAAA,CAAa,GAAGpB,uMAAA,EAAS;IAC3C,MAAMqB,mBAAA,OAAsBtB,iMAAA,EAAO;IACnC,MAAM,EAAEuB,CAAAA,EAAG,OAAG9B,0QAAA;IAEd,MAAM+B,eAAA,OAAkB5B,sMAAA;8DACrBa,OAAA;YACC,MAAMgB,iBAAA;uFAAoB,MAAAA,CAAA;oBACxBJ,YAAA,CAAa;oBAEb,IAAI;wBACF,MAAMK,QAAA,GAAWC,OAAA,CAAQhB,UAAA;wBACzB,MAAMiB,UAAA,8BAAapB,cAAA,+BAAkBG,UAAA;wBAErC,MAAMkB,MAAA,GAAS,MAAMb,cAAA,CAAe;4BAClCR,cAAA,EAAgBoB,UAAA;4BAChBnB,KAAA,EAAAA,OAAA;4BACAC,UAAA;4BACAoB,cAAA,EAAgB;gCACdC,QAAA,EAAU;oCACRL,QAAA,GAAW,YAAY;oCACvBE,UAAA,E;uCACIhB,SAAA,GAAY;wCAAC;qCAAQ,GAAG,EAAE;oCAC9Bc,QAAA,GAAWP,SAAA,GAAYa,MAAA,CAAOvB,OAAA;oCAC9B;iCACD,CAACwB,MAAM,CAACN,OAAA;4BACX;4BACAO,mBAAA,EAAqB;4BACrBC,sBAAA,EAAwB;4BACxBrB,YAAA,EAAcsB,MAAA,CAAOC,WAAW,CAACvB,YAAA,CAAawB,OAAO;4BACrDC,QAAA,EAAU;gCACRC,aAAA,EAAe;gCACfC,6BAAA,EAA+B;4BACjC;wBACF;wBAEA,oDAAIZ,MAAA,CAAQa,QAAA,EAAU;4BACpBxB,eAAA,CAAgBW,MAAA,CAAOa,QAAQ;4BAC/BrB,YAAA,CAAa;wBACf;oBACF,EAAE,OAAOsB,KAAA,EAAO;wBACdvD,8NAAA,CAAMuD,KAAK,gDAACA,KAAA,CAAOC,OAAA,KAAWrB,CAAA,CAAE;wBAChCV,UAAA,CAAWH,UAAA;oBACX,kEAAA;oBACF;gBACF;;YAEA,KAAKe,iBAAA;QACP;4DACA;QACEZ,UAAA;QACAL,cAAA;QACAE,UAAA;QACAC,UAAA;QACAC,SAAA;QACAI,cAAA;QACAF,YAAA;QACAS,CAAA;KACD;QAGH1B,oMAAA;0CAAU;YACR,IAAI,CAACyB,mBAAA,CAAoBuB,OAAO,IAAI9B,gBAAA,CAAiB8B,OAAO,KAAK/B,YAAA,EAAc;gBAC7EC,gBAAA,CAAiB8B,OAAO,GAAG/B,YAAA;gBAC3BU,eAAA,CAAgBf,KAAA;gBAChBa,mBAAA,CAAoBuB,OAAO,GAAG;YAChC;QACF;yCAAG;QAACpC,KAAA;QAAOe,eAAA;QAAiBV,YAAA;KAAa;IAEzC,IAAIM,SAAA,EAAW;QACb,OAAA,WAAA,OAAO0B,uMAAA,EAAC3D,uOAAA,EAAA,CAAA;IACV;IAEA,OAAO8B,YAAA;AACT;AACO,MAAM8B,aAAA,GAKRxC,KAAA;IAAA,MAAAyC,CAAA,OAAA/D,0MAAA,EAAA;IACH,MAAA,EAAAuB,cAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,UAAAA,EAAA,GAA0DJ,KAAA;IAC1D,MAAA,EAAAgB,CAAAA,EAAA,OAAc9B,0QAAA;IAAA,IAAAwD,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAAxC,cAAA,IAAAwC,CAAA,CAAA,EAAA,KAAAvC,KAAA,IAAAuC,CAAA,CAAA,EAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,EAAA,KAAArC,UAAA,IAAAqC,CAAA,CAAA,EAAA,KAAAzB,CAAA,EAAA;QAGZ0B,EAAA,OAAAH,uMAAA,EAAA5D,+NAAA,EAAA;YAAAgE,SAAA,EAAAhD,SAAA;YAAAiD,MAAA,EAAA;YAAAC,IAAA,EAGQ1C,UAAA;YAAA2C,KAAA,EACC9B,CAAA,CAAE;YAAA+B,QAAA,MAETR,uMAAA,EAAAxC,oBAAA,EAAA;gBAAAE,cAAA;gBAAAC,KAAA;gBAAAC,UAAA;gBAAAC;YAAA,C;;;;;;;;;;;WANFsC,E;CAcJ;AAEO,MAAMM,gBAAA,IAAmBN,EAAA;IAAA,MAAAD,CAAA,OAAA/D,0MAAA,EAAA;IAAC,MAAA,EAAAuB,cAAA,EAAAC,KAAA,EAAAE,UAAAA,EAAA,GAAAsC,EAQhC;IACC,MAAAO,WAAA,GAAoBlE,yOAAA;IACpB,MAAAe,IAAA,OAAaP,gMAAA;IACb,MAAA,EAAAe,UAAA,EAAA4C,UAAA,EAAAC,SAAA,EAAAC,WAAAA,EAAA,OAA2DpE,iOAAA;IAC3D,MAAA,CAAAqE,MAAA,EAAAC,SAAA,CAAA,GAA4B5D,uMAAA,EAAA,KAAS;IAAA,IAAA6D,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAQ,WAAA,IAAAR,CAAA,CAAA,EAAA,KAAA3C,IAAA,EAAA;QAElByD,EAAA,GAAA3D,uBAAA,CAAA;YAAAC,KAAA,EACVoD,WAAA;YAAAnD;QAAA,CAET;QAAA2C,CAAA,CAAA,EAAA,GAAAQ,WAAA;QAAAR,CAAA,CAAA,EAAA,GAAA3C,IAAA;QAAA2C,CAAA,CAAA,EAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,EAAA;IAAA;IAHA,MAAAtC,UAAA,GAAmBoD,EAGnB;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,EAAA,KAAAS,UAAA,EAAA;QAEUM,EAAA,GAAAA,CAAA;;YACRF,SAAA,CAAUlC,OAAA,2BAAQ8B,UAAU,CAAC/C,UAAA,CAAA,kFAAAkD,MAAa;QAAA;QACzCI,EAAA,GAAA;YAACP,UAAA;YAAY/C,UAAA;SAAA;QAAWsC,CAAA,CAAA,EAAA,GAAAtC,UAAA;QAAAsC,CAAA,CAAA,EAAA,GAAAS,UAAA;QAAAT,CAAA,CAAA,EAAA,GAAAe,EAAA;QAAAf,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAf,CAAA,CAAA,EAAA;QAAAgB,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;QAF3BnD,oMAAA,EAAUkE,EAEV,EAAGC,EAAwB;IAAA,IAAAC,EAAA;IAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,EAAA,KAAAW,WAAA,EAAA;QAEMM,EAAA,GAAAA,CAAA;YAC/BN,WAAA,CAAYjD,UAAA;QAAA;QACdsC,CAAA,CAAA,EAAA,GAAAtC,UAAA;QAAAsC,CAAA,CAAA,EAAA,GAAAW,WAAA;QAAAX,CAAA,CAAA,EAAA,GAAAiB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjB,CAAA,CAAA,EAAA;IAAA;IAFA,MAAAkB,YAAA,GAAqBD,EAEO;IAAA,IAAAE,EAAA;IAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAnC,UAAA,IAAAmC,CAAA,CAAA,GAAA,KAAAtC,UAAA,EAAA;QAEIyD,EAAA,GAAAA,CAAA;YAC9BtD,UAAA,CAAWH,UAAA;QAAA;QACbsC,CAAA,CAAA,GAAA,GAAAnC,UAAA;QAAAmC,CAAA,CAAA,GAAA,GAAAtC,UAAA;QAAAsC,CAAA,CAAA,GAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,GAAA;IAAA;IAFA,MAAAoB,WAAA,GAAoBD,EAEO;IAAA,IAAAE,EAAA;IAAA,IAAArB,CAAA,CAAA,GAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,GAAA,KAAAU,SAAA,EAAA;QAEIW,EAAA,GAAAA,CAAA;YAC7BX,SAAA,CAAUhD,UAAA;QAAA;QACZsC,CAAA,CAAA,GAAA,GAAAtC,UAAA;QAAAsC,CAAA,CAAA,GAAA,GAAAU,SAAA;QAAAV,CAAA,CAAA,GAAA,GAAAqB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAArB,CAAA,CAAA,GAAA;IAAA;IAFA,MAAAsB,UAAA,GAAmBD,EAEO;IAAA,IAAAE,EAAA;IAAA,IAAAvB,CAAA,CAAA,GAAA,KAAAxC,cAAA,IAAAwC,CAAA,CAAA,GAAA,KAAAvC,KAAA,IAAAuC,CAAA,CAAA,GAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,GAAA,KAAArC,UAAA,EAAA;QAGjB4D,EAAA,GAAAA,CAAA,OACLzB,uMAAA,EAAAC,aAAA,EAAA;gBAAAvC,cAAA;gBAAAC,KAAA;gBAAAC,UAAA;gBAAAC;YAAA,C;;;;;;;;;IAFJ,MAAA6D,cAAA,GACSD,E;IAQyC,IAAAE,EAAA;IAAA,IAAAzB,CAAA,CAAA,GAAA,KAAAwB,cAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAoB,WAAA,IAAApB,CAAA,CAAA,GAAA,KAAAQ,WAAA,IAAAR,CAAA,CAAA,GAAA,KAAAtC,UAAA,IAAAsC,CAAA,CAAA,GAAA,KAAAY,MAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAsB,UAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAkB,YAAA,EAAA;QAGzCO,EAAA,GAAA;YAAAL,WAAA;YAAAlF,MAAA,EAEGsF,cAAA;YAAAhB,WAAA;YAAA9C,UAAA;YAAAgE,YAAA,EAGMd,MAAA;YAAAU,UAAA;YAAAJ;QAAA;QAGhBlB,CAAA,CAAA,GAAA,GAAAwB,cAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAoB,WAAA;QAAApB,CAAA,CAAA,GAAA,GAAAQ,WAAA;QAAAR,CAAA,CAAA,GAAA,GAAAtC,UAAA;QAAAsC,CAAA,CAAA,GAAA,GAAAY,MAAA;QAAAZ,CAAA,CAAA,GAAA,GAAAsB,UAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAkB,YAAA;QAAAlB,CAAA,CAAA,GAAA,GAAAyB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAzB,CAAA,CAAA,GAAA;IAAA;IAAA,OAROyB,EAQP;AAAA,CAGJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1841, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/SelectComparison/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/SelectComparison/index.tsx"], "sourcesContent": ["'use client'\n\nimport { fieldBaseClass, ReactSelect, useTranslation } from '@payloadcms/ui'\nimport React, { memo, useCallback, useMemo } from 'react'\n\nimport type { CompareOption } from '../Default/types.js'\n\nimport './index.scss'\n\nimport type { Props } from './types.js'\n\nimport { useVersionDrawer } from './VersionDrawer/index.js'\n\nconst baseClass = 'compare-version'\n\nexport const SelectComparison: React.FC<Props> = memo((props) => {\n  const {\n    collectionSlug,\n    docID,\n    globalSlug,\n    onChange: onChangeFromProps,\n    versionFromID,\n    versionFromOptions,\n  } = props\n  const { t } = useTranslation()\n\n  const { Drawer, openDrawer } = useVersionDrawer({ collectionSlug, docID, globalSlug })\n\n  const options = useMemo(() => {\n    return [\n      ...versionFromOptions,\n      {\n        label: <span className={`${baseClass}-moreVersions`}>{t('version:moreVersions')}</span>,\n        value: 'more',\n      },\n    ]\n  }, [t, versionFromOptions])\n\n  const currentOption = useMemo(\n    () => versionFromOptions.find((option) => option.value === versionFromID),\n    [versionFromOptions, versionFromID],\n  )\n\n  const onChange = useCallback(\n    (val: CompareOption) => {\n      if (val.value === 'more') {\n        openDrawer()\n        return\n      }\n      onChangeFromProps(val)\n    },\n    [onChangeFromProps, openDrawer],\n  )\n\n  return (\n    <div className={[fieldBaseClass, baseClass].filter(Boolean).join(' ')}>\n      <ReactSelect\n        isClearable={false}\n        isSearchable={false}\n        onChange={onChange}\n        options={options}\n        placeholder={t('version:selectVersionToCompare')}\n        value={currentOption}\n      />\n      <Drawer />\n    </div>\n  )\n})\n"], "names": ["fieldBaseClass", "ReactSelect", "useTranslation", "React", "memo", "useCallback", "useMemo", "useVersionDrawer", "baseClass", "SelectComparison", "props", "collectionSlug", "docID", "globalSlug", "onChange", "onChangeFromProps", "versionFromID", "versionFromOptions", "t", "Drawer", "openDrawer", "options", "label", "_jsx", "className", "value", "currentOption", "find", "option", "val", "_jsxs", "filter", "Boolean", "join", "isClearable", "isSearchable", "placeholder"], "mappings": ";;;;;AAEA,SAASA,cAAc,EAAEC,WAAW,EAAEC,cAAc,QAAQ;;AAC5D,OAAOC,KAAA,IAASC,IAAI,EAAEC,WAAW,EAAEC,OAAO,QAAQ;AAQlD,SAASC,gBAAgB,QAAQ;AAXjC;;;;;AAaA,MAAMC,SAAA,GAAY;AAEX,MAAMC,gBAAA,GAAA,WAAA,OAAoCL,+LAAA,GAAMM,KAAA;IACrD,MAAM,EACJC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,QAAA,EAAUC,iBAAiB,EAC3BC,aAAa,EACbC,kBAAAA,EACD,GAAGP,KAAA;IACJ,MAAM,EAAEQ,CAAAA,EAAG,OAAGhB,0QAAA;IAEd,MAAM,EAAEiB,MAAM,EAAEC,UAAAA,EAAY,OAAGb,+PAAA,EAAiB;QAAEI,cAAA;QAAgBC,KAAA;QAAOC;IAAW;IAEpF,MAAMQ,OAAA,OAAUf,kMAAA;6CAAQ;YACtB,OAAO,C;mBACFW,kBAAA;gBACH;oBACEK,KAAA,EAAA,WAAA,OAAOC,uMAAA,EAAC,QAAA;wBAAKC,SAAA,EAAW,GAAG,OAAAhB,SAAA,EAAA,cAAwB;kCAAGU,CAAA,CAAE;;oBACxDO,KAAA,EAAO;gBACT;aACD;QACH;4CAAG;QAACP,CAAA;QAAGD,kBAAA;KAAmB;IAE1B,MAAMS,aAAA,OAAgBpB,kMAAA;mDACpB,IAAMW,kBAAA,CAAmBU,IAAI;4DAAEC,MAAA,GAAWA,MAAA,CAAOH,KAAK,KAAKT,aAAA;;kDAC3D;QAACC,kBAAA;QAAoBD,aAAA;KAAc;IAGrC,MAAMF,QAAA,OAAWT,sMAAA;mDACdwB,GAAA;YACC,IAAIA,GAAA,CAAIJ,KAAK,KAAK,QAAQ;gBACxBL,UAAA;gBACA;YACF;YACAL,iBAAA,CAAkBc,GAAA;QACpB;iDACA;QAACd,iBAAA;QAAmBK,UAAA;KAAW;IAGjC,OAAA,WAAA,OACEU,wMAAA,EAAC,OAAA;QAAIN,SAAA,EAAW;YAACxB,uOAAA;YAAgBQ,SAAA;SAAU,CAACuB,MAAM,CAACC,OAAA,EAASC,IAAI,CAAC;;8BAC/DV,uMAAA,EAACtB,oOAAA,EAAA;gBACCiC,WAAA,EAAa;gBACbC,YAAA,EAAc;gBACdrB,QAAA,EAAUA,QAAA;gBACVO,OAAA,EAASA,OAAA;gBACTe,WAAA,EAAalB,CAAA,CAAE;gBACfO,KAAA,EAAOC;;8BAETH,uMAAA,EAACJ,MAAA,EAAA,CAAA;SAAA;;AAGP", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/SelectLocales/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/SelectLocales/index.tsx"], "sourcesContent": ["'use client'\n\nimport { AnimateHeight } from '@payloadcms/ui'\nimport { PillSelector, type SelectablePill } from '@payloadcms/ui'\nimport React from 'react'\n\nconst baseClass = 'select-version-locales'\n\nexport type SelectedLocaleOnChange = (args: { locales: SelectablePill[] }) => void\nexport type Props = {\n  locales: SelectablePill[]\n  localeSelectorOpen: boolean\n  onChange: SelectedLocaleOnChange\n}\n\nexport const SelectLocales: React.FC<Props> = ({ locales, localeSelectorOpen, onChange }) => {\n  return (\n    <AnimateHeight\n      className={baseClass}\n      height={localeSelectorOpen ? 'auto' : 0}\n      id={`${baseClass}-locales`}\n    >\n      <PillSelector\n        onClick={({ pill }) => {\n          const newLocales = locales.map((locale) => {\n            if (locale.name === pill.name) {\n              return {\n                ...locale,\n                selected: !pill.selected,\n              }\n            } else {\n              return locale\n            }\n          })\n          onChange({ locales: newLocales })\n        }}\n        pills={locales}\n      />\n    </AnimateHeight>\n  )\n}\n"], "names": ["AnimateHeight", "PillSelector", "React", "baseClass", "SelectLocales", "locales", "localeSelectorOpen", "onChange", "_jsx", "className", "height", "id", "onClick", "pill", "newLocales", "map", "locale", "name", "selected", "pills"], "mappings": ";;;;;AAEA,SAASA,aAAa,QAAQ;AAE9B,OAAOE,KAAA,MAAW;AAJlB;;;;;AAMA,MAAMC,SAAA,GAAY;AASX,MAAMC,aAAA,GAAiCA;QAAC,EAAEC,OAAO,EAAEC,kBAAkB,EAAEC,QAAAA,EAAU;IACtF,OAAA,WAAA,OACEC,uMAAA,EAACR,sOAAA,EAAA;QACCS,SAAA,EAAWN,SAAA;QACXO,MAAA,EAAQJ,kBAAA,GAAqB,SAAS;QACtCK,EAAA,EAAI,GAAG,OAAAR,SAAA,EAAA,SAAmB;kBAE1B,WAAA,OAAAK,uMAAA,EAACP,qOAAA,EAAA;YACCW,OAAA,EAASA;oBAAC,EAAEC,IAAAA,EAAM;gBAChB,MAAMC,UAAA,GAAaT,OAAA,CAAQU,GAAG,EAAEC,MAAA;oBAC9B,IAAIA,MAAA,CAAOC,IAAI,KAAKJ,IAAA,CAAKI,IAAI,EAAE;wBAC7B,OAAO;4BACL,GAAGD,MAAM;4BACTE,QAAA,EAAU,CAACL,IAAA,CAAKK,QAAAA;wBAClB;oBACF,OAAO;wBACL,OAAOF,MAAA;oBACT;gBACF;gBACAT,QAAA,CAAS;oBAAEF,OAAA,EAASS;gBAAW;YACjC;YACAK,KAAA,EAAOd;;;AAIf", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/Default/SelectedLocalesContext.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/Default/SelectedLocalesContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, use } from 'react'\n\ntype SelectedLocalesContextType = {\n  selectedLocales: string[]\n}\n\nexport const SelectedLocalesContext = createContext<SelectedLocalesContextType>({\n  selectedLocales: [],\n})\n\nexport const useSelectedLocales = () => use(SelectedLocalesContext)\n"], "names": ["createContext", "use", "SelectedLocalesContext", "selectedLocales", "useSelectedLocales"], "mappings": ";;;;;;AAEA,SAASA,aAAa,EAAEC,GAAG,QAAQ;AAFnC;;AAQO,MAAMC,sBAAA,GAAA,WAAA,OAAyBF,wMAAA,EAA0C;IAC9EG,eAAA,EAAiB,EAAA;AACnB;AAEO,MAAMC,kBAAA,GAAqBA,CAAA,OAAMH,8LAAA,EAAIC,sBAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1984, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/Default/SetStepNav.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/Default/SetStepNav.tsx"], "sourcesContent": ["'use client'\n\nimport type { ClientCollectionConfig, ClientGlobalConfig } from 'payload'\nimport type React from 'react'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { useConfig, useDocumentTitle, useLocale, useStepNav, useTranslation } from '@payloadcms/ui'\nimport { formatAdminURL } from 'payload/shared'\nimport { useEffect } from 'react'\n\nexport const SetStepNav: React.FC<{\n  readonly collectionConfig?: ClientCollectionConfig\n  readonly globalConfig?: ClientGlobalConfig\n  readonly id?: number | string\n  readonly isTrashed?: boolean\n  versionToCreatedAtFormatted?: string\n  versionToID?: string\n}> = ({\n  id,\n  collectionConfig,\n  globalConfig,\n  isTrashed,\n  versionToCreatedAtFormatted,\n  versionToID,\n}) => {\n  const { config } = useConfig()\n  const { setStepNav } = useStepNav()\n  const { i18n, t } = useTranslation()\n  const locale = useLocale()\n  const { title } = useDocumentTitle()\n\n  useEffect(() => {\n    const {\n      routes: { admin: adminRoute },\n    } = config\n\n    if (collectionConfig) {\n      const collectionSlug = collectionConfig.slug\n\n      const pluralLabel = collectionConfig.labels?.plural\n\n      const docBasePath: `/${string}` = isTrashed\n        ? `/collections/${collectionSlug}/trash/${id}`\n        : `/collections/${collectionSlug}/${id}`\n\n      const nav = [\n        {\n          label: getTranslation(pluralLabel, i18n),\n          url: formatAdminURL({\n            adminRoute,\n            path: `/collections/${collectionSlug}`,\n          }),\n        },\n      ]\n\n      if (isTrashed) {\n        nav.push({\n          label: t('general:trash'),\n          url: formatAdminURL({\n            adminRoute,\n            path: `/collections/${collectionSlug}/trash`,\n          }),\n        })\n      }\n\n      nav.push(\n        {\n          label: title,\n          url: formatAdminURL({\n            adminRoute,\n            path: docBasePath,\n          }),\n        },\n        {\n          label: t('version:versions'),\n          url: formatAdminURL({\n            adminRoute,\n            path: `${docBasePath}/versions`,\n          }),\n        },\n        {\n          label: versionToCreatedAtFormatted,\n          url: undefined,\n        },\n      )\n\n      setStepNav(nav)\n      return\n    }\n\n    if (globalConfig) {\n      const globalSlug = globalConfig.slug\n\n      setStepNav([\n        {\n          label: globalConfig.label,\n          url: formatAdminURL({\n            adminRoute,\n            path: `/globals/${globalSlug}`,\n          }),\n        },\n        {\n          label: t('version:versions'),\n          url: formatAdminURL({\n            adminRoute,\n            path: `/globals/${globalSlug}/versions`,\n          }),\n        },\n        {\n          label: versionToCreatedAtFormatted,\n        },\n      ])\n    }\n  }, [\n    config,\n    setStepNav,\n    id,\n    isTrashed,\n    locale,\n    t,\n    i18n,\n    collectionConfig,\n    globalConfig,\n    title,\n    versionToCreatedAtFormatted,\n    versionToID,\n  ])\n\n  return null\n}\n"], "names": ["c", "_c", "getTranslation", "useConfig", "useDocumentTitle", "useLocale", "useStepNav", "useTranslation", "formatAdminURL", "useEffect", "SetStepNav", "t0", "$", "id", "collectionConfig", "globalConfig", "isTrashed", "versionToCreatedAtFormatted", "versionToID", "config", "setStepNav", "i18n", "t", "locale", "title", "t1", "routes", "t2", "admin", "adminRoute", "collectionSlug", "slug", "plural<PERSON><PERSON>l", "labels", "plural", "doc<PERSON><PERSON><PERSON><PERSON>", "nav", "label", "url", "path", "push", "undefined", "globalSlug"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;AAKA,SAASC,cAAc,QAAQ;AAC/B,SAASC,SAAS,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,cAAc,QAAQ;;;AACnF,SAASC,cAAc,QAAQ;AAC/B,SAASC,SAAS,QAAQ;AAR1B;;;;;;AAUO,MAAMC,UAAA,IAORC,EAAA;IAAA,MAAAC,CAAA,OAAAX,0MAAA,EAAA;IAAC,MAAA,EAAAY,EAAA,EAAAC,gBAAA,EAAAC,YAAA,EAAAC,SAAA,EAAAC,2BAAA,EAAAC,WAAAA,EAAA,GAAAP,EAOL;IACC,MAAA,EAAAQ,MAAAA,EAAA,OAAmBhB,gQAAA;IACnB,MAAA,EAAAiB,UAAAA,EAAA,OAAuBd,mOAAA;IACvB,MAAA,EAAAe,IAAA,EAAAC,CAAAA,EAAA,OAAoBf,0QAAA;IACpB,MAAAgB,MAAA,OAAelB,kOAAA;IACf,MAAA,EAAAmB,KAAAA,EAAA,OAAkBpB,yOAAA;IAAA,IAAAqB,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAE,gBAAA,IAAAF,CAAA,CAAA,EAAA,KAAAO,MAAA,IAAAP,CAAA,CAAA,EAAA,KAAAG,YAAA,IAAAH,CAAA,CAAA,EAAA,KAAAS,IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAC,EAAA,IAAAD,CAAA,CAAA,EAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAQ,UAAA,IAAAR,CAAA,CAAA,EAAA,KAAAU,CAAA,IAAAV,CAAA,CAAA,EAAA,KAAAY,KAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAK,2BAAA,EAAA;QAERQ,EAAA,GAAAA,CAAA;YACR,MAAA,EAAAC,MAAA,EAAAC,EAAAA,EAAA,GAEIR,MAAA;YADM,MAAA,EAAAS,KAAA,EAAAC,UAAAA,EAAA,GAAAF,EAAqB;YAAA,IAG3Bb,gBAAA,EAAA;;gBACF,MAAAgB,cAAA,GAAuBhB,gBAAA,CAAAiB,IAAA;gBAEvB,MAAAC,WAAA,gDAAoBC,MAAA,6DAAAnB,gBAAA,SAAAoB,MAAA;gBAEpB,MAAAC,WAAA,GAAkCnB,SAAA,GAC9B,gBAAwCH,EAAA,KAAxBiB,cAAA,EAAA,WAAwB,CAAI,YAC5C,uBAAgBA,cAAA,EAAA,KAAkB,CAAI,MAAJjB,EAAA;gBAEtC,MAAAuB,GAAA,GAAA;oBAAA;wBAAAC,KAAA,MAEWnC,kOAAA,EAAe8B,WAAA,EAAaX,IAAA;wBAAAiB,GAAA,MAC9B9B,6MAAA,EAAA;4BAAAqB,UAAA;4BAAAU,IAAA,EAEG,gBAAgB,OAAAT,cAAA;wBAAgB,CACxC;oBAAA;iBAAA;gBAEH,IAEGd,SAAA,EAAA;oBACFoB,GAAA,CAAAI,IAAA,CAAA;wBAAAH,KAAA,EACSf,CAAA,CAAE;wBAAAgB,GAAA,EACJ9B,iNAAA,EAAA;4BAAAqB,UAAA;4BAAAU,IAAA,EAEG,gBAAgB,OAAAT,cAAA,EAAA;wBAAsB,CAC9C;oBAAA,CACF;gBAAA;gBAGFM,GAAA,CAAAI,IAAA,CAAA;oBAAAH,KAAA,EAEWb,KAAA;oBAAAc,GAAA,MACF9B,6MAAA,EAAA;wBAAAqB,UAAA;wBAAAU,IAAA,EAEGJ;oBAAA,CACR;gBAAA,GAAA;oBAAAE,KAAA,EAGOf,CAAA,CAAE;oBAAAgB,GAAA,MACJ9B,6MAAA,EAAA;wBAAAqB,UAAA;wBAAAU,IAAA,EAEG,GAAG,OAAAJ,WAAA,EAAA;oBAAsB,CACjC;gBAAA,GAAA;oBAAAE,KAAA,EAGOpB,2BAAA;oBAAAqB,GAAA,EAAAG;gBAAA,CAET;gBAGFrB,UAAA,CAAWgB,GAAA;gBAAA;YAAA;YAAA,IAITrB,YAAA,EAAA;gBACF,MAAA2B,UAAA,GAAmB3B,YAAA,CAAAgB,IAAA;gBAEnBX,UAAA,CAAA;oBAAA;wBAAAiB,KAAA,EAEWtB,YAAA,CAAAsB,KAAA;wBAAAC,GAAA,MACF9B,6MAAA,EAAA;4BAAAqB,UAAA;4BAAAU,IAAA,EAEG,YAAY,OAAAG,UAAA;wBAAY,CAChC;oBAAA;oBAAA;wBAAAL,KAAA,EAGOf,CAAA,CAAE;wBAAAgB,GAAA,MACJ9B,6MAAA,EAAA;4BAAAqB,UAAA;4BAAAU,IAAA,EAEG,YAAY,OAAAG,UAAA,EAAA;wBAAqB,CACzC;oBAAA;oBAAA;wBAAAL,KAAA,EAGOpB;oBAAA;iBAEV;YAAA;QAAA;QAELL,CAAA,CAAA,EAAA,GAAAE,gBAAA;QAAAF,CAAA,CAAA,EAAA,GAAAO,MAAA;QAAAP,CAAA,CAAA,EAAA,GAAAG,YAAA;QAAAH,CAAA,CAAA,EAAA,GAAAS,IAAA;QAAAT,CAAA,CAAA,EAAA,GAAAC,EAAA;QAAAD,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAQ,UAAA;QAAAR,CAAA,CAAA,EAAA,GAAAU,CAAA;QAAAV,CAAA,CAAA,EAAA,GAAAY,KAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAK,2BAAA;QAAAL,CAAA,CAAA,GAAA,GAAAa,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAb,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAe,EAAA;IAAA,IAAAf,CAAA,CAAA,GAAA,KAAAE,gBAAA,IAAAF,CAAA,CAAA,GAAA,KAAAO,MAAA,IAAAP,CAAA,CAAA,GAAA,KAAAG,YAAA,IAAAH,CAAA,CAAA,GAAA,KAAAS,IAAA,IAAAT,CAAA,CAAA,GAAA,KAAAC,EAAA,IAAAD,CAAA,CAAA,GAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,GAAA,KAAAW,MAAA,IAAAX,CAAA,CAAA,GAAA,KAAAQ,UAAA,IAAAR,CAAA,CAAA,GAAA,KAAAU,CAAA,IAAAV,CAAA,CAAA,GAAA,KAAAY,KAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAK,2BAAA,IAAAL,CAAA,CAAA,GAAA,KAAAM,WAAA,EAAA;QAAGS,EAAA,GAAA;YACDR,MAAA;YACAC,UAAA;YACAP,EAAA;YACAG,SAAA;YACAO,MAAA;YACAD,CAAA;YACAD,IAAA;YACAP,gBAAA;YACAC,YAAA;YACAS,KAAA;YACAP,2BAAA;YACAC,WAAA;SAAA;QACDN,CAAA,CAAA,GAAA,GAAAE,gBAAA;QAAAF,CAAA,CAAA,GAAA,GAAAO,MAAA;QAAAP,CAAA,CAAA,GAAA,GAAAG,YAAA;QAAAH,CAAA,CAAA,GAAA,GAAAS,IAAA;QAAAT,CAAA,CAAA,GAAA,GAAAC,EAAA;QAAAD,CAAA,CAAA,GAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAW,MAAA;QAAAX,CAAA,CAAA,GAAA,GAAAQ,UAAA;QAAAR,CAAA,CAAA,GAAA,GAAAU,CAAA;QAAAV,CAAA,CAAA,GAAA,GAAAY,KAAA;QAAAZ,CAAA,CAAA,GAAA,GAAAK,2BAAA;QAAAL,CAAA,CAAA,GAAA,GAAAM,WAAA;QAAAN,CAAA,CAAA,GAAA,GAAAe,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAf,CAAA,CAAA,GAAA;IAAA;QA/FDH,oMAAA,EAAUgB,EAkFV,EAAGE,EAaF;IAAA,OAAA;AAAA,CAGH", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/Default/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/Default/index.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  CheckboxInput,\n  ChevronIcon,\n  formatTimeToNow,\n  Gutter,\n  Pill,\n  type SelectablePill,\n  useConfig,\n  useDocumentInfo,\n  useLocale,\n  useRouteTransition,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { usePathname, useRouter, useSearchParams } from 'next/navigation.js'\nimport React, { type FormEventHandler, useCallback, useEffect, useMemo, useState } from 'react'\n\nimport type { CompareOption, DefaultVersionsViewProps } from './types.js'\n\nimport { Restore } from '../Restore/index.js'\nimport './index.scss'\nimport { SelectComparison } from '../SelectComparison/index.js'\nimport { type SelectedLocaleOnChange, SelectLocales } from '../SelectLocales/index.js'\nimport { SelectedLocalesContext } from './SelectedLocalesContext.js'\nimport { SetStepNav } from './SetStepNav.js'\n\nconst baseClass = 'view-version'\n\nexport const DefaultVersionView: React.FC<DefaultVersionsViewProps> = ({\n  canUpdate,\n  modifiedOnly: modified<PERSON><PERSON>lyP<PERSON>,\n  RenderedDiff,\n  selectedLocales: selectedLocalesFromProps,\n  versionFromCreatedAt,\n  versionFromID,\n  versionFromOptions,\n  versionToCreatedAt,\n  versionToCreatedAtFormatted,\n  VersionToCreatedAtLabel,\n  versionToID,\n  versionToStatus,\n}) => {\n  const { config, getEntityConfig } = useConfig()\n  const { code } = useLocale()\n  const { i18n, t } = useTranslation()\n\n  const [locales, setLocales] = useState<SelectablePill[]>([])\n  const [localeSelectorOpen, setLocaleSelectorOpen] = React.useState(false)\n\n  useEffect(() => {\n    if (config.localization) {\n      const updatedLocales = config.localization.locales.map((locale) => {\n        let label = locale.label\n        if (typeof locale.label !== 'string' && locale.label[code]) {\n          label = locale.label[code]\n        }\n\n        return {\n          name: locale.code,\n          Label: label,\n          selected: selectedLocalesFromProps.includes(locale.code),\n        } as SelectablePill\n      })\n      setLocales(updatedLocales)\n    }\n  }, [code, config.localization, selectedLocalesFromProps])\n\n  const { id: originalDocID, collectionSlug, globalSlug, isTrashed } = useDocumentInfo()\n  const { startRouteTransition } = useRouteTransition()\n\n  const { collectionConfig, globalConfig } = useMemo(() => {\n    return {\n      collectionConfig: getEntityConfig({ collectionSlug }),\n      globalConfig: getEntityConfig({ globalSlug }),\n    }\n  }, [collectionSlug, globalSlug, getEntityConfig])\n\n  const router = useRouter()\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  const [modifiedOnly, setModifiedOnly] = useState(modifiedOnlyProp)\n\n  const updateSearchParams = useCallback(\n    (args: {\n      modifiedOnly?: boolean\n      selectedLocales?: SelectablePill[]\n      versionFromID?: string\n    }) => {\n      // If the selected comparison doc or locales change, update URL params so that version page\n      // This is so that RSC can update the version comparison state\n      const current = new URLSearchParams(Array.from(searchParams.entries()))\n\n      if (args?.versionFromID) {\n        current.set('versionFrom', args?.versionFromID)\n      }\n\n      if (args?.selectedLocales) {\n        if (!args.selectedLocales.length) {\n          current.delete('localeCodes')\n        } else {\n          const selectedLocaleCodes: string[] = []\n          for (const locale of args.selectedLocales) {\n            if (locale.selected) {\n              selectedLocaleCodes.push(locale.name)\n            }\n          }\n          current.set('localeCodes', JSON.stringify(selectedLocaleCodes))\n        }\n      }\n\n      if (args?.modifiedOnly === false) {\n        current.set('modifiedOnly', 'false')\n      } else if (args?.modifiedOnly === true) {\n        current.delete('modifiedOnly')\n      }\n\n      const search = current.toString()\n      const query = search ? `?${search}` : ''\n\n      startRouteTransition(() => router.push(`${pathname}${query}`))\n    },\n    [pathname, router, searchParams, startRouteTransition],\n  )\n\n  const onToggleModifiedOnly: FormEventHandler<HTMLInputElement> = useCallback(\n    (event) => {\n      const newModified = (event.target as HTMLInputElement).checked\n      setModifiedOnly(newModified)\n      updateSearchParams({\n        modifiedOnly: newModified,\n      })\n    },\n    [updateSearchParams],\n  )\n\n  const onChangeSelectedLocales: SelectedLocaleOnChange = useCallback(\n    ({ locales }) => {\n      setLocales(locales)\n      updateSearchParams({\n        selectedLocales: locales,\n      })\n    },\n    [updateSearchParams],\n  )\n\n  const onChangeVersionFrom: (val: CompareOption) => void = useCallback(\n    (val) => {\n      updateSearchParams({\n        versionFromID: val.value,\n      })\n    },\n    [updateSearchParams],\n  )\n\n  const { localization } = config\n\n  const versionToTimeAgo = useMemo(\n    () =>\n      t('version:versionAgo', {\n        distance: formatTimeToNow({\n          date: versionToCreatedAt,\n          i18n,\n        }),\n      }),\n    [versionToCreatedAt, i18n, t],\n  )\n\n  const versionFromTimeAgo = useMemo(\n    () =>\n      versionFromCreatedAt\n        ? t('version:versionAgo', {\n            distance: formatTimeToNow({\n              date: versionFromCreatedAt,\n              i18n,\n            }),\n          })\n        : undefined,\n    [versionFromCreatedAt, i18n, t],\n  )\n\n  return (\n    <main className={baseClass}>\n      <Gutter className={`${baseClass}-controls-top`}>\n        <div className={`${baseClass}-controls-top__wrapper`}>\n          <h2>{i18n.t('version:compareVersions')}</h2>\n          <div className={`${baseClass}-controls-top__wrapper-actions`}>\n            <span className={`${baseClass}__modifiedCheckBox`}>\n              <CheckboxInput\n                checked={modifiedOnly}\n                id={'modifiedOnly'}\n                label={i18n.t('version:modifiedOnly')}\n                onToggle={onToggleModifiedOnly}\n              />\n            </span>\n            {localization && (\n              <Pill\n                aria-controls={`${baseClass}-locales`}\n                aria-expanded={localeSelectorOpen}\n                className={`${baseClass}__toggle-locales`}\n                icon={<ChevronIcon direction={localeSelectorOpen ? 'up' : 'down'} />}\n                onClick={() => setLocaleSelectorOpen((localeSelectorOpen) => !localeSelectorOpen)}\n                pillStyle=\"light\"\n                size=\"small\"\n              >\n                <span className={`${baseClass}__toggle-locales-label`}>\n                  {t('general:locales')}:{' '}\n                </span>\n                <span className={`${baseClass}__toggle-locales-list`}>\n                  {locales\n                    .filter((locale) => locale.selected)\n                    .map((locale) => locale.name)\n                    .join(', ')}\n                </span>\n              </Pill>\n            )}\n          </div>\n        </div>\n\n        {localization && (\n          <SelectLocales\n            locales={locales}\n            localeSelectorOpen={localeSelectorOpen}\n            onChange={onChangeSelectedLocales}\n          />\n        )}\n      </Gutter>\n      <Gutter className={`${baseClass}-controls-bottom`}>\n        <div className={`${baseClass}-controls-bottom__wrapper`}>\n          <div className={`${baseClass}__version-from`}>\n            <div className={`${baseClass}__version-from-labels`}>\n              <span>{t('version:comparingAgainst')}</span>\n              {versionFromTimeAgo && (\n                <span className={`${baseClass}__time-elapsed`}>{versionFromTimeAgo}</span>\n              )}\n            </div>\n            <SelectComparison\n              collectionSlug={collectionSlug}\n              docID={originalDocID}\n              globalSlug={globalSlug}\n              onChange={onChangeVersionFrom}\n              versionFromID={versionFromID}\n              versionFromOptions={versionFromOptions}\n            />\n          </div>\n\n          <div className={`${baseClass}__version-to`}>\n            <div className={`${baseClass}__version-to-labels`}>\n              <span>{t('version:currentlyViewing')}</span>\n              <span className={`${baseClass}__time-elapsed`}>{versionToTimeAgo}</span>\n            </div>\n            <div className={`${baseClass}__version-to-version`}>\n              {VersionToCreatedAtLabel}\n              {canUpdate && !isTrashed && (\n                <Restore\n                  className={`${baseClass}__restore`}\n                  collectionConfig={collectionConfig}\n                  globalConfig={globalConfig}\n                  label={collectionConfig?.labels.singular || globalConfig?.label}\n                  originalDocID={originalDocID}\n                  status={versionToStatus}\n                  versionDateFormatted={versionToCreatedAtFormatted}\n                  versionID={versionToID}\n                />\n              )}\n            </div>\n          </div>\n        </div>\n      </Gutter>\n      <SetStepNav\n        collectionConfig={collectionConfig}\n        globalConfig={globalConfig}\n        id={originalDocID}\n        isTrashed={isTrashed}\n        versionToCreatedAtFormatted={versionToCreatedAtFormatted}\n        versionToID={versionToID}\n      />\n      <Gutter className={`${baseClass}__diff-wrap`}>\n        <SelectedLocalesContext value={{ selectedLocales: locales.map((locale) => locale.name) }}>\n          {versionToCreatedAt && RenderedDiff}\n        </SelectedLocalesContext>\n      </Gutter>\n    </main>\n  )\n}\n"], "names": ["CheckboxInput", "ChevronIcon", "formatTimeToNow", "<PERSON><PERSON>", "<PERSON>ll", "useConfig", "useDocumentInfo", "useLocale", "useRouteTransition", "useTranslation", "usePathname", "useRouter", "useSearchParams", "React", "useCallback", "useEffect", "useMemo", "useState", "Rest<PERSON>", "SelectComparison", "SelectLocales", "SelectedLocalesContext", "SetStepNav", "baseClass", "DefaultVersionView", "canUpdate", "modifiedOnly", "modifiedOnlyProp", "RenderedDiff", "selectedLocales", "selectedLocalesFromProps", "versionFromCreatedAt", "versionFromID", "versionFromOptions", "versionToCreatedAt", "versionToCreatedAtFormatted", "VersionToCreatedAtLabel", "versionToID", "versionToStatus", "config", "getEntityConfig", "code", "i18n", "t", "locales", "setLocales", "localeSelectorOpen", "setLocaleSelectorOpen", "localization", "updatedLocales", "map", "locale", "label", "name", "Label", "selected", "includes", "id", "originalDocID", "collectionSlug", "globalSlug", "isTrashed", "startRouteTransition", "collectionConfig", "globalConfig", "router", "pathname", "searchParams", "setModifiedOnly", "updateSearchParams", "args", "current", "URLSearchParams", "Array", "from", "entries", "set", "length", "delete", "selectedLocaleCodes", "push", "JSON", "stringify", "search", "toString", "query", "onToggleModifiedOnly", "event", "newModified", "target", "checked", "onChangeSelectedLocales", "onChangeVersionFrom", "val", "value", "versionToTimeAgo", "distance", "date", "versionFromTimeAgo", "undefined", "_jsxs", "className", "_jsx", "onToggle", "icon", "direction", "onClick", "pillStyle", "size", "filter", "join", "onChange", "docID", "labels", "singular", "status", "versionDateFormatted", "versionID"], "mappings": ";;;;;AAEA,SACEA,aAAa,EACbC,WAAW,EACXC,eAAe,EACfC,MAAM,EACNC,IAAI,EAEJC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,kBAAkB,EAClBC,cAAc,QACT;;;AACP,SAASC,WAAW,EAAEC,SAAS,EAAEC,eAAe,QAAQ;AACxD,OAAOC,KAAA,IAAgCC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,QAAQ;AAIxF,SAASC,OAAO,QAAQ;AAExB,SAASC,gBAAgB,QAAQ;AACjC,SAAsCC,aAAa,QAAQ;AAC3D,SAASC,sBAAsB,QAAQ;AACvC,SAASC,UAAU,QAAQ;AAzB3B;;;;;;;;;;AA2BA,MAAMC,SAAA,GAAY;AAEX,MAAMC,kBAAA,GAAyDA;QAAC,EACrEC,SAAS,EACTC,YAAA,EAAcC,gBAAgB,EAC9BC,YAAY,EACZC,eAAA,EAAiBC,wBAAwB,EACzCC,oBAAoB,EACpBC,aAAa,EACbC,kBAAkB,EAClBC,kBAAkB,EAClBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,WAAW,EACXC,eAAAA,EACD;IACC,MAAM,EAAEC,MAAM,EAAEC,eAAAA,EAAiB,OAAGnC,gQAAA;IACpC,MAAM,EAAEoC,IAAAA,EAAM,GAAGlC,sOAAA;IACjB,MAAM,EAAEmC,IAAI,EAAEC,CAAAA,EAAG,OAAGlC,0QAAA;IAEpB,MAAM,CAACmC,OAAA,EAASC,UAAA,CAAW,OAAG5B,mMAAA,EAA2B,EAAE;IAC3D,MAAM,CAAC6B,kBAAA,EAAoBC,qBAAA,CAAsB,GAAGlC,kMAAA,CAAMI,QAAQ,CAAC;QAEnEF,oMAAA;wCAAU;YACR,IAAIwB,MAAA,CAAOS,YAAY,EAAE;gBACvB,MAAMC,cAAA,GAAiBV,MAAA,CAAOS,YAAY,CAACJ,OAAO,CAACM,GAAG;oEAAEC,MAAA;wBACtD,IAAIC,KAAA,GAAQD,MAAA,CAAOC,KAAK;wBACxB,IAAI,OAAOD,MAAA,CAAOC,KAAK,KAAK,YAAYD,MAAA,CAAOC,KAAK,CAACX,IAAA,CAAK,EAAE;4BAC1DW,KAAA,GAAQD,MAAA,CAAOC,KAAK,CAACX,IAAA,CAAK;wBAC5B;wBAEA,OAAO;4BACLY,IAAA,EAAMF,MAAA,CAAOV,IAAI;4BACjBa,KAAA,EAAOF,KAAA;4BACPG,QAAA,EAAUzB,wBAAA,CAAyB0B,QAAQ,CAACL,MAAA,CAAOV,IAAI;wBACzD;oBACF;;gBACAI,UAAA,CAAWI,cAAA;YACb;QACF;uCAAG;QAACR,IAAA;QAAMF,MAAA,CAAOS,YAAY;QAAElB,wBAAA;KAAyB;IAExD,MAAM,EAAE2B,EAAA,EAAIC,aAAa,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAAA,EAAW,OAAGvD,wOAAA;IACrE,MAAM,EAAEwD,oBAAAA,EAAsB,OAAGtD,2OAAA;IAEjC,MAAM,EAAEuD,gBAAgB,EAAEC,YAAAA,EAAc,OAAGhD,kMAAA;sCAAQ;YACjD,OAAO;gBACL+C,gBAAA,EAAkBvB,eAAA,CAAgB;oBAAEmB;gBAAe;gBACnDK,YAAA,EAAcxB,eAAA,CAAgB;oBAAEoB;gBAAW;YAC7C;QACF;qCAAG;QAACD,cAAA;QAAgBC,UAAA;QAAYpB,eAAA;KAAgB;IAEhD,MAAMyB,MAAA,OAAStD,4KAAA;IACf,MAAMuD,QAAA,OAAWxD,8KAAA;IACjB,MAAMyD,YAAA,OAAevD,kLAAA;IACrB,MAAM,CAACc,YAAA,EAAc0C,eAAA,CAAgB,OAAGnD,mMAAA,EAASU,gBAAA;IAEjD,MAAM0C,kBAAA,OAAqBvD,sMAAA;+DACxBwD,IAAA;YAKC,2FAAA;YACA,8DAAA;YACA,MAAMC,OAAA,GAAU,IAAIC,eAAA,CAAgBC,KAAA,CAAMC,IAAI,CAACP,YAAA,CAAaQ,OAAO;YAEnE,gDAAIL,IAAA,CAAMtC,aAAA,EAAe;gBACvBuC,OAAA,CAAQK,GAAG,CAAC,eAAeN,IAAA,6CAAMtC,aAAA;YACnC;YAEA,gDAAIsC,IAAA,CAAMzC,eAAA,EAAiB;gBACzB,IAAI,CAACyC,IAAA,CAAKzC,eAAe,CAACgD,MAAM,EAAE;oBAChCN,OAAA,CAAQO,MAAM,CAAC;gBACjB,OAAO;oBACL,MAAMC,mBAAA,GAAgC,EAAE;oBACxC,KAAK,MAAM5B,QAAA,IAAUmB,IAAA,CAAKzC,eAAe,CAAE;wBACzC,IAAIsB,QAAA,CAAOI,QAAQ,EAAE;4BACnBwB,mBAAA,CAAoBC,IAAI,CAAC7B,QAAA,CAAOE,IAAI;wBACtC;oBACF;oBACAkB,OAAA,CAAQK,GAAG,CAAC,eAAeK,IAAA,CAAKC,SAAS,CAACH,mBAAA;gBAC5C;YACF;YAEA,KAAIT,IAAA,6CAAM5C,YAAA,MAAiB,OAAO;gBAChC6C,OAAA,CAAQK,GAAG,CAAC,gBAAgB;YAC9B,OAAO,iDAAIN,IAAA,CAAM5C,YAAA,MAAiB,MAAM;gBACtC6C,OAAA,CAAQO,MAAM,CAAC;YACjB;YAEA,MAAMK,MAAA,GAASZ,OAAA,CAAQa,QAAQ;YAC/B,MAAMC,KAAA,GAAQF,MAAA,GAAS,IAAI,CAAQ,MAARA,MAAA,IAAW;YAEtCrB,oBAAA;sEAAqB,IAAMG,MAAA,CAAOe,IAAI,CAAC,GAAcK,KAAA,EAAXnB,QAAA,EAAW,CAAO;;QAC9D;6DACA;QAACA,QAAA;QAAUD,MAAA;QAAQE,YAAA;QAAcL,oBAAA;KAAqB;IAGxD,MAAMwB,oBAAA,OAA2DxE,sMAAA;iEAC9DyE,KAAA;YACC,MAAMC,WAAA,GAAcD,KAAC,CAAME,MAAM,CAAsBC,OAAO;YAC9DtB,eAAA,CAAgBoB,WAAA;YAChBnB,kBAAA,CAAmB;gBACjB3C,YAAA,EAAc8D;YAChB;QACF;+DACA;QAACnB,kBAAA;KAAmB;IAGtB,MAAMsB,uBAAA,GAAkD7E,0MAAA;mEACtD;gBAAC,EAAE8B,OAAO,EAAPA,SAAAA,EAAS;YACVC,UAAA,CAAWD,SAAA;YACXyB,kBAAA,CAAmB;gBACjBxC,eAAA,EAAiBe;YACnB;QACF;kEACA;QAACyB,kBAAA;KAAmB;IAGtB,MAAMuB,mBAAA,OAAoD9E,sMAAA;gEACvD+E,GAAA;YACCxB,kBAAA,CAAmB;gBACjBrC,aAAA,EAAe6D,GAAA,CAAIC,KAAAA;YACrB;QACF;8DACA;QAACzB,kBAAA;KAAmB;IAGtB,MAAM,EAAErB,YAAAA,EAAc,GAAGT,MAAA;IAEzB,MAAMwD,gBAAA,OAAmB/E,kMAAA;wDACvB,IACE2B,CAAA,CAAE,sBAAsB;gBACtBqD,QAAA,MAAU9F,wOAAA,EAAgB;oBACxB+F,IAAA,EAAM/D,kBAAA;oBACNQ;gBACF;YACF;uDACF;QAACR,kBAAA;QAAoBQ,IAAA;QAAMC,CAAA;KAAE;IAG/B,MAAMuD,kBAAA,OAAqBlF,kMAAA;0DACzB,IACEe,oBAAA,GACIY,CAAA,CAAE,sBAAsB;gBACtBqD,QAAA,MAAU9F,wOAAA,EAAgB;oBACxB+F,IAAA,EAAMlE,oBAAA;oBACNW;gBACF;YACF,KACAyD,SAAA;yDACN;QAACpE,oBAAA;QAAsBW,IAAA;QAAMC,CAAA;KAAE;IAGjC,OAAA,WAAA,OACEyD,wMAAA,EAAC,QAAA;QAAKC,SAAA,EAAW9E,SAAA;;8BACf6E,wMAAA,EAACjG,+NAAA,EAAA;gBAAOkG,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,cAAwB;;kCAC5C6E,4MAAA,EAAC,OAAA;wBAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,uBAAiC;;8CAClD+E,uMAAA,EAAC,MAAA;0CAAI5D,IAAA,CAAKC,CAAC,CAAC;;8CACZyD,wMAAA,EAAC,OAAA;gCAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,+BAAyC;;sDAC1D+E,uMAAA,EAAC,QAAA;wCAAKD,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,CAA6B;kDAC/C,WAAA,GAAA+E,2MAAA,EAACtG,sOAAA,EAAA;4CACC0F,OAAA,EAAShE,YAAA;4CACT+B,EAAA,EAAI;4CACJL,KAAA,EAAOV,IAAA,CAAKC,CAAC,CAAC;4CACd4D,QAAA,EAAUjB;;;oCAGbtC,YAAA,IAAA,WAAA,OACCoD,wMAAA,EAAChG,6NAAA,EAAA;wCACC,iBAAe,GAAG,OAAAmB,SAAA,EAAA,SAAmB;wCACrC,iBAAeuB,kBAAA;wCACfuD,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,iBAA2B;wCACzCiF,IAAA,EAAA,WAAA,OAAMF,uMAAA,EAACrG,oOAAA,EAAA;4CAAYwG,SAAA,EAAW3D,kBAAA,GAAqB,OAAO;;wCAC1D4D,OAAA,EAASA,CAAA,GAAM3D,qBAAA,EAAuBD,oBAAA,GAAuB,CAACA,oBAAA;wCAC9D6D,SAAA,EAAU;wCACVC,IAAA,EAAK;;8DAELR,wMAAA,EAAC,QAAA;gDAAKC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,KAAiC;;oDAClDoB,CAAA,CAAE;oDAAmB;oDAAE;iDAAA;;8DAE1B2D,uMAAA,EAAC,QAAA;gDAAKD,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,sBAAgC;0DACjDqB,OAAA,CACEiE,MAAM,EAAE1D,QAAA,GAAWA,QAAA,CAAOI,QAAQ,EAClCL,GAAG,EAAEC,QAAA,GAAWA,QAAA,CAAOE,IAAI,EAC3ByD,IAAI,CAAC;;;;;;;;oBAOjB9D,YAAA,IAAA,WAAA,OACCsD,uMAAA,EAAClF,wOAAA,EAAA;wBACCwB,OAAA,EAASA,OAAA;wBACTE,kBAAA,EAAoBA,kBAAA;wBACpBiE,QAAA,EAAUpB;;;;8BAIhBW,uMAAA,EAACnG,+NAAA,EAAA;gBAAOkG,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,iBAA2B;0BAC/C,WAAA,OAAA6E,wMAAA,EAAC,OAAA;oBAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,0BAAoC;;0CACrD6E,wMAAA,EAAC,OAAA;4BAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,eAAyB;;kDAC1C6E,wMAAA,EAAC,OAAA;oCAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,sBAAgC;;0DACjD+E,uMAAA,EAAC,QAAA;sDAAM3D,CAAA,CAAE;;wCACRuD,kBAAA,IAAA,WAAA,OACCI,uMAAA,EAAC,QAAA;4CAAKD,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,eAAyB;sDAAG2E;;;;kDAGpDI,uMAAA,EAACnF,8OAAA,EAAA;oCACCwC,cAAA,EAAgBA,cAAA;oCAChBqD,KAAA,EAAOtD,aAAA;oCACPE,UAAA,EAAYA,UAAA;oCACZmD,QAAA,EAAUnB,mBAAA;oCACV5D,aAAA,EAAeA,aAAA;oCACfC,kBAAA,EAAoBA;;;;0CAIxBmE,wMAAA,EAAC,OAAA;4BAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,aAAuB;;kDACxC6E,wMAAA,EAAC,OAAA;oCAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,EAA8B;;0DAC/C+E,uMAAA,EAAC,QAAA;sDAAM3D,CAAA,CAAE;;0DACT2D,uMAAA,EAAC,QAAA;4CAAKD,SAAA,EAAW,GAAG,OAAA9E,QAAyB,CAAzB,EAAA;sDAA4BwE;;;;kDAElDK,wMAAA,EAAC,OAAA;oCAAIC,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,qBAA+B;;wCAC/Ca,uBAAA;wCACAX,SAAA,IAAa,CAACoC,SAAA,IAAA,WAAA,OACbyC,uMAAA,EAACpF,4NAAA,EAAA;4CACCmF,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,UAAoB;4CAClCwC,gBAAA,EAAkBA,gBAAA;4CAClBC,YAAA,EAAcA,YAAA;4CACdZ,KAAA,uEAAOW,gBAAA,CAAkBkD,MAAA,CAAOC,QAAA,kEAAYlD,YAAA,CAAcZ,KAAA;4CAC1DM,aAAA,EAAeA,aAAA;4CACfyD,MAAA,EAAQ7E,eAAA;4CACR8E,oBAAA,EAAsBjF,2BAAA;4CACtBkF,SAAA,EAAWhF;;;;;;;;;8BAOvBiE,uMAAA,EAAChF,oOAAA,EAAA;gBACCyC,gBAAA,EAAkBA,gBAAA;gBAClBC,YAAA,EAAcA,YAAA;gBACdP,EAAA,EAAIC,aAAA;gBACJG,SAAA,EAAWA,SAAA;gBACX1B,2BAAA,EAA6BA,2BAAA;gBAC7BE,WAAA,EAAaA;;0BAEfiE,2MAAA,EAACnG,+NAAA,EAAA;gBAAOkG,SAAA,EAAW,GAAG,OAAA9E,SAAA,EAAA,YAAsB;0BAC1C,WAAA,OAAA+E,uMAAA,EAACjF,4PAAA,EAAA;oBAAuByE,KAAA,EAAO;wBAAEjE,eAAA,EAAiBe,OAAA,CAAQM,GAAG,EAAEC,QAAA,GAAWA,QAAA,CAAOE,IAAI;oBAAE;8BACpFnB,kBAAA,IAAsBN;;;;;AAKjC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/utilities/fieldHasChanges.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/utilities/fieldHasChanges.ts"], "sourcesContent": ["export function fieldHasChanges(a: unknown, b: unknown) {\n  return JSON.stringify(a) !== JSON.stringify(b)\n}\n"], "names": ["fieldHasChanges", "a", "b", "JSON", "stringify"], "mappings": ";;;;AAAO,SAASA,gBAAgBC,CAAU,EAAEC,CAAU;IACpD,OAAOC,IAAA,CAAKC,SAAS,CAACH,CAAA,MAAOE,IAAA,CAAKC,SAAS,CAACF,CAAA;AAC9C", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2466, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/utilities/getFieldsForRowComparison.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/utilities/getFieldsForRowComparison.ts"], "sourcesContent": ["import type {\n  ArrayFieldClient,\n  BaseVersionField,\n  BlocksFieldClient,\n  ClientBlock,\n  ClientConfig,\n  ClientField,\n  VersionField,\n} from 'payload'\n\nimport { getUniqueListBy } from 'payload/shared'\n\n/**\n * Get the fields for a row in an iterable field for comparison.\n * - Array fields: the fields of the array field, because the fields are the same for each row.\n * - Blocks fields: the union of fields from the comparison and version row,\n *   because the fields from the version and comparison rows may differ.\n */\nexport function getFieldsForRowComparison({\n  baseVersionField,\n  config,\n  field,\n  row,\n  valueFromRow,\n  valueToRow,\n}: {\n  baseVersionField: BaseVersionField\n  config: ClientConfig\n  field: ArrayFieldClient | BlocksFieldClient\n  row: number\n  valueFromRow: any\n  valueToRow: any\n}): { fields: ClientField[]; versionFields: VersionField[] } {\n  let fields: ClientField[] = []\n  let versionFields: VersionField[] = []\n\n  if (field.type === 'array' && 'fields' in field) {\n    fields = field.fields\n    versionFields = baseVersionField.rows?.length\n      ? baseVersionField.rows[row]\n      : baseVersionField.fields\n  } else if (field.type === 'blocks') {\n    if (valueToRow?.blockType === valueFromRow?.blockType) {\n      const matchedBlock: ClientBlock =\n        config?.blocksMap?.[valueToRow?.blockType] ??\n        (((('blocks' in field || 'blockReferences' in field) &&\n          (field.blockReferences ?? field.blocks)?.find(\n            (block) => typeof block !== 'string' && block.slug === valueToRow?.blockType,\n          )) || {\n          fields: [],\n        }) as ClientBlock)\n\n      fields = matchedBlock.fields\n      versionFields = baseVersionField.rows?.length\n        ? baseVersionField.rows[row]\n        : baseVersionField.fields\n    } else {\n      const matchedVersionBlock =\n        config?.blocksMap?.[valueToRow?.blockType] ??\n        (((('blocks' in field || 'blockReferences' in field) &&\n          (field.blockReferences ?? field.blocks)?.find(\n            (block) => typeof block !== 'string' && block.slug === valueToRow?.blockType,\n          )) || {\n          fields: [],\n        }) as ClientBlock)\n\n      const matchedComparisonBlock =\n        config?.blocksMap?.[valueFromRow?.blockType] ??\n        (((('blocks' in field || 'blockReferences' in field) &&\n          (field.blockReferences ?? field.blocks)?.find(\n            (block) => typeof block !== 'string' && block.slug === valueFromRow?.blockType,\n          )) || {\n          fields: [],\n        }) as ClientBlock)\n\n      fields = getUniqueListBy<ClientField>(\n        [...matchedVersionBlock.fields, ...matchedComparisonBlock.fields],\n        'name',\n      )\n\n      // buildVersionFields already merged the fields of the version and comparison rows together\n      versionFields = baseVersionField.rows?.length\n        ? baseVersionField.rows[row]\n        : baseVersionField.fields\n    }\n  }\n\n  return { fields, versionFields }\n}\n"], "names": ["getUniqueListBy", "getFieldsForRowComparison", "baseVersionField", "config", "field", "row", "valueFromRow", "valueToRow", "fields", "<PERSON>Fields", "type", "rows", "length", "blockType", "matchedBlock", "blocksMap", "blockReferences", "blocks", "find", "block", "slug", "matchedVersionBlock", "matchedComparisonBlock"], "mappings": ";;;;AAUA,SAASA,eAAe,QAAQ;;AAQzB,SAASC,0BAA0B;UACxCC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,YAAY,EACZC,UAAAA,EAQD;IACC,IAAIC,MAAA,GAAwB,EAAE;IAC9B,IAAIC,aAAA,GAAgC,EAAE;IAEtC,IAAIL,KAAA,CAAMM,IAAI,KAAK,WAAW,YAAYN,KAAA,EAAO;;QAC/CI,MAAA,GAASJ,KAAA,CAAMI,MAAM;QACrBC,aAAA,+CAAiCE,IAAI,2DAArBT,gBAAA,OAAuBU,MAAA,IACnCV,gBAAA,CAAiBS,IAAI,CAACN,GAAA,CAAI,GAC1BH,gBAAA,CAAiBM,MAAM;IAC7B,OAAO,IAAIJ,KAAA,CAAMM,IAAI,KAAK,UAAU;QAClC,6DAAIH,UAAA,CAAYM,SAAA,mEAAcP,YAAA,CAAcO,SAAA,GAAW;gBAEnDV,MAAA,oBAScD,gBAAA;gBAPXE,KAAA;YAHL,MAAMU,YAAA,yHACIC,SAAA,uEAAA,yDAAYR,UAAA,CAAYM,SAAA,CAAU,2GACxC,CAAE,YAAYT,KAAA,IAAS,qBAAqBA,KAAI,OAC/C,wCAAMY,eAAe,2EAAIZ,KAAA,CAAMa,MAAM,gDAAGC,IAAA,CACtCC,KAAA,IAAU,OAAOA,KAAA,KAAU,YAAYA,KAAA,CAAMC,IAAI,8DAAKb,UAAA,CAAYM,SAAA,OAC/D;gBACNL,MAAA,EAAQ,EAAA;YACV;YAEFA,MAAA,GAASM,YAAA,CAAaN,MAAM;YAC5BC,aAAA,gDAAiCE,IAAI,oFAAEC,MAAA,IACnCV,gBAAA,CAAiBS,IAAI,CAACN,GAAA,CAAI,GAC1BH,gBAAA,CAAiBM,MAAM;QAC7B,OAAO;gEAaA;;YAZL,MAAMa,mBAAA,2HACIN,SAAA,yEAAA,yDAAYR,UAAA,CAAYM,SAAA,CAAU,mEAA1CV,MAAA,oCACE,CAAE,YAAYC,KAAA,IAAS,qBAAqBA,KAAI,iBAC/CA,KAAA,2BAAMY,eAAe,6EAAIZ,KAAA,CAAMa,MAAM,2CAArC,OAAwCC,IAAA,EACtCC,KAAA,GAAU,OAAOA,KAAA,KAAU,YAAYA,KAAA,CAAMC,IAAI,6BAAKb,UAAA,kCAAYM,SAAA,OAC/D;gBACNL,MAAA,EAAQ,EAAA;YACV;gBAKGJ,KAAA;YAHL,MAAMkB,sBAAA,4HACIP,SAAA,yEAAA,6DAAYT,YAAA,CAAcO,SAAA,CAAU,oEAA5CV,MAAA,qCACE,CAAE,YAAYC,KAAA,IAAS,qBAAqBA,KAAI,iDACzCY,eAAe,6EAAIZ,KAAA,CAAMa,MAAM,kDAAGC,IAAA,EACtCC,KAAA,GAAU,OAAOA,KAAA,KAAU,YAAYA,KAAA,CAAMC,IAAI,kEAAKd,YAAA,CAAcO,SAAA,OACjE;gBACNL,MAAA,EAAQ,EAAA;YACV;YAEFA,MAAA,OAASR,+MAAA,EACP,C;mBAAIqB,mBAAA,CAAoBb,MAAM,E;mBAAKc,sBAAA,CAAuBd,MAAM;aAAC,EACjE;YAGF,2FAAA;YACAC,aAAA,gDAAiCE,IAAI,4DAArBT,gBAAA,QAAuBU,MAAA,IACnCV,gBAAA,CAAiBS,IAAI,CAACN,GAAA,CAAI,GAC1BH,gBAAA,CAAiBM,MAAM;QAC7B;IACF;IAEA,OAAO;QAAEA,MAAA;QAAQC;IAAc;AACjC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2516, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/utilities/countChangedFields.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/utilities/countChangedFields.ts"], "sourcesContent": ["import type { ArrayFieldClient, BlocksFieldClient, ClientConfig, ClientField } from 'payload'\n\nimport { fieldShouldBeLocalized, groupHasName } from 'payload/shared'\n\nimport { fieldHasChanges } from './fieldHasChanges.js'\nimport { getFieldsForRowComparison } from './getFieldsForRowComparison.js'\n\ntype Args = {\n  config: ClientConfig\n  fields: ClientField[]\n  locales: string[] | undefined\n  parentIsLocalized: boolean\n  valueFrom: unknown\n  valueTo: unknown\n}\n\n/**\n * Recursively counts the number of changed fields between comparison and\n * version data for a given set of fields.\n */\nexport function countChangedFields({\n  config,\n  fields,\n  locales,\n  parentIsLocalized,\n  valueFrom,\n  valueTo,\n}: Args) {\n  let count = 0\n\n  fields.forEach((field) => {\n    // Don't count the id field since it is not displayed in the UI\n    if ('name' in field && field.name === 'id') {\n      return\n    }\n    const fieldType = field.type\n    switch (fieldType) {\n      // Iterable fields are arrays and blocks fields. We iterate over each row and\n      // count the number of changed fields in each.\n      case 'array':\n      case 'blocks': {\n        if (locales && fieldShouldBeLocalized({ field, parentIsLocalized })) {\n          locales.forEach((locale) => {\n            const valueFromRows = valueFrom?.[field.name]?.[locale] ?? []\n            const valueToRows = valueTo?.[field.name]?.[locale] ?? []\n            count += countChangedFieldsInRows({\n              config,\n              field,\n              locales,\n              parentIsLocalized: parentIsLocalized || field.localized,\n              valueFromRows,\n              valueToRows,\n            })\n          })\n        } else {\n          const valueFromRows = valueFrom?.[field.name] ?? []\n          const valueToRows = valueTo?.[field.name] ?? []\n          count += countChangedFieldsInRows({\n            config,\n            field,\n            locales,\n            parentIsLocalized: parentIsLocalized || field.localized,\n            valueFromRows,\n            valueToRows,\n          })\n        }\n        break\n      }\n\n      // Regular fields without nested fields.\n      case 'checkbox':\n      case 'code':\n      case 'date':\n      case 'email':\n      case 'join':\n      case 'json':\n      case 'number':\n      case 'point':\n      case 'radio':\n      case 'relationship':\n      case 'richText':\n      case 'select':\n      case 'text':\n      case 'textarea':\n      case 'upload': {\n        // Fields that have a name and contain data. We can just check if the data has changed.\n        if (locales && fieldShouldBeLocalized({ field, parentIsLocalized })) {\n          locales.forEach((locale) => {\n            if (\n              fieldHasChanges(valueTo?.[field.name]?.[locale], valueFrom?.[field.name]?.[locale])\n            ) {\n              count++\n            }\n          })\n        } else if (fieldHasChanges(valueTo?.[field.name], valueFrom?.[field.name])) {\n          count++\n        }\n        break\n      }\n      // Fields that have nested fields, but don't nest their fields' data.\n      case 'collapsible':\n      case 'row': {\n        count += countChangedFields({\n          config,\n          fields: field.fields,\n          locales,\n          parentIsLocalized: parentIsLocalized || field.localized,\n          valueFrom,\n          valueTo,\n        })\n\n        break\n      }\n\n      // Fields that have nested fields and nest their fields' data.\n      case 'group': {\n        if (groupHasName(field)) {\n          if (locales && fieldShouldBeLocalized({ field, parentIsLocalized })) {\n            locales.forEach((locale) => {\n              count += countChangedFields({\n                config,\n                fields: field.fields,\n                locales,\n                parentIsLocalized: parentIsLocalized || field.localized,\n                valueFrom: valueFrom?.[field.name]?.[locale],\n                valueTo: valueTo?.[field.name]?.[locale],\n              })\n            })\n          } else {\n            count += countChangedFields({\n              config,\n              fields: field.fields,\n              locales,\n              parentIsLocalized: parentIsLocalized || field.localized,\n              valueFrom: valueFrom?.[field.name],\n              valueTo: valueTo?.[field.name],\n            })\n          }\n        } else {\n          // Unnamed group field: data is NOT nested under `field.name`\n          count += countChangedFields({\n            config,\n            fields: field.fields,\n            locales,\n            parentIsLocalized: parentIsLocalized || field.localized,\n            valueFrom,\n            valueTo,\n          })\n        }\n        break\n      }\n\n      // Each tab in a tabs field has nested fields. The fields data may be\n      // nested or not depending on the existence of a name property.\n      case 'tabs': {\n        field.tabs.forEach((tab) => {\n          if ('name' in tab && locales && tab.localized) {\n            // Named localized tab\n            locales.forEach((locale) => {\n              count += countChangedFields({\n                config,\n                fields: tab.fields,\n                locales,\n                parentIsLocalized: parentIsLocalized || tab.localized,\n                valueFrom: valueFrom?.[tab.name]?.[locale],\n                valueTo: valueTo?.[tab.name]?.[locale],\n              })\n            })\n          } else if ('name' in tab) {\n            // Named tab\n            count += countChangedFields({\n              config,\n              fields: tab.fields,\n              locales,\n              parentIsLocalized: parentIsLocalized || tab.localized,\n              valueFrom: valueFrom?.[tab.name],\n              valueTo: valueTo?.[tab.name],\n            })\n          } else {\n            // Unnamed tab\n            count += countChangedFields({\n              config,\n              fields: tab.fields,\n              locales,\n              parentIsLocalized: parentIsLocalized || tab.localized,\n              valueFrom,\n              valueTo,\n            })\n          }\n        })\n        break\n      }\n\n      // UI fields don't have data and are not displayed in the version view\n      // so we can ignore them.\n      case 'ui': {\n        break\n      }\n\n      default: {\n        const _exhaustiveCheck: never = fieldType\n        throw new Error(`Unexpected field.type in countChangedFields : ${String(fieldType)}`)\n      }\n    }\n  })\n\n  return count\n}\n\ntype countChangedFieldsInRowsArgs = {\n  config: ClientConfig\n  field: ArrayFieldClient | BlocksFieldClient\n  locales: string[] | undefined\n  parentIsLocalized: boolean\n  valueFromRows: unknown[]\n  valueToRows: unknown[]\n}\n\nexport function countChangedFieldsInRows({\n  config,\n  field,\n  locales,\n  parentIsLocalized,\n  valueFromRows = [],\n  valueToRows = [],\n}: countChangedFieldsInRowsArgs) {\n  let count = 0\n  let i = 0\n\n  while (valueFromRows[i] || valueToRows[i]) {\n    const valueFromRow = valueFromRows?.[i] || {}\n    const valueToRow = valueToRows?.[i] || {}\n\n    const { fields: rowFields } = getFieldsForRowComparison({\n      baseVersionField: { type: 'text', fields: [], path: '', schemaPath: '' }, // Doesn't matter, as we don't need the versionFields output here\n      config,\n      field,\n      row: i,\n      valueFromRow,\n      valueToRow,\n    })\n\n    count += countChangedFields({\n      config,\n      fields: rowFields,\n      locales,\n      parentIsLocalized: parentIsLocalized || field.localized,\n      valueFrom: valueFromRow,\n      valueTo: valueToRow,\n    })\n\n    i++\n  }\n  return count\n}\n"], "names": ["fieldShouldBeLocalized", "groupHasName", "fieldHasChanges", "getFieldsForRowComparison", "count<PERSON><PERSON><PERSON><PERSON><PERSON>s", "config", "fields", "locales", "parentIsLocalized", "valueFrom", "valueTo", "count", "for<PERSON>ach", "field", "name", "fieldType", "type", "locale", "valueFromRows", "valueToRows", "countChangedFieldsInRows", "localized", "tabs", "tab", "_exhaustiveCheck", "Error", "String", "i", "valueFromRow", "valueToRow", "rowFields", "baseVersionField", "path", "schemaPath", "row"], "mappings": ";;;;;;AAEA,SAASA,sBAAsB,EAAEC,YAAY,QAAQ;AAErD,SAASC,eAAe,QAAQ;AAChC,SAASC,yBAAyB,QAAQ;;;;AAenC,SAASC,mBAAmB;UACjCC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACTC,OAAAA,EACK;IACL,IAAIC,KAAA,GAAQ;IAEZL,MAAA,CAAOM,OAAO,EAAEC,KAAA;QACd,+DAAA;QACA,IAAI,UAAUA,KAAA,IAASA,KAAA,CAAMC,IAAI,KAAK,MAAM;YAC1C;QACF;QACA,MAAMC,SAAA,GAAYF,KAAA,CAAMG,IAAI;QAC5B,OAAQD,SAAA;YACN,6EAAA;YACA,8CAAA;YACA,KAAK;YACL,KAAK;gBAAU;oBACb,IAAIR,OAAA,QAAWP,mNAAA,EAAuB;wBAAEa,KAAA;wBAAOL;oBAAkB,IAAI;wBACnED,OAAA,CAAQK,OAAO,EAAEK,MAAA;;;4BACf,MAAMC,aAAA,GAAgBT,SAAA,+GAAA,CAAYI,KAAA,CAAMC,IAAI,CAAC,+EAAA,CAAGG,MAAA,CAAO,uFAAI,EAAE;;4BAC7D,MAAME,WAAA,IAAcT,OAAA,sGAAA,CAAUG,KAAA,CAAMC,IAAI,CAAC,2EAAA,CAAGG,MAAA,CAAO,mFAAI,EAAE;4BACzDN,KAAA,IAASS,wBAAA,CAAyB;gCAChCf,MAAA;gCACAQ,KAAA;gCACAN,OAAA;gCACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;gCACvDH,aAAA;gCACAC;4BACF;wBACF;oBACF,OAAO;;wBACL,MAAMD,aAAA,2FAAgB,CAAYL,KAAA,CAAMC,IAAI,CAAC,iDAAvBL,SAAA,eAA2B,EAAE;;wBACnD,MAAMU,WAAA,0BAAcT,OAAA,kDAAA,CAAUG,KAAA,CAAMC,IAAI,CAAC,qEAAI,EAAE;wBAC/CH,KAAA,IAASS,wBAAA,CAAyB;4BAChCf,MAAA;4BACAQ,KAAA;4BACAN,OAAA;4BACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;4BACvDH,aAAA;4BACAC;wBACF;oBACF;oBACA;gBACF;YAEA,wCAAA;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAU;oBACb,uFAAA;oBACA,IAAIZ,OAAA,QAAWP,mNAAA,EAAuB;wBAAEa,KAAA;wBAAOL;oBAAkB,IAAI;wBACnED,OAAA,CAAQK,OAAO,CAAEK,MAAA;qDAEoCR,SAAA;4BADnD,QACEP,sQAAA,kFAAgB,CAAUW,KAAA,CAAMC,IAAI,CAAC,wDAArBJ,OAAA,YAAqB,CAAGO,MAAA,CAAO,0FAAE,CAAYJ,KAAA,CAAMC,IAAI,CAAC,+EAAA,CAAGG,MAAA,CAAO,GAClF;gCACAN,KAAA;4BACF;wBACF;oBACF,OAAO,QAAIT,sQAAA,oDAAgBQ,OAAA,CAAUG,KAAA,CAAMC,IAAI,CAAC,EAAEL,SAAA,sDAAA,CAAYI,KAAA,CAAMC,IAAI,CAAC,GAAG;wBAC1EH,KAAA;oBACF;oBACA;gBACF;YACA,qEAAA;YACA,KAAK;YACL,KAAK;gBAAO;oBACVA,KAAA,IAASP,kBAAA,CAAmB;wBAC1BC,MAAA;wBACAC,MAAA,EAAQO,KAAA,CAAMP,MAAM;wBACpBC,OAAA;wBACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;wBACvDZ,SAAA;wBACAC;oBACF;oBAEA;gBACF;YAEA,8DAAA;YACA,KAAK;gBAAS;oBACZ,QAAIT,yMAAA,EAAaY,KAAA,GAAQ;wBACvB,IAAIN,OAAA,QAAWP,mNAAA,EAAuB;4BAAEa,KAAA;4BAAOL;wBAAkB,IAAI;4BACnED,OAAA,CAAQK,OAAO,EAAEK,MAAA;2DAOJP,OAAA;gCANXC,KAAA,IAASP,kBAAA,CAAmB;oCAC1BC,MAAA;oCACAC,MAAA,EAAQO,KAAA,CAAMP,MAAM;oCACpBC,OAAA;oCACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;oCACvDZ,SAAA,0FAAW,CAAYI,KAAA,CAAMC,IAAI,CAAC,0DAAvBL,SAAA,YAAuB,CAAGQ,MAAA,CAAO;oCAC5CP,OAAA,kFAAS,CAAUG,KAAA,CAAMC,IAAI,CAAC,2EAAA,CAAGG,MAAA,CAAA;gCACnC;4BACF;wBACF,OAAO;4BACLN,KAAA,IAASP,kBAAA,CAAmB;gCAC1BC,MAAA;gCACAC,MAAA,EAAQO,KAAA,CAAMP,MAAM;gCACpBC,OAAA;gCACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;gCACvDZ,SAAA,EAAWA,SAAA,sDAAA,CAAYI,KAAA,CAAMC,IAAI,CAAC;gCAClCJ,OAAA,oDAASA,OAAA,CAAUG,KAAA,CAAMC,IAAI,CAAA;4BAC/B;wBACF;oBACF,OAAO;wBACL,6DAAA;wBACAH,KAAA,IAASP,kBAAA,CAAmB;4BAC1BC,MAAA;4BACAC,MAAA,EAAQO,KAAA,CAAMP,MAAM;4BACpBC,OAAA;4BACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;4BACvDZ,SAAA;4BACAC;wBACF;oBACF;oBACA;gBACF;YAEA,qEAAA;YACA,+DAAA;YACA,KAAK;gBAAQ;oBACXG,KAAA,CAAMS,IAAI,CAACV,OAAO,EAAEW,GAAA;wBAClB,IAAI,UAAUA,GAAA,IAAOhB,OAAA,IAAWgB,GAAA,CAAIF,SAAS,EAAE;4BAC7C,sBAAA;4BACAd,OAAA,CAAQK,OAAO,EAAEK,MAAA;oCAMFR,SAAA,YACFC,OAAA;gCANXC,KAAA,IAASP,kBAAA,CAAmB;oCAC1BC,MAAA;oCACAC,MAAA,EAAQiB,GAAA,CAAIjB,MAAM;oCAClBC,OAAA;oCACAC,iBAAA,EAAmBA,iBAAA,IAAqBe,GAAA,CAAIF,SAAS;oCACrDZ,SAAA,wFAAW,CAAYc,GAAA,CAAIT,IAAI,CAAC,2EAAA,CAAGG,MAAA,CAAO;oCAC1CP,OAAA,gFAAS,CAAUa,GAAA,CAAIT,IAAI,CAAC,uEAAA,CAAGG,MAAA,CAAA;gCACjC;4BACF;wBACF,OAAO,IAAI,UAAUM,GAAA,EAAK;4BACxB,YAAA;4BACAZ,KAAA,IAASP,kBAAA,CAAmB;gCAC1BC,MAAA;gCACAC,MAAA,EAAQiB,GAAA,CAAIjB,MAAM;gCAClBC,OAAA;gCACAC,iBAAA,EAAmBA,iBAAA,IAAqBe,GAAA,CAAIF,SAAS;gCACrDZ,SAAA,EAAWA,SAAA,sDAAA,CAAYc,GAAA,CAAIT,IAAI,CAAC;gCAChCJ,OAAA,oDAASA,OAAA,CAAUa,GAAA,CAAIT,IAAI,CAAA;4BAC7B;wBACF,OAAO;4BACL,cAAA;4BACAH,KAAA,IAASP,kBAAA,CAAmB;gCAC1BC,MAAA;gCACAC,MAAA,EAAQiB,GAAA,CAAIjB,MAAM;gCAClBC,OAAA;gCACAC,iBAAA,EAAmBA,iBAAA,IAAqBe,GAAA,CAAIF,SAAS;gCACrDZ,SAAA;gCACAC;4BACF;wBACF;oBACF;oBACA;gBACF;YAEA,sEAAA;YACA,yBAAA;YACA,KAAK;gBAAM;oBACT;gBACF;YAEA;gBAAS;oBACP,MAAMc,gBAAA,GAA0BT,SAAA;oBAChC,MAAM,IAAIU,KAAA,CAAM,iDAAwD,CAAY,MAAnBC,MAAA,CAAOX,SAAA;gBAC1E;QACF;IACF;IAEA,OAAOJ,KAAA;AACT;AAWO,SAASS,8BAOe;UAN7Bf,MAAM,EACNQ,KAAK,EACLN,OAAO,EACPC,iBAAiB,EACjBU,aAAA,GAAgB,EAAE,EAClBC,WAAA,GAAc,EAAA,KANyB;IAQvC,IAAIR,KAAA,GAAQ;IACZ,IAAIgB,CAAA,GAAI;IAER,MAAOT,aAAa,CAACS,CAAA,CAAE,IAAIR,WAAW,CAACQ,CAAA,CAAE,CAAE;QACzC,MAAMC,YAAA,kEAAeV,aAAA,CAAgBS,CAAA,CAAE,KAAI,CAAC;QAC5C,MAAME,UAAA,8DAAaV,WAAA,CAAcQ,CAAA,CAAE,KAAI,CAAC;QAExC,MAAM,EAAErB,MAAA,EAAQwB,SAAAA,EAAW,OAAG3B,0RAAA,EAA0B;YACtD4B,gBAAA,EAAkB;gBAAEf,IAAA,EAAM;gBAAQV,MAAA,EAAQ,EAAE;gBAAE0B,IAAA,EAAM;gBAAIC,UAAA,EAAY;YAAG;YACvE5B,MAAA;YACAQ,KAAA;YACAqB,GAAA,EAAKP,CAAA;YACLC,YAAA;YACAC;QACF;QAEAlB,KAAA,IAASP,kBAAA,CAAmB;YAC1BC,MAAA;YACAC,MAAA,EAAQwB,SAAA;YACRvB,OAAA;YACAC,iBAAA,EAAmBA,iBAAA,IAAqBK,KAAA,CAAMQ,SAAS;YACvDZ,SAAA,EAAWmB,YAAA;YACXlB,OAAA,EAASmB;QACX;QAEAF,CAAA;IACF;IACA,OAAOhB,KAAA;AACT", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2760, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/DiffCollapser/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/DiffCollapser/index.tsx"], "sourcesContent": ["'use client'\nimport type { ClientField } from 'payload'\n\nimport { ChevronIcon, FieldDiffLabel, useConfig, useTranslation } from '@payloadcms/ui'\nimport { fieldIsArrayType, fieldIsBlockType } from 'payload/shared'\nimport React, { useState } from 'react'\n\nimport './index.scss'\nimport { countChangedFields, countChangedFieldsInRows } from '../utilities/countChangedFields.js'\n\nconst baseClass = 'diff-collapser'\n\ntype Props = {\n  hideGutter?: boolean\n  initCollapsed?: boolean\n  Label: React.ReactNode\n  locales: string[] | undefined\n  parentIsLocalized: boolean\n  valueTo: unknown\n} & (\n  | {\n      // fields collapser\n      children: React.ReactNode\n      field?: never\n      fields: ClientField[]\n      isIterable?: false\n      valueFrom: unknown\n    }\n  | {\n      // iterable collapser\n      children: React.ReactNode\n      field: ClientField\n      fields?: never\n      isIterable: true\n      valueFrom?: unknown\n    }\n)\n\nexport const DiffCollapser: React.FC<Props> = ({\n  children,\n  field,\n  fields,\n  hideGutter = false,\n  initCollapsed = false,\n  isIterable = false,\n  Label,\n  locales,\n  parentIsLocalized,\n  valueFrom,\n  valueTo,\n}) => {\n  const { t } = useTranslation()\n  const [isCollapsed, setIsCollapsed] = useState(initCollapsed)\n  const { config } = useConfig()\n\n  let changeCount = 0\n\n  if (isIterable) {\n    if (!fieldIsArrayType(field) && !fieldIsBlockType(field)) {\n      throw new Error(\n        'DiffCollapser: field must be an array or blocks field when isIterable is true',\n      )\n    }\n    const valueFromRows = valueFrom ?? []\n    const valueToRows = valueTo ?? []\n\n    if (!Array.isArray(valueFromRows) || !Array.isArray(valueToRows)) {\n      throw new Error(\n        'DiffCollapser: valueFrom and valueTro must be arrays when isIterable is true',\n      )\n    }\n\n    changeCount = countChangedFieldsInRows({\n      config,\n      field,\n      locales,\n      parentIsLocalized,\n      valueFromRows,\n      valueToRows,\n    })\n  } else {\n    changeCount = countChangedFields({\n      config,\n      fields,\n      locales,\n      parentIsLocalized,\n      valueFrom,\n      valueTo,\n    })\n  }\n\n  const contentClassNames = [\n    `${baseClass}__content`,\n    isCollapsed && `${baseClass}__content--is-collapsed`,\n    hideGutter && `${baseClass}__content--hide-gutter`,\n  ]\n    .filter(Boolean)\n    .join(' ')\n\n  return (\n    <div className={baseClass}>\n      <FieldDiffLabel>\n        <button\n          aria-label={isCollapsed ? 'Expand' : 'Collapse'}\n          className={`${baseClass}__toggle-button`}\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          type=\"button\"\n        >\n          <div className={`${baseClass}__label`}>{Label}</div>\n\n          <ChevronIcon direction={isCollapsed ? 'right' : 'down'} size={'small'} />\n        </button>\n        {changeCount > 0 && isCollapsed && (\n          <span className={`${baseClass}__field-change-count`}>\n            {t('version:changedFieldsCount', { count: changeCount })}\n          </span>\n        )}\n      </FieldDiffLabel>\n      <div className={contentClassNames}>{children}</div>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "ChevronIcon", "FieldDiffLabel", "useConfig", "useTranslation", "fieldIsArrayType", "fieldIsBlockType", "React", "useState", "count<PERSON><PERSON><PERSON><PERSON><PERSON>s", "countChangedFieldsInRows", "baseClass", "DiffCollapser", "t0", "$", "children", "field", "fields", "<PERSON><PERSON><PERSON>", "t1", "initCollapsed", "t2", "isIterable", "t3", "Label", "locales", "parentIsLocalized", "valueFrom", "valueTo", "undefined", "t", "isCollapsed", "setIsCollapsed", "config", "t4", "changeCount", "Error", "valueFromRows", "valueToRows", "Array", "isArray", "t5", "t6", "t7", "filter", "Boolean", "contentClassNames", "join", "t8", "_jsxs", "className", "onClick", "type", "_jsx", "direction", "size", "count"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,WAAW,EAAEC,cAAc,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;;AACvE,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ;AACnD,OAAOC,KAAA,IAASC,QAAQ,QAAQ;AAGhC,SAASC,kBAAkB,EAAEC,wBAAwB,QAAQ;AAR7D;;;;;;;AAUA,MAAMC,SAAA,GAAY;AA4BX,MAAMC,aAAA,IAAiCC,EAAA;IAAA,MAAAC,CAAA,OAAAd,0MAAA,EAAA;IAAC,MAAA,EAAAe,QAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,aAAA,EAAAC,EAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,KAAA,EAAAC,OAAA,EAAAC,iBAAA,EAAAC,SAAA,EAAAC,OAAAA,EAAA,GAAAf,EAY9C;IARC,MAAAK,UAAA,GAAAC,EAAkB,KAAAU,SAAA,GAAA,QAAlBV,EAAkB;IAClB,MAAAC,aAAA,GAAAC,EAAqB,KAAAQ,SAAA,GAAA,QAArBR,EAAqB;IACrB,MAAAC,UAAA,GAAAC,EAAkB,KAAAM,SAAA,GAAA,QAAlBN,EAAkB;IAOlB,MAAA,EAAAO,CAAAA,EAAA,GAAc1B,8QAAA;IACd,MAAA,CAAA2B,WAAA,EAAAC,cAAA,CAAA,OAAsCxB,mMAAA,EAASY,aAAA;IAC/C,MAAA,EAAAa,MAAAA,EAAA,OAAmB9B,gQAAA;IAAA,IAAA+B,EAAA;IAAA,IAAApB,CAAA,CAAA,EAAA,KAAAU,KAAA,IAAAV,CAAA,CAAA,EAAA,KAAAC,QAAA,IAAAD,CAAA,CAAA,EAAA,KAAAmB,MAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAE,KAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,MAAA,IAAAH,CAAA,CAAA,EAAA,KAAAI,UAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAiB,WAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAQ,UAAA,IAAAR,CAAA,CAAA,EAAA,KAAAW,OAAA,IAAAX,CAAA,CAAA,EAAA,KAAAY,iBAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAgB,CAAA,IAAAhB,CAAA,CAAA,GAAA,KAAAa,SAAA,IAAAb,CAAA,CAAA,GAAA,KAAAc,OAAA,EAAA;QAEnB,IAAAO,WAAA;QAAkB,IAEdb,UAAA,EAAA;YAAA,IACE,KAACjB,6MAAA,EAAiBW,KAAA,KAAA,KAAWV,6MAAA,EAAiBU,KAAA,GAAA;gBAAA,MAAA,IAAAoB,KAAA,CAE9C;YAAA;YAGJ,MAAAC,aAAA,GAAsBV,SAAA,gDAAA,EAAe;YACrC,MAAAW,WAAA,4CAAoBV,OAAA,GAAA,EAAa;YAAA,IAE7B,CAACW,KAAA,CAAAC,OAAA,CAAcH,aAAA,KAAA,CAAmBE,KAAA,CAAAC,OAAA,CAAcF,WAAA,GAAA;gBAAA,MAAA,IAAAF,KAAA,CAEhD;YAAA;YAIJD,WAAA,CAAAA,CAAA,KAAcA,kRAAAA,EAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;YAAAA,CAOdA,CAAA;QAPA,OAAA;YASAA,WAAA,CAAAA,CAAA,KAAcA,4QAAAA,EAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;gBAAAA;YAAAA,CAOdA,CAAA;QAPA;QAYA,MAAAM,EAAA,GAAAV,WAAA,IAAe,GAAA,OAAApB,SAAA,EAAA,wBAAqC;QACpD,MAAA+B,EAAA,GAAAxB,UAAA,IAAc,GAAA,OAAAP,SAAA,EAAA,uBAAoC;QAAA,IAAAgC,EAAA;QAAA,IAAA7B,CAAA,CAAA,GAAA,KAAA2B,EAAA,IAAA3B,CAAA,CAAA,GAAA,KAAA4B,EAAA,EAAA;YAH1BC,EAAA,GAAA,CACxB;gBAAA,GAAA,OAAAhC,SAAA,EAAA,UAAuB;gBACvB8B,EAAoD;gBACpDC,EAAkD;aAAA,CAAAE,MAAA,CAAAC,OAE1C;YAAA/B,CAAA,CAAA,GAAA,GAAA2B,EAAA;YAAA3B,CAAA,CAAA,GAAA,GAAA4B,EAAA;YAAA5B,CAAA,CAAA,GAAA,GAAA6B,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA7B,CAAA,CAAA,GAAA;QAAA;QALV,MAAAgC,iBAAA,GAA0BH,EAKhB,CAAAI,IAAA,CACF;QAAA,IAAAC,EAAA;QAAA,IAAAlC,CAAA,CAAA,GAAA,KAAAiB,WAAA,EAAA;YAQSiB,EAAA,GAAAA,CAAA,GAAMhB,cAAA,CAAA,CAAgBD,WAAA;YAAAjB,CAAA,CAAA,GAAA,GAAAiB,WAAA;YAAAjB,CAAA,CAAA,GAAA,GAAAkC,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAlC,CAAA,CAAA,GAAA;QAAA;QALrCoB,EAAA,OAAAe,wMAAA,EAAC,OAAA;YAAAC,SAAA,EAAAvC,SAAA;YAAAI,QAAA,EAAA;oBACCkC,wMAAA,EAAA/C,uOAAA,EAAA;oBAAAa,QAAA,EAAA;4BACEkC,wMAAA,EAAC,UAAA;4BAAA,cACalB,WAAA,GAAc,WAAW;4BAAAmB,SAAA,EAC1B,GAAA,OAAAvC,SAAA,EAAA,gBAA6B;4BAAAwC,OAAA,EAC/BH,EAAsB;4BAAAI,IAAA,EAC1B;4BAAArC,QAAA,EAAA;oCAELsC,uMAAA,EAAC,OAAA;oCAAAH,SAAA,EAAe,GAAA,OAAAvC,SAAA,EAAA,QAAqB;oCAAAI,QAAA,EAAGS;gCAAA,C;oCAExC6B,uMAAA,EAAApD,oOAAA,EAAA;oCAAAqD,SAAA,EAAwBvB,WAAA,GAAc,UAAU;oCAAAwB,IAAA,EAAc;gCAAA,C;6B;;wBAE/DpB,WAAA,GAAA,CAAc,IAAKJ,WAAA,QAClBsB,uMAAA,EAAC,QAAA;4BAAAH,SAAA,EAAgB,GAAA,OAAAvC,SAAA,EAAA,qBAAkC;4BAAAI,QAAA,EAChDe,CAAA,CAAE,8BAAA;gCAAA0B,KAAA,EAAuCrB;4BAAA,CAAY;wBAAA,C;qB;;oBAI5DkB,uMAAA,EAAC,OAAA;oBAAAH,SAAA,EAAeJ,iBAAA;oBAAA/B;gBAAA,C;a;;;;;;;;;;;;;;;;;;;WAlBlBmB,E;CAqBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/RenderVersionFieldsToDiff.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/RenderVersionFieldsToDiff.tsx"], "sourcesContent": ["'use client'\nconst baseClass = 'render-field-diffs'\nimport type { VersionField } from 'payload'\n\nimport './index.scss'\n\nimport { ShimmerEffect } from '@payloadcms/ui'\nimport React, { Fragment, useEffect } from 'react'\n\nexport const RenderVersionFieldsToDiff = ({\n  parent = false,\n  versionFields,\n}: {\n  /**\n   * If true, this is the parent render version fields component, not one nested in\n   * a field with children (e.g. group)\n   */\n  parent?: boolean\n  versionFields: VersionField[]\n}): React.ReactNode => {\n  const [hasMounted, setHasMounted] = React.useState(false)\n\n  // defer rendering until after the first mount as the CSS is loaded with Emotion\n  // this will ensure that the CSS is loaded before rendering the diffs and prevent CLS\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  return (\n    <div className={`${baseClass}${parent ? ` ${baseClass}--parent` : ''}`}>\n      {!hasMounted ? (\n        <Fragment>\n          <ShimmerEffect height=\"8rem\" width=\"100%\" />\n        </Fragment>\n      ) : (\n        versionFields?.map((field, fieldIndex) => {\n          if (field.fieldByLocale) {\n            const LocaleComponents: React.ReactNode[] = []\n            for (const [locale, baseField] of Object.entries(field.fieldByLocale)) {\n              LocaleComponents.push(\n                <div\n                  className={`${baseClass}__locale`}\n                  data-field-path={baseField.path}\n                  data-locale={locale}\n                  key={[locale, fieldIndex].join('-')}\n                >\n                  <div className={`${baseClass}__locale-value`}>{baseField.CustomComponent}</div>\n                </div>,\n              )\n            }\n            return (\n              <div className={`${baseClass}__field`} key={fieldIndex}>\n                {LocaleComponents}\n              </div>\n            )\n          } else if (field.field) {\n            return (\n              <div\n                className={`${baseClass}__field field__${field.field.type}`}\n                data-field-path={field.field.path}\n                key={fieldIndex}\n              >\n                {field.field.CustomComponent}\n              </div>\n            )\n          }\n\n          return null\n        })\n      )}\n    </div>\n  )\n}\n"], "names": ["c", "_c", "baseClass", "ShimmerEffect", "React", "Fragment", "useEffect", "RenderVersionFieldsToDiff", "t0", "$", "parent", "t1", "<PERSON>Fields", "undefined", "hasMounted", "setHasMounted", "useState", "t2", "t3", "Symbol", "for", "t4", "t5", "_jsx", "className", "children", "height", "width", "map", "_temp", "field", "fieldIndex", "fieldByLocale", "LocaleComponents", "locale", "baseField", "Object", "entries", "push", "path", "CustomComponent", "join", "type"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAMA,SAASE,aAAa,QAAQ;AAC9B,OAAOC,KAAA,IAASC,QAAQ,EAAEC,SAAS,QAAQ;AAP3C;;;AACA,MAAMJ,SAAA,GAAY;;;AAQX,MAAMK,yBAAA,IAA4BC,EAAA;IAAA,MAAAC,CAAA,OAAAR,0MAAA,EAAA;IAAC,MAAA,EAAAS,MAAA,EAAAC,EAAA,EAAAC,aAAAA,EAAA,GAAAJ,EAUzC;IATC,MAAAE,MAAA,GAAAC,EAAc,KAAAE,SAAA,GAAA,QAAdF,EAAc;IAUd,MAAA,CAAAG,UAAA,EAAAC,aAAA,CAAA,GAAoCX,kMAAA,CAAAY,QAAA,CAAA,KAAe;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAU,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIzCH,EAAA,GAAAA,CAAA;YACRF,aAAA,CAAA,IAAc;QAAA;QACbG,EAAA,GAAA,EAAA;QAAET,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAS,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAR,CAAA,CAAA,EAAA;QAAAS,EAAA,GAAAT,CAAA,CAAA,EAAA;IAAA;QAFLH,oMAAA,EAAUW,EAEV,EAAGC,EAAE;IAGa,MAAAG,EAAA,GAAA,UAAAnB,SAAA,EAAkD,CAAI,MAAvCQ,MAAA,GAAS,IAAA,OAAAR,EAAuB,OAAvB,EAAA,cAA0B;IAAI,IAAAoB,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAK,UAAA,IAAAL,CAAA,CAAA,EAAA,KAAAY,EAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAG,aAAA,EAAA;QAAtEU,EAAA,OAAAC,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAeH,EAAsD;YAAAI,QAAA,EACnE,CAACX,UAAA,OACAS,uMAAA,EAAAlB,mMAAA,EAAA;gBAAAoB,QAAA,MACEF,uMAAA,EAAApB,wQAAA,EAAA;oBAAAuB,MAAA,EAAsB;oBAAAC,KAAA,EAAa;gBAAA,C;+EAGrCf,aAAA,CAAAgB,GAAA,CAAAC,KAAA;QAiCA,C;;;;;;;;WAvCJP,E;CA2CJ;AA/DyC,SAAAO,MAAAC,KAAA,EAAAC,UAAA;IAAA,IA2B3BD,KAAA,CAAAE,aAAA,EAAA;QACF,MAAAC,gBAAA,GAAA,EAAA;QAA8C,KACzC,MAAA,CAAAC,MAAA,EAAAC,SAAA,CAAyB,IAAIC,MAAA,CAAAC,OAAA,CAAeP,KAAA,CAAAE,aAAmB,EAAA;YAClEC,gBAAA,CAAAK,IAAA,KACEf,uMAAA,EAAC,OAAA;gBAAAC,SAAA,EACY,GAAA,OAAAtB,SAAA,EAAA,SAAsB;gBAAA,mBAChBiC,SAAA,CAAAI,IAAA;gBAAA,eACJL,MAAA;gBAAAT,QAAA,MAGbF,uMAAA,EAAC,OAAA;oBAAAC,SAAA,EAAe,GAAA,OAAAtB,SAAA,EAAA,eAA4B;oBAAAuB,QAAA,EAAGU,SAAA,CAAAK,eAAAA;gBAAA,C;eAF1C;gBAACN,MAAA;gBAAQH,UAAA;aAAA,CAAAU,IAAA,CAAiB;QAAA;QAAA,WAOnClB,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAe,GAAA,OAAAtB,SAAA,EAAA,QAAqB;YAAAuB,QAAA,EAClCQ;QAAA,GADyCF,UAAA;IAAA,OAAA;QAAA,IAIrCD,KAAA,CAAAA,KAAA,EAAA;YAAA,WAEPP,uMAAA,EAAC,OAAA;gBAAAC,SAAA,EACY,UAAAtB,SAAA,EAAA,mBAA8B,CAAkB,MAAlB4B,KAAA,CAAAA,KAAA,CAAAY,IAAA;gBAAkB,mBAC1CZ,KAAA,CAAAA,KAAA,CAAAS,IAAA;gBAAAd,QAAA,EAGhBK,KAAA,CAAAA,KAAA,CAAAU,eAAAA;YAAA,GAFIT,UAAA;QAAA;IAAA;IAAA,OAAA;AAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Collapsible/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Collapsible/index.tsx"], "sourcesContent": ["'use client'\nimport type { CollapsibleFieldDiffClientComponent } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nimport { useSelectedLocales } from '../../../Default/SelectedLocalesContext.js'\nimport { DiffCollapser } from '../../DiffCollapser/index.js'\nimport { RenderVersionFieldsToDiff } from '../../RenderVersionFieldsToDiff.js'\n\nconst baseClass = 'collapsible-diff'\n\nexport const Collapsible: CollapsibleFieldDiffClientComponent = ({\n  baseVersionField,\n  comparisonValue: valueFrom,\n  field,\n  parentIsLocalized,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n  const { selectedLocales } = useSelectedLocales()\n\n  if (!baseVersionField.fields?.length) {\n    return null\n  }\n\n  return (\n    <div className={baseClass}>\n      <DiffCollapser\n        fields={field.fields}\n        Label={\n          'label' in field &&\n          field.label &&\n          typeof field.label !== 'function' && <span>{getTranslation(field.label, i18n)}</span>\n        }\n        locales={selectedLocales}\n        parentIsLocalized={parentIsLocalized || field.localized}\n        valueFrom={valueFrom}\n        valueTo={valueTo}\n      >\n        <RenderVersionFieldsToDiff versionFields={baseVersionField.fields} />\n      </DiffCollapser>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "useTranslation", "React", "useSelectedLocales", "DiffCollapser", "RenderVersionFieldsToDiff", "baseClass", "Collapsible", "t0", "$", "baseVersionField", "comparisonValue", "valueFrom", "field", "parentIsLocalized", "versionValue", "valueTo", "i18n", "selectedLocales", "fields", "length", "t1", "_jsx", "className", "children", "Label", "label", "localized", "<PERSON>Fields"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,cAAc,QAAQ;AAC/B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAElB,SAASC,kBAAkB,QAAQ;AACnC,SAASC,aAAa,QAAQ;AAC9B,SAASC,yBAAyB,QAAQ;AAT1C;;;;;;;;;AAWA,MAAMC,SAAA,GAAY;AAEX,MAAMC,WAAA,IAAmDC,EAAA;QAUzDE,gBAAA;IAVyD,MAAAD,CAAA,OAAAV,0MAAA,EAAA;IAAC,MAAA,EAAAW,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAR,EAMhE;IACC,MAAA,EAAAS,IAAAA,EAAA,OAAiBhB,0QAAA;IACjB,MAAA,EAAAiB,eAAAA,EAAA,OAA4Bf,wPAAA;IAAA,IAAA,+CAEvBgB,MAAA,sFAAAC,MAAA,GAAA;QAAA,OAAA;IAAA;IAAA,IAAAC,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAC,gBAAA,CAAAS,MAAA,IAAAV,CAAA,CAAA,EAAA,KAAAI,KAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAQ,IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAK,iBAAA,IAAAL,CAAA,CAAA,EAAA,KAAAS,eAAA,IAAAT,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAO,OAAA,EAAA;QAKHK,EAAA,OAAAC,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAAjB,SAAA;YAAAkB,QAAA,MACCF,uMAAA,EAAAlB,8PAAA,EAAA;gBAAAe,MAAA,EACUN,KAAA,CAAAM,MAAA;gBAAAM,KAAA,EAEN,WAAWZ,KAAA,IACXA,KAAA,CAAAa,KAAW,IACX,OAAOb,KAAA,CAAAa,KAAA,KAAgB,kBAAcJ,uMAAA,EAAC,QAAA;oBAAAE,QAAA,MAAMxB,kOAAA,EAAea,KAAA,CAAAa,KAAA,EAAaT,IAAA;gBAAA,C;yBAEjEC,eAAA;gBAAAJ,iBAAA,EACUA,iBAAA,IAAqBD,KAAA,CAAAc,SAAe;gBAAAf,SAAA;gBAAAI,OAAA;gBAAAQ,QAAA,MAIvDF,uMAAA,EAAAjB,6QAAA,EAAA;oBAAAuB,aAAA,EAA0ClB,gBAAA,CAAAS,MAAAA;gBAAA,C;;;;;;;;;;;;;;WAb9CE,E;CAiBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Date/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Date/index.tsx"], "sourcesContent": ["'use client'\nimport type { DateFieldDiffClientComponent } from 'payload'\n\nimport {\n  FieldDiffContainer,\n  getHTMLDiffComponents,\n  useConfig,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { formatDate } from '@payloadcms/ui/shared'\n\nimport './index.scss'\n\nimport React from 'react'\n\nconst baseClass = 'date-diff'\n\nexport const DateDiffComponent: DateFieldDiffClientComponent = ({\n  comparisonValue: valueFrom,\n  field,\n  locale,\n  nestingLevel,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n  const {\n    config: {\n      admin: { dateFormat },\n    },\n  } = useConfig()\n\n  const formattedFromDate = valueFrom\n    ? formatDate({\n        date: typeof valueFrom === 'string' ? new Date(valueFrom) : (valueFrom as Date),\n        i18n,\n        pattern: dateFormat,\n      })\n    : ''\n\n  const formattedToDate = valueTo\n    ? formatDate({\n        date: typeof valueTo === 'string' ? new Date(valueTo) : (valueTo as Date),\n        i18n,\n        pattern: dateFormat,\n      })\n    : ''\n\n  const { From, To } = getHTMLDiffComponents({\n    fromHTML:\n      `<div class=\"${baseClass}\" data-enable-match=\"true\" data-date=\"${formattedFromDate}\"><p>` +\n      formattedFromDate +\n      '</p></div>',\n    toHTML:\n      `<div class=\"${baseClass}\" data-enable-match=\"true\" data-date=\"${formattedToDate}\"><p>` +\n      formattedToDate +\n      '</p></div>',\n    tokenizeByCharacter: false,\n  })\n\n  return (\n    <FieldDiffContainer\n      className={baseClass}\n      From={From}\n      i18n={i18n}\n      label={{\n        label: field.label,\n        locale,\n      }}\n      nestingLevel={nestingLevel}\n      To={To}\n    />\n  )\n}\n"], "names": ["c", "_c", "FieldDiff<PERSON><PERSON><PERSON>", "getHTMLDiffComponents", "useConfig", "useTranslation", "formatDate", "React", "baseClass", "DateDiffComponent", "t0", "$", "comparisonValue", "valueFrom", "field", "locale", "nestingLevel", "versionValue", "valueTo", "i18n", "config", "t1", "admin", "t2", "dateFormat", "formattedFromDate", "date", "Date", "pattern", "formattedToDate", "t3", "t4", "t5", "label", "From", "To", "fromHTML", "toHTML", "tokenizeByCharacter", "_jsx", "className"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SACEC,kBAAkB,EAClBC,qBAAqB,EACrBC,SAAS,EACTC,cAAc,QACT;;;AACP,SAASC,UAAU,QAAQ;AAI3B,OAAOC,KAAA,MAAW;AAblB;;;;;;AAeA,MAAMC,SAAA,GAAY;AAEX,MAAMC,iBAAA,IAAkDC,EAAA;IAAA,MAAAC,CAAA,OAAAV,0MAAA,EAAA;IAAC,MAAA,EAAAW,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAR,EAM/D;IACC,MAAA,EAAAS,IAAAA,EAAA,OAAiBd,0QAAA;IACjB,MAAA,EAAAe,MAAA,EAAAC,EAAAA,EAAA,OAIIjB,gQAAA;IAHM,MAAA,EAAAkB,KAAA,EAAAC,EAAAA,EAAA,GAAAF,EAEP;IADQ,MAAA,EAAAG,UAAAA,EAAA,GAAAD,EAAc;IAIzB,MAAAE,iBAAA,GAA0BZ,SAAA,OACtBP,mOAAA,EAAA;QAAAoB,IAAA,EACQ,OAAOb,SAAA,KAAc,WAAA,IAAAc,IAAA,CAAoBd,SAAA,IAAcA,SAAA;QAAAM,IAAA;QAAAS,OAAA,EAEpDJ;IAAA,CACX,IACA;IAEJ,MAAAK,eAAA,GAAwBX,OAAA,OACpBZ,mOAAA,EAAA;QAAAoB,IAAA,EACQ,OAAOR,OAAA,KAAY,WAAA,IAAAS,IAAA,CAAoBT,OAAA,IAAYA,OAAA;QAAAC,IAAA;QAAAS,OAAA,EAEhDJ;IAAA,CACX,IACA;IAIA,MAAAM,EAAA,GAAA,sBAAAtB,SAAA,EAAA,0CAAiE,MAAwB,CAAxBiB,iBAAA,EAAA,WACjEA,iBAAA,GACA;IAEA,MAAAM,EAAA,GAAA,sBAAAvB,SAAA,EAAA,0CAAiE,OAAAqB,eAAA,EAAA,MAAsB,KACvFA,eAAA,GACA;IAAA,IAAAG,EAAA;IAAA,IAAArB,CAAA,CAAA,EAAA,KAAAG,KAAA,CAAAmB,KAAA,IAAAtB,CAAA,CAAA,EAAA,KAAAQ,IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAI,MAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,YAAA,IAAAL,CAAA,CAAA,EAAA,KAAAmB,EAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAoB,EAAA,EAAA;QARJ,MAAA,EAAAG,IAAA,EAAAC,EAAAA,EAAA,OAAqBhC,8OAAA,EAAA;YAAAiC,QAAA,EAEjBN,EAEA;YAAAO,MAAA,EAEAN,EAEA;YAAAO,mBAAA,EAAA;QAAA,CAEJ;QAGEN,EAAA,OAAAO,uMAAA,EAAArC,2OAAA,EAAA;YAAAsC,SAAA,EAAAhC,SAAA;YAAA0B,IAAA;YAAAf,IAAA;YAAAc,KAAA,EAAA;gBAAAA,KAAA,EAKWnB,KAAA,CAAAmB,KAAA;gBAAAlB;YAAA;YAAAC,YAAA;YAAAmB;QAAA,C;;;;;;;;;;;WALXH,E;CAYJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3128, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Group/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Group/index.tsx"], "sourcesContent": ["'use client'\nimport type { GroupFieldDiffClientComponent } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\n\nimport './index.scss'\n\nimport { useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nimport { useSelectedLocales } from '../../../Default/SelectedLocalesContext.js'\nimport { DiffCollapser } from '../../DiffCollapser/index.js'\nimport { RenderVersionFieldsToDiff } from '../../RenderVersionFieldsToDiff.js'\n\nconst baseClass = 'group-diff'\n\nexport const Group: GroupFieldDiffClientComponent = ({\n  baseVersionField,\n  comparisonValue: valueFrom,\n  field,\n  locale,\n  parentIsLocalized,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n  const { selectedLocales } = useSelectedLocales()\n\n  return (\n    <div className={baseClass}>\n      <DiffCollapser\n        fields={field.fields}\n        Label={\n          'label' in field && field.label && typeof field.label !== 'function' ? (\n            <span>\n              {locale && <span className={`${baseClass}__locale-label`}>{locale}</span>}\n              {getTranslation(field.label, i18n)}\n            </span>\n          ) : (\n            <span className={`${baseClass}__locale-label ${baseClass}__locale-label--no-label`}>\n              &lt;{i18n.t('version:noLabelGroup')}&gt;\n            </span>\n          )\n        }\n        locales={selectedLocales}\n        parentIsLocalized={parentIsLocalized || field.localized}\n        valueFrom={valueFrom}\n        valueTo={valueTo}\n      >\n        <RenderVersionFieldsToDiff versionFields={baseVersionField.fields} />\n      </DiffCollapser>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "useTranslation", "React", "useSelectedLocales", "DiffCollapser", "RenderVersionFieldsToDiff", "baseClass", "Group", "t0", "$", "baseVersionField", "comparisonValue", "valueFrom", "field", "locale", "parentIsLocalized", "versionValue", "valueTo", "i18n", "selectedLocales", "t1", "fields", "_jsx", "className", "children", "Label", "label", "_jsxs", "t", "localized", "<PERSON>Fields"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,cAAc,QAAQ;AAI/B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAElB,SAASC,kBAAkB,QAAQ;AACnC,SAASC,aAAa,QAAQ;AAC9B,SAASC,yBAAyB,QAAQ;AAZ1C;;;;;;;;;AAcA,MAAMC,SAAA,GAAY;AAEX,MAAMC,KAAA,IAAuCC,EAAA;IAAA,MAAAC,CAAA,OAAAV,0MAAA,EAAA;IAAC,MAAA,EAAAW,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAT,EAOpD;IACC,MAAA,EAAAU,IAAAA,EAAA,GAAiBjB,8QAAA;IACjB,MAAA,EAAAkB,eAAAA,EAAA,OAA4BhB,wPAAA;IAAA,IAAAiB,EAAA;IAAA,IAAAX,CAAA,CAAA,EAAA,KAAAC,gBAAA,CAAAW,MAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAI,KAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAS,IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAK,MAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,iBAAA,IAAAN,CAAA,CAAA,EAAA,KAAAU,eAAA,IAAAV,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAQ,OAAA,EAAA;QAG1BG,EAAA,OAAAE,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAAjB,SAAA;YAAAkB,QAAA,MACCF,uMAAA,EAAAlB,8PAAA,EAAA;gBAAAiB,MAAA,EACUR,KAAA,CAAAQ,MAAA;gBAAAI,KAAA,EAEN,WAAWZ,KAAA,IAASA,KAAA,CAAAa,KAAW,IAAI,OAAOb,KAAA,CAAAa,KAAA,KAAgB,iBACxDC,wMAAA,EAAC,QAAA;oBAAAH,QAAA,EAAA;wBACEV,MAAA,QAAUQ,uMAAA,EAAC,QAAA;4BAAAC,SAAA,EAAgB,GAAA,OAAAjB,SAAA,EAAA,eAA4B;4BAAAkB,QAAA,EAAGV;wBAAA,C;4BAC1Dd,kOAAA,EAAea,KAAA,CAAAa,KAAA,EAAaR,IAAA;qBAAA;gBAAA,C,QAG/BS,wMAAA,EAAC,QAAA;oBAAAJ,SAAA,EAAgB,UAAAjB,SAAA,EAAA,mBAAA,OAAAA,SAAA,EAAA,yBAAiE;oBAAAkB,QAAA,EAAA;wBAAE;wBAC7EN,IAAA,CAAAU,CAAA,CAAO;wBAAwB;qBAAA;gBAAA,C;yBAIjCT,eAAA;gBAAAJ,iBAAA,EACUA,iBAAA,IAAqBF,KAAA,CAAAgB,SAAe;gBAAAjB,SAAA;gBAAAK,OAAA;gBAAAO,QAAA,MAIvDF,uMAAA,EAAAjB,6QAAA,EAAA;oBAAAyB,aAAA,EAA0CpB,gBAAA,CAAAW,MAAAA;gBAAA,C;;;;;;;;;;;;;;;WApB9CD,E;CAwBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Iterable/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Iterable/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { FieldDiffClientProps } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { useConfig, useTranslation } from '@payloadcms/ui'\n\nimport './index.scss'\n\nimport { fieldIsArrayType, fieldIsBlockType } from 'payload/shared'\nimport React from 'react'\n\nimport { useSelectedLocales } from '../../../Default/SelectedLocalesContext.js'\nimport { DiffCollapser } from '../../DiffCollapser/index.js'\nimport { RenderVersionFieldsToDiff } from '../../RenderVersionFieldsToDiff.js'\nimport { getFieldsForRowComparison } from '../../utilities/getFieldsForRowComparison.js'\n\nconst baseClass = 'iterable-diff'\n\nexport const Iterable: React.FC<FieldDiffClientProps> = ({\n  baseVersionField,\n  comparisonValue: valueFrom,\n  field,\n  locale,\n  parentIsLocalized,\n  versionValue: valueTo,\n}) => {\n  const { i18n, t } = useTranslation()\n  const { selectedLocales } = useSelectedLocales()\n  const { config } = useConfig()\n\n  if (!fieldIsArrayType(field) && !fieldIsBlockType(field)) {\n    throw new Error(`Expected field to be an array or blocks type but got: ${field.type}`)\n  }\n\n  const valueToRowCount = Array.isArray(valueTo) ? valueTo.length : 0\n  const valueFromRowCount = Array.isArray(valueFrom) ? valueFrom.length : 0\n  const maxRows = Math.max(valueToRowCount, valueFromRowCount)\n\n  return (\n    <div className={baseClass}>\n      <DiffCollapser\n        field={field}\n        isIterable\n        Label={\n          'label' in field &&\n          field.label &&\n          typeof field.label !== 'function' && (\n            <span>\n              {locale && <span className={`${baseClass}__locale-label`}>{locale}</span>}\n              {getTranslation(field.label, i18n)}\n            </span>\n          )\n        }\n        locales={selectedLocales}\n        parentIsLocalized={parentIsLocalized}\n        valueFrom={valueFrom}\n        valueTo={valueTo}\n      >\n        {maxRows > 0 && (\n          <div className={`${baseClass}__rows`}>\n            {Array.from({ length: maxRows }, (_, i) => {\n              const valueToRow = valueTo?.[i] || {}\n              const valueFromRow = valueFrom?.[i] || {}\n\n              const { fields, versionFields } = getFieldsForRowComparison({\n                baseVersionField,\n                config,\n                field,\n                row: i,\n                valueFromRow,\n                valueToRow,\n              })\n\n              if (!versionFields?.length) {\n                // Rows without a diff create \"holes\" in the baseVersionField.rows (=versionFields) array - this is to maintain the correct row indexes.\n                // It does mean that this row has no diff and should not be rendered => skip it.\n                return null\n              }\n\n              const rowNumber = String(i + 1).padStart(2, '0')\n              const rowLabel = fieldIsArrayType(field)\n                ? `${t('general:item')} ${rowNumber}`\n                : `${t('fields:block')} ${rowNumber}`\n\n              return (\n                <div className={`${baseClass}__row`} key={i}>\n                  <DiffCollapser\n                    fields={fields}\n                    hideGutter={true}\n                    Label={\n                      <div className={`${baseClass}-label-container`}>\n                        <div className={`${baseClass}-label-prefix`}></div>\n                        <span className={`${baseClass}__label`}>{rowLabel}</span>\n                      </div>\n                    }\n                    locales={selectedLocales}\n                    parentIsLocalized={parentIsLocalized || field.localized}\n                    valueFrom={valueFromRow}\n                    valueTo={valueToRow}\n                  >\n                    <RenderVersionFieldsToDiff versionFields={versionFields} />\n                  </DiffCollapser>\n                </div>\n              )\n            })}\n          </div>\n        )}\n        {maxRows === 0 && (\n          <div className={`${baseClass}__no-rows`}>\n            {i18n.t('version:noRowsFound', {\n              label:\n                'labels' in field && field.labels?.plural\n                  ? getTranslation(field.labels.plural, i18n)\n                  : i18n.t('general:rows'),\n            })}\n          </div>\n        )}\n      </DiffCollapser>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "useConfig", "useTranslation", "fieldIsArrayType", "fieldIsBlockType", "React", "useSelectedLocales", "DiffCollapser", "RenderVersionFieldsToDiff", "getFieldsForRowComparison", "baseClass", "Iterable", "t0", "$", "baseVersionField", "comparisonValue", "valueFrom", "field", "locale", "parentIsLocalized", "versionValue", "valueTo", "i18n", "t", "selectedLocales", "config", "Error", "type", "valueToRowCount", "Array", "isArray", "length", "valueFromRowCount", "maxRows", "Math", "max", "t1", "_jsx", "className", "children", "_jsxs", "isIterable", "Label", "label", "from", "_", "i", "valueToRow", "valueFromRow", "fields", "<PERSON>Fields", "row", "rowNumber", "String", "padStart", "row<PERSON>abel", "<PERSON><PERSON><PERSON>", "localized", "labels", "plural"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAIA,SAASC,cAAc,QAAQ;AAC/B,SAASC,SAAS,EAAEC,cAAc,QAAQ;;AAI1C,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ;AACnD,OAAOC,KAAA,MAAW;AAElB,SAASC,kBAAkB,QAAQ;AACnC,SAASC,aAAa,QAAQ;AAC9B,SAASC,yBAAyB,QAAQ;AAC1C,SAASC,yBAAyB,QAAQ;AAf1C;;;;;;;;;;;AAiBA,MAAMC,SAAA,GAAY;AAEX,MAAMC,QAAA,IAA2CC,EAAA;IAAA,MAAAC,CAAA,OAAAd,0MAAA,EAAA;IAAC,MAAA,EAAAe,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAT,EAOxD;IACC,MAAA,EAAAU,IAAA,EAAAC,CAAAA,EAAA,GAAoBrB,8QAAA;IACpB,MAAA,EAAAsB,eAAAA,EAAA,OAA4BlB,wPAAA;IAC5B,MAAA,EAAAmB,MAAAA,EAAA,OAAmBxB,gQAAA;IAAA,IAEf,KAACE,6MAAA,EAAiBc,KAAA,KAAA,KAAWb,6MAAA,EAAiBa,KAAA,GAAA;QAAA,MAAA,IAAAS,KAAA,CAChC,yDAAyD,CAAY,MAAZT,KAAA,CAAAU,IAAA;IAAY;IAGvF,MAAAC,eAAA,GAAwBC,KAAA,CAAAC,OAAA,CAAcT,OAAA,IAAWA,OAAA,CAAAU,MAAA,GAAA,CAAiB;IAClE,MAAAC,iBAAA,GAA0BH,KAAA,CAAAC,OAAA,CAAcd,SAAA,IAAaA,SAAA,CAAAe,MAAA,GAAA,CAAmB;IACxE,MAAAE,OAAA,GAAgBC,IAAA,CAAAC,GAAA,CAASP,eAAA,EAAiBI,iBAAA;IAAA,IAAAI,EAAA;IAAA,IAAAvB,CAAA,CAAA,EAAA,KAAAC,gBAAA,IAAAD,CAAA,CAAA,EAAA,KAAAY,MAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAI,KAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAS,IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAK,MAAA,IAAAL,CAAA,CAAA,EAAA,KAAAoB,OAAA,IAAApB,CAAA,CAAA,EAAA,KAAAM,iBAAA,IAAAN,CAAA,CAAA,EAAA,KAAAW,eAAA,IAAAX,CAAA,CAAA,EAAA,KAAAU,CAAA,IAAAV,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,GAAA,KAAAQ,OAAA,EAAA;YA2EPJ,KAAA;QAxEjCmB,EAAA,OAAAC,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAA5B,SAAA;YAAA6B,QAAA,MACCC,wMAAA,EAAAjC,8PAAA,EAAA;gBAAAU,KAAA;gBAAAwB,UAAA,EAAA;gBAAAC,KAAA,EAII,WAAWzB,KAAA,IACXA,KAAA,CAAA0B,KAAW,IACX,OAAO1B,KAAA,CAAA0B,KAAA,KAAgB,kBACrBH,wMAAA,EAAC,QAAA;oBAAAD,QAAA,EAAA;wBACErB,MAAA,QAAUmB,uMAAA,EAAC,QAAA;4BAAAC,SAAA,EAAgB,GAAA,OAAA5B,SAAA,EAAA,eAA4B;4BAAA6B,QAAA,EAAGrB;wBAAA,C;4BAC1DlB,kOAAA,EAAeiB,KAAA,CAAA0B,KAAA,EAAarB,IAAA;qBAAA;gBAAA,C;yBAI1BE,eAAA;gBAAAL,iBAAA;gBAAAH,SAAA;gBAAAK,OAAA;gBAAAkB,QAAA,EAAA;oBAKRN,OAAA,GAAA,CAAU,QACTI,uMAAA,EAAC,OAAA;wBAAAC,SAAA,EAAe,GAAA,OAAA5B,SAAA,EAAA,OAAoB;wBAAA6B,QAAA,EACjCV,KAAA,CAAAe,IAAA,CAAA;4BAAAb,MAAA,EAAqBE;wBAAA,GAAA,CAAAY,CAAA,EAAAC,CAAA;4BACpB,MAAAC,UAAA,sDAAmB1B,OAAA,CAAUyB,CAAA,CAAA,KAAA,CAAA,CAAO;4BACpC,MAAAE,YAAA,IAAqBhC,SAAA,sDAAA,CAAY8B,CAAA,CAAA,KAAA,CAAA,CAAO;4BAExC,MAAA,EAAAG,MAAA,EAAAC,aAAAA,EAAA,OAAkCzC,0RAAA,EAAA;gCAAAK,gBAAA;gCAAAW,MAAA;gCAAAR,KAAA;gCAAAkC,GAAA,EAI3BL,CAAA;gCAAAE,YAAA;gCAAAD;4BAAA,CAGP;4BAAA,IAAA,gEAEKG,aAAA,CAAAnB,MAAA,GAAA;gCAAA,OAAA;4BAAA;4BAML,MAAAqB,SAAA,GAAkBC,MAAA,CAAOP,CAAA,GAAA,CAAI,EAAAQ,QAAA,CAAA,GAAe;4BAC5C,MAAAC,QAAA,OAAiBpD,6MAAA,EAAiBc,KAAA,IAC9B,UAAGM,CAAA,CAAE,iBAAA,KAAmB,CAAW,MAAX6B,SAAA,IACxB,UAAG7B,CAAA,CAAE,iBAAA,KAAmB,CAAW,MAAX6B,SAAA;4BAAW,OAGrCf,2MAAA,EAAC,OAAA;gCAAAC,SAAA,EAAe,GAAA,OAAA5B,SAAA,EAAA,MAAmB;gCAAA6B,QAAA,MACjCF,uMAAA,EAAA9B,8PAAA,EAAA;oCAAA0C,MAAA;oCAAAO,UAAA,EAAA;oCAAAd,KAAA,MAIIF,wMAAA,EAAC,OAAA;wCAAAF,SAAA,EAAe,GAAA,OAAA5B,SAAA,EAAA,iBAA8B;wCAAA6B,QAAA,EAAA;4CAC5CF,2MAAA,EAAC,OAAA;gDAAAC,SAAA,EAAe,GAAA,OAAA5B,SAAA,EAAA;4CAA2B,C;gDAC3C2B,uMAAA,EAAC,QAAA;gDAAAC,SAAA,EAAgB,GAAA,OAAA5B,SAAA,EAAA,QAAqB;gDAAA6B,QAAA,EAAGgB;4CAAA,C;yC;;6CAGpC/B,eAAA;oCAAAL,iBAAA,EACUA,iBAAA,IAAqBF,KAAA,CAAAwC,SAAe;oCAAAzC,SAAA,EAC5CgC,YAAA;oCAAA3B,OAAA,EACF0B,UAAA;oCAAAR,QAAA,MAETF,uMAAA,EAAA7B,6QAAA,EAAA;wCAAA0C;oCAAA,C;;+BAfsCJ,CAAA;wBAAA,CAmB9C;oBAAA,C;oBAGHb,OAAA,KAAA,CAAY,QACXI,uMAAA,EAAC,OAAA;wBAAAC,SAAA,EAAe,GAAA,OAAA5B,SAAA,EAAA,UAAuB;wBAAA6B,QAAA,EACpCjB,IAAA,CAAAC,CAAA,CAAO,uBAAA;4BAAAoB,KAAA,EAEJ,YAAY1B,KAAA,4BAASyC,MAAA,gEAAAC,MAAc,QAC/B3D,kOAAA,EAAeiB,KAAA,CAAAyC,MAAA,CAAAC,MAAA,EAAqBrC,IAAA,IACpCA,IAAA,CAAAC,CAAA,CAAO;wBAAA,CACf;oBAAA,C;iB;;;;;;;;;;;;;;;;;;WA3ERa,E;CAiFJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3343, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Row/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Row/index.tsx"], "sourcesContent": ["'use client'\nimport type { RowFieldDiffClientComponent } from 'payload'\n\nimport React from 'react'\n\nimport { RenderVersionFieldsToDiff } from '../../RenderVersionFieldsToDiff.js'\n\nconst baseClass = 'row-diff'\n\nexport const Row: RowFieldDiffClientComponent = ({ baseVersionField }) => {\n  return (\n    <div className={baseClass}>\n      <RenderVersionFieldsToDiff versionFields={baseVersionField.fields} />\n    </div>\n  )\n}\n"], "names": ["React", "RenderVersionFieldsToDiff", "baseClass", "Row", "baseVersionField", "_jsx", "className", "<PERSON>Fields", "fields"], "mappings": ";;;;;AAGA,OAAOA,KAAA,MAAW;AAElB,SAASC,yBAAyB,QAAQ;AAL1C;;;;AAOA,MAAMC,SAAA,GAAY;AAEX,MAAMC,GAAA,GAAmCA;QAAC,EAAEC,gBAAAA,EAAkB;IACnE,OAAA,WAAA,OACEC,uMAAA,EAAC,OAAA;QAAIC,SAAA,EAAWJ,SAAA;kBACd,WAAA,OAAAG,uMAAA,EAACJ,6QAAA,EAAA;YAA0BM,aAAA,EAAeH,gBAAA,CAAiBI,MAAAA;;;AAGjE", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3368, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Select/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Select/index.tsx"], "sourcesContent": ["'use client'\nimport type { I18nClient } from '@payloadcms/translations'\nimport type { Option, SelectField, SelectFieldDiffClientComponent } from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { FieldDiffContainer, getHTMLDiffComponents, useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'select-diff'\n\nconst getOptionsToRender = (\n  value: string,\n  options: SelectField['options'],\n  hasMany: boolean,\n): Option | Option[] => {\n  if (hasMany && Array.isArray(value)) {\n    return value.map(\n      (val) =>\n        options.find((option) => (typeof option === 'string' ? option : option.value) === val) ||\n        String(val),\n    )\n  }\n  return (\n    options.find((option) => (typeof option === 'string' ? option : option.value) === value) ||\n    String(value)\n  )\n}\n\n/**\n * Translates option labels while ensuring they are strings.\n * If `options.label` is a JSX element, it falls back to `options.value` because `DiffViewer`\n * expects all values to be strings.\n */\nconst getTranslatedOptions = (options: Option | Option[], i18n: I18nClient): string => {\n  if (Array.isArray(options)) {\n    return options\n      .map((option) => {\n        if (typeof option === 'string') {\n          return option\n        }\n        const translatedLabel = getTranslation(option.label, i18n)\n\n        // Ensure the result is a string, otherwise use option.value\n        return typeof translatedLabel === 'string' ? translatedLabel : option.value\n      })\n      .join(', ')\n  }\n\n  if (typeof options === 'string') {\n    return options\n  }\n\n  const translatedLabel = getTranslation(options.label, i18n)\n\n  return typeof translatedLabel === 'string' ? translatedLabel : options.value\n}\n\nexport const Select: SelectFieldDiffClientComponent = ({\n  comparisonValue: valueFrom,\n  diffMethod,\n  field,\n  locale,\n  nestingLevel,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n\n  const options = 'options' in field && field.options\n\n  const renderedValueFrom =\n    typeof valueFrom !== 'undefined'\n      ? getTranslatedOptions(\n          getOptionsToRender(\n            typeof valueFrom === 'string' ? valueFrom : JSON.stringify(valueFrom),\n            options,\n            field.hasMany,\n          ),\n          i18n,\n        )\n      : ''\n\n  const renderedValueTo =\n    typeof valueTo !== 'undefined'\n      ? getTranslatedOptions(\n          getOptionsToRender(\n            typeof valueTo === 'string' ? valueTo : JSON.stringify(valueTo),\n            options,\n            field.hasMany,\n          ),\n          i18n,\n        )\n      : ''\n\n  const { From, To } = getHTMLDiffComponents({\n    fromHTML: '<p>' + renderedValueFrom + '</p>',\n    toHTML: '<p>' + renderedValueTo + '</p>',\n    tokenizeByCharacter: true,\n  })\n\n  return (\n    <FieldDiffContainer\n      className={baseClass}\n      From={From}\n      i18n={i18n}\n      label={{\n        label: field.label,\n        locale,\n      }}\n      nestingLevel={nestingLevel}\n      To={To}\n    />\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "FieldDiff<PERSON><PERSON><PERSON>", "getHTMLDiffComponents", "useTranslation", "React", "baseClass", "getOptionsToRender", "value", "options", "hasMany", "Array", "isArray", "map", "val", "find", "option", "String", "getTranslatedOptions", "i18n", "<PERSON><PERSON><PERSON><PERSON>", "label", "join", "Select", "t0", "$", "comparisonValue", "valueFrom", "field", "locale", "nestingLevel", "versionValue", "valueTo", "renderedValueFrom", "JSON", "stringify", "renderedValueTo", "t1", "t2", "t3", "From", "To", "fromHTML", "toHTML", "tokenizeByCharacter", "_jsx", "className"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAIA,SAASC,cAAc,QAAQ;AAC/B,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ;;AAC1E,OAAOC,KAAA,MAAW;AANlB;;;;;;AAUA,MAAMC,SAAA,GAAY;AAElB,MAAMC,kBAAA,GAAqBA,CACzBC,KAAA,EACAC,OAAA,EACAC,OAAA;IAEA,IAAIA,OAAA,IAAWC,KAAA,CAAMC,OAAO,CAACJ,KAAA,GAAQ;QACnC,OAAOA,KAAA,CAAMK,GAAG,EACbC,GAAA,GACCL,OAAA,CAAQM,IAAI,EAAEC,MAAA,GAAW,CAAC,OAAOA,MAAA,KAAW,WAAWA,MAAA,GAASA,MAAA,CAAOR,KAAK,MAAMM,GAAA,KAClFG,MAAA,CAAOH,GAAA;IAEb;IACA,OACEL,OAAA,CAAQM,IAAI,EAAEC,MAAA,GAAW,CAAC,OAAOA,MAAA,KAAW,WAAWA,MAAA,GAASA,MAAA,CAAOR,KAAK,MAAMA,KAAA,KAClFS,MAAA,CAAOT,KAAA;AAEX;AAEA;;;;IAKA,MAAMU,oBAAA,GAAuBA,CAACT,OAAA,EAA4BU,IAAA;IACxD,IAAIR,KAAA,CAAMC,OAAO,CAACH,OAAA,GAAU;QAC1B,OAAOA,OAAA,CACJI,GAAG,EAAEG,MAAA;YACJ,IAAI,OAAOA,MAAA,KAAW,UAAU;gBAC9B,OAAOA,MAAA;YACT;YACA,MAAMI,eAAA,OAAkBnB,kOAAA,EAAee,MAAA,CAAOK,KAAK,EAAEF,IAAA;YAErD,4DAAA;YACA,OAAO,OAAOC,eAAA,KAAoB,WAAWA,eAAA,GAAkBJ,MAAA,CAAOR,KAAK;QAC7E,GACCc,IAAI,CAAC;IACV;IAEA,IAAI,OAAOb,OAAA,KAAY,UAAU;QAC/B,OAAOA,OAAA;IACT;IAEA,MAAMW,eAAA,OAAkBnB,kOAAA,EAAeQ,OAAA,CAAQY,KAAK,EAAEF,IAAA;IAEtD,OAAO,OAAOC,eAAA,KAAoB,WAAWA,eAAA,GAAkBX,OAAA,CAAQD,KAAK;AAC9E;AAEO,MAAMe,MAAA,IAAyCC,EAAA;IAAA,MAAAC,CAAA,OAAAzB,0MAAA,EAAA;IAAC,MAAA,EAAA0B,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAR,EAOtD;IACC,MAAA,EAAAL,IAAAA,EAAA,OAAiBf,0QAAA;IAEjB,MAAAK,OAAA,GAAgB,aAAamB,KAAA,IAASA,KAAA,CAAAnB,OAAa;IAEnD,MAAAwB,iBAAA,GACE,OAAON,SAAA,KAAc,cACjBT,oBAAA,CACEX,kBAAA,CACE,OAAOoB,SAAA,KAAc,WAAWA,SAAA,GAAYO,IAAA,CAAAC,SAAA,CAAeR,SAAA,GAC3DlB,OAAA,EACAmB,KAAA,CAAAlB,OAAa,GAEfS,IAAA,IAEF;IAEN,MAAAiB,eAAA,GACE,OAAOJ,OAAA,KAAY,cACfd,oBAAA,CACEX,kBAAA,CACE,OAAOyB,OAAA,KAAY,WAAWA,OAAA,GAAUE,IAAA,CAAAC,SAAA,CAAeH,OAAA,GACvDvB,OAAA,EACAmB,KAAA,CAAAlB,OAAa,GAEfS,IAAA,IAEF;IAGM,MAAAkB,EAAA,GAAA,QAAQJ,iBAAA,GAAoB;IAC9B,MAAAK,EAAA,GAAA,QAAQF,eAAA,GAAkB;IAAA,IAAAG,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAG,KAAA,CAAAP,KAAA,IAAAI,CAAA,CAAA,EAAA,KAAAN,IAAA,IAAAM,CAAA,CAAA,EAAA,KAAAI,MAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,YAAA,IAAAL,CAAA,CAAA,EAAA,KAAAY,EAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAa,EAAA,EAAA;QAFpC,MAAA,EAAAE,IAAA,EAAAC,EAAAA,EAAA,OAAqBtC,8OAAA,EAAA;YAAAuC,QAAA,EACTL,EAA4B;YAAAM,MAAA,EAC9BL,EAA0B;YAAAM,mBAAA,EAAA;QAAA,CAEpC;QAGEL,EAAA,OAAAM,uMAAA,EAAA3C,2OAAA,EAAA;YAAA4C,SAAA,EAAAxC,SAAA;YAAAkC,IAAA;YAAArB,IAAA;YAAAE,KAAA,EAAA;gBAAAA,KAAA,EAKWO,KAAA,CAAAP,KAAA;gBAAAQ;YAAA;YAAAC,YAAA;YAAAW;QAAA,C;;;;;;;;;;;WALXF,E;CAYJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3455, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Tabs/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Tabs/index.tsx"], "sourcesContent": ["'use client'\nimport type {\n  ClientTab,\n  FieldDiffClientProps,\n  TabsFieldClient,\n  TabsFieldDiffClientComponent,\n  VersionTab,\n} from 'payload'\n\nimport { getTranslation } from '@payloadcms/translations'\nimport { useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\nimport { useSelectedLocales } from '../../../Default/SelectedLocalesContext.js'\nimport { DiffCollapser } from '../../DiffCollapser/index.js'\nimport { RenderVersionFieldsToDiff } from '../../RenderVersionFieldsToDiff.js'\n\nconst baseClass = 'tabs-diff'\n\nexport const Tabs: TabsFieldDiffClientComponent = (props) => {\n  const { baseVersionField, comparisonValue: valueFrom, field, versionValue: valueTo } = props\n  const { selectedLocales } = useSelectedLocales()\n\n  return (\n    <div className={baseClass}>\n      {baseVersionField.tabs.map((tab, i) => {\n        if (!tab?.fields?.length) {\n          return null\n        }\n        const fieldTab = field.tabs?.[i]\n        return (\n          <div className={`${baseClass}__tab`} key={i}>\n            {(() => {\n              if ('name' in fieldTab && selectedLocales && fieldTab.localized) {\n                // Named localized tab\n                return selectedLocales.map((locale, index) => {\n                  const localizedTabProps: TabProps = {\n                    ...props,\n                    comparisonValue: valueFrom?.[tab.name]?.[locale],\n                    fieldTab,\n                    locale,\n                    tab,\n                    versionValue: valueTo?.[tab.name]?.[locale],\n                  }\n                  return (\n                    <div className={`${baseClass}__tab-locale`} key={[locale, index].join('-')}>\n                      <div className={`${baseClass}__tab-locale-value`}>\n                        <Tab key={locale} {...localizedTabProps} />\n                      </div>\n                    </div>\n                  )\n                })\n              } else if ('name' in tab && tab.name) {\n                // Named tab\n                const namedTabProps: TabProps = {\n                  ...props,\n                  comparisonValue: valueFrom?.[tab.name],\n                  fieldTab,\n                  tab,\n                  versionValue: valueTo?.[tab.name],\n                }\n                return <Tab key={i} {...namedTabProps} />\n              } else {\n                // Unnamed tab\n                return <Tab fieldTab={fieldTab} key={i} {...props} tab={tab} />\n              }\n            })()}\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n\ntype TabProps = {\n  fieldTab: ClientTab\n  tab: VersionTab\n} & FieldDiffClientProps<TabsFieldClient>\n\nconst Tab: React.FC<TabProps> = ({\n  comparisonValue: valueFrom,\n  fieldTab,\n  locale,\n  parentIsLocalized,\n  tab,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n  const { selectedLocales } = useSelectedLocales()\n\n  if (!tab.fields?.length) {\n    return null\n  }\n\n  return (\n    <DiffCollapser\n      fields={fieldTab.fields}\n      Label={\n        'label' in tab &&\n        tab.label &&\n        typeof tab.label !== 'function' && (\n          <span>\n            {locale && <span className={`${baseClass}__locale-label`}>{locale}</span>}\n            {getTranslation(tab.label, i18n)}\n          </span>\n        )\n      }\n      locales={selectedLocales}\n      parentIsLocalized={parentIsLocalized || fieldTab.localized}\n      valueFrom={valueFrom}\n      valueTo={valueTo}\n    >\n      <RenderVersionFieldsToDiff versionFields={tab.fields} />\n    </DiffCollapser>\n  )\n}\n"], "names": ["c", "_c", "getTranslation", "useTranslation", "React", "useSelectedLocales", "DiffCollapser", "RenderVersionFieldsToDiff", "baseClass", "Tabs", "props", "$", "baseVersionField", "comparisonValue", "valueFrom", "field", "versionValue", "valueTo", "selectedLocales", "t0", "tabs", "t1", "tab", "i", "fields", "length", "fieldTab", "_jsx", "className", "children", "localized", "map", "locale", "index", "localizedTabProps", "name", "Tab", "join", "namedTabProps", "parentIsLocalized", "i18n", "Label", "label", "_jsxs", "<PERSON>Fields"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AASA,SAASC,cAAc,QAAQ;AAC/B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAGlB,SAASC,kBAAkB,QAAQ;AACnC,SAASC,aAAa,QAAQ;AAC9B,SAASC,yBAAyB,QAAQ;AAhB1C;;;;;;;;;AAkBA,MAAMC,SAAA,GAAY;AAEX,MAAMC,IAAA,GAAqCC,KAAA;IAAA,MAAAC,CAAA,OAAAV,0MAAA,EAAA;IAChD,MAAA,EAAAW,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAuFP,KAAA;IACvF,MAAA,EAAAQ,eAAAA,EAAA,OAA4Bb,wPAAA;IAAA,IAAAc,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAC,gBAAA,CAAAQ,IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAI,KAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAD,KAAA,IAAAC,CAAA,CAAA,EAAA,KAAAO,eAAA,IAAAP,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAM,OAAA,EAAA;QAAA,IAAAI,EAAA;QAAA,IAAAV,CAAA,CAAA,EAAA,KAAAI,KAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAD,KAAA,IAAAC,CAAA,CAAA,EAAA,KAAAO,eAAA,IAAAP,CAAA,CAAA,GAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,GAAA,KAAAM,OAAA,EAAA;YAIGI,EAAA,GAAAA,CAAAC,GAAA,EAAAC,CAAA;iCAIRR,KAAA;gBAJQ,IAAA,+DACpBS,MAAA,gDAAAF,GAAA,SAAAG,MAAA,GAAA;oBAAA,OAAA;gBAAA;gBAGL,MAAAC,QAAA,wBAAiBN,IAAA,2DAAA,CAAaG,CAAA,CAAA;gBAAE,WAE9BI,uMAAA,EAAC,OAAA;oBAAAC,SAAA,EAAe,GAAA,OAAApB,SAAA,EAAA,MAAmB;oBAAAqB,QAAA,EAChC,CAAA;wBAAA,IACK,UAAUH,QAAA,IAAYR,eAAA,IAAmBQ,QAAA,CAAAI,SAAkB,EAAA;4BAAA,OAEtDZ,eAAA,CAAAa,GAAA,CAAA,CAAAC,MAAA,EAAAC,KAAA;oCAGcnB,SAAA,YAIHG,OAAA;gCANhB,MAAAiB,iBAAA,GAAA;oCAAA,GACKxB,KAAK;oCAAAG,eAAA,wFACS,CAAYS,GAAA,CAAAa,IAAA,CAAA,2EAAA,CAAYH,MAAA,CAAA;oCAAAN,QAAA;oCAAAM,MAAA;oCAAAV,GAAA;oCAAAN,YAAA,gFAI3B,CAAUM,GAAA,CAAAa,IAAA,CAAA,uEAAA,CAAYH,MAAA,CAAA;gCAAA;gCACtC,WAEEL,uMAAA,EAAC,OAAA;oCAAAC,SAAA,EAAe,GAAA,OAAApB,SAAA,EAAA,aAA0B;oCAAAqB,QAAA,MACxCF,uMAAA,EAAC,OAAA;wCAAAC,SAAA,EAAe,GAAA,OAAApB,SAAA,EAAA,mBAAgC;wCAAAqB,QAAA,MAC9CF,uMAAA,EAAAS,GAAA,EAAA;4CAAA,GAAsBF,iBAAAA;wCAAiB,GAA7BF,MAAA;oCAAA,C;mCAFmC;oCAACA,MAAA;oCAAQC,KAAA;iCAAA,CAAAI,IAAA,CAAY;4BAAA,CAM1E;wBAAA,OAAA;4BAAA,IACS,UAAUf,GAAA,IAAOA,GAAA,CAAAa,IAAQ,EAAA;gCAElC,MAAAG,aAAA,GAAA;oCAAA,GACK5B,KAAK;oCAAAG,eAAA,wDACSC,SAAA,CAAYQ,GAAA,CAAAa,IAAA,CAAA;oCAAAT,QAAA;oCAAAJ,GAAA;oCAAAN,YAAA,oDAGfC,OAAA,CAAUK,GAAA,CAAAa,IAAA,CAAA;gCAAA;gCAC1B,WACOR,uMAAA,EAAAS,GAAA,EAAA;oCAAA,GAAiBE,aAAAA;gCAAa,GAApBf,CAAA;4BAAA,OAAA;gCAAA,OAGVI,2MAAA,EAAAS,GAAA,EAAA;oCAAAV,QAAA;oCAAA,GAAqChB,KAAK;oCAAAY;gCAAA,GAAZC,CAAA;4BAAA;wBAAA;oBAAA,CAAA,EAEzC;gBAAA,GAnCwCA,CAAA;YAAA;YAsC9CZ,CAAA,CAAA,EAAA,GAAAI,KAAA;YAAAJ,CAAA,CAAA,EAAA,GAAAD,KAAA;YAAAC,CAAA,CAAA,EAAA,GAAAO,eAAA;YAAAP,CAAA,CAAA,GAAA,GAAAG,SAAA;YAAAH,CAAA,CAAA,GAAA,GAAAM,OAAA;YAAAN,CAAA,CAAA,GAAA,GAAAU,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAV,CAAA,CAAA,GAAA;QAAA;QA7CFQ,EAAA,OAAAQ,uMAAA,EAAC,OAAA;YAAAC,SAAA,EAAApB,SAAA;YAAAqB,QAAA,EACEjB,gBAAA,CAAAQ,IAAA,CAAAW,GAAA,CAA0BV,EA4C3B;QAAA,C;;;;;;;;;;;WA7CFF,E;CAgDJ;AAOA,MAAMiB,GAAA,IAA0BjB,EAAA;QAWzBG,GAAA;IAXyB,MAAAX,CAAA,OAAAV,0MAAA,EAAA;IAAC,MAAA,EAAAY,eAAA,EAAAC,SAAA,EAAAY,QAAA,EAAAM,MAAA,EAAAO,iBAAA,EAAAjB,GAAA,EAAAN,YAAA,EAAAC,OAAAA,EAAA,GAAAE,EAOhC;IACC,MAAA,EAAAqB,IAAAA,EAAA,OAAiBrC,0QAAA;IACjB,MAAA,EAAAe,eAAAA,EAAA,OAA4Bb,wPAAA;IAAA,IAAA,qBAEvBmB,MAAA,4DAAAC,MAAA,GAAA;QAAA,OAAA;IAAA;IAAA,IAAAJ,EAAA;IAAA,IAAAV,CAAA,CAAA,EAAA,KAAAe,QAAA,CAAAF,MAAA,IAAAb,CAAA,CAAA,EAAA,KAAAe,QAAA,CAAAI,SAAA,IAAAnB,CAAA,CAAA,EAAA,KAAA6B,IAAA,IAAA7B,CAAA,CAAA,EAAA,KAAAqB,MAAA,IAAArB,CAAA,CAAA,EAAA,KAAA4B,iBAAA,IAAA5B,CAAA,CAAA,EAAA,KAAAO,eAAA,IAAAP,CAAA,CAAA,EAAA,KAAAW,GAAA,IAAAX,CAAA,CAAA,EAAA,KAAAG,SAAA,IAAAH,CAAA,CAAA,EAAA,KAAAM,OAAA,EAAA;QAKHI,EAAA,OAAAM,uMAAA,EAAArB,8PAAA,EAAA;YAAAkB,MAAA,EACUE,QAAA,CAAAF,MAAA;YAAAiB,KAAA,EAEN,WAAWnB,GAAA,IACXA,GAAA,CAAAoB,KAAS,IACT,OAAOpB,GAAA,CAAAoB,KAAA,KAAc,kBACnBC,wMAAA,EAAC,QAAA;gBAAAd,QAAA,EAAA;oBACEG,MAAA,QAAUL,uMAAA,EAAC,QAAA;wBAAAC,SAAA,EAAgB,GAAA,OAAApB,SAAA,EAAA,eAA4B;wBAAAqB,QAAA,EAAGG;oBAAA,C;wBAC1D9B,kOAAA,EAAeoB,GAAA,CAAAoB,KAAA,EAAWF,IAAA;iBAAA;YAAA,C;qBAIxBtB,eAAA;YAAAqB,iBAAA,EACUA,iBAAA,IAAqBb,QAAA,CAAAI,SAAkB;YAAAhB,SAAA;YAAAG,OAAA;YAAAY,QAAA,MAI1DF,uMAAA,EAAApB,6QAAA,EAAA;gBAAAqC,aAAA,EAA0CtB,GAAA,CAAAE,MAAAA;YAAA,C;;;;;;;;;;;;;;;WAjB5CH,E;CAoBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3615, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/RenderFieldsToDiff/fields/Text/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/RenderFieldsToDiff/fields/Text/index.tsx"], "sourcesContent": ["'use client'\nimport type { TextFieldDiffClientComponent } from 'payload'\n\nimport { FieldDiffContainer, getHTMLDiffComponents, useTranslation } from '@payloadcms/ui'\n\nimport './index.scss'\n\nimport React from 'react'\n\nconst baseClass = 'text-diff'\n\nfunction formatValue(value: unknown): {\n  tokenizeByCharacter: boolean\n  value: string\n} {\n  if (typeof value === 'string') {\n    return { tokenizeByCharacter: true, value }\n  }\n  if (typeof value === 'number') {\n    return {\n      tokenizeByCharacter: true,\n      value: String(value),\n    }\n  }\n  if (typeof value === 'boolean') {\n    return {\n      tokenizeByCharacter: false,\n      value: String(value),\n    }\n  }\n\n  if (value && typeof value === 'object') {\n    return {\n      tokenizeByCharacter: false,\n      value: `<pre>${JSON.stringify(value, null, 2)}</pre>`,\n    }\n  }\n\n  return {\n    tokenizeByCharacter: true,\n    value: undefined,\n  }\n}\n\nexport const Text: TextFieldDiffClientComponent = ({\n  comparisonValue: valueFrom,\n  field,\n  locale,\n  nestingLevel,\n  versionValue: valueTo,\n}) => {\n  const { i18n } = useTranslation()\n\n  let placeholder = ''\n\n  if (valueTo == valueFrom) {\n    placeholder = `<span class=\"html-diff-no-value\"><span>`\n  }\n\n  const formattedValueFrom = formatValue(valueFrom)\n  const formattedValueTo = formatValue(valueTo)\n\n  let tokenizeByCharacter = true\n  if (formattedValueFrom.value?.length) {\n    tokenizeByCharacter = formattedValueFrom.tokenizeByCharacter\n  } else if (formattedValueTo.value?.length) {\n    tokenizeByCharacter = formattedValueTo.tokenizeByCharacter\n  }\n\n  const renderedValueFrom = formattedValueFrom.value ?? placeholder\n  const renderedValueTo: string = formattedValueTo.value ?? placeholder\n\n  const { From, To } = getHTMLDiffComponents({\n    fromHTML: '<p>' + renderedValueFrom + '</p>',\n    toHTML: '<p>' + renderedValueTo + '</p>',\n    tokenizeByCharacter,\n  })\n\n  return (\n    <FieldDiffContainer\n      className={baseClass}\n      From={From}\n      i18n={i18n}\n      label={{\n        label: field.label,\n        locale,\n      }}\n      nestingLevel={nestingLevel}\n      To={To}\n    />\n  )\n}\n"], "names": ["c", "_c", "FieldDiff<PERSON><PERSON><PERSON>", "getHTMLDiffComponents", "useTranslation", "React", "baseClass", "formatValue", "value", "tokenizeByCharacter", "String", "JSON", "stringify", "undefined", "Text", "t0", "$", "comparisonValue", "valueFrom", "field", "locale", "nestingLevel", "versionValue", "valueTo", "i18n", "placeholder", "t1", "label", "formattedValueFrom", "formattedValueTo", "length", "renderedValueFrom", "renderedValueTo", "From", "To", "fromHTML", "toHTML", "_jsx", "className"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ;;AAI1E,OAAOC,KAAA,MAAW;AAPlB;;;;;AASA,MAAMC,SAAA,GAAY;AAElB,SAASC,YAAYC,KAAc;IAIjC,IAAI,OAAOA,KAAA,KAAU,UAAU;QAC7B,OAAO;YAAEC,mBAAA,EAAqB;YAAMD;QAAM;IAC5C;IACA,IAAI,OAAOA,KAAA,KAAU,UAAU;QAC7B,OAAO;YACLC,mBAAA,EAAqB;YACrBD,KAAA,EAAOE,MAAA,CAAOF,KAAA;QAChB;IACF;IACA,IAAI,OAAOA,KAAA,KAAU,WAAW;QAC9B,OAAO;YACLC,mBAAA,EAAqB;YACrBD,KAAA,EAAOE,MAAA,CAAOF,KAAA;QAChB;IACF;IAEA,IAAIA,KAAA,IAAS,OAAOA,KAAA,KAAU,UAAU;QACtC,OAAO;YACLC,mBAAA,EAAqB;YACrBD,KAAA,EAAO,QAAoC,OAA5BG,IAAA,CAAKC,SAAS,CAACJ,KAAA,EAAO,MAAM,IAAA;QAC7C;IACF;IAEA,OAAO;QACLC,mBAAA,EAAqB;QACrBD,KAAA,EAAOK;IACT;AACF;AAEO,MAAMC,IAAA,IAAqCC,EAAA;IAAA,MAAAC,CAAA,OAAAf,0MAAA,EAAA;IAAC,MAAA,EAAAgB,eAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,MAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,OAAAA,EAAA,GAAAR,EAMlD;IACC,MAAA,EAAAS,IAAAA,EAAA,OAAiBpB,0QAAA;IAEjB,IAAAqB,WAAA,GAAkB;IAAA,IAEdF,OAAA,IAAWL,SAAA,EAAA;QACbO,WAAA,CAAAA,CAAA,CAAcA,2CAAyC;IAAvD;IAAA,IAAAC,EAAA;IAAA,IAAAV,CAAA,CAAA,EAAA,KAAAG,KAAA,CAAAQ,KAAA,IAAAX,CAAA,CAAA,EAAA,KAAAQ,IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAI,MAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,YAAA,IAAAL,CAAA,CAAA,EAAA,KAAAS,WAAA,IAAAT,CAAA,CAAA,EAAA,KAAAE,SAAA,IAAAF,CAAA,CAAA,EAAA,KAAAO,OAAA,EAAA;;QAGF,MAAAK,kBAAA,GAA2BrB,WAAA,CAAYW,SAAA;QACvC,MAAAW,gBAAA,GAAyBtB,WAAA,CAAYgB,OAAA;QAErC,IAAAd,mBAAA,GAAA;QAA0B,oDACtBD,KAAA,8DAAAoB,kBAAA,QAAAE,MAAA,EAAA;YACFrB,mBAAA,CAAAA,CAAA,CAAsBmB,kBAAA,CAAAnB,mBAAA;QAAtB,OAAA;;YAAA,gDACSD,KAAA,4DAAAqB,gBAAA,QAAAC,MAAA,EAAA;gBACTrB,mBAAA,CAAAA,CAAA,CAAsBoB,gBAAA,CAAApB,mBAAA;YAAtB;QAAA;;QAGF,MAAAsB,iBAAA,oDAA0BvB,KAAA,sDAAAoB,kBAAA,WAA4BH,WAAA;;QACtD,MAAAO,eAAA,gDAAgCxB,KAAA,oDAAAqB,gBAAA,WAA0BJ,WAAA;QAE1D,MAAA,EAAAQ,IAAA,EAAAC,EAAAA,EAAA,OAAqB/B,8OAAA,EAAA;YAAAgC,QAAA,EACT,QAAQJ,iBAAA,GAAoB;YAAAK,MAAA,EAC9B,QAAQJ,eAAA,GAAkB;YAAAvB;QAAA,CAEpC;QAGEiB,EAAA,OAAAW,uMAAA,EAAAnC,2OAAA,EAAA;YAAAoC,SAAA,EAAAhC,SAAA;YAAA2B,IAAA;YAAAT,IAAA;YAAAG,KAAA,EAAA;gBAAAA,KAAA,EAKWR,KAAA,CAAAQ,KAAA;gBAAAP;YAAA;YAAAC,YAAA;YAAAa;QAAA,C;;;;;;;;;;;;WALXR,E;CAYJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3719, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/VersionPillLabel/getVersionLabel.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/VersionPillLabel/getVersionLabel.ts"], "sourcesContent": ["import type { TFunction } from '@payloadcms/translations'\nimport type { Pill } from '@payloadcms/ui'\n\ntype Args = {\n  currentlyPublishedVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n  latestDraftVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n  t: TFunction\n  version: {\n    id: number | string\n    version: { _status?: string }\n  }\n}\n\n/**\n * Gets the appropriate version label and version pill styling\n * given existing versions and the current version status.\n */\nexport function getVersionLabel({\n  currentlyPublishedVersion,\n  latestDraftVersion,\n  t,\n  version,\n}: Args): {\n  label: string\n  name: 'currentDraft' | 'currentlyPublished' | 'draft' | 'previouslyPublished' | 'published'\n  pillStyle: Parameters<typeof Pill>[0]['pillStyle']\n} {\n  const publishedNewerThanDraft =\n    currentlyPublishedVersion?.updatedAt > latestDraftVersion?.updatedAt\n\n  if (version.version._status === 'draft') {\n    if (publishedNewerThanDraft) {\n      return {\n        name: 'draft',\n        label: t('version:draft'),\n        pillStyle: 'light',\n      }\n    } else {\n      return {\n        name: version.id === latestDraftVersion?.id ? 'currentDraft' : 'draft',\n        label:\n          version.id === latestDraftVersion?.id ? t('version:currentDraft') : t('version:draft'),\n        pillStyle: 'light',\n      }\n    }\n  } else {\n    const isCurrentlyPublished = version.id === currentlyPublishedVersion?.id\n    return {\n      name: isCurrentlyPublished ? 'currentlyPublished' : 'previouslyPublished',\n      label: isCurrentlyPublished\n        ? t('version:currentlyPublished')\n        : t('version:previouslyPublished'),\n      pillStyle: isCurrentlyPublished ? 'success' : 'light',\n    }\n  }\n}\n"], "names": ["getVersionLabel", "currentlyPublishedVersion", "latestDraftVersion", "t", "version", "publishedNewerThanDraft", "updatedAt", "_status", "name", "label", "pillStyle", "id", "isCurrentlyPublished"], "mappings": "AAmBA;;;GAIA;;;;AAAO,SAASA;UACdC,yBAAyB,EACzBC,kBAAkB,EAClBC,CAAC,EACDC,OAAAA,EACK,GALyB;IAU9B,MAAMC,uBAAA,0FACJJ,yBAAA,CAA2BK,SAAA,6EAAYJ,kBAAA,CAAoBI,SAAA;IAE7D,IAAIF,OAAA,CAAQA,OAAO,CAACG,OAAO,KAAK,SAAS;QACvC,IAAIF,uBAAA,EAAyB;YAC3B,OAAO;gBACLG,IAAA,EAAM;gBACNC,KAAA,EAAON,CAAA,CAAE;gBACTO,SAAA,EAAW;YACb;QACF,OAAO;YACL,OAAO;gBACLF,IAAA,EAAMJ,OAAA,CAAQO,EAAE,MAAKT,kBAAA,yEAAoBS,EAAA,IAAK,iBAAiB;gBAC/DF,KAAA,EACEL,OAAA,CAAQO,EAAE,8EAAKT,kBAAA,CAAoBS,EAAA,IAAKR,CAAA,CAAE,0BAA0BA,CAAA,CAAE;gBACxEO,SAAA,EAAW;YACb;QACF;IACF,OAAO;QACL,MAAME,oBAAA,GAAuBR,OAAA,CAAQO,EAAE,4FAAKV,yBAAA,CAA2BU,EAAA;QACvE,OAAO;YACLH,IAAA,EAAMI,oBAAA,GAAuB,uBAAuB;YACpDH,KAAA,EAAOG,oBAAA,GACHT,CAAA,CAAE,gCACFA,CAAA,CAAE;YACNO,SAAA,EAAWE,oBAAA,GAAuB,YAAY;QAChD;IACF;AACF", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3756, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/VersionPillLabel/VersionPillLabel.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/VersionPillLabel/VersionPillLabel.tsx"], "sourcesContent": ["'use client'\n\nimport { Pill, useConfig, useTranslation } from '@payloadcms/ui'\nimport { formatDate } from '@payloadcms/ui/shared'\nimport React from 'react'\n\nimport './index.scss'\nimport { getVersionLabel } from './getVersionLabel.js'\n\nconst baseClass = 'version-pill-label'\n\nconst renderPill = (label: React.ReactNode, pillStyle: Parameters<typeof Pill>[0]['pillStyle']) => {\n  return (\n    <Pill pillStyle={pillStyle} size=\"small\">\n      {label}\n    </Pill>\n  )\n}\n\nexport const VersionPillLabel: React.FC<{\n  currentlyPublishedVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n  disableDate?: boolean\n\n  doc: {\n    [key: string]: unknown\n    id: number | string\n    publishedLocale?: string\n    updatedAt?: string\n    version: {\n      [key: string]: unknown\n      _status: string\n    }\n  }\n  /**\n   * By default, the date is displayed first, followed by the version label.\n   * @default false\n   */\n  labelFirst?: boolean\n  labelOverride?: React.ReactNode\n  /**\n   * @default 'pill'\n   */\n  labelStyle?: 'pill' | 'text'\n  labelSuffix?: React.ReactNode\n  latestDraftVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n}> = ({\n  currentlyPublishedVersion,\n  disableDate = false,\n  doc,\n  labelFirst = false,\n  labelOverride,\n  labelStyle = 'pill',\n  labelSuffix,\n  latestDraftVersion,\n}) => {\n  const {\n    config: {\n      admin: { dateFormat },\n      localization,\n    },\n  } = useConfig()\n  const { i18n, t } = useTranslation()\n\n  const { label, pillStyle } = getVersionLabel({\n    currentlyPublishedVersion,\n    latestDraftVersion,\n    t,\n    version: doc,\n  })\n  const labelText: React.ReactNode = (\n    <span>\n      {labelOverride || label}\n      {labelSuffix}\n    </span>\n  )\n\n  const showDate = !disableDate && doc.updatedAt\n  const formattedDate = showDate\n    ? formatDate({ date: doc.updatedAt, i18n, pattern: dateFormat })\n    : null\n\n  const localeCode = Array.isArray(doc.publishedLocale)\n    ? doc.publishedLocale[0]\n    : doc.publishedLocale\n\n  const locale =\n    localization && localization?.locales\n      ? localization.locales.find((loc) => loc.code === localeCode)\n      : null\n  const localeLabel = locale ? locale?.label?.[i18n?.language] || locale?.label : null\n\n  return (\n    <div className={baseClass}>\n      {labelFirst ? (\n        <React.Fragment>\n          {labelStyle === 'pill' ? (\n            renderPill(labelText, pillStyle)\n          ) : (\n            <span className={`${baseClass}-text`}>{labelText}</span>\n          )}\n          {showDate && <span className={`${baseClass}-date`}>{formattedDate}</span>}\n        </React.Fragment>\n      ) : (\n        <React.Fragment>\n          {showDate && <span className={`${baseClass}-date`}>{formattedDate}</span>}\n          {labelStyle === 'pill' ? (\n            renderPill(labelText, pillStyle)\n          ) : (\n            <span className={`${baseClass}-text`}>{labelText}</span>\n          )}\n        </React.Fragment>\n      )}\n      {localeLabel && <Pill size=\"small\">{localeLabel}</Pill>}\n    </div>\n  )\n}\n"], "names": ["c", "_c", "<PERSON>ll", "useConfig", "useTranslation", "formatDate", "React", "getVersionLabel", "baseClass", "renderPill", "label", "pillStyle", "_jsx", "size", "VersionPillLabel", "t0", "$", "currentlyPublishedVersion", "disableDate", "t1", "doc", "labelFirst", "t2", "labelOverride", "labelStyle", "t3", "labelSuffix", "latestDraftVersion", "undefined", "config", "t4", "admin", "t5", "localization", "dateFormat", "i18n", "t", "t6", "version", "labelText", "_jsxs", "children", "showDate", "updatedAt", "formattedDate", "date", "pattern", "localeCode", "Array", "isArray", "publishedLocale", "locale", "locales", "find", "loc", "code", "localeLabel", "language", "className", "Fragment"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAEA,SAASC,IAAI,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;;AAChD,SAASC,UAAU,QAAQ;AAC3B,OAAOC,KAAA,MAAW;AAGlB,SAASC,eAAe,QAAQ;AAPhC;;;;;;;AASA,MAAMC,SAAA,GAAY;AAElB,MAAMC,UAAA,GAAaA,CAACC,KAAA,EAAwBC,SAAA;IAC1C,OAAA,WAAA,OACEC,uMAAA,EAACV,6NAAA,EAAA;QAAKS,SAAA,EAAWA,SAAA;QAAWE,IAAA,EAAK;kBAC9BH;;AAGP;AAEO,MAAMI,gBAAA,IAgCRC,EAAA;IAAA,MAAAC,CAAA,OAAAf,0MAAA,EAAA;IAAC,MAAA,EAAAgB,yBAAA,EAAAC,WAAA,EAAAC,EAAA,EAAAC,GAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,EAAA,EAAAC,WAAA,EAAAC,kBAAAA,EAAA,GAAAZ,EASL;IAPC,MAAAG,WAAA,GAAAC,EAAmB,KAAAS,SAAA,GAAA,QAAnBT,EAAmB;IAEnB,MAAAE,UAAA,GAAAC,EAAkB,KAAAM,SAAA,GAAA,QAAlBN,EAAkB;IAElB,MAAAE,UAAA,GAAAC,EAAmB,KAAAG,SAAA,GAAN,MAAM,GAAnBH,EAAmB;IAInB,MAAA,EAAAI,MAAA,EAAAC,EAAAA,EAAA,OAKI3B,gQAAA;IAJM,MAAA,EAAA4B,KAAA,EAAAC,EAAA,EAAAC,YAAAA,EAAA,GAAAH,EAGP;IAFQ,MAAA,EAAAI,UAAAA,EAAA,GAAAF,EAAc;IAIzB,MAAA,EAAAG,IAAA,EAAAC,CAAAA,EAAA,OAAoBhC,0QAAA;IAAA,IAAAiC,EAAA;IAAA,IAAArB,CAAA,CAAA,EAAA,KAAAC,yBAAA,IAAAD,CAAA,CAAA,EAAA,KAAAkB,UAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAE,WAAA,IAAAF,CAAA,CAAA,EAAA,KAAAI,GAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAmB,IAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAK,UAAA,IAAAL,CAAA,CAAA,EAAA,KAAAO,aAAA,IAAAP,CAAA,CAAA,EAAA,KAAAQ,UAAA,IAAAR,CAAA,CAAA,EAAA,KAAAU,WAAA,IAAAV,CAAA,CAAA,EAAA,KAAAW,kBAAA,IAAAX,CAAA,CAAA,GAAA,KAAAiB,YAAA,IAAAjB,CAAA,CAAA,GAAA,KAAAoB,CAAA,EAAA;YA4BSe,MAAA;QA1B7B,MAAA,EAAAzC,KAAA,EAAAC,SAAAA,EAAA,GAA6BJ,2PAAA,EAAA;YAAAU,yBAAA;YAAAU,kBAAA;YAAAS,CAAA;YAAAE,OAAA,EAIlBlB;QAAA,CACX;QACA,MAAAmB,SAAA,OACEC,wMAAA,EAAC,QAAA;YAAAC,QAAA,EAAA;gBACElB,aAAA,IAAiBb,KAAA;gBACjBgB,WAAA;aAAA;QAAA,C;QAIL,MAAAgB,QAAA,GAAiB,CAACxB,WAAA,IAAeE,GAAA,CAAAuB,SAAa;QAC9C,MAAAC,aAAA,GAAsBF,QAAA,OAClBrC,mOAAA,EAAA;YAAAwC,IAAA,EAAmBzB,GAAA,CAAAuB,SAAA;YAAAR,IAAA;YAAAW,OAAA,EAA8BZ;QAAA,CAAW,IAAA,IAC5D;QAEJ,MAAAa,UAAA,GAAmBC,KAAA,CAAAC,OAAA,CAAc7B,GAAA,CAAA8B,eAAmB,IAChD9B,GAAA,CAAA8B,eAAA,CAAA,EAAA,GACA9B,GAAA,CAAA8B,eAAmB;QAEvB,MAAAC,MAAA,GACElB,YAAA,iEAAgBA,YAAA,CAAAmB,OAAc,IAC1BnB,YAAA,CAAAmB,OAAA,CAAAC,IAAA,EAAAC,GAAA,GAAmCA,GAAA,CAAAC,IAAA,KAAaR,UAAA,IAAA,IAChD;QACN,MAAAS,WAAA,GAAoBL,MAAA,4EAASzC,KAAA,+DAAA,6CAAgByB,IAAA,CAAAsB,QAAA,CAAA,yBAAmBN,MAAA,8BAAAzC,KAAQ,IAAA,IAAQ;QAG9E2B,EAAA,OAAAG,wMAAA,EAAC,OAAA;YAAAkB,SAAA,EAAAlD,SAAA;YAAAiC,QAAA,EAAA;gBACEpB,UAAA,OACCmB,wMAAA,EAAAlC,kMAAA,CAAAqD,QAAA,EAAA;oBAAAlB,QAAA,EAAA;wBACGjB,UAAA,KAAe,SACdf,UAAA,CAAW8B,SAAA,EAAW5B,SAAA,QAEtBC,uMAAA,EAAC,QAAA;4BAAA8C,SAAA,EAAgB,GAAA,OAAAlD,SAAA,EAAA,MAAmB;4BAAAiC,QAAA,EAAGF;wBAAA,C;wBAExCG,QAAA,QAAY9B,uMAAA,EAAC,QAAA;4BAAA8C,SAAA,EAAgB,GAAA,OAAAlD,SAAA,EAAA,MAAmB;4BAAAiC,QAAA,EAAGG;wBAAA,C;qB;yBAGtDJ,wMAAA,EAAAlC,kMAAA,CAAAqD,QAAA,EAAA;oBAAAlB,QAAA,EAAA;wBACGC,QAAA,QAAY9B,uMAAA,EAAC,QAAA;4BAAA8C,SAAA,EAAgB,GAAA,OAAAlD,SAAA,EAAA,MAAmB;4BAAAiC,QAAA,EAAGG;wBAAA,C;wBACnDpB,UAAA,KAAe,SACdf,UAAA,CAAW8B,SAAA,EAAW5B,SAAA,QAEtBC,uMAAA,EAAC,QAAA;4BAAA8C,SAAA,EAAgB,GAAA,MAAmB,CAAnBlD,SAAA,EAAA;4BAAmBiC,QAAA,EAAGF;wBAAA,C;qB;;gBAI5CiB,WAAA,QAAe5C,uMAAA,EAAAV,6NAAA,EAAA;oBAAAW,IAAA,EAAW;oBAAA4B,QAAA,EAASe;gBAAA,C;a;;;;;;;;;;;;;;;;;;WApBtCnB,E;CAuBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Version/SelectComparison/VersionDrawer/CreatedAtCell.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Version/SelectComparison/VersionDrawer/CreatedAtCell.tsx"], "sourcesContent": ["'use client'\nimport { useConfig, useModal, useRouteTransition, useTranslation } from '@payloadcms/ui'\nimport { formatDate } from '@payloadcms/ui/shared'\nimport { usePathname, useRouter, useSearchParams } from 'next/navigation.js'\n\nimport type { CreatedAtCellProps } from '../../../Versions/cells/CreatedAt/index.js'\n\nexport const VersionDrawerCreatedAtCell: React.FC<CreatedAtCellProps> = ({\n  rowData: { id, updatedAt } = {},\n}) => {\n  const {\n    config: {\n      admin: { dateFormat },\n    },\n  } = useConfig()\n  const { closeAllModals } = useModal()\n  const router = useRouter()\n  const pathname = usePathname()\n  const searchParams = useSearchParams()\n  const { startRouteTransition } = useRouteTransition()\n\n  const { i18n } = useTranslation()\n\n  return (\n    <button\n      className=\"created-at-cell\"\n      onClick={() => {\n        closeAllModals()\n        const current = new URLSearchParams(Array.from(searchParams.entries()))\n\n        if (id) {\n          current.set('versionFrom', String(id))\n        }\n\n        const search = current.toString()\n        const query = search ? `?${search}` : ''\n\n        startRouteTransition(() => router.push(`${pathname}${query}`))\n      }}\n      type=\"button\"\n    >\n      {formatDate({ date: updatedAt, i18n, pattern: dateFormat })}\n    </button>\n  )\n}\n"], "names": ["useConfig", "useModal", "useRouteTransition", "useTranslation", "formatDate", "usePathname", "useRouter", "useSearchParams", "VersionDrawerCreatedAtCell", "t0", "rowData", "t1", "id", "updatedAt", "undefined", "config", "t2", "admin", "t3", "dateFormat", "closeAllModals", "router", "pathname", "searchParams", "startRouteTransition", "i18n", "_jsx", "className", "onClick", "current", "URLSearchParams", "Array", "from", "entries", "set", "String", "search", "toString", "query", "push", "type", "children", "date", "pattern"], "mappings": ";;;;;AACA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,cAAc,QAAQ;;;AACxE,SAASC,UAAU,QAAQ;AAC3B,SAASC,WAAW,EAAEC,SAAS,EAAEC,eAAe,QAAQ;AAHxD;;;;;AAOO,MAAMC,0BAAA,IAA2DC,EAAA;IAAC,MAAA,EAAAC,OAAA,EAAAC,EAAAA,EAAA,GAAAF,EAExE;IADU,MAAA,EAAAG,EAAA,EAAAC,SAAAA,EAAA,GAAAF,EAAsB,KAAAG,SAAA,GAAA,CAAA,IAAtBH,EAAsB;IAE/B,MAAA,EAAAI,MAAA,EAAAC,EAAAA,EAAA,OAIIhB,gQAAA;IAHM,MAAA,EAAAiB,KAAA,EAAAC,EAAAA,EAAA,GAAAF,EAEP;IADQ,MAAA,EAAAG,UAAAA,EAAA,GAAAD,EAAc;IAGzB,MAAA,EAAAE,cAAAA,EAAA,OAA2BnB,iOAAA;IAC3B,MAAAoB,MAAA,OAAef,4KAAA;IACf,MAAAgB,QAAA,OAAiBjB,8KAAA;IACjB,MAAAkB,YAAA,OAAqBhB,kLAAA;IACrB,MAAA,EAAAiB,oBAAAA,EAAA,OAAiCtB,2OAAA;IAEjC,MAAA,EAAAuB,IAAAA,EAAA,OAAiBtB,0QAAA;IAAA,WAGfuB,uMAAA,EAAC,UAAA;QAAAC,SAAA,EACW;QAAAC,OAAA,EAAAA,CAAA;YAERR,cAAA;YACA,MAAAS,OAAA,GAAA,IAAAC,eAAA,CAAoCC,KAAA,CAAAC,IAAA,CAAWT,YAAA,CAAAU,OAAA,CAAoB;YAAA,IAE/DrB,EAAA,EAAA;gBACFiB,OAAA,CAAAK,GAAA,CAAY,eAAeC,MAAA,CAAOvB,EAAA;YAAA;YAGpC,MAAAwB,MAAA,GAAeP,OAAA,CAAAQ,QAAA,CAAgB;YAC/B,MAAAC,KAAA,GAAcF,MAAA,GAAS,IAAI,CAAQ,MAARA,MAAA,IAAW;YAEtCZ,oBAAA,CAAA,IAA2BH,MAAA,CAAAkB,IAAA,CAAY,UAAGjB,QAAA,EAAW,CAAO,MAAPgB,KAAA;QAAO;QAAAE,IAAA,EAEzD;QAAAC,QAAA,MAEJrC,mOAAA,EAAA;YAAAsC,IAAA,EAAmB7B,SAAA;YAAAY,IAAA;YAAAkB,OAAA,EAA0BxB;QAAA,CAAW;IAAA,C;CAG/D", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3922, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Versions/cells/AutosaveCell/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Versions/cells/AutosaveCell/index.tsx"], "sourcesContent": ["'use client'\nimport { Pill, useTranslation } from '@payloadcms/ui'\nimport React from 'react'\n\nimport './index.scss'\nimport { VersionPillLabel } from '../../../Version/VersionPillLabel/VersionPillLabel.js'\n\nconst baseClass = 'autosave-cell'\n\ntype AutosaveCellProps = {\n  currentlyPublishedVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n  latestDraftVersion?: {\n    id: number | string\n    updatedAt: string\n  }\n  rowData: {\n    autosave?: boolean\n    id: number | string\n    publishedLocale?: string\n    version: {\n      _status: string\n    }\n  }\n}\n\nexport const AutosaveCell: React.FC<AutosaveCellProps> = ({\n  currentlyPublishedVersion,\n  latestDraftVersion,\n  rowData,\n}) => {\n  const { t } = useTranslation()\n\n  return (\n    <div className={`${baseClass}__items`}>\n      {rowData?.autosave && <Pill size=\"small\">{t('version:autosave')}</Pill>}\n      <VersionPillLabel\n        currentlyPublishedVersion={currentlyPublishedVersion}\n        disableDate={true}\n        doc={rowData}\n        labelFirst={false}\n        labelStyle=\"pill\"\n        latestDraftVersion={latestDraftVersion}\n      />\n    </div>\n  )\n}\n"], "names": ["c", "_c", "<PERSON>ll", "useTranslation", "React", "VersionPillLabel", "baseClass", "AutosaveCell", "t0", "$", "currentlyPublishedVersion", "latestDraftVersion", "rowData", "t", "t1", "_jsxs", "className", "children", "autosave", "_jsx", "size", "disableDate", "doc", "labelFirst", "labelStyle"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SAASC,IAAI,EAAEC,cAAc,QAAQ;;AACrC,OAAOC,KAAA,MAAW;AAGlB,SAASC,gBAAgB,QAAQ;AALjC;;;;;;AAOA,MAAMC,SAAA,GAAY;AAqBX,MAAMC,YAAA,IAA4CC,EAAA;IAAA,MAAAC,CAAA,OAAAR,0MAAA,EAAA;IAAC,MAAA,EAAAS,yBAAA,EAAAC,kBAAA,EAAAC,OAAAA,EAAA,GAAAJ,EAIzD;IACC,MAAA,EAAAK,CAAAA,EAAA,OAAcV,0QAAA;IAAA,IAAAW,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAC,yBAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,kBAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,OAAA,IAAAH,CAAA,CAAA,EAAA,KAAAI,CAAA,EAAA;QAGZC,EAAA,OAAAC,wMAAA,EAAC,OAAA;YAAAC,SAAA,EAAe,GAAA,OAAAV,SAAA,EAAA,QAAqB;YAAAW,QAAA,EAAA;mEAClCL,OAAA,CAAAM,QAAA,KAAqBC,2MAAA,EAAAjB,6NAAA,EAAA;oBAAAkB,IAAA,EAAW;oBAAAH,QAAA,EAASJ,CAAA,CAAE;gBAAA,C;oBAC5CM,uMAAA,EAAAd,yPAAA,EAAA;oBAAAK,yBAAA;oBAAAW,WAAA,EAAA;oBAAAC,GAAA,EAGOV,OAAA;oBAAAW,UAAA,EAAA;oBAAAC,UAAA,EAEM;oBAAAb;gBAAA,C;a;;;;;;;;;;WAPfG,E;CAYJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3976, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Versions/cells/CreatedAt/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Versions/cells/CreatedAt/index.tsx"], "sourcesContent": ["'use client'\nimport { Link, useConfig, useTranslation } from '@payloadcms/ui'\nimport { formatDate } from '@payloadcms/ui/shared'\nimport { formatAdminURL } from 'payload/shared'\nimport React from 'react'\n\nexport type CreatedAtCellProps = {\n  collectionSlug?: string\n  docID?: number | string\n  globalSlug?: string\n  isTrashed?: boolean\n  rowData?: {\n    id: number | string\n    updatedAt: Date | number | string\n  }\n}\n\nexport const CreatedAtCell: React.FC<CreatedAtCellProps> = ({\n  collectionSlug,\n  docID,\n  globalSlug,\n  isTrashed,\n  rowData: { id, updatedAt } = {},\n}) => {\n  const {\n    config: {\n      admin: { dateFormat },\n      routes: { admin: adminRoute },\n    },\n  } = useConfig()\n\n  const { i18n } = useTranslation()\n\n  const trashedDocPrefix = isTrashed ? 'trash/' : ''\n\n  let to: string\n\n  if (collectionSlug) {\n    to = formatAdminURL({\n      adminRoute,\n      path: `/collections/${collectionSlug}/${trashedDocPrefix}${docID}/versions/${id}`,\n    })\n  }\n\n  if (globalSlug) {\n    to = formatAdminURL({\n      adminRoute,\n      path: `/globals/${globalSlug}/versions/${id}`,\n    })\n  }\n\n  return (\n    <Link href={to} prefetch={false}>\n      {formatDate({ date: updatedAt, i18n, pattern: dateFormat })}\n    </Link>\n  )\n}\n"], "names": ["Link", "useConfig", "useTranslation", "formatDate", "formatAdminURL", "React", "CreatedAtCell", "t0", "collectionSlug", "docID", "globalSlug", "isTrashed", "rowData", "t1", "id", "updatedAt", "undefined", "config", "t2", "admin", "t3", "routes", "t4", "dateFormat", "adminRoute", "i18n", "trashedDocPrefix", "to", "_jsx", "href", "prefetch", "children", "date", "pattern"], "mappings": ";;;;;AACA,SAASA,IAAI,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;;AAChD,SAASC,UAAU,QAAQ;AAC3B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAJlB;;;;;;AAiBO,MAAMC,aAAA,IAA8CC,EAAA;IAAC,MAAA,EAAAC,cAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,EAAAA,EAAA,GAAAN,EAM3D;IADU,MAAA,EAAAO,EAAA,EAAAC,SAAAA,EAAA,GAAAF,EAAsB,KAAAG,SAAA,GAAA,CAAA,IAAtBH,EAAsB;IAE/B,MAAA,EAAAI,MAAA,EAAAC,EAAAA,EAAA,OAKIjB,gQAAA;IAJM,MAAA,EAAAkB,KAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,EAAAA,EAAA,GAAAJ,EAGP;IAFQ,MAAA,EAAAK,UAAAA,EAAA,GAAAH,EAAc;IACb,MAAA,EAAAD,KAAA,EAAAK,UAAAA,EAAA,GAAAF,EAAqB;IAIjC,MAAA,EAAAG,IAAAA,EAAA,OAAiBvB,0QAAA;IAEjB,MAAAwB,gBAAA,GAAyBf,SAAA,GAAY,WAAW;IAE5CgB,GAAA,CAAAA,EAAA;IAAA,IAEAnB,cAAA,EAAA;QACFmB,EAAA,CAAAA,CAAA,KAAKA,6MAAAA,EAAAA;YAAAA;YAAAA,KAAA,CAEGA,gBAAkCD,OAAlBlB,SAAkB,KAAlB,EAAA,KAAqCC,KAAA,kCAAA,cAAkB,OAAAK,EAAA;QAAI,CACnF;IAHA;IAAA,IAMEJ,UAAA,EAAA;QACFiB,EAAA,CAAAA,CAAA,KAAKA,6MAAAA,EAAAA;YAAAA;YAAAA,KAAA,CAEGA,YAAmCb,EAAA,KAAvBJ,UAAA,EAAA,cAAuB;QAAI,CAC/C;IAHA;IAAA,WAOAkB,uMAAA,EAAA5B,6NAAA,EAAA;QAAA6B,IAAA,EAAYF,EAAA;QAAAG,QAAA,EAAA;QAAAC,QAAA,MACT5B,mOAAA,EAAA;YAAA6B,IAAA,EAAmBjB,SAAA;YAAAU,IAAA;YAAAQ,OAAA,EAA0BV;QAAA,CAAW;IAAA,C;CAG/D", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4029, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Versions/cells/ID/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Versions/cells/ID/index.tsx"], "sourcesContent": ["'use client'\nimport React, { Fragment } from 'react'\n\nexport function IDCell({ id }: { id: number | string }) {\n  return <Fragment>{id}</Fragment>\n}\n"], "names": ["React", "Fragment", "IDCell", "id", "_jsx"], "mappings": ";;;;;AACA,OAAOA,KAAA,IAASC,QAAQ,QAAQ;AADhC;;;AAGO,SAASC;UAASC,EAAAA,EAA6B,GAA/B;IACrB,OAAA,WAAA,OAAOC,uMAAA,EAACH,mMAAA,EAAA;kBAAUE;;AACpB", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4048, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Versions/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Versions/index.client.tsx"], "sourcesContent": ["'use client'\nimport type { Column, SanitizedCollectionConfig } from 'payload'\n\nimport {\n  LoadingOverlayToggle,\n  Pagination,\n  PerPage,\n  Table,\n  useListQuery,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { useSearchParams } from 'next/navigation.js'\nimport React from 'react'\n\nexport const VersionsViewClient: React.FC<{\n  readonly baseClass: string\n  readonly columns: Column[]\n  readonly fetchURL: string\n  readonly paginationLimits?: SanitizedCollectionConfig['admin']['pagination']['limits']\n}> = (props) => {\n  const { baseClass, columns, paginationLimits } = props\n\n  const { data, handlePageChange, handlePerPageChange } = useListQuery()\n\n  const searchParams = useSearchParams()\n  const limit = searchParams.get('limit')\n\n  const { i18n } = useTranslation()\n\n  const versionCount = data?.totalDocs || 0\n\n  return (\n    <React.Fragment>\n      <LoadingOverlayToggle name=\"versions\" show={!data} />\n      {versionCount === 0 && (\n        <div className={`${baseClass}__no-versions`}>\n          {i18n.t('version:noFurtherVersionsFound')}\n        </div>\n      )}\n      {versionCount > 0 && (\n        <React.Fragment>\n          <Table columns={columns} data={data?.docs} />\n          <div className={`${baseClass}__page-controls`}>\n            <Pagination\n              hasNextPage={data.hasNextPage}\n              hasPrevPage={data.hasPrevPage}\n              limit={data.limit}\n              nextPage={data.nextPage}\n              numberOfNeighbors={1}\n              onChange={handlePageChange}\n              page={data.page}\n              prevPage={data.prevPage}\n              totalPages={data.totalPages}\n            />\n            {data?.totalDocs > 0 && (\n              <React.Fragment>\n                <div className={`${baseClass}__page-info`}>\n                  {data.page * data.limit - (data.limit - 1)}-\n                  {data.totalPages > 1 && data.totalPages !== data.page\n                    ? data.limit * data.page\n                    : data.totalDocs}{' '}\n                  {i18n.t('general:of')} {data.totalDocs}\n                </div>\n                <PerPage\n                  handleChange={handlePerPageChange}\n                  limit={limit ? Number(limit) : 10}\n                  limits={paginationLimits}\n                />\n              </React.Fragment>\n            )}\n          </div>\n        </React.Fragment>\n      )}\n    </React.Fragment>\n  )\n}\n"], "names": ["c", "_c", "LoadingOverlayToggle", "Pagination", "PerPage", "Table", "useListQuery", "useTranslation", "useSearchParams", "React", "VersionsViewClient", "props", "$", "baseClass", "columns", "paginationLimits", "data", "handlePageChange", "handlePerPageChange", "searchParams", "t0", "get", "limit", "i18n", "versionCount", "totalDocs", "t1", "t2", "_jsxs", "Fragment", "children", "_jsx", "name", "show", "className", "t", "docs", "hasNextPage", "hasPrevPage", "nextPage", "numberOfNeighbors", "onChange", "page", "prevPage", "totalPages", "handleChange", "Number", "limits"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SACEC,oBAAoB,EACpBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,YAAY,EACZC,cAAc,QACT;;AACP,SAASC,eAAe,QAAQ;AAChC,OAAOC,KAAA,MAAW;AAZlB;;;;;;AAcO,MAAMC,kBAAA,IAKRC,KAAA;IAAA,MAAAC,CAAA,OAAAX,0MAAA,EAAA;IACH,MAAA,EAAAY,SAAA,EAAAC,OAAA,EAAAC,gBAAAA,EAAA,GAAiDJ,KAAA;IAEjD,MAAA,EAAAK,IAAA,EAAAC,gBAAA,EAAAC,mBAAAA,EAAA,OAAwDZ,qOAAA;IAExD,MAAAa,YAAA,OAAqBX,kLAAA;IAAA,IAAAY,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAO,YAAA,EAAA;QACPC,EAAA,GAAAD,YAAA,CAAAE,GAAA,CAAiB;QAAAT,CAAA,CAAA,EAAA,GAAAO,YAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAA/B,MAAAU,KAAA,GAAcF,EAAiB;IAE/B,MAAA,EAAAG,IAAAA,EAAA,OAAiBhB,0QAAA;IAEjB,MAAAiB,YAAA,gDAAqBR,IAAA,CAAAS,SAAA,KAAA,CAAmB;IAIQ,MAAAC,EAAA,GAAA,CAACV,IAAA;IAAA,IAAAW,EAAA;IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAC,SAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,OAAA,IAAAF,CAAA,CAAA,EAAA,KAAAI,IAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,gBAAA,IAAAL,CAAA,CAAA,EAAA,KAAAM,mBAAA,IAAAN,CAAA,CAAA,EAAA,KAAAW,IAAA,IAAAX,CAAA,CAAA,EAAA,KAAAU,KAAA,IAAAV,CAAA,CAAA,EAAA,KAAAG,gBAAA,IAAAH,CAAA,CAAA,GAAA,KAAAc,EAAA,IAAAd,CAAA,CAAA,GAAA,KAAAY,YAAA,EAAA;QAD/CG,EAAA,OAAAC,wMAAA,EAAAnB,kMAAA,CAAAoB,QAAA,EAAA;YAAAC,QAAA,EAAA;oBACEC,uMAAA,EAAA7B,6OAAA,EAAA;oBAAA8B,IAAA,EAA2B;oBAAAC,IAAA,EAAiBP;gBAAC,C;gBAC5CF,YAAA,KAAA,CAAiB,QAChBO,uMAAA,EAAC,OAAA;oBAAAG,SAAA,EAAe,GAAG,OAAArB,SAAA,EAAA,cAAwB;oBAAAiB,QAAA,EACxCP,IAAA,CAAAY,CAAA,CAAO;gBAAA,C;gBAGXX,YAAA,GAAA,CAAe,QACdI,wMAAA,EAAAnB,kMAAA,CAAAoB,QAAA,EAAA;oBAAAC,QAAA,EAAA;4BACEC,uMAAA,EAAA1B,8NAAA,EAAA;4BAAAS,OAAA;4BAAAE,IAAA,EAA+BA,IAAA,6CAAAoB,IAAAA;wBAAA,C;4BAC/BR,wMAAA,EAAC,OAAA;4BAAAM,SAAA,EAAe,GAAG,OAAArB,SAAA,EAAA,gBAA0B;4BAAAiB,QAAA,EAAA;oCAC3CC,uMAAA,EAAA5B,mOAAA,EAAA;oCAAAkC,WAAA,EACerB,IAAA,CAAAqB,WAAA;oCAAAC,WAAA,EACAtB,IAAA,CAAAsB,WAAA;oCAAAhB,KAAA,EACNN,IAAA,CAAAM,KAAA;oCAAAiB,QAAA,EACGvB,IAAA,CAAAuB,QAAA;oCAAAC,iBAAA,EAAA;oCAAAC,QAAA,EAEAxB,gBAAA;oCAAAyB,IAAA,EACJ1B,IAAA,CAAA0B,IAAA;oCAAAC,QAAA,EACI3B,IAAA,CAAA2B,QAAA;oCAAAC,UAAA,EACE5B,IAAA,CAAA4B,UAAAA;gCAAA,C;6EAEb5B,IAAA,CAAAS,SAAA,IAAA,CAAkB,QACjBG,wMAAA,EAAAnB,kMAAA,CAAAoB,QAAA,EAAA;oCAAAC,QAAA,EAAA;4CACEF,wMAAA,EAAC,OAAA;4CAAAM,SAAA,EAAe,GAAG,OAAArB,KAAsB,IAAtB,EAAA;4CAAsBiB,QAAA,EAAA;gDACtCd,IAAA,CAAA0B,IAAA,GAAY1B,IAAA,CAAAM,KAAU,GAAA,CAAIN,IAAA,CAAAM,KAAA,GAAA,CAAa;gDAAG;gDAC1CN,IAAA,CAAA4B,UAAA,GAAA,CAAkB,IAAK5B,IAAA,CAAA4B,UAAA,KAAoB5B,IAAA,CAAA0B,IAAS,GACjD1B,IAAA,CAAAM,KAAA,GAAaN,IAAA,CAAA0B,IAAS,GACtB1B,IAAA,CAAAS,SAAc;gDAAE;gDACnBF,IAAA,CAAAY,CAAA,CAAO;gDAAc;gDAAEnB,IAAA,CAAAS,SAAA;6CAAA;wCAAA,C;4CAE1BM,uMAAA,EAAA3B,gOAAA,EAAA;4CAAAyC,YAAA,EACgB3B,mBAAA;4CAAAI,KAAA,EACPA,KAAA,GAAQwB,MAAA,CAAOxB,KAAA,IAAA,EAAS;4CAAAyB,MAAA,EACvBhC;wCAAA,C;qC;;;;;;;;;;;;;;;;;;;;;;WAlCtBY,E;CA2CJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4160, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/elements/FormHeader/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/elements/FormHeader/index.tsx"], "sourcesContent": ["import React from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'form-header'\n\ntype Props = {\n  description?: React.ReactNode | string\n  heading: string\n}\nexport function FormHeader({ description, heading }: Props) {\n  if (!heading) {\n    return null\n  }\n\n  return (\n    <div className={baseClass}>\n      <h1>{heading}</h1>\n      {<PERSON><PERSON><PERSON>(description) && <p>{description}</p>}\n    </div>\n  )\n}\n"], "names": ["React", "baseClass", "FormHeader", "description", "heading", "_jsxs", "className", "_jsx", "Boolean"], "mappings": ";;;;;AAAA,OAAOA,KAAA,MAAW;;;AAIlB,MAAMC,SAAA,GAAY;AAMX,SAASC;UAAaC,WAAW,EAAEC,OAAAA,EAAgB,GAA/B;IACzB,IAAI,CAACA,OAAA,EAAS;QACZ,OAAO;IACT;IAEA,OAAA,WAAA,OACEC,wMAAA,EAAC,OAAA;QAAIC,SAAA,EAAWL,SAAA;;8BACdM,uMAAA,EAAC,MAAA;0BAAIH;;YACJI,OAAA,CAAQL,WAAA,KAAA,WAAA,OAAgBI,uMAAA,EAAC,KAAA;0BAAGJ;;;;AAGnC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4190, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/ForgotPassword/ForgotPasswordForm/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/ForgotPassword/ForgotPasswordForm/index.tsx"], "sourcesContent": ["'use client'\n\nimport type { FormProps } from '@payloadcms/ui'\nimport type { FormState, PayloadRequest } from 'payload'\n\nimport { EmailField, Form, FormSubmit, TextField, useConfig, useTranslation } from '@payloadcms/ui'\nimport { email, text } from 'payload/shared'\nimport React, { useState } from 'react'\n\nimport { FormHeader } from '../../../elements/FormHeader/index.js'\n\nexport const ForgotPasswordForm: React.FC = () => {\n  const { config, getEntityConfig } = useConfig()\n\n  const {\n    admin: { user: userSlug },\n    routes: { api },\n  } = config\n\n  const { t } = useTranslation()\n  const [hasSubmitted, setHasSubmitted] = useState(false)\n  const collectionConfig = getEntityConfig({ collectionSlug: userSlug })\n  const loginWithUsername = collectionConfig?.auth?.loginWithUsername\n\n  const handleResponse: FormProps['handleResponse'] = (res, successToast, errorToast) => {\n    res\n      .json()\n      .then(() => {\n        setHasSubmitted(true)\n        successToast(t('general:submissionSuccessful'))\n      })\n      .catch(() => {\n        errorToast(\n          loginWithUsername\n            ? t('authentication:usernameNotValid')\n            : t('authentication:emailNotValid'),\n        )\n      })\n  }\n\n  const initialState: FormState = loginWithUsername\n    ? {\n        username: {\n          initialValue: '',\n          valid: true,\n          value: undefined,\n        },\n      }\n    : {\n        email: {\n          initialValue: '',\n          valid: true,\n          value: undefined,\n        },\n      }\n\n  if (hasSubmitted) {\n    return (\n      <FormHeader\n        description={t('authentication:checkYourEmailForPasswordReset')}\n        heading={t('authentication:emailSent')}\n      />\n    )\n  }\n\n  return (\n    <Form\n      action={`${api}/${userSlug}/forgot-password`}\n      handleResponse={handleResponse}\n      initialState={initialState}\n      method=\"POST\"\n    >\n      <FormHeader\n        description={\n          loginWithUsername\n            ? t('authentication:forgotPasswordUsernameInstructions')\n            : t('authentication:forgotPasswordEmailInstructions')\n        }\n        heading={t('authentication:forgotPassword')}\n      />\n\n      {loginWithUsername ? (\n        <TextField\n          field={{\n            name: 'username',\n            label: t('authentication:username'),\n            required: true,\n          }}\n          path=\"username\"\n          validate={(value) =>\n            text(value, {\n              name: 'username',\n              type: 'text',\n              blockData: {},\n              data: {},\n              event: 'onChange',\n              path: ['username'],\n              preferences: { fields: {} },\n              req: {\n                payload: {\n                  config,\n                },\n                t,\n              } as unknown as PayloadRequest,\n              required: true,\n              siblingData: {},\n            })\n          }\n        />\n      ) : (\n        <EmailField\n          field={{\n            name: 'email',\n            admin: {\n              autoComplete: 'email',\n            },\n            label: t('general:email'),\n            required: true,\n          }}\n          path=\"email\"\n          validate={(value) =>\n            email(value, {\n              name: 'email',\n              type: 'email',\n              blockData: {},\n              data: {},\n              event: 'onChange',\n              path: ['email'],\n              preferences: { fields: {} },\n              req: { payload: { config }, t } as unknown as PayloadRequest,\n              required: true,\n              siblingData: {},\n            })\n          }\n        />\n      )}\n      <FormSubmit size=\"large\">{t('general:submit')}</FormSubmit>\n    </Form>\n  )\n}\n"], "names": ["c", "_c", "EmailField", "Form", "FormSubmit", "TextField", "useConfig", "useTranslation", "email", "text", "React", "useState", "FormHeader", "ForgotPasswordForm", "$", "config", "getEntityConfig", "admin", "t0", "routes", "t1", "user", "userSlug", "api", "t", "hasSubmitted", "setHasSubmitted", "t2", "collectionSlug", "collectionConfig", "loginWithUsername", "auth", "t3", "res", "successToast", "errorToast", "json", "then", "catch", "handleResponse", "t4", "t5", "Symbol", "for", "initialState", "username", "initialValue", "valid", "value", "undefined", "t6", "_jsx", "description", "heading", "_jsxs", "action", "method", "children", "field", "name", "label", "required", "path", "validate", "type", "blockData", "data", "event", "preferences", "fields", "req", "payload", "siblingData", "autoComplete", "value_0", "size"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAKA,SAASC,UAAU,EAAEC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;;AACnF,SAASC,KAAK,EAAEC,IAAI,QAAQ;AAC5B,OAAOC,KAAA,IAASC,QAAQ,QAAQ;AAEhC,SAASC,UAAU,QAAQ;AAT3B;;;;;;;AAWO,MAAMC,kBAAA,GAA+BA,CAAA;QAWhBgB,gBAAA;IAXgB,MAAAf,CAAA,OAAAb,0MAAA,EAAA;IAC1C,MAAA,EAAAc,MAAA,EAAAC,eAAAA,EAAA,OAAoCV,gQAAA;IAEpC,MAAA,EAAAW,KAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,EAAAA,EAAA,GAGIL,MAAA;IAFK,MAAA,EAAAM,IAAA,EAAAC,QAAAA,EAAA,GAAAJ,EAAkB;IACjB,MAAA,EAAAK,GAAAA,EAAA,GAAAH,EAAO;IAGjB,MAAA,EAAAI,CAAAA,EAAA,OAAcjB,0QAAA;IACd,MAAA,CAAAkB,YAAA,EAAAC,eAAA,CAAA,OAAwCf,mMAAA,EAAA,KAAS;IAAA,IAAAgB,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAE,eAAA,IAAAF,CAAA,CAAA,EAAA,KAAAQ,QAAA,EAAA;QACxBK,EAAA,GAAAX,eAAA,CAAA;YAAAY,cAAA,EAAkCN;QAAA,CAAS;QAAAR,CAAA,CAAA,EAAA,GAAAE,eAAA;QAAAF,CAAA,CAAA,EAAA,GAAAQ,QAAA;QAAAR,CAAA,CAAA,EAAA,GAAAa,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAb,CAAA,CAAA,EAAA;IAAA;IAApE,MAAAe,gBAAA,GAAyBF,EAA2C;IACpE,MAAAG,iBAAA,kHAA0BC,IAAA,kFAAAD,iBAAA;IAAwB,IAAAE,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAgB,iBAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAU,CAAA,EAAA;QAEEQ,EAAA,GAAAA,CAAAC,GAAA,EAAAC,YAAA,EAAAC,UAAA;YAClDF,GAAA,CAAAG,IAAA,CACO,EAAAC,IAAA,CAAA;gBAEHX,eAAA,CAAA,IAAgB;gBAChBQ,YAAA,CAAaV,CAAA,CAAE;YAAA,CACjB,EAAAc,KAAA,CAAA;gBAEEH,UAAA,CACEL,iBAAA,GACIN,CAAA,CAAE,qCACFA,CAAA,CAAE;YAAA,CAEV;QAAA;QACJV,CAAA,CAAA,EAAA,GAAAgB,iBAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAU,CAAA;QAAAV,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAdA,MAAAyB,cAAA,GAAoDP,EAcpD;IAAA,IAAAQ,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA3B,CAAA,CAAA,EAAA,KAAAS,GAAA,IAAAT,CAAA,CAAA,EAAA,KAAAC,MAAA,IAAAD,CAAA,CAAA,EAAA,KAAAyB,cAAA,IAAAzB,CAAA,CAAA,EAAA,KAAAW,YAAA,IAAAX,CAAA,CAAA,GAAA,KAAAgB,iBAAA,IAAAhB,CAAA,CAAA,GAAA,KAAAU,CAAA,IAAAV,CAAA,CAAA,GAAA,KAAAQ,QAAA,EAAA;QAoBImB,EAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA,6B;;YAlBJ,MAAAC,YAAA,GAAgCd,iBAAA,GAAA;gBAAAe,QAAA,EAAA;oBAAAC,YAAA,EAGV;oBAAAC,KAAA,EAAA;oBAAAC,KAAA,EAAAC;gBAAA;YAAA,IAAA;gBAAAzC,KAAA,EAAA;oBAAAsC,YAAA,EAOA;oBAAAC,KAAA,EAAA;oBAAAC,KAAA,EAAAC;gBAAA;YAAA,CAIlB;YAAA,IAEAxB,YAAA,EAAA;gBAAA,IAAAyB,EAAA;gBAAA,IAAApC,CAAA,CAAA,GAAA,KAAAU,CAAA,EAAA;oBAEA0B,EAAA,OAAAC,uMAAA,EAAAvC,0NAAA,EAAA;wBAAAwC,WAAA,EACe5B,CAAA,CAAE;wBAAA6B,OAAA,EACN7B,CAAA,CAAE;oBAAA,C;;;;;;gBAFbiB,EAAA,GAAAS,E;;;YAQFV,EAAA,OAAAc,wMAAA,EAAAnD,6NAAA,EAAA;gBAAAoD,MAAA,EACU,UAAGhC,GAAA,EAAA,KAAO,OAAAD,QAAA,EAAA,iBAA0B;gBAAAiB,cAAA;gBAAAK,YAAA;gBAAAY,MAAA,EAGrC;gBAAAC,QAAA,EAAA;wBAEPN,uMAAA,EAAAvC,0NAAA,EAAA;wBAAAwC,WAAA,EAEItB,iBAAA,GACIN,CAAA,CAAE,uDACFA,CAAA,CAAE;wBAAA6B,OAAA,EAEC7B,CAAA,CAAE;oBAAA,C;oBAGZM,iBAAA,OACCqB,uMAAA,EAAA9C,kOAAA,EAAA;wBAAAqD,KAAA,EAAA;4BAAAC,IAAA,EAEU;4BAAAC,KAAA,EACCpC,CAAA,CAAE;4BAAAqC,QAAA,EAAA;wBAAA;wBAAAC,IAAA,EAGN;wBAAAC,QAAA,GAAAf,KAAA,OAEHvC,6LAAA,EAAKuC,KAAA,EAAA;gCAAAW,IAAA,EACG;gCAAAK,IAAA,EACA;gCAAAC,SAAA,EAAA,CAAA;gCAAAC,IAAA,EAAA,CAAA;gCAAAC,KAAA,EAGC;gCAAAL,IAAA,EAAA;oCACA;iCAAA;gCAAAM,WAAA,EAAA;oCAAAC,MAAA,EAAA,CAAA;gCAAA;gCAAAC,GAAA,EAAA;oCAAAC,OAAA,EAAA;wCAAAxD;oCAAA;oCAAAS;gCAAA;gCAAAqC,QAAA,EAAA;gCAAAW,WAAA,EAAA,CAAA;4BAAA,CAUT;oBAAA,C,QAIJrB,uMAAA,EAAAjD,mOAAA,EAAA;wBAAAwD,KAAA,EAAA;4BAAAC,IAAA,EAEU;4BAAA1C,KAAA,EAAA;gCAAAwD,YAAA,EAEU;4BAAA;4BAAAb,KAAA,EAETpC,CAAA,CAAE;4BAAAqC,QAAA,EAAA;wBAAA;wBAAAC,IAAA,EAGN;wBAAAC,QAAA,GAAAW,OAAA,OAEHlE,8LAAA,EAAMwC,OAAA,EAAA;gCAAAW,IAAA,EACE;gCAAAK,IAAA,EACA;gCAAAC,SAAA,EAAA,CAAA;gCAAAC,IAAA,EAAA,CAAA;gCAAAC,KAAA,EAGC;gCAAAL,IAAA,EAAA;oCACA;iCAAA;gCAAAM,WAAA,EAAA;oCAAAC,MAAA,EAAA,CAAA;gCAAA;gCAAAC,GAAA,EAAA;oCAAAC,OAAA,EAAA;wCAAAxD;oCAAA;oCAAAS;gCAAA;gCAAAqC,QAAA,EAAA;gCAAAW,WAAA,EAAA,CAAA;4BAAA,CAKT;oBAAA,C;wBAINrB,uMAAA,EAAA/C,mOAAA,EAAA;wBAAAuE,IAAA,EAAiB;wBAAAlB,QAAA,EAASjC,CAAA,CAAE;oBAAA,C;iB;;;;;;;;;;;;;;;;;;;WAtE9BgB,E;CAyEJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4380, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Login/LoginField/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Login/LoginField/index.tsx"], "sourcesContent": ["'use client'\nimport type { Validate, ValidateOptions } from 'payload'\n\nimport { <PERSON>ail<PERSON>ield, TextField, useTranslation } from '@payloadcms/ui'\nimport { email, username } from 'payload/shared'\nimport React from 'react'\n\nexport type LoginFieldProps = {\n  readonly required?: boolean\n  readonly type: 'email' | 'emailOrUsername' | 'username'\n  readonly validate?: Validate\n}\n\nexport const LoginField: React.FC<LoginFieldProps> = ({ type, required = true }) => {\n  const { t } = useTranslation()\n\n  if (type === 'email') {\n    return (\n      <EmailField\n        field={{\n          name: 'email',\n          admin: {\n            autoComplete: 'email',\n          },\n          label: t('general:email'),\n          required,\n        }}\n        path=\"email\"\n        validate={email}\n      />\n    )\n  }\n\n  if (type === 'username') {\n    return (\n      <TextField\n        field={{\n          name: 'username',\n          label: t('authentication:username'),\n          required,\n        }}\n        path=\"username\"\n        validate={username}\n      />\n    )\n  }\n\n  if (type === 'emailOrUsername') {\n    return (\n      <TextField\n        field={{\n          name: 'username',\n          label: t('authentication:emailOrUsername'),\n          required,\n        }}\n        path=\"username\"\n        validate={(value, options) => {\n          const passesUsername = username(value, options)\n          const passesEmail = email(\n            value,\n            options as ValidateOptions<any, { username?: string }, any, any>,\n          )\n\n          if (!passesEmail && !passesUsername) {\n            return `${t('general:email')}: ${passesEmail} ${t('general:username')}: ${passesUsername}`\n          }\n\n          return true\n        }}\n      />\n    )\n  }\n\n  return null\n}\n"], "names": ["c", "_c", "EmailField", "TextField", "useTranslation", "email", "username", "React", "LoginField", "t0", "$", "type", "required", "t1", "undefined", "t", "t2", "_jsx", "field", "name", "admin", "autoComplete", "label", "path", "validate", "t3", "value", "options", "passesUsername", "passesEmail"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAGA,SAASC,UAAU,EAAEC,SAAS,EAAEC,cAAc,QAAQ;;AACtD,SAASC,KAAK,EAAEC,QAAQ,QAAQ;AAChC,OAAOC,KAAA,MAAW;AALlB;;;;;;AAaO,MAAMC,UAAA,IAAwCC,EAAA;IAAA,MAAAC,CAAA,OAAAT,0MAAA,EAAA;IAAC,MAAA,EAAAU,IAAA,EAAAC,QAAA,EAAAC,EAAAA,EAAA,GAAAJ,EAAyB;IAAjB,MAAAG,QAAA,GAAAC,EAAe,KAAAC,SAAA,GAAA,OAAfD,EAAe;IAC3E,MAAA,EAAAE,CAAAA,EAAA,OAAcX,0QAAA;IAAA,IAEVO,IAAA,KAAS,SAAA;QAAA,IAAAK,EAAA;QAAA,IAAAN,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAK,CAAA,EAAA;YAETC,EAAA,OAAAC,uMAAA,EAAAf,mOAAA,EAAA;gBAAAgB,KAAA,EAAA;oBAAAC,IAAA,EAEU;oBAAAC,KAAA,EAAA;wBAAAC,YAAA,EAEU;oBAAA;oBAAAC,KAAA,EAETP,CAAA,CAAE;oBAAAH;gBAAA;gBAAAW,IAAA,EAGN;gBAAAC,QAAA,EAAAnB,8LAAAA;YAAA,C;;;;;;;eATPW,E;;QAeAL,IAAA,KAAS,YAAA;QAAA,IAAAK,EAAA;QAAA,IAAAN,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAK,CAAA,EAAA;YAETC,EAAA,OAAAC,uMAAA,EAAAd,kOAAA,EAAA;gBAAAe,KAAA,EAAA;oBAAAC,IAAA,EAEU;oBAAAG,KAAA,EACCP,CAAA,CAAE;oBAAAH;gBAAA;gBAAAW,IAAA,EAGN;gBAAAC,QAAA,EAAAlB,iMAAAA;YAAA,C;;;;;;;eANPU,E;;QAYAL,IAAA,KAAS,mBAAA;QAAA,IAAAK,EAAA;QAAA,IAAAN,CAAA,CAAA,EAAA,KAAAE,QAAA,IAAAF,CAAA,CAAA,EAAA,KAAAK,CAAA,EAAA;YAAA,IAAAU,EAAA;YAAA,IAAAf,CAAA,CAAA,EAAA,KAAAK,CAAA,EAAA;gBASGU,EAAA,GAAAA,CAAAC,KAAA,EAAAC,OAAA;oBACR,MAAAC,cAAA,OAAuBtB,iMAAA,EAASoB,KAAA,EAAOC,OAAA;oBACvC,MAAAE,WAAA,GAAoBxB,kMAAA,EAClBqB,KAAA,EACAC,OAAA;oBAAA,IAGE,CAACE,WAAA,IAAA,CAAgBD,cAAA,EAAA;wBAAA,OACZ,UAAGb,CAAA,CAAE,kBAAA,aAAqBc,WAAA,EAAA,KAAyCD,OAA1Bb,CAAA,CAAE,KAAwB,gBAAxB,MAAwB,CAAgB;oBAAA;oBAAA,OAAA;gBAAA;gBAI9FL,CAAA,CAAA,EAAA,GAAAK,CAAA;gBAAAL,CAAA,CAAA,GAAA,GAAAe,EAAA;YAAA,OAAA;gBAAAA,EAAA,GAAAf,CAAA,CAAA,GAAA;YAAA;YAnBFM,EAAA,OAAAC,uMAAA,EAAAd,kOAAA,EAAA;gBAAAe,KAAA,EAAA;oBAAAC,IAAA,EAEU;oBAAAG,KAAA,EACCP,CAAA,CAAE;oBAAAH;gBAAA;gBAAAW,IAAA,EAGN;gBAAAC,QAAA,EACKC;YAYV,C;;;;;;;eAnBFT,E;;;CAyBN", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4485, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Login/LoginForm/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Login/LoginForm/index.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\nconst baseClass = 'login__form'\n\nimport type { UserWithToken } from '@payloadcms/ui'\nimport type { FormState } from 'payload'\n\nimport {\n  Form,\n  FormSubmit,\n  Link,\n  PasswordField,\n  useAuth,\n  useConfig,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { formatAdminURL, getLoginOptions, getSafeRedirect } from 'payload/shared'\n\nimport type { LoginFieldProps } from '../LoginField/index.js'\n\nimport { LoginField } from '../LoginField/index.js'\nimport './index.scss'\n\nexport const LoginForm: React.FC<{\n  prefillEmail?: string\n  prefillPassword?: string\n  prefillUsername?: string\n  searchParams: { [key: string]: string | string[] | undefined }\n}> = ({ prefillEmail, prefillPassword, prefillUsername, searchParams }) => {\n  const { config, getEntityConfig } = useConfig()\n\n  const {\n    admin: {\n      routes: { forgot: forgotRoute },\n      user: userSlug,\n    },\n    routes: { admin: adminRoute, api: apiRoute },\n  } = config\n\n  const collectionConfig = getEntityConfig({ collectionSlug: userSlug })\n  const { auth: authOptions } = collectionConfig\n  const loginWithUsername = authOptions.loginWithUsername\n  const { canLoginWithEmail, canLoginWithUsername } = getLoginOptions(loginWithUsername)\n\n  const [loginType] = React.useState<LoginFieldProps['type']>(() => {\n    if (canLoginWithEmail && canLoginWithUsername) {\n      return 'emailOrUsername'\n    }\n    if (canLoginWithUsername) {\n      return 'username'\n    }\n    return 'email'\n  })\n\n  const { t } = useTranslation()\n  const { setUser } = useAuth()\n\n  const initialState: FormState = {\n    password: {\n      initialValue: prefillPassword ?? undefined,\n      valid: true,\n      value: prefillPassword ?? undefined,\n    },\n  }\n\n  if (loginWithUsername) {\n    initialState.username = {\n      initialValue: prefillUsername ?? undefined,\n      valid: true,\n      value: prefillUsername ?? undefined,\n    }\n  } else {\n    initialState.email = {\n      initialValue: prefillEmail ?? undefined,\n      valid: true,\n      value: prefillEmail ?? undefined,\n    }\n  }\n\n  const handleLogin = (data: UserWithToken) => {\n    setUser(data)\n  }\n\n  return (\n    <Form\n      action={`${apiRoute}/${userSlug}/login`}\n      className={baseClass}\n      disableSuccessStatus\n      initialState={initialState}\n      method=\"POST\"\n      onSuccess={handleLogin}\n      redirect={getSafeRedirect({ fallbackTo: adminRoute, redirectTo: searchParams?.redirect })}\n      waitForAutocomplete\n    >\n      <div className={`${baseClass}__inputWrap`}>\n        <LoginField type={loginType} />\n        <PasswordField\n          field={{\n            name: 'password',\n            label: t('general:password'),\n            required: true,\n          }}\n          path=\"password\"\n        />\n      </div>\n      <Link\n        href={formatAdminURL({\n          adminRoute,\n          path: forgotRoute,\n        })}\n        prefetch={false}\n      >\n        {t('authentication:forgotPasswordQuestion')}\n      </Link>\n      <FormSubmit size=\"large\">{t('authentication:login')}</FormSubmit>\n    </Form>\n  )\n}\n"], "names": ["c", "_c", "React", "baseClass", "Form", "FormSubmit", "Link", "PasswordField", "useAuth", "useConfig", "useTranslation", "formatAdminURL", "getLoginOptions", "getSafeRedirect", "LoginField", "LoginForm", "t0", "$", "prefillEmail", "prefillPassword", "prefillUsername", "searchParams", "config", "getEntityConfig", "admin", "t1", "routes", "t2", "t3", "user", "userSlug", "forgot", "forgotRoute", "adminRoute", "api", "apiRoute", "loginWithUsername", "t4", "collectionConfig", "collectionSlug", "auth", "authOptions", "canLoginWithEmail", "canLoginWithUsername", "t5", "loginType", "useState", "t", "setUser", "t6", "undefined", "t7", "t8", "redirect", "initialState", "password", "initialValue", "valid", "value", "username", "email", "t9", "data", "handleLogin", "_jsxs", "action", "className", "disableSuccessStatus", "method", "onSuccess", "fallbackTo", "redirectTo", "waitForAutocomplete", "children", "_jsx", "type", "field", "name", "label", "required", "path", "href", "prefetch", "size"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AAEA,OAAOC,KAAA,MAAW;AAOlB,SACEE,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,aAAa,EACbC,OAAO,EACPC,SAAS,EACTC,cAAc,QACT;;;;;AACP,SAASC,cAAc,EAAEC,eAAe,EAAEC,eAAe,QAAQ;AAIjE,SAASC,UAAU,QAAQ;AAtB3B;;;;AAIA,MAAMX,SAAA,GAAY;;;;AAqBX,MAAMY,SAAA,IAKRC,EAAA;IAAA,MAAAC,CAAA,OAAAhB,0MAAA,EAAA;IAAC,MAAA,EAAAiB,YAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,YAAAA,EAAA,GAAAL,EAAgE;IACpE,MAAA,EAAAM,MAAA,EAAAC,eAAAA,EAAA,OAAoCd,gQAAA;IAEpC,MAAA,EAAAe,KAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,EAAAA,EAAA,GAMIL,MAAA;IALK,MAAA,EAAAI,MAAA,EAAAE,EAAA,EAAAC,IAAA,EAAAC,QAAAA,EAAA,GAAAL,EAGN;IAFS,MAAA,EAAAM,MAAA,EAAAC,WAAAA,EAAA,GAAAJ,EAAuB;IAGzB,MAAA,EAAAJ,KAAA,EAAAS,UAAA,EAAAC,GAAA,EAAAC,QAAAA,EAAA,GAAAR,EAAoC;IAAA,IAAAS,iBAAA;IAAA,IAAAC,EAAA;IAAA,IAAApB,CAAA,CAAA,EAAA,KAAAM,eAAA,IAAAN,CAAA,CAAA,EAAA,KAAAa,QAAA,EAAA;QAG9C,MAAAQ,gBAAA,GAAyBf,eAAA,CAAA;YAAAgB,cAAA,EAAkCT;QAAA,CAAS;QACpE,MAAA,EAAAU,IAAA,EAAAC,WAAAA,EAAA,GAA8BH,gBAAA;QAC9BF,iBAAA,GAA0BK,WAAA,CAAAL,iBAAA;QAC0BC,EAAA,OAAAzB,0MAAA,EAAgBwB,iBAAA;QAAAnB,CAAA,CAAA,EAAA,GAAAM,eAAA;QAAAN,CAAA,CAAA,EAAA,GAAAa,QAAA;QAAAb,CAAA,CAAA,EAAA,GAAAmB,iBAAA;QAAAnB,CAAA,CAAA,EAAA,GAAAoB,EAAA;IAAA,OAAA;QAAAD,iBAAA,GAAAnB,CAAA,CAAA,EAAA;QAAAoB,EAAA,GAAApB,CAAA,CAAA,EAAA;IAAA;IAApE,MAAA,EAAAyB,iBAAA,EAAAC,oBAAAA,EAAA,GAAoDN,EAAgB;IAAA,IAAAO,EAAA;IAAA,IAAA3B,CAAA,CAAA,EAAA,KAAAyB,iBAAA,IAAAzB,CAAA,CAAA,EAAA,KAAA0B,oBAAA,EAAA;QAERC,EAAA,GAAAA,CAAA;YAAA,IACtDF,iBAAA,IAAqBC,oBAAA,EAAA;gBAAA,OAChB;YAAA;YAAA,IAELA,oBAAA,EAAA;gBAAA,OACK;YAAA;YAAA,OAEF;QAAA;QACT1B,CAAA,CAAA,EAAA,GAAAyB,iBAAA;QAAAzB,CAAA,CAAA,EAAA,GAAA0B,oBAAA;QAAA1B,CAAA,CAAA,EAAA,GAAA2B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA3B,CAAA,CAAA,EAAA;IAAA;IARA,MAAA,CAAA4B,SAAA,CAAA,GAAoB3C,kMAAA,CAAA4C,QAAA,CAAwCF,EAQ5D;IAEA,MAAA,EAAAG,CAAAA,EAAA,OAAcrC,0QAAA;IACd,MAAA,EAAAsC,OAAAA,EAAA,OAAoBxC,gOAAA;IAIF,MAAAyC,EAAA,+BAAA9B,eAAA,gCAAA+B,SAAmB;IAE1B,MAAAC,EAAA,4DAAAhC,eAAA,GAAA+B,SAAmB;IAAA,IAAAE,EAAA;IAAA,IAAAnC,CAAA,CAAA,EAAA,KAAAgB,UAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAkB,QAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAe,WAAA,IAAAf,CAAA,CAAA,GAAA,KAAA4B,SAAA,IAAA5B,CAAA,CAAA,GAAA,KAAAmB,iBAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAC,YAAA,IAAAD,CAAA,CAAA,GAAA,KAAAG,eAAA,IAAAH,CAAA,CAAA,GAAA,kEAAAI,YAAA,CAAAgC,QAAA,KAAApC,CAAA,CAAA,GAAA,KAAA+B,OAAA,IAAA/B,CAAA,CAAA,GAAA,KAAA8B,CAAA,IAAA9B,CAAA,CAAA,GAAA,KAAAgC,EAAA,IAAAhC,CAAA,CAAA,GAAA,KAAAkC,EAAA,IAAAlC,CAAA,CAAA,GAAA,KAAAa,QAAA,EAAA;QAJ9B,MAAAwB,YAAA,GAAA;YAAAC,QAAA,EAAA;gBAAAC,YAAA,EAEkBP,EAAmB;gBAAAQ,KAAA,EAAA;gBAAAC,KAAA,EAE1BP;YAAmB;QAAA;QAE9B,IAEIf,iBAAA,EAAA;YACFkB,YAAA,CAAAK,QAAA,GAAA;gBAAAH,YAAA,2DACgBpC,eAAA,GAAA8B,SAAmB;gBAAAO,KAAA,EAAA;gBAAAC,KAAA,EAE1BtC,eAAA,4DAAA8B;YAAmB;QAAA,OAAA;YAG5BI,YAAA,CAAAM,KAAA,GAAA;gBAAAJ,YAAA,qDACgBtC,YAAA,GAAAgC,SAAgB;gBAAAO,KAAA,EAAA;gBAAAC,KAAA,qDAEvBxC,YAAA,GAAAgC;YAAgB;QAAA;QAAA,IAAAW,EAAA;QAAA,IAAA5C,CAAA,CAAA,GAAA,KAAA+B,OAAA,EAAA;YAIPa,EAAA,IAAAC,IAAA;gBAClBd,OAAA,CAAQc,IAAA;YAAA;YACV7C,CAAA,CAAA,GAAA,GAAA+B,OAAA;YAAA/B,CAAA,CAAA,GAAA,GAAA4C,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA5C,CAAA,CAAA,GAAA;QAAA;QAFA,MAAA8C,WAAA,GAAoBF,EAEpB;QAGET,EAAA,OAAAY,wMAAA,EAAA5D,6NAAA,EAAA;YAAA6D,MAAA,EACU,UAAG9B,QAAA,EAAA,KAAY,OAAAL,QAAA,EAAA,OAAgB;YAAAoC,SAAA,EAAA/D,SAAA;YAAAgE,oBAAA,EAAA;YAAAb,YAAA;YAAAc,MAAA,EAIhC;YAAAC,SAAA,EACIN,WAAA;YAAAV,QAAA,MACDxC,+MAAA,EAAA;gBAAAyD,UAAA,EAA8BrC,UAAA;gBAAAsC,UAAA,EAAwBlD,YAAA,6DAAAgC,QAAAA;YAAA,CAAuB;YAAAmB,mBAAA,EAAA;YAAAC,QAAA,EAAA;oBAGvFT,wMAAA,EAAC,OAAA;oBAAAE,SAAA,EAAe,GAAA,OAAA/D,SAAA,EAAA,YAAyB;oBAAAsE,QAAA,EAAA;4BACvCC,uMAAA,EAAA5D,gOAAA,EAAA;4BAAA6D,IAAA,EAAkB9B;wBAAA,C;4BAClB6B,uMAAA,EAAAnE,sOAAA,EAAA;4BAAAqE,KAAA,EAAA;gCAAAC,IAAA,EAEU;gCAAAC,KAAA,EACC/B,CAAA,CAAE;gCAAAgC,QAAA,EAAA;4BAAA;4BAAAC,IAAA,EAGN;wBAAA,C;qB;;gBAGTN,2MAAA,EAAApE,6NAAA,EAAA;oBAAA2E,IAAA,MACQtE,6MAAA,EAAA;wBAAAsB,UAAA;wBAAA+C,IAAA,EAEEhD;oBAAA,CACR;oBAAAkD,QAAA,EAAA;oBAAAT,QAAA,EAGC1B,CAAA,CAAE;gBAAA,C;gBAEL2B,2MAAA,EAAArE,mOAAA,EAAA;oBAAA8E,IAAA,EAAiB;oBAAAV,QAAA,EAAS1B,CAAA,CAAE;gBAAA,C;a;;;;;;;;;;;;;;;;;;;WA9B9BK,E;CAiCJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4654, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Logout/LogoutClient.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Logout/LogoutClient.tsx"], "sourcesContent": ["'use client'\nimport {\n  But<PERSON>,\n  <PERSON>adingOverlay,\n  toast,\n  useAuth,\n  useRouteTransition,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { useRouter } from 'next/navigation.js'\nimport { formatAdminURL } from 'payload/shared'\nimport React, { useEffect } from 'react'\n\nimport './index.scss'\n\nconst baseClass = 'logout'\n\n/**\n * This component should **just** be the inactivity route and do nothing with logging the user out.\n *\n * It currently handles too much, the auth provider should just log the user out and then\n * we could remove the useEffect in this file. So instead of the logout button\n * being an anchor link, it should be a button that calls `logOut` in the provider.\n *\n * This view is still useful if cookies attempt to refresh and fail, i.e. the user\n * is logged out due to inactivity.\n */\nexport const LogoutClient: React.FC<{\n  adminRoute: string\n  inactivity?: boolean\n  redirect: string\n}> = (props) => {\n  const { adminRoute, inactivity, redirect } = props\n\n  const { logOut, user } = useAuth()\n\n  const { startRouteTransition } = useRouteTransition()\n\n  const isLoggedIn = React.useMemo(() => {\n    return Boolean(user?.id)\n  }, [user?.id])\n\n  const navigatingToLoginRef = React.useRef(false)\n\n  const [loginRoute] = React.useState(() =>\n    formatAdminURL({\n      adminRoute,\n      path: `/login${\n        inactivity && redirect && redirect.length > 0\n          ? `?redirect=${encodeURIComponent(redirect)}`\n          : ''\n      }`,\n    }),\n  )\n\n  const { t } = useTranslation()\n  const router = useRouter()\n\n  const handleLogOut = React.useCallback(async () => {\n    if (!inactivity && !navigatingToLoginRef.current) {\n      navigatingToLoginRef.current = true\n      await logOut()\n      toast.success(t('authentication:loggedOutSuccessfully'))\n      startRouteTransition(() => router.push(loginRoute))\n      return\n    }\n  }, [inactivity, logOut, loginRoute, router, startRouteTransition, t])\n\n  useEffect(() => {\n    if (isLoggedIn) {\n      void handleLogOut()\n    } else if (!navigatingToLoginRef.current) {\n      navigatingToLoginRef.current = true\n      startRouteTransition(() => router.push(loginRoute))\n    }\n  }, [handleLogOut, isLoggedIn, loginRoute, router, startRouteTransition])\n\n  if (!isLoggedIn && inactivity) {\n    return (\n      <div className={`${baseClass}__wrap`}>\n        <h2>{t('authentication:loggedOutInactivity')}</h2>\n        <Button buttonStyle=\"secondary\" el=\"link\" size=\"large\" url={loginRoute}>\n          {t('authentication:logBackIn')}\n        </Button>\n      </div>\n    )\n  }\n\n  return <LoadingOverlay animationDuration={'0ms'} loadingText={t('authentication:loggingOut')} />\n}\n"], "names": ["c", "_c", "<PERSON><PERSON>", "LoadingOverlay", "toast", "useAuth", "useRouteTransition", "useTranslation", "useRouter", "formatAdminURL", "React", "useEffect", "baseClass", "LogoutClient", "props", "$", "adminRoute", "inactivity", "redirect", "logOut", "user", "startRouteTransition", "id", "isLoggedIn", "Boolean", "navigatingToLoginRef", "useRef", "t0", "path", "length", "encodeURIComponent", "loginRoute", "useState", "t", "router", "t1", "current", "success", "push", "handleLogOut", "t2", "t3", "t4", "_jsxs", "className", "children", "_jsx", "buttonStyle", "el", "size", "url", "animationDuration", "loadingText"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SACEC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,OAAO,EACPC,kBAAkB,EAClBC,cAAc,QACT;;AACP,SAASC,SAAS,QAAQ;AAC1B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,IAASC,SAAS,QAAQ;AAXjC;;;;;;;AAeA,MAAMC,SAAA,GAAY;AAYX,MAAMC,YAAA,IAIRC,KAAA;IAAA,MAAAC,CAAA,OAAAd,0MAAA,EAAA;IACH,MAAA,EAAAe,UAAA,EAAAC,UAAA,EAAAC,QAAAA,EAAA,GAA6CJ,KAAA;IAE7C,MAAA,EAAAK,MAAA,EAAAC,IAAAA,EAAA,OAAyBf,gOAAA;IAEzB,MAAA,EAAAgB,oBAAAA,EAAA,OAAiCf,2OAAA;IAI7Bc,IAAA,6CAAAE,EAAA;IAFJ,MAAAC,UAAA,GACSC,OAAA,6CAAQJ,IAAA,CAAAE,EAAM;IAGvB,MAAAG,oBAAA,GAA6Bf,kMAAA,CAAAgB,MAAA,CAAA,KAAa;IAAA,IAAAC,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAC,UAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,UAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,QAAA,EAAA;QAENS,EAAA,GAAAA,CAAA,OAClClB,6MAAA,EAAA;gBAAAO,UAAA;gBAAAY,IAAA,EAEQ,SAGA,OAFJX,UAAA,IAAcC,QAAA,IAAYA,QAAA,CAAAW,MAAA,GAAA,CAAkB,GACxC,aAAgC,CAAW,MAA9BC,kBAAA,CAAmBZ,QAAA,KAChC;YACJ,CACJ;QAAAH,CAAA,CAAA,EAAA,GAAAC,UAAA;QAAAD,CAAA,CAAA,EAAA,GAAAE,UAAA;QAAAF,CAAA,CAAA,EAAA,GAAAG,QAAA;QAAAH,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;IARF,MAAA,CAAAgB,UAAA,CAAA,GAAqBrB,kMAAA,CAAAsB,QAAA,CAAeL,EAQlC;IAGF,MAAA,EAAAM,CAAAA,EAAA,OAAc1B,0QAAA;IACd,MAAA2B,MAAA,OAAe1B,4KAAA;IAAA,IAAA2B,EAAA;IAAA,IAAApB,CAAA,CAAA,EAAA,KAAAE,UAAA,IAAAF,CAAA,CAAA,EAAA,KAAAI,MAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAgB,UAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAmB,MAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAM,oBAAA,IAAAN,CAAA,CAAA,EAAA,KAAAkB,CAAA,EAAA;QAEwBE,EAAA,GAAA,MAAAA,CAAA;YAAA,IACjC,CAAClB,UAAA,IAAA,CAAeQ,oBAAA,CAAAW,OAA4B,EAAA;gBAC9CX,oBAAA,CAAAW,OAAA,GAAA;gBAAA,MACMjB,MAAA;gBACNf,8NAAA,CAAAiC,OAAA,CAAcJ,CAAA,CAAE;gBAChBZ,oBAAA,CAAA,IAA2Ba,MAAA,CAAAI,IAAA,CAAYP,UAAA;gBAAA;YAAA;QAAA;QAG3ChB,CAAA,CAAA,EAAA,GAAAE,UAAA;QAAAF,CAAA,CAAA,EAAA,GAAAI,MAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAgB,UAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAmB,MAAA;QAAAnB,CAAA,CAAA,EAAA,GAAAM,oBAAA;QAAAN,CAAA,CAAA,EAAA,GAAAkB,CAAA;QAAAlB,CAAA,CAAA,GAAA,GAAAoB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAApB,CAAA,CAAA,GAAA;IAAA;IARA,MAAAwB,YAAA,GAAqBJ,EAQ+C;IAAA,IAAAK,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA1B,CAAA,CAAA,GAAA,KAAAwB,YAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAQ,UAAA,IAAAR,CAAA,CAAA,GAAA,KAAAgB,UAAA,IAAAhB,CAAA,CAAA,GAAA,KAAAmB,MAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAM,oBAAA,EAAA;QAE1DmB,EAAA,GAAAA,CAAA;YAAA,IACJjB,UAAA,EAAA;gBACGgB,YAAA;YAAA,OAAA;gBAAA,IAAA,CACKd,oBAAA,CAAAW,OAAA,EAAA;oBACVX,oBAAA,CAAAW,OAAA,GAAA;oBACAf,oBAAA,CAAA,IAA2Ba,MAAA,CAAAI,IAAA,CAAYP,UAAA;gBAAA;YAAA;QAAA;QAExCU,EAAA,GAAA;YAACF,YAAA;YAAchB,UAAA;YAAYQ,UAAA;YAAYG,MAAA;YAAQb,oBAAA;SAAA;QAAqBN,CAAA,CAAA,GAAA,GAAAwB,YAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAQ,UAAA;QAAAR,CAAA,CAAA,GAAA,GAAAgB,UAAA;QAAAhB,CAAA,CAAA,GAAA,GAAAmB,MAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAM,oBAAA;QAAAN,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA0B,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAzB,CAAA,CAAA,GAAA;QAAA0B,EAAA,GAAA1B,CAAA,CAAA,GAAA;IAAA;QAPvEJ,oMAAA,EAAU6B,EAOV,EAAGC,EAAoE;IAAA,IAEnE,CAAClB,UAAA,IAAcN,UAAA,EAAA;QAAA,IAAAyB,EAAA;QAAA,IAAA3B,CAAA,CAAA,GAAA,KAAAgB,UAAA,IAAAhB,CAAA,CAAA,GAAA,KAAAkB,CAAA,EAAA;YAEfS,EAAA,OAAAC,wMAAA,EAAC,OAAA;gBAAAC,SAAA,EAAe,GAAA,OAAAhC,AAAoB,SAApB,EAAA;gBAAoBiC,QAAA,EAAA;wBAClCC,uMAAA,EAAC,MAAA;wBAAAD,QAAA,EAAIZ,CAAA,CAAE;oBAAA,C;wBACPa,uMAAA,EAAA5C,+NAAA,EAAA;wBAAA6C,WAAA,EAAoB;wBAAAC,EAAA,EAAe;wBAAAC,IAAA,EAAY;wBAAAC,GAAA,EAAanB,UAAA;wBAAAc,QAAA,EACzDZ,CAAA,CAAE;oBAAA,C;iB;;;;;;;;eAHPS,E;;;;QASGA,EAAA,OAAAI,uMAAA,EAAA3C,uOAAA,EAAA;YAAAgD,iBAAA,EAAmC;YAAAC,WAAA,EAAoBnB,CAAA,CAAE;QAAA,C;;;;;;WAAzDS,E;CACT", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4794, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/ResetPassword/ResetPasswordForm/index.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/ResetPassword/ResetPasswordForm/index.tsx"], "sourcesContent": ["'use client'\nimport {\n  Confirm<PERSON><PERSON><PERSON><PERSON>ield,\n  Form,\n  FormSubmit,\n  HiddenField,\n  PasswordField,\n  useAuth,\n  useConfig,\n  useTranslation,\n} from '@payloadcms/ui'\nimport { useRouter } from 'next/navigation.js'\nimport { type FormState } from 'payload'\nimport { formatAdminURL } from 'payload/shared'\nimport React from 'react'\n\ntype Args = {\n  readonly token: string\n}\n\nexport const ResetPasswordForm: React.FC<Args> = ({ token }) => {\n  const i18n = useTranslation()\n  const {\n    config: {\n      admin: {\n        routes: { login: loginRoute },\n        user: userSlug,\n      },\n      routes: { admin: adminRoute, api: apiRoute },\n      serverURL,\n    },\n  } = useConfig()\n\n  const history = useRouter()\n  const { fetchFullUser } = useAuth()\n\n  const onSuccess = React.useCallback(async () => {\n    const user = await fetchFullUser()\n    if (user) {\n      history.push(adminRoute)\n    } else {\n      history.push(\n        formatAdminURL({\n          adminRoute,\n          path: loginRoute,\n        }),\n      )\n    }\n  }, [adminRoute, fetchFullUser, history, loginRoute])\n\n  const initialState: FormState = {\n    'confirm-password': {\n      initialValue: '',\n      valid: false,\n      value: '',\n    },\n    password: {\n      initialValue: '',\n      valid: false,\n      value: '',\n    },\n    token: {\n      initialValue: token,\n      valid: true,\n      value: token,\n    },\n  }\n\n  return (\n    <Form\n      action={`${serverURL}${apiRoute}/${userSlug}/reset-password`}\n      initialState={initialState}\n      method=\"POST\"\n      onSuccess={onSuccess}\n    >\n      <div className=\"inputWrap\">\n        <PasswordField\n          field={{\n            name: 'password',\n            label: i18n.t('authentication:newPassword'),\n            required: true,\n          }}\n          path=\"password\"\n          schemaPath={`${userSlug}.password`}\n        />\n        <ConfirmPasswordField />\n        <HiddenField path=\"token\" schemaPath={`${userSlug}.token`} value={token} />\n      </div>\n      <FormSubmit size=\"large\">{i18n.t('authentication:resetPassword')}</FormSubmit>\n    </Form>\n  )\n}\n"], "names": ["c", "_c", "ConfirmPasswordField", "Form", "FormSubmit", "HiddenField", "PasswordField", "useAuth", "useConfig", "useTranslation", "useRouter", "formatAdminURL", "React", "ResetPasswordForm", "t0", "$", "token", "i18n", "config", "t1", "admin", "t2", "routes", "t3", "serverURL", "t4", "user", "userSlug", "login", "loginRoute", "adminRoute", "api", "apiRoute", "history", "fetchFullUser", "t5", "push", "path", "onSuccess", "t6", "initialState", "initialValue", "valid", "value", "password", "_jsxs", "action", "method", "children", "className", "_jsx", "field", "name", "label", "t", "required", "schemaPath", "size"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;;AACA,SACEC,oBAAoB,EACpBC,IAAI,EACJC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,OAAO,EACPC,SAAS,EACTC,cAAc,QACT;;;AACP,SAASC,SAAS,QAAQ;AAE1B,SAASC,cAAc,QAAQ;AAC/B,OAAOC,KAAA,MAAW;AAdlB;;;;;;;AAoBO,MAAMC,iBAAA,IAAoCC,EAAA;IAAA,MAAAC,CAAA,OAAAd,0MAAA,EAAA;IAAC,MAAA,EAAAe,KAAAA,EAAA,GAAAF,EAAS;IACzD,MAAAG,IAAA,OAAaR,0QAAA;IACb,MAAA,EAAAS,MAAA,EAAAC,EAAAA,EAAA,OASIX,gQAAA;IARM,MAAA,EAAAY,KAAA,EAAAC,EAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,SAAAA,EAAA,GAAAL,EAOP;IANQ,MAAA,EAAAG,MAAA,EAAAG,EAAA,EAAAC,IAAA,EAAAC,QAAAA,EAAA,GAAAN,EAGN;IAFS,MAAA,EAAAO,KAAA,EAAAC,UAAAA,EAAA,GAAAJ,EAAqB;IAGvB,MAAA,EAAAL,KAAA,EAAAU,UAAA,EAAAC,GAAA,EAAAC,QAAAA,EAAA,GAAAT,EAAoC;IAKhD,MAAAU,OAAA,OAAgBvB,4KAAA;IAChB,MAAA,EAAAwB,aAAAA,EAAA,OAA0B3B,gOAAA;IAAA,IAAA4B,EAAA;IAAA,IAAApB,CAAA,CAAA,EAAA,KAAAe,UAAA,IAAAf,CAAA,CAAA,EAAA,KAAAmB,aAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAkB,OAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAc,UAAA,EAAA;QAEUM,EAAA,GAAA,MAAAA,CAAA;YAClC,MAAAT,IAAA,GAAA,MAAmBQ,aAAA;YAAA,IACfR,IAAA,EAAA;gBACFO,OAAA,CAAAG,IAAA,CAAaN,UAAA;YAAA,OAAA;gBAEbG,OAAA,CAAAG,IAAA,KACEzB,6MAAA,EAAA;oBAAAmB,UAAA;oBAAAO,IAAA,EAEQR;gBAAA,CACR;YAAA;QAAA;QAGNd,CAAA,CAAA,EAAA,GAAAe,UAAA;QAAAf,CAAA,CAAA,EAAA,GAAAmB,aAAA;QAAAnB,CAAA,CAAA,EAAA,GAAAkB,OAAA;QAAAlB,CAAA,CAAA,EAAA,GAAAc,UAAA;QAAAd,CAAA,CAAA,EAAA,GAAAoB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAApB,CAAA,CAAA,EAAA;IAAA;IAZA,MAAAuB,SAAA,GAAkBH,EAYiC;IAAA,IAAAI,EAAA;IAAA,IAAAxB,CAAA,CAAA,EAAA,KAAAiB,QAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAE,IAAA,IAAAF,CAAA,CAAA,EAAA,KAAAuB,SAAA,IAAAvB,CAAA,CAAA,EAAA,KAAAS,SAAA,IAAAT,CAAA,CAAA,EAAA,KAAAC,KAAA,IAAAD,CAAA,CAAA,GAAA,KAAAY,QAAA,EAAA;QAEnD,MAAAa,YAAA,GAAA;YAAA,oBAAA;gBAAAC,YAAA,EAEkB;gBAAAC,KAAA,EAAA;gBAAAC,KAAA,EAEP;YAAA;YAAAC,QAAA,EAAA;gBAAAH,YAAA,EAGO;gBAAAC,KAAA,EAAA;gBAAAC,KAAA,EAEP;YAAA;YAAA3B,KAAA,EAAA;gBAAAyB,YAAA,EAGOzB,KAAA;gBAAA0B,KAAA,EAAA;gBAAAC,KAAA,EAEP3B;YAAA;QAAA;QAKTuB,EAAA,OAAAM,wMAAA,EAAA1C,6NAAA,EAAA;YAAA2C,MAAA,EACU,UAAGtB,SAAA,SAAYQ,QAAA,EAAA,KAAY,OAAAL,QAAA,EAAA,gBAAyB;YAAAa,YAAA;YAAAO,MAAA,EAErD;YAAAT,SAAA;YAAAU,QAAA,EAAA;oBAGPH,wMAAA,EAAC,OAAA;oBAAAI,SAAA,EAAc;oBAAAD,QAAA,EAAA;4BACbE,uMAAA,EAAA5C,sOAAA,EAAA;4BAAA6C,KAAA,EAAA;gCAAAC,IAAA,EAEU;gCAAAC,KAAA,EACCpC,IAAA,CAAAqC,CAAA,CAAO;gCAAAC,QAAA,EAAA;4BAAA;4BAAAlB,IAAA,EAGX;4BAAAmB,UAAA,EACO,GAAG,OAAA7B,QAAA,EAAA;wBAAmB,C;4BAEpCuB,uMAAA,EAAAhD,6OAAA,EAAA,CAAA,CAAC;4BACDgD,uMAAA,EAAA7C,oOAAA,EAAA;4BAAAgC,IAAA,EAAkB;4BAAAmB,UAAA,EAAoB,GAAG,OAAA7B,QAAA,EAAA,OAAgB;4BAAAgB,KAAA,EAAS3B;wBAAA,C;qB;;oBAEpEkC,uMAAA,EAAA9C,mOAAA,EAAA;oBAAAqD,IAAA,EAAiB;oBAAAT,QAAA,EAAS/B,IAAA,CAAAqC,CAAA,CAAO;gBAAA,C;a;;;;;;;;;;;;WAnBnCf,E;CAsBJ", "ignoreList": [], "debugId": null}}, {"offset": {"line": 4913, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/git/payload-test/node_modules/@payloadcms/next/dist/views/Verify/index.client.js", "sources": ["file:///home/<USER>/git/payload-test/node_modules/%40payloadcms/next/src/views/Verify/index.client.tsx"], "sourcesContent": ["'use client'\nimport { toast, useRouteTransition } from '@payloadcms/ui'\nimport { useRouter } from 'next/navigation.js'\nimport React, { useEffect } from 'react'\n\ntype Props = {\n  message: string\n  redirectTo: string\n}\nexport function ToastAndRedirect({ message, redirectTo }: Props) {\n  const router = useRouter()\n  const { startRouteTransition } = useRouteTransition()\n  const hasToastedRef = React.useRef(false)\n\n  useEffect(() => {\n    let timeoutID\n\n    if (toast) {\n      timeoutID = setTimeout(() => {\n        toast.success(message)\n        hasToastedRef.current = true\n        startRouteTransition(() => router.push(redirectTo))\n      }, 100)\n    }\n\n    return () => {\n      if (timeoutID) {\n        clearTimeout(timeoutID)\n      }\n    }\n  }, [router, redirectTo, message, startRouteTransition])\n\n  return null\n}\n"], "names": ["c", "_c", "toast", "useRouteTransition", "useRouter", "React", "useEffect", "ToastAndRedirect", "t0", "$", "message", "redirectTo", "router", "startRouteTransition", "hasToastedRef", "useRef", "t1", "t2", "timeoutID", "current", "push", "clearTimeout"], "mappings": ";;;;AAAA,SAAAA,CAAA,IAAAC,EAAA;AACA,SAASC,KAAK,EAAEC,kBAAkB,QAAQ;AAC1C,SAASC,SAAS,QAAQ;AAC1B,OAAOC,KAAA,IAASC,SAAS,QAAQ;AAHjC;;;;;AASO,SAAAC,iBAAAC,EAAA;IAAA,MAAAC,CAAA,OAAAR,0MAAA,EAAA;IAA0B,MAAA,EAAAS,OAAA,EAAAC,UAAAA,EAAA,GAAAH,EAA8B;IAC7D,MAAAI,MAAA,GAAeR,gLAAA;IACf,MAAA,EAAAS,oBAAAA,EAAA,OAAiCV,2OAAA;IACjC,MAAAW,aAAA,GAAsBT,kMAAA,CAAAU,MAAA,CAAA,KAAa;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAC,OAAA,IAAAD,CAAA,CAAA,EAAA,KAAAE,UAAA,IAAAF,CAAA,CAAA,EAAA,KAAAG,MAAA,IAAAH,CAAA,CAAA,EAAA,KAAAI,oBAAA,EAAA;QAEzBG,EAAA,GAAAA,CAAA;YACJE,GAAA,CAAAA,SAAA;YAAA,IAAAhB,8NAAA,EAAA;gBAGFgB,SAAA,CAAAA,CAAA,CAAYA,WAAAA,EAAA,CAAAA,EAAA,CAAAA;oBACVA,8NAAAA,CAAAA,OAAAA,CAAcR,OAAA;oBACdI,aAAA,CAAAK,OAAA,GAAA;oBACAN,oBAAA,CAAA,IAA2BD,MAAA,CAAAQ,IAAA,CAAYT,UAAA;gBAAA,GAAA,GACtC;YAJH;YAAA,OAAA;gBAAA,IAQIO,SAAA,EAAA;oBACFG,YAAA,CAAaH,SAAA;gBAAA;YAAA;QAAA;QAGhBD,EAAA,GAAA;YAACL,MAAA;YAAQD,UAAA;YAAYD,OAAA;YAASG,oBAAA;SAAA;QAAqBJ,CAAA,CAAA,EAAA,GAAAC,OAAA;QAAAD,CAAA,CAAA,EAAA,GAAAE,UAAA;QAAAF,CAAA,CAAA,EAAA,GAAAG,MAAA;QAAAH,CAAA,CAAA,EAAA,GAAAI,oBAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAP,CAAA,CAAA,EAAA;QAAAQ,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;QAhBtDH,oMAAA,EAAUU,EAgBV,EAAGC,EAAmD;IAAA,OAAA;AAAA", "ignoreList": [], "debugId": null}}]}