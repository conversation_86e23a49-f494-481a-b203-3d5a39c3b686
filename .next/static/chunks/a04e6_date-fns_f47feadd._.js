(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatDistance.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatDistance",
    ()=>formatDistance
]);
const formatDistanceLocale = {
    lessThanXSeconds: {
        one: {
            regular: "mniej niż sekunda",
            past: "mniej niż sekundę",
            future: "mniej niż sekundę"
        },
        twoFour: "mniej niż {{count}} sekundy",
        other: "mniej niż {{count}} sekund"
    },
    xSeconds: {
        one: {
            regular: "sekunda",
            past: "sekundę",
            future: "sekundę"
        },
        twoFour: "{{count}} sekundy",
        other: "{{count}} sekund"
    },
    halfAMinute: {
        one: "pół minuty",
        twoFour: "pół minuty",
        other: "pół minuty"
    },
    lessThanXMinutes: {
        one: {
            regular: "mniej niż minuta",
            past: "mniej niż minutę",
            future: "mniej niż minutę"
        },
        twoFour: "mniej niż {{count}} minuty",
        other: "mniej niż {{count}} minut"
    },
    xMinutes: {
        one: {
            regular: "minuta",
            past: "minutę",
            future: "minutę"
        },
        twoFour: "{{count}} minuty",
        other: "{{count}} minut"
    },
    aboutXHours: {
        one: {
            regular: "około godziny",
            past: "około godziny",
            future: "około godzinę"
        },
        twoFour: "około {{count}} godziny",
        other: "około {{count}} godzin"
    },
    xHours: {
        one: {
            regular: "godzina",
            past: "godzinę",
            future: "godzinę"
        },
        twoFour: "{{count}} godziny",
        other: "{{count}} godzin"
    },
    xDays: {
        one: {
            regular: "dzień",
            past: "dzień",
            future: "1 dzień"
        },
        twoFour: "{{count}} dni",
        other: "{{count}} dni"
    },
    aboutXWeeks: {
        one: "około tygodnia",
        twoFour: "około {{count}} tygodni",
        other: "około {{count}} tygodni"
    },
    xWeeks: {
        one: "tydzień",
        twoFour: "{{count}} tygodnie",
        other: "{{count}} tygodni"
    },
    aboutXMonths: {
        one: "około miesiąc",
        twoFour: "około {{count}} miesiące",
        other: "około {{count}} miesięcy"
    },
    xMonths: {
        one: "miesiąc",
        twoFour: "{{count}} miesiące",
        other: "{{count}} miesięcy"
    },
    aboutXYears: {
        one: "około rok",
        twoFour: "około {{count}} lata",
        other: "około {{count}} lat"
    },
    xYears: {
        one: "rok",
        twoFour: "{{count}} lata",
        other: "{{count}} lat"
    },
    overXYears: {
        one: "ponad rok",
        twoFour: "ponad {{count}} lata",
        other: "ponad {{count}} lat"
    },
    almostXYears: {
        one: "prawie rok",
        twoFour: "prawie {{count}} lata",
        other: "prawie {{count}} lat"
    }
};
function declensionGroup(scheme, count) {
    if (count === 1) {
        return scheme.one;
    }
    const rem100 = count % 100;
    // ends with 11-20
    if (rem100 <= 20 && rem100 > 10) {
        return scheme.other;
    }
    const rem10 = rem100 % 10;
    // ends with 2, 3, 4
    if (rem10 >= 2 && rem10 <= 4) {
        return scheme.twoFour;
    }
    return scheme.other;
}
function declension(scheme, count, time) {
    const group = declensionGroup(scheme, count);
    const finalText = typeof group === "string" ? group : group[time];
    return finalText.replace("{{count}}", String(count));
}
const formatDistance = (token, count, options)=>{
    const scheme = formatDistanceLocale[token];
    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {
        return declension(scheme, count, "regular");
    }
    if (options.comparison && options.comparison > 0) {
        return "za " + declension(scheme, count, "future");
    } else {
        return declension(scheme, count, "past") + " temu";
    }
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatLong.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatLong",
    ()=>formatLong
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildFormatLongFn.js [app-client] (ecmascript)");
;
const dateFormats = {
    full: "EEEE, do MMMM y",
    long: "do MMMM y",
    medium: "do MMM y",
    short: "dd.MM.y"
};
const timeFormats = {
    full: "HH:mm:ss zzzz",
    long: "HH:mm:ss z",
    medium: "HH:mm:ss",
    short: "HH:mm"
};
const dateTimeFormats = {
    full: "{{date}} {{time}}",
    long: "{{date}} {{time}}",
    medium: "{{date}}, {{time}}",
    short: "{{date}}, {{time}}"
};
const formatLong = {
    date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateFormats,
        defaultWidth: "full"
    }),
    time: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: timeFormats,
        defaultWidth: "full"
    }),
    dateTime: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildFormatLongFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildFormatLongFn"])({
        formats: dateTimeFormats,
        defaultWidth: "full"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/isSameWeek.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "isSameWeek",
    ()=>isSameWeek
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$_lib$2f$normalizeDates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/_lib/normalizeDates.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/startOfWeek.js [app-client] (ecmascript)");
;
;
function isSameWeek(laterDate, earlierDate, options) {
    const [laterDate_, earlierDate_] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$_lib$2f$normalizeDates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeDates"])(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);
    return +(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfWeek"])(laterDate_, options) === +(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$startOfWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfWeek"])(earlierDate_, options);
}
const __TURBOPACK__default__export__ = isSameWeek;
}),
"[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatRelative.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "formatRelative",
    ()=>formatRelative
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$isSameWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/isSameWeek.js [app-client] (ecmascript)");
;
const adjectivesLastWeek = {
    masculine: "ostatni",
    feminine: "ostatnia"
};
const adjectivesThisWeek = {
    masculine: "ten",
    feminine: "ta"
};
const adjectivesNextWeek = {
    masculine: "następny",
    feminine: "następna"
};
const dayGrammaticalGender = {
    0: "feminine",
    1: "masculine",
    2: "masculine",
    3: "feminine",
    4: "masculine",
    5: "masculine",
    6: "feminine"
};
function dayAndTimeWithAdjective(token, date, baseDate, options) {
    let adjectives;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$isSameWeek$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSameWeek"])(date, baseDate, options)) {
        adjectives = adjectivesThisWeek;
    } else if (token === "lastWeek") {
        adjectives = adjectivesLastWeek;
    } else if (token === "nextWeek") {
        adjectives = adjectivesNextWeek;
    } else {
        throw new Error("Cannot determine adjectives for token ".concat(token));
    }
    const day = date.getDay();
    const grammaticalGender = dayGrammaticalGender[day];
    const adjective = adjectives[grammaticalGender];
    return "'".concat(adjective, "' eeee 'o' p");
}
const formatRelativeLocale = {
    lastWeek: dayAndTimeWithAdjective,
    yesterday: "'wczoraj o' p",
    today: "'dzisiaj o' p",
    tomorrow: "'jutro o' p",
    nextWeek: dayAndTimeWithAdjective,
    other: "P"
};
const formatRelative = (token, date, baseDate, options)=>{
    const format = formatRelativeLocale[token];
    if (typeof format === "function") {
        return format(token, date, baseDate, options);
    }
    return format;
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/localize.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "localize",
    ()=>localize
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildLocalizeFn.js [app-client] (ecmascript)");
;
const eraValues = {
    narrow: [
        "p.n.e.",
        "n.e."
    ],
    abbreviated: [
        "p.n.e.",
        "n.e."
    ],
    wide: [
        "przed naszą erą",
        "naszej ery"
    ]
};
const quarterValues = {
    narrow: [
        "1",
        "2",
        "3",
        "4"
    ],
    abbreviated: [
        "I kw.",
        "II kw.",
        "III kw.",
        "IV kw."
    ],
    wide: [
        "I kwartał",
        "II kwartał",
        "III kwartał",
        "IV kwartał"
    ]
};
const monthValues = {
    narrow: [
        "S",
        "L",
        "M",
        "K",
        "M",
        "C",
        "L",
        "S",
        "W",
        "P",
        "L",
        "G"
    ],
    abbreviated: [
        "sty",
        "lut",
        "mar",
        "kwi",
        "maj",
        "cze",
        "lip",
        "sie",
        "wrz",
        "paź",
        "lis",
        "gru"
    ],
    wide: [
        "styczeń",
        "luty",
        "marzec",
        "kwiecień",
        "maj",
        "czerwiec",
        "lipiec",
        "sierpień",
        "wrzesień",
        "październik",
        "listopad",
        "grudzień"
    ]
};
const monthFormattingValues = {
    narrow: [
        "s",
        "l",
        "m",
        "k",
        "m",
        "c",
        "l",
        "s",
        "w",
        "p",
        "l",
        "g"
    ],
    abbreviated: [
        "sty",
        "lut",
        "mar",
        "kwi",
        "maj",
        "cze",
        "lip",
        "sie",
        "wrz",
        "paź",
        "lis",
        "gru"
    ],
    wide: [
        "stycznia",
        "lutego",
        "marca",
        "kwietnia",
        "maja",
        "czerwca",
        "lipca",
        "sierpnia",
        "września",
        "października",
        "listopada",
        "grudnia"
    ]
};
const dayValues = {
    narrow: [
        "N",
        "P",
        "W",
        "Ś",
        "C",
        "P",
        "S"
    ],
    short: [
        "nie",
        "pon",
        "wto",
        "śro",
        "czw",
        "pią",
        "sob"
    ],
    abbreviated: [
        "niedz.",
        "pon.",
        "wt.",
        "śr.",
        "czw.",
        "pt.",
        "sob."
    ],
    wide: [
        "niedziela",
        "poniedziałek",
        "wtorek",
        "środa",
        "czwartek",
        "piątek",
        "sobota"
    ]
};
const dayFormattingValues = {
    narrow: [
        "n",
        "p",
        "w",
        "ś",
        "c",
        "p",
        "s"
    ],
    short: [
        "nie",
        "pon",
        "wto",
        "śro",
        "czw",
        "pią",
        "sob"
    ],
    abbreviated: [
        "niedz.",
        "pon.",
        "wt.",
        "śr.",
        "czw.",
        "pt.",
        "sob."
    ],
    wide: [
        "niedziela",
        "poniedziałek",
        "wtorek",
        "środa",
        "czwartek",
        "piątek",
        "sobota"
    ]
};
const dayPeriodValues = {
    narrow: {
        am: "a",
        pm: "p",
        midnight: "półn.",
        noon: "poł",
        morning: "rano",
        afternoon: "popoł.",
        evening: "wiecz.",
        night: "noc"
    },
    abbreviated: {
        am: "AM",
        pm: "PM",
        midnight: "północ",
        noon: "południe",
        morning: "rano",
        afternoon: "popołudnie",
        evening: "wieczór",
        night: "noc"
    },
    wide: {
        am: "AM",
        pm: "PM",
        midnight: "północ",
        noon: "południe",
        morning: "rano",
        afternoon: "popołudnie",
        evening: "wieczór",
        night: "noc"
    }
};
const dayPeriodFormattingValues = {
    narrow: {
        am: "a",
        pm: "p",
        midnight: "o półn.",
        noon: "w poł.",
        morning: "rano",
        afternoon: "po poł.",
        evening: "wiecz.",
        night: "w nocy"
    },
    abbreviated: {
        am: "AM",
        pm: "PM",
        midnight: "o północy",
        noon: "w południe",
        morning: "rano",
        afternoon: "po południu",
        evening: "wieczorem",
        night: "w nocy"
    },
    wide: {
        am: "AM",
        pm: "PM",
        midnight: "o północy",
        noon: "w południe",
        morning: "rano",
        afternoon: "po południu",
        evening: "wieczorem",
        night: "w nocy"
    }
};
const ordinalNumber = (dirtyNumber, _options)=>{
    return String(dirtyNumber);
};
const localize = {
    ordinalNumber,
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: eraValues,
        defaultWidth: "wide"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: quarterValues,
        defaultWidth: "wide",
        argumentCallback: (quarter)=>quarter - 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: monthValues,
        defaultWidth: "wide",
        formattingValues: monthFormattingValues,
        defaultFormattingWidth: "wide"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayValues,
        defaultWidth: "wide",
        formattingValues: dayFormattingValues,
        defaultFormattingWidth: "wide"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildLocalizeFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildLocalizeFn"])({
        values: dayPeriodValues,
        defaultWidth: "wide",
        formattingValues: dayPeriodFormattingValues,
        defaultFormattingWidth: "wide"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/match.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "match",
    ()=>match
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchFn.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js [app-client] (ecmascript)");
;
;
const matchOrdinalNumberPattern = /^(\d+)?/i;
const parseOrdinalNumberPattern = /\d+/i;
const matchEraPatterns = {
    narrow: /^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,
    abbreviated: /^(p\.?\s*n\.?\s*e\.?\s*|n\.?\s*e\.?\s*)/i,
    wide: /^(przed\s*nasz(ą|a)\s*er(ą|a)|naszej\s*ery)/i
};
const parseEraPatterns = {
    any: [
        /^p/i,
        /^n/i
    ]
};
const matchQuarterPatterns = {
    narrow: /^[1234]/i,
    abbreviated: /^(I|II|III|IV)\s*kw\.?/i,
    wide: /^(I|II|III|IV)\s*kwarta(ł|l)/i
};
const parseQuarterPatterns = {
    narrow: [
        /1/i,
        /2/i,
        /3/i,
        /4/i
    ],
    any: [
        /^I kw/i,
        /^II kw/i,
        /^III kw/i,
        /^IV kw/i
    ]
};
const matchMonthPatterns = {
    narrow: /^[slmkcwpg]/i,
    abbreviated: /^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,
    wide: /^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i
};
const parseMonthPatterns = {
    narrow: [
        /^s/i,
        /^l/i,
        /^m/i,
        /^k/i,
        /^m/i,
        /^c/i,
        /^l/i,
        /^s/i,
        /^w/i,
        /^p/i,
        /^l/i,
        /^g/i
    ],
    any: [
        /^st/i,
        /^lu/i,
        /^mar/i,
        /^k/i,
        /^maj/i,
        /^c/i,
        /^lip/i,
        /^si/i,
        /^w/i,
        /^p/i,
        /^lis/i,
        /^g/i
    ]
};
const matchDayPatterns = {
    narrow: /^[npwścs]/i,
    short: /^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,
    abbreviated: /^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\.?/i,
    wide: /^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i
};
const parseDayPatterns = {
    narrow: [
        /^n/i,
        /^p/i,
        /^w/i,
        /^ś/i,
        /^c/i,
        /^p/i,
        /^s/i
    ],
    abbreviated: [
        /^n/i,
        /^po/i,
        /^w/i,
        /^(ś|s)r/i,
        /^c/i,
        /^pt/i,
        /^so/i
    ],
    any: [
        /^n/i,
        /^po/i,
        /^w/i,
        /^(ś|s)r/i,
        /^c/i,
        /^pi/i,
        /^so/i
    ]
};
const matchDayPeriodPatterns = {
    narrow: /^(^a$|^p$|pó(ł|l)n\.?|o\s*pó(ł|l)n\.?|po(ł|l)\.?|w\s*po(ł|l)\.?|po\s*po(ł|l)\.?|rano|wiecz\.?|noc|w\s*nocy)/i,
    any: /^(am|pm|pó(ł|l)noc|o\s*pó(ł|l)nocy|po(ł|l)udnie|w\s*po(ł|l)udnie|popo(ł|l)udnie|po\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\s*nocy)/i
};
const parseDayPeriodPatterns = {
    narrow: {
        am: /^a$/i,
        pm: /^p$/i,
        midnight: /pó(ł|l)n/i,
        noon: /po(ł|l)/i,
        morning: /rano/i,
        afternoon: /po\s*po(ł|l)/i,
        evening: /wiecz/i,
        night: /noc/i
    },
    any: {
        am: /^am/i,
        pm: /^pm/i,
        midnight: /pó(ł|l)n/i,
        noon: /po(ł|l)/i,
        morning: /rano/i,
        afternoon: /po\s*po(ł|l)/i,
        evening: /wiecz/i,
        night: /noc/i
    }
};
const match = {
    ordinalNumber: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchPatternFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchPatternFn"])({
        matchPattern: matchOrdinalNumberPattern,
        parsePattern: parseOrdinalNumberPattern,
        valueCallback: (value)=>parseInt(value, 10)
    }),
    era: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchEraPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseEraPatterns,
        defaultParseWidth: "any"
    }),
    quarter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchQuarterPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseQuarterPatterns,
        defaultParseWidth: "any",
        valueCallback: (index)=>index + 1
    }),
    month: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchMonthPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseMonthPatterns,
        defaultParseWidth: "any"
    }),
    day: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPatterns,
        defaultMatchWidth: "wide",
        parsePatterns: parseDayPatterns,
        defaultParseWidth: "any"
    }),
    dayPeriod: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$_lib$2f$buildMatchFn$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildMatchFn"])({
        matchPatterns: matchDayPeriodPatterns,
        defaultMatchWidth: "any",
        parsePatterns: parseDayPeriodPatterns,
        defaultParseWidth: "any"
    })
};
}),
"[project]/git/payload-test/node_modules/date-fns/locale/pl.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "pl",
    ()=>pl
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatDistance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatLong.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/formatRelative.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$localize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/localize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$match$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/locale/pl/_lib/match.js [app-client] (ecmascript)");
;
;
;
;
;
const pl = {
    code: "pl",
    formatDistance: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatDistance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDistance"],
    formatLong: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatLong$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatLong"],
    formatRelative: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$formatRelative$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatRelative"],
    localize: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$localize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localize"],
    match: __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$locale$2f$pl$2f$_lib$2f$match$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["match"],
    options: {
        weekStartsOn: 1 /* Monday */ ,
        firstWeekContainsDate: 4
    }
};
const __TURBOPACK__default__export__ = pl;
}),
]);

//# sourceMappingURL=a04e6_date-fns_f47feadd._.js.map