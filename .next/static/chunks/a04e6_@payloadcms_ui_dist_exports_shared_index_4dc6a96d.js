(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/shared/index.js [app-client] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "EntityType",
    ()=>ue,
    "PayloadIcon",
    ()=>J,
    "PayloadLogo",
    ()=>G,
    "Translation",
    ()=>I,
    "WithServerSideProps",
    ()=>V,
    "abortAndIgnore",
    ()=>oe,
    "filterFields",
    ()=>D,
    "findLocaleFromCode",
    ()=>ie,
    "formatDate",
    ()=>v,
    "formatDocTitle",
    ()=>me,
    "getInitialColumns",
    ()=>re,
    "groupNavItems",
    ()=>de,
    "handleAbortRef",
    ()=>ne,
    "handleBackToDashboard",
    ()=>xe,
    "handleGoBack",
    ()=>Te,
    "handleTakeOver",
    ()=>De,
    "hasSavePermission",
    ()=>Ce,
    "isClientUserObject",
    ()=>we,
    "isEditing",
    ()=>ve,
    "mergeFieldStyles",
    ()=>_,
    "reduceToSerializableFields",
    ()=>Y,
    "requests",
    ()=>se,
    "sanitizeID",
    ()=>Me,
    "withMergedProps",
    ()=>H
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$isReactComponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/payload/dist/utilities/isReactComponent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/payload/dist/config/types.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$fields$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/payload/dist/fields/config/types.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$qs$2d$esm$2f$lib$2f$stringify$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/qs-esm/lib/stringify.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$formatAdminURL$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/payload/dist/utilities/formatAdminURL.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$transpose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/date-fns/transpose.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$translations$2f$dist$2f$utilities$2f$getTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/@payloadcms/translations/dist/utilities/getTranslation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$mergeListSearchAndWhere$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/payload/dist/utilities/mergeListSearchAndWhere.js [app-client] (ecmascript)");
;
;
var L = (param)=>{
    let { elements: e, translationString: t } = param;
    let r = /(<[^>]+>.*?<\/[^>]+>)/g, o = t.split(r);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
        children: o.map((n, s)=>{
            if (e && n.startsWith("<") && n.endsWith(">")) {
                let i = n[1], a = e[i];
                if (a) {
                    let l = new RegExp("<".concat(i, ">(.*?)</").concat(i, ">"), "g"), h = n.replace(l, (c, f)=>f);
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(a, {
                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(L, {
                            translationString: h
                        })
                    }, s);
                }
            }
            return n;
        })
    });
}, I = (param)=>{
    let { elements: e, i18nKey: t, t: r, variables: o } = param;
    let n = r(t, o || {});
    return e ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(L, {
        elements: e,
        translationString: n
    }) : n;
};
;
;
;
function H(param) {
    let { Component: e, sanitizeServerOnlyProps: t, toMergeIntoProps: r } = param;
    return t === void 0 && (t = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$isReactComponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isReactServerComponentOrFunction"])(e)), (n)=>{
        let s = W(n, r);
        return t && __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverProps"].forEach((i)=>{
            delete s[i];
        }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(e, {
            ...s
        });
    };
}
function W(e, t) {
    return {
        ...e,
        ...t
    };
}
;
;
;
var V = (param)=>{
    let { Component: e, serverOnlyProps: t, ...r } = param;
    return e ? ((n)=>{
        let s = {
            ...n,
            ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$isReactComponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isReactServerComponentOrFunction"])(e) ? t !== null && t !== void 0 ? t : {} : {}
        };
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(e, {
            ...s
        });
    })(r) : null;
};
var _ = (e)=>{
    var _e_admin, _e_admin1, _e_admin_style, _e_admin2;
    return {
        ...(e === null || e === void 0 ? void 0 : (_e_admin = e.admin) === null || _e_admin === void 0 ? void 0 : _e_admin.style) || {},
        ...(e === null || e === void 0 ? void 0 : (_e_admin1 = e.admin) === null || _e_admin1 === void 0 ? void 0 : _e_admin1.width) ? {
            "--field-width": e.admin.width
        } : {
            flex: "1 1 auto"
        },
        ...(e === null || e === void 0 ? void 0 : (_e_admin2 = e.admin) === null || _e_admin2 === void 0 ? void 0 : (_e_admin_style = _e_admin2.style) === null || _e_admin_style === void 0 ? void 0 : _e_admin_style.flex) ? {
            flex: e.admin.style.flex
        } : {}
    };
};
var q = [
    "validate",
    "customComponents"
], K = (e)=>{
    let t = {
        ...e
    };
    for (let r of q)delete t[r];
    return t;
}, Y = (e)=>{
    let t = {};
    for(let r in e)t[r] = K(e[r]);
    return t;
};
;
;
var J = (param)=>{
    let { fill: e } = param;
    let t = e || "var(--theme-elevation-1000)";
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        className: "graphic-icon",
        height: "100%",
        viewBox: "0 0 25 25",
        width: "100%",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M11.8673 21.2336L4.40922 16.9845C4.31871 16.9309 4.25837 16.8355 4.25837 16.7282V10.1609C4.25837 10.0477 4.38508 9.97616 4.48162 10.0298L13.1404 14.9642C13.2611 15.0358 13.412 14.9464 13.412 14.8093V11.6091C13.412 11.4839 13.3456 11.3647 13.2309 11.2992L2.81624 5.36353C2.72573 5.30989 2.60505 5.30989 2.51454 5.36353L1.15085 6.14422C1.06034 6.19786 1 6.29321 1 6.40048V18.5995C1 18.7068 1.06034 18.8021 1.15085 18.8558L11.8491 24.9583C11.9397 25.0119 12.0603 25.0119 12.1509 24.9583L21.1355 19.8331C21.2562 19.7616 21.2562 19.5948 21.1355 19.5232L18.3357 17.9261C18.2211 17.8605 18.0883 17.8605 17.9737 17.9261L12.175 21.2336C12.0845 21.2872 11.9638 21.2872 11.8733 21.2336H11.8673Z",
                fill: t
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M22.8491 6.13827L12.1508 0.0417218C12.0603 -0.0119135 11.9397 -0.0119135 11.8491 0.0417218L6.19528 3.2658C6.0746 3.33731 6.0746 3.50418 6.19528 3.57569L8.97092 5.16091C9.08557 5.22647 9.21832 5.22647 9.33296 5.16091L11.8672 3.71872C11.9578 3.66508 12.0784 3.66508 12.1689 3.71872L19.627 7.96782C19.7175 8.02146 19.7778 8.11681 19.7778 8.22408V14.8212C19.7778 14.9464 19.8442 15.0656 19.9589 15.1311L22.7345 16.7104C22.8552 16.7819 23.006 16.6925 23.006 16.5554V6.40048C23.006 6.29321 22.9457 6.19786 22.8552 6.14423L22.8491 6.13827Z",
                fill: t
            })
        ]
    });
};
;
;
var X = "\n  .graphic-logo path {\n    fill: var(--theme-elevation-1000);\n  }\n", G = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("svg", {
        className: "graphic-logo",
        fill: "none",
        height: "43.5",
        id: "b",
        viewBox: "0 0 193.38 43.5",
        width: "193.38",
        xmlns: "http://www.w3.org/2000/svg",
        children: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("style", {
                children: X
            }),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                id: "c",
                children: [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                        d: "M18.01,35.63l-12.36-7.13c-.15-.09-.25-.25-.25-.43v-11.02c0-.19.21-.31.37-.22l14.35,8.28c.2.12.45-.03.45-.26v-5.37c0-.21-.11-.41-.3-.52L3.01,9c-.15-.09-.35-.09-.5,0l-2.26,1.31c-.15.09-.25.25-.25.43v20.47c0,.**********.43l17.73,10.24c.***********.5,0l14.89-8.6c.2-.12.2-.4,0-.52l-4.64-2.68c-.19-.11-.41-.11-.6,0l-9.61,5.55c-.15.09-.35.09-.5,0Z"
                    }),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                        d: "M36.21,10.3L18.48.07c-.15-.09-.35-.09-.5,0l-9.37,5.41c-.2.12-.2.4,0,.52l4.6,2.66c.***********.6,0l4.2-2.42c.15-.09.35-.09.5,0l12.36,7.13c.***********.25.43v11.07c0,.**********.52l4.6,2.65c.2.12.45-.03.45-.26V10.74c0-.18-.1-.34-.25-.43Z"
                    }),
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                        id: "d",
                        children: [
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M193.38,9.47c0,1.94-1.48,3.32-3.3,3.32s-3.31-1.39-3.31-3.32,1.49-3.31,3.31-3.31,3.3,1.39,3.3,3.31ZM192.92,9.47c0-1.68-1.26-2.88-2.84-2.88s-2.84,1.2-2.84,2.88,1.26,2.89,2.84,2.89,2.84-1.2,2.84-2.89ZM188.69,11.17v-3.51h1.61c.85,0,1.35.39,1.35,1.15,0,.53-.3.86-.67,1.02l.79,1.35h-.89l-.72-1.22h-.64v1.22h-.82ZM190.18,9.31c.46,0,.64-.16.64-.5s-.19-.49-.64-.49h-.67v.99h.67Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M54.72,24.84v10.93h-5.4V6.1h12.26c7.02,0,11.1,3.2,11.1,9.39s-4.07,9.35-11.06,9.35h-6.9,0ZM61.12,20.52c4.07,0,6.11-1.66,6.11-5.03s-2.04-5.03-6.11-5.03h-6.4v10.06h6.4Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M85.94,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.18-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM85.73,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M90.39,14.66h5.4l5.86,15.92h.08l5.57-15.92h5.28l-8.23,21.49c-2,5.28-4.45,7.32-8.89,7.36-.71,0-1.7-.08-2.45-.21v-4.03c.***********,1.41.13,2.16,0,3.07-.75,4.2-3.66l-8.23-21.07h0Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M113.46,35.77V6.1h5.32v29.67h-5.32Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M130.79,36.27c-6.23,0-10.68-4.2-10.68-11.05s4.45-11.05,10.68-11.05,10.68,4.24,10.68,11.05-4.45,11.05-10.68,11.05ZM130.79,32.32c3.41,0,5.36-2.66,5.36-7.11s-1.95-7.11-5.36-7.11-5.36,2.7-5.36,7.11,1.91,7.11,5.36,7.11Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M156.19,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.19-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM155.98,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z"
                            }),
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                d: "M178.5,32.41c-1.04,2.12-3.58,3.87-6.78,3.87-5.53,0-9.31-4.49-9.31-11.05s3.78-11.05,9.31-11.05c3.28,0,5.69,1.83,6.69,3.95V6.1h5.32v29.67h-5.24v-3.37h0ZM178.55,24.84c0-4.11-1.95-6.78-5.32-6.78s-5.45,2.83-5.45,7.15,2,7.15,5.45,7.15,5.32-2.66,5.32-6.78v-.75h0Z"
                            })
                        ]
                    })
                ]
            })
        ]
    });
;
var D = (e)=>{
    let t = (r)=>{
        var _r_admin;
        return r.type !== "ui" && (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$fields$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fieldIsHiddenOrDisabled"])(r) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$fields$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fieldIsID"])(r) || (r === null || r === void 0 ? void 0 : (_r_admin = r.admin) === null || _r_admin === void 0 ? void 0 : _r_admin.disableListColumn) === !0;
    };
    return (e !== null && e !== void 0 ? e : []).reduce((r, o)=>{
        if (t(o)) return r;
        if (o.type === "tabs" && "tabs" in o) {
            let n = {
                ...o,
                tabs: o.tabs.map((s)=>({
                        ...s,
                        fields: D(s.fields)
                    }))
            };
            return r.push(n), r;
        }
        if ("fields" in o && Array.isArray(o.fields)) {
            let n = {
                ...o,
                fields: D(o.fields)
            };
            return r.push(n), r;
        }
        return r.push(o), r;
    }, []);
};
;
var C = (e, t)=>e === null || e === void 0 ? void 0 : e.reduce((r, o)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$fields$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fieldAffectsData"])(o) && o.name === t ? r : !(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$fields$2f$config$2f$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fieldAffectsData"])(o) && "fields" in o ? [
            ...r,
            ...C(o.fields, t)
        ] : o.type === "tabs" && "tabs" in o ? [
            ...r,
            ...o.tabs.reduce((n, s)=>[
                    ...n,
                    ..."name" in s ? [
                        s.name
                    ] : C(s.fields, t)
                ], [])
        ] : [
            ...r,
            o.name
        ], []), re = (e, t, r)=>{
    let o = [];
    if (Array.isArray(r) && r.length >= 1) o = r;
    else {
        t && o.push(t);
        let n = C(e, t);
        o = o.concat(n), o = o.slice(0, 4);
    }
    return o.map((n)=>({
            accessor: n,
            active: !0
        }));
};
function oe(e) {
    if (e) try {
        e.abort();
    } catch (e) {}
}
function ne(e) {
    let t = new AbortController;
    if (e.current) try {
        e.current.abort();
    } catch (e) {}
    return e.current = t, t;
}
;
var se = {
    delete: function(e) {
        let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
            headers: {}
        };
        let r = t && t.headers ? {
            ...t.headers
        } : {}, o = {
            ...t,
            credentials: "include",
            headers: {
                ...r
            },
            method: "delete"
        };
        return fetch(e, o);
    },
    get: function(e) {
        let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
            headers: {}
        };
        let r = "";
        return t.params && (r = __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$qs$2d$esm$2f$lib$2f$stringify$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["stringify"](t.params, {
            addQueryPrefix: !0
        })), fetch("".concat(e).concat(r), {
            credentials: "include",
            ...t
        });
    },
    patch: function(e) {
        let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
            headers: {}
        };
        let r = t && t.headers ? {
            ...t.headers
        } : {}, o = {
            ...t,
            credentials: "include",
            headers: {
                ...r
            },
            method: "PATCH"
        };
        return fetch(e, o);
    },
    post: function(e) {
        let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
            headers: {}
        };
        let r = t && t.headers ? {
            ...t.headers
        } : {}, o = {
            ...t,
            credentials: "include",
            headers: {
                ...r
            },
            method: "post"
        };
        return fetch("".concat(e), o);
    },
    put: function(e) {
        let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
            headers: {}
        };
        let r = t && t.headers ? {
            ...t.headers
        } : {}, o = {
            ...t,
            credentials: "include",
            headers: {
                ...r
            },
            method: "put"
        };
        return fetch(e, o);
    }
};
var ie = (e, t)=>!(e === null || e === void 0 ? void 0 : e.locales) || e.locales.length === 0 ? null : e.locales.find((r)=>(r === null || r === void 0 ? void 0 : r.code) === t);
;
var le = {}, d = {};
function u(e, t) {
    try {
        var _le, _e;
        let o = ((_le = le)[_e = e] || (_le[_e] = new Intl.DateTimeFormat("en-GB", {
            timeZone: e,
            hour: "numeric",
            timeZoneName: "longOffset"
        }).format))(t).split("GMT")[1] || "";
        return o in d ? d[o] : Z(o, o.split(":"));
    } catch (e1) {
        if (e in d) return d[e];
        let r = e === null || e === void 0 ? void 0 : e.match(ce);
        return r ? Z(e, r.slice(1)) : NaN;
    }
}
var ce = /([+-]\d\d):?(\d\d)?/;
function Z(e, t) {
    let r = +t[0], o = +(t[1] || 0);
    return d[e] = r > 0 ? r * 60 + o : r * 60 - o;
}
var m = class e extends Date {
    static tz(t) {
        for(var _len = arguments.length, r = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
            r[_key - 1] = arguments[_key];
        }
        return r.length ? new e(...r, t) : new e(Date.now(), t);
    }
    withTimeZone(t) {
        return new e(+this, t);
    }
    getTimezoneOffset() {
        return -u(this.timeZone, this);
    }
    setTime(t) {
        return Date.prototype.setTime.apply(this, arguments), w(this), +this;
    }
    [Symbol.for("constructDateFrom")](t) {
        return new e(+new Date(t), this.timeZone);
    }
    constructor(...t){
        super(), t.length > 1 && typeof t[t.length - 1] == "string" && (this.timeZone = t.pop()), this.internal = new Date, isNaN(u(this.timeZone, this)) ? this.setTime(NaN) : t.length ? typeof t[0] == "number" && (t.length === 1 || t.length === 2 && typeof t[1] != "number") ? this.setTime(t[0]) : typeof t[0] == "string" ? this.setTime(+new Date(t[0])) : t[0] instanceof Date ? this.setTime(+t[0]) : (this.setTime(+new Date(...t)), N(this, NaN), w(this)) : this.setTime(Date.now());
    }
}, j = /^(get|set)(?!UTC)/;
Object.getOwnPropertyNames(Date.prototype).forEach((e)=>{
    if (!j.test(e)) return;
    let t = e.replace(j, "$1UTC");
    m.prototype[t] && (e.startsWith("get") ? m.prototype[e] = function() {
        return this.internal[t]();
    } : (m.prototype[e] = function() {
        return Date.prototype[t].apply(this.internal, arguments), fe(this), +this;
    }, m.prototype[t] = function() {
        return Date.prototype[t].apply(this, arguments), w(this), +this;
    }));
});
function w(e) {
    e.internal.setTime(+e), e.internal.setUTCMinutes(e.internal.getUTCMinutes() - e.getTimezoneOffset());
}
function fe(e) {
    Date.prototype.setFullYear.call(e, e.internal.getUTCFullYear(), e.internal.getUTCMonth(), e.internal.getUTCDate()), Date.prototype.setHours.call(e, e.internal.getUTCHours(), e.internal.getUTCMinutes(), e.internal.getUTCSeconds(), e.internal.getUTCMilliseconds()), N(e);
}
function N(e) {
    let t = u(e.timeZone, e), r = new Date(+e);
    r.setUTCHours(r.getUTCHours() - 1);
    let o = -new Date(+e).getTimezoneOffset(), n = -new Date(+r).getTimezoneOffset(), s = o - n, i = Date.prototype.getHours.apply(e) !== e.internal.getUTCHours();
    s && i && e.internal.setUTCMinutes(e.internal.getUTCMinutes() + s);
    let a = o - t;
    a && Date.prototype.setUTCMinutes.call(e, Date.prototype.getUTCMinutes.call(e) + a);
    let l = u(e.timeZone, e), c = -new Date(+e).getTimezoneOffset() - l, f = l !== t, b = c - a;
    if (f && b) {
        Date.prototype.setUTCMinutes.call(e, Date.prototype.getUTCMinutes.call(e) + b);
        let A = u(e.timeZone, e), y = l - A;
        y && (e.internal.setUTCMinutes(e.internal.getUTCMinutes() + y), Date.prototype.setUTCMinutes.call(e, Date.prototype.getUTCMinutes.call(e) + y));
    }
}
;
var v = (param)=>{
    let { date: e, i18n: t, pattern: r, timezone: o } = param;
    let n = new m(new Date(e));
    if (o) {
        let s = m.tz(o), i = n.withTimeZone(o), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$transpose$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transpose"])(i, s);
        return t.dateFNS ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(a, r, {
            locale: t.dateFNS
        }) : "".concat(t.t("general:loading"), "...");
    }
    return t.dateFNS ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(n, r, {
        locale: t.dateFNS
    }) : "".concat(t.t("general:loading"), "...");
};
;
function M(e) {
    return typeof e == "object" && "root" in e;
}
function x(e, t) {
    for (let r of e)"text" in r && r.text ? t += r.text : "children" in r || (t += "[".concat(r.type, "]")), "children" in r && r.children && (t += x(r.children, t));
    return t;
}
var R = (e)=>Array.isArray(e) ? e.map((t)=>typeof t == "object" && t !== null ? t.id : String(t)).filter(Boolean).join(", ") : typeof e == "object" && e !== null ? e.id || "" : String(e);
var me = (param)=>{
    let { collectionConfig: e, data: t, dateFormat: r, fallback: o, globalConfig: n, i18n: s } = param;
    var _i_root_children_, _i_root_children, _o_root_children_, _o_root_children;
    let i;
    if (e) {
        var _e_admin;
        let a = e === null || e === void 0 ? void 0 : (_e_admin = e.admin) === null || _e_admin === void 0 ? void 0 : _e_admin.useAsTitle;
        if (a && (i = t === null || t === void 0 ? void 0 : t[a], i)) {
            let l = e.fields.find((f)=>"name" in f && f.name === a), h = (l === null || l === void 0 ? void 0 : l.type) === "date", c = (l === null || l === void 0 ? void 0 : l.type) === "relationship";
            if (h) {
                var _l_admin_date, _l_admin;
                let f = "date" in l.admin && (l === null || l === void 0 ? void 0 : (_l_admin = l.admin) === null || _l_admin === void 0 ? void 0 : (_l_admin_date = _l_admin.date) === null || _l_admin_date === void 0 ? void 0 : _l_admin_date.displayFormat) || r;
                i = v({
                    date: i,
                    i18n: s,
                    pattern: f
                }) || i;
            }
            c && (i = R(t[a]));
        }
    }
    return n && (i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$translations$2f$dist$2f$utilities$2f$getTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslation"])(n === null || n === void 0 ? void 0 : n.label, s) || (n === null || n === void 0 ? void 0 : n.slug)), i && M(i) && (i = x(((_i_root_children = i.root.children) === null || _i_root_children === void 0 ? void 0 : (_i_root_children_ = _i_root_children[0]) === null || _i_root_children_ === void 0 ? void 0 : _i_root_children_.children) || [], "")), !i && M(o) && (i = x(((_o_root_children = o.root.children) === null || _o_root_children === void 0 ? void 0 : (_o_root_children_ = _o_root_children[0]) === null || _o_root_children_ === void 0 ? void 0 : _o_root_children_.children) || [], "")), i || (i = typeof o == "string" ? o : "[".concat(s.t("general:untitled"), "]")), i;
};
;
var ue = function(e) {
    return e.collection = "collections", e.global = "globals", e;
}({});
function de(e, t, r) {
    return e.reduce((n, s)=>{
        var _s_entity_admin, _s_entity, _t_s_type_toLowerCase_s_entity_slug, _t_s_type_toLowerCase;
        if (((_s_entity = s.entity) === null || _s_entity === void 0 ? void 0 : (_s_entity_admin = _s_entity.admin) === null || _s_entity_admin === void 0 ? void 0 : _s_entity_admin.group) === !1) return n;
        if (t === null || t === void 0 ? void 0 : (_t_s_type_toLowerCase = t[s.type.toLowerCase()]) === null || _t_s_type_toLowerCase === void 0 ? void 0 : (_t_s_type_toLowerCase_s_entity_slug = _t_s_type_toLowerCase[s.entity.slug]) === null || _t_s_type_toLowerCase_s_entity_slug === void 0 ? void 0 : _t_s_type_toLowerCase_s_entity_slug.read) {
            let i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$translations$2f$dist$2f$utilities$2f$getTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslation"])(s.entity.admin.group, r), a = "labels" in s.entity ? s.entity.labels.plural : s.entity.label, l = typeof a == "function" ? a({
                i18n: r,
                t: r.t
            }) : a;
            if (s.entity.admin.group) {
                let h = n.find((f)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$translations$2f$dist$2f$utilities$2f$getTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslation"])(f.label, r) === i), c = h;
                h || (c = {
                    entities: [],
                    label: i
                }, n.push(c)), c.entities.push({
                    slug: s.entity.slug,
                    type: s.type,
                    label: l
                });
            } else n.find((c)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$translations$2f$dist$2f$utilities$2f$getTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslation"])(c.label, r) === r.t("general:".concat(s.type))).entities.push({
                slug: s.entity.slug,
                type: s.type,
                label: l
            });
        }
        return n;
    }, [
        {
            entities: [],
            label: r.t("general:collections")
        },
        {
            entities: [],
            label: r.t("general:globals")
        }
    ]).filter((n)=>n.entities.length > 0);
}
;
var xe = (param)=>{
    let { adminRoute: e, router: t } = param;
    let r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$formatAdminURL$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatAdminURL"])({
        adminRoute: e,
        path: "/"
    });
    t.push(r);
};
;
var Te = (param)=>{
    let { adminRoute: e, collectionSlug: t, router: r } = param;
    let o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$payload$2f$dist$2f$utilities$2f$formatAdminURL$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatAdminURL"])({
        adminRoute: e,
        path: t ? "/collections/".concat(t) : "/"
    });
    r.push(o);
};
var De = async (param)=>{
    let { id: e, clearRouteCache: t, collectionSlug: r, documentLockStateRef: o, globalSlug: n, isLockingEnabled: s, isWithinDoc: i, setCurrentEditor: a, setIsReadOnlyForIncomingUser: l, updateDocumentEditor: h, user: c } = param;
    if (s) try {
        var _o_current;
        await h(e, r !== null && r !== void 0 ? r : n, c), i || (o.current.hasShownLockedModal = !0), o.current = {
            hasShownLockedModal: (_o_current = o.current) === null || _o_current === void 0 ? void 0 : _o_current.hasShownLockedModal,
            isLocked: !0,
            user: c
        }, a(c), i && l && l(!1), t && t();
    } catch (f) {
        console.error("Error during document takeover:", f);
    }
};
var Ce = (e)=>{
    let { collectionSlug: t, docPermissions: r, globalSlug: o, isEditing: n } = e;
    return t ? !!(n && (r === null || r === void 0 ? void 0 : r.update) || !n && (r === null || r === void 0 ? void 0 : r.create)) : o ? !!(r === null || r === void 0 ? void 0 : r.update) : !1;
};
var we = (e)=>e && typeof e == "object";
var ve = (param)=>{
    let { id: e, collectionSlug: t, globalSlug: r } = param;
    return !!(r || t && e);
};
function Me(e) {
    return e === void 0 || typeof e == "number" ? e : decodeURIComponent(e);
}
;
;
 //# sourceMappingURL=index.js.map
}),
]);

//# sourceMappingURL=a04e6_%40payloadcms_ui_dist_exports_shared_index_4dc6a96d.js.map