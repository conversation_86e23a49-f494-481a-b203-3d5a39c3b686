(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/client/CodeEditor-UTOLPRAV.js [app-client] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([]);
// Workaround for react-datepicker and other cjs dependencies potentially inserting require("react") statements
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
// Workaround end
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$ui$2f$dist$2f$exports$2f$client$2f$chunk$2d$S2X3JKSP$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/client/chunk-S2X3JKSP.js [app-client] (ecmascript)");
"use client";
;
;
function require(m) {
    if (m === 'react') return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__;
    if (m === 'react-dom') return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__;
    throw new Error("Unknown module ".concat(m));
}
;
;
;
 //# sourceMappingURL=CodeEditor-UTOLPRAV.js.map
}),
"[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/client/CodeEditor-UTOLPRAV.js [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$ui$2f$dist$2f$exports$2f$client$2f$chunk$2d$S2X3JKSP$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"]
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$ui$2f$dist$2f$exports$2f$client$2f$CodeEditor$2d$UTOLPRAV$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/client/CodeEditor-UTOLPRAV.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$payload$2d$test$2f$node_modules$2f40$payloadcms$2f$ui$2f$dist$2f$exports$2f$client$2f$chunk$2d$S2X3JKSP$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/payload-test/node_modules/@payloadcms/ui/dist/exports/client/chunk-S2X3JKSP.js [app-client] (ecmascript)");
}),
]);

//# sourceMappingURL=a04e6_%40payloadcms_ui_dist_exports_client_CodeEditor-UTOLPRAV_7969cbda.js.map