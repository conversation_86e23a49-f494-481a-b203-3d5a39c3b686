/* [project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/HTMLDiff/index.scss.css [app-client] (css) */
@layer payload-default {
  :root {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-600);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-800);
    --diff-delete-link-color: var(--theme-error-600);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-600);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-800);
    --diff-create-link-color: var(--theme-success-600);
  }

  html[data-theme="dark"] {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-650);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-900);
    --diff-delete-link-color: var(--theme-error-750);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-650);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-900);
    --diff-create-link-color: var(--theme-success-750);
  }

  .html-diff {
    letter-spacing: .02em;
    font-size: 14px;
  }

  .html-diff-no-value {
    color: var(--theme-elevation-400);
  }

  .html-diff pre {
    margin-top: 0;
    margin-bottom: 0;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
  }

  .html-diff li::marker {
    color: var(--theme-text);
  }

  .html-diff [data-match-type="delete"]:not([data-enable-match="false"]):not(:is([data-enable-match="false"] *)) {
    color: var(--diff-delete-pill-color);
    -webkit-text-decoration-color: var(--diff-delete-pill-color);
    text-decoration-color: var(--diff-delete-pill-color);
    background-color: var(--diff-delete-pill-bg);
    border-radius: 4px;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
    text-decoration-thickness: 1px;
  }

  .html-diff a[data-match-type="delete"] :not([data-enable-match="false"]) :not(:is([data-enable-match="false"] *)) {
    color: var(--diff-delete-link-color);
  }

  .html-diff a[data-match-type="create"]:not(img) :not([data-enable-match="false"]) :not(:is([data-enable-match="false"] *)) {
    color: var(--diff-create-link-color);
  }

  .html-diff [data-match-type="create"]:not(img):not([data-enable-match="false"]):not(:is([data-enable-match="false"] *)) {
    background-color: var(--diff-create-pill-bg);
    color: var(--diff-create-pill-color);
    border-radius: 4px;
  }

  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper {
    display: inline-flex;
  }

  .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    display: flex;
  }

  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper, .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    flex-direction: row;
    align-items: center;
    position: relative;
  }

  .html-diff .html-diff-create-inline-wrapper:after, .html-diff .html-diff-delete-inline-wrapper:after, .html-diff .html-diff-create-block-wrapper:after, .html-diff .html-diff-delete-block-wrapper:after {
    content: "";
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }
}

/*# sourceMappingURL=a04e6_%40payloadcms_ui_dist_elements_HTMLDiff_index_scss_css_bad6b30c._.single.css.map*/