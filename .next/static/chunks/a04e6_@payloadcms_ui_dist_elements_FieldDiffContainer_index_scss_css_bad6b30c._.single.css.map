{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/home/<USER>/git/payload-test/node_modules/@payloadcms/ui/dist/scss/type.scss", "turbopack:///turbopack:///[project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/home/<USER>/git/payload-test/node_modules/@payloadcms/ui/dist/scss/resets.scss", "turbopack:///turbopack:///[project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/home/<USER>/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/index.scss"], "sourcesContent": ["@import 'vars';\n@import 'queries';\n\n/////////////////////////////\n// HEADINGS\n/////////////////////////////\n\n@layer payload-default {\n  %h1,\n  %h2,\n  %h3,\n  %h4,\n  %h5,\n  %h6 {\n    font-family: var(--font-body);\n    font-weight: 500;\n  }\n\n  %h1 {\n    margin: 0;\n    font-size: base(1.6);\n    line-height: base(1.8);\n\n    @include small-break {\n      letter-spacing: -0.5px;\n      font-size: base(1.25);\n    }\n  }\n\n  %h2 {\n    margin: 0;\n    font-size: base(1.3);\n    line-height: base(1.6);\n\n    @include small-break {\n      font-size: base(0.85);\n    }\n  }\n\n  %h3 {\n    margin: 0;\n    font-size: base(1);\n    line-height: base(1.2);\n\n    @include small-break {\n      font-size: base(0.65);\n      line-height: 1.25;\n    }\n  }\n\n  %h4 {\n    margin: 0;\n    font-size: base(0.8);\n    line-height: base(1);\n    letter-spacing: -0.375px;\n  }\n\n  %h5 {\n    margin: 0;\n    font-size: base(0.65);\n    line-height: base(0.8);\n  }\n\n  %h6 {\n    margin: 0;\n    font-size: base(0.6);\n    line-height: base(0.8);\n  }\n\n  %small {\n    margin: 0;\n    font-size: 12px;\n    line-height: 20px;\n  }\n\n  /////////////////////////////\n  // TYPE STYLES\n  /////////////////////////////\n\n  %large-body {\n    font-size: base(0.6);\n    line-height: base(1);\n    letter-spacing: base(0.02);\n\n    @include mid-break {\n      font-size: base(0.7);\n      line-height: base(1);\n    }\n\n    @include small-break {\n      font-size: base(0.55);\n      line-height: base(0.75);\n    }\n  }\n\n  %body {\n    font-size: $baseline-body-size;\n    line-height: $baseline-px;\n    font-weight: normal;\n    font-family: var(--font-body);\n  }\n\n  %code {\n    font-size: base(0.4);\n    color: var(--theme-elevation-400);\n\n    span {\n      color: var(--theme-elevation-800);\n    }\n  }\n}\n", "@layer payload-default {\n  %btn-reset {\n    border: 0;\n    background: none;\n    box-shadow: none;\n    border-radius: 0;\n    padding: 0;\n    color: currentColor;\n    font-family: var(--font-body);\n  }\n}\n\n@mixin btn-reset {\n  border: 0;\n  background: none;\n  box-shadow: none;\n  border-radius: 0;\n  padding: 0;\n  color: currentColor;\n  font-family: var(--font-body);\n}\n", "@import '../../scss/styles.scss';\n\n@layer payload-default {\n  .field-diff {\n    &__locale-label {\n      background: var(--theme-elevation-100);\n      border-radius: var(--style-radius-s);\n      padding: calc(var(--base) * 0.2);\n      // border-radius: $style-radius-m;\n      [dir='ltr'] & {\n        margin-right: calc(var(--base) * 0.25);\n      }\n      [dir='rtl'] & {\n        margin-left: calc(var(--base) * 0.25);\n      }\n    }\n\n    &-container {\n      position: relative;\n\n      // Vertical separator line - not needed anymore, as the parent version view container adds a vertical line that spans the entire height of the container.\n      /*\n    &::after {\n      content: '';\n      position: absolute;\n      top: 0;\n      bottom: 0;\n      left: var(--left-offset);\n      width: 1px;\n      background-color: var(--theme-elevation-100);\n      transform: translateX(-50%); // Center the line\n    }*/\n    }\n\n    &-content {\n      display: grid;\n      // Need to use 50% 50% so that we can apply overflow-x without the column shrinking to the content width.\n      // Need -base(0.5) to enure the gap is center aligned - this is required when using 50% over 1fr.\n      grid-template-columns: calc(50% - base(0.5)) calc(50% - base(0.5));\n      gap: base(1);\n      background: var(--theme-elevation-50);\n      padding: base(0.5);\n    }\n  }\n}\n"], "names": [], "mappings": "AAOA;EAHI;;;;;;EAKE;;;;EAGA;;;;EAKF;;;;EAiBA", "ignoreList": [0, 1, 2]}}]}