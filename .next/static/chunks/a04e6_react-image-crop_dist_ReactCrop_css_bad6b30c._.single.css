/* [project]/git/payload-test/node_modules/react-image-crop/dist/ReactCrop.css [app-client] (css) */
.ReactCrop {
  cursor: crosshair;
  max-width: 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.ReactCrop *, .ReactCrop :before, .ReactCrop :after {
  box-sizing: border-box;
}

.ReactCrop--disabled, .ReactCrop--locked {
  cursor: inherit;
}

.ReactCrop__child-wrapper {
  max-height: inherit;
}

.ReactCrop__child-wrapper > img, .ReactCrop__child-wrapper > video {
  max-width: 100%;
  max-height: inherit;
  display: block;
}

.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > img, .ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > video, .ReactCrop:not(.ReactCrop--disabled) .ReactCrop__crop-selection {
  touch-action: none;
}

.ReactCrop__crop-selection {
  cursor: move;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateZ(0);
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, .5);
}

.ReactCrop--disabled .ReactCrop__crop-selection {
  cursor: inherit;
}

.ReactCrop--circular-crop .ReactCrop__crop-selection {
  border-radius: 50%;
}

.ReactCrop--no-animate .ReactCrop__crop-selection {
  outline: 1px dashed #fff;
}

.ReactCrop__crop-selection:not(.ReactCrop--no-animate .ReactCrop__crop-selection) {
  color: #fff;
  background-image: linear-gradient(to right, #fff 50%, #444 50%), linear-gradient(to right, #fff 50%, #444 50%), linear-gradient(#fff 50%, #444 50%), linear-gradient(#fff 50%, #444 50%);
  background-position: 0 0, 0 100%, 0 0, 100% 0;
  background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
  background-size: 10px 1px, 10px 1px, 1px 10px, 1px 10px;
  animation: 1s linear infinite marching-ants;
}

@keyframes marching-ants {
  0% {
    background-position: 0 0, 0 100%, 0 0, 100% 0;
  }

  to {
    background-position: 20px 0, -20px 100%, 0 -20px, 100% 20px;
  }
}

.ReactCrop__crop-selection:focus {
  border-style: solid;
  border-color: #00f;
  outline: none;
}

.ReactCrop--invisible-crop .ReactCrop__crop-selection {
  display: none;
}

.ReactCrop__rule-of-thirds-vt:before, .ReactCrop__rule-of-thirds-vt:after, .ReactCrop__rule-of-thirds-hz:before, .ReactCrop__rule-of-thirds-hz:after {
  content: "";
  background-color: rgba(255, 255, 255, .4);
  display: block;
  position: absolute;
}

.ReactCrop__rule-of-thirds-vt:before, .ReactCrop__rule-of-thirds-vt:after {
  width: 1px;
  height: 100%;
}

.ReactCrop__rule-of-thirds-vt:before {
  left: 33.3333%;
}

.ReactCrop__rule-of-thirds-vt:after {
  left: 66.6667%;
}

.ReactCrop__rule-of-thirds-hz:before, .ReactCrop__rule-of-thirds-hz:after {
  width: 100%;
  height: 1px;
}

.ReactCrop__rule-of-thirds-hz:before {
  top: 33.3333%;
}

.ReactCrop__rule-of-thirds-hz:after {
  top: 66.6667%;
}

.ReactCrop__drag-handle {
  position: absolute;
}

.ReactCrop__drag-handle:after {
  content: "";
  background-color: rgba(0, 0, 0, .2);
  border: 1px solid rgba(255, 255, 255, .7);
  outline: 1px solid rgba(0, 0, 0, 0);
  width: 10px;
  height: 10px;
  display: block;
  position: absolute;
}

.ReactCrop__drag-handle:focus:after {
  background: #2dbfff;
  border-color: #00f;
}

.ReactCrop .ord-nw {
  cursor: nw-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 0;
  left: 0;
}

.ReactCrop .ord-nw:after {
  top: 0;
  left: 0;
}

.ReactCrop .ord-n {
  cursor: n-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 0;
  left: 50%;
}

.ReactCrop .ord-n:after {
  top: 0;
}

.ReactCrop .ord-ne {
  cursor: ne-resize;
  margin-top: -5px;
  margin-right: -5px;
  top: 0;
  right: 0;
}

.ReactCrop .ord-ne:after {
  top: 0;
  right: 0;
}

.ReactCrop .ord-e {
  cursor: e-resize;
  margin-top: -5px;
  margin-right: -5px;
  top: 50%;
  right: 0;
}

.ReactCrop .ord-e:after {
  right: 0;
}

.ReactCrop .ord-se {
  cursor: se-resize;
  margin-bottom: -5px;
  margin-right: -5px;
  bottom: 0;
  right: 0;
}

.ReactCrop .ord-se:after {
  bottom: 0;
  right: 0;
}

.ReactCrop .ord-s {
  cursor: s-resize;
  margin-bottom: -5px;
  margin-left: -5px;
  bottom: 0;
  left: 50%;
}

.ReactCrop .ord-s:after {
  bottom: 0;
}

.ReactCrop .ord-sw {
  cursor: sw-resize;
  margin-bottom: -5px;
  margin-left: -5px;
  bottom: 0;
  left: 0;
}

.ReactCrop .ord-sw:after {
  bottom: 0;
  left: 0;
}

.ReactCrop .ord-w {
  cursor: w-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 50%;
  left: 0;
}

.ReactCrop .ord-w:after {
  left: 0;
}

.ReactCrop__disabled .ReactCrop__drag-handle {
  cursor: inherit;
}

.ReactCrop__drag-bar {
  position: absolute;
}

.ReactCrop__drag-bar.ord-n {
  width: 100%;
  height: 6px;
  margin-top: -3px;
  top: 0;
  left: 0;
}

.ReactCrop__drag-bar.ord-e {
  width: 6px;
  height: 100%;
  margin-right: -3px;
  top: 0;
  right: 0;
}

.ReactCrop__drag-bar.ord-s {
  width: 100%;
  height: 6px;
  margin-bottom: -3px;
  bottom: 0;
  left: 0;
}

.ReactCrop__drag-bar.ord-w {
  width: 6px;
  height: 100%;
  margin-left: -3px;
  top: 0;
  left: 0;
}

.ReactCrop--new-crop .ReactCrop__drag-bar, .ReactCrop--new-crop .ReactCrop__drag-handle, .ReactCrop--fixed-aspect .ReactCrop__drag-bar, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w {
  display: none;
}

@media (pointer: coarse) {
  .ReactCrop .ord-n, .ReactCrop .ord-e, .ReactCrop .ord-s, .ReactCrop .ord-w {
    display: none;
  }

  .ReactCrop__drag-handle {
    width: 24px;
    height: 24px;
  }
}

/*# sourceMappingURL=a04e6_react-image-crop_dist_ReactCrop_css_bad6b30c._.single.css.map*/