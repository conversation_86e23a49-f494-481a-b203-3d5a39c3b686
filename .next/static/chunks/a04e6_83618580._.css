/* [project]/git/payload-test/node_modules/react-image-crop/dist/ReactCrop.css [app-client] (css) */
.ReactCrop {
  cursor: crosshair;
  max-width: 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.ReactCrop *, .ReactCrop :before, .ReactCrop :after {
  box-sizing: border-box;
}

.ReactCrop--disabled, .ReactCrop--locked {
  cursor: inherit;
}

.ReactCrop__child-wrapper {
  max-height: inherit;
}

.ReactCrop__child-wrapper > img, .ReactCrop__child-wrapper > video {
  max-width: 100%;
  max-height: inherit;
  display: block;
}

.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > img, .ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > video, .ReactCrop:not(.ReactCrop--disabled) .ReactCrop__crop-selection {
  touch-action: none;
}

.ReactCrop__crop-selection {
  cursor: move;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateZ(0);
  box-shadow: 0 0 0 9999em rgba(0, 0, 0, .5);
}

.ReactCrop--disabled .ReactCrop__crop-selection {
  cursor: inherit;
}

.ReactCrop--circular-crop .ReactCrop__crop-selection {
  border-radius: 50%;
}

.ReactCrop--no-animate .ReactCrop__crop-selection {
  outline: 1px dashed #fff;
}

.ReactCrop__crop-selection:not(.ReactCrop--no-animate .ReactCrop__crop-selection) {
  color: #fff;
  background-image: linear-gradient(to right, #fff 50%, #444 50%), linear-gradient(to right, #fff 50%, #444 50%), linear-gradient(#fff 50%, #444 50%), linear-gradient(#fff 50%, #444 50%);
  background-position: 0 0, 0 100%, 0 0, 100% 0;
  background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
  background-size: 10px 1px, 10px 1px, 1px 10px, 1px 10px;
  animation: 1s linear infinite marching-ants;
}

@keyframes marching-ants {
  0% {
    background-position: 0 0, 0 100%, 0 0, 100% 0;
  }

  to {
    background-position: 20px 0, -20px 100%, 0 -20px, 100% 20px;
  }
}

.ReactCrop__crop-selection:focus {
  border-style: solid;
  border-color: #00f;
  outline: none;
}

.ReactCrop--invisible-crop .ReactCrop__crop-selection {
  display: none;
}

.ReactCrop__rule-of-thirds-vt:before, .ReactCrop__rule-of-thirds-vt:after, .ReactCrop__rule-of-thirds-hz:before, .ReactCrop__rule-of-thirds-hz:after {
  content: "";
  background-color: rgba(255, 255, 255, .4);
  display: block;
  position: absolute;
}

.ReactCrop__rule-of-thirds-vt:before, .ReactCrop__rule-of-thirds-vt:after {
  width: 1px;
  height: 100%;
}

.ReactCrop__rule-of-thirds-vt:before {
  left: 33.3333%;
}

.ReactCrop__rule-of-thirds-vt:after {
  left: 66.6667%;
}

.ReactCrop__rule-of-thirds-hz:before, .ReactCrop__rule-of-thirds-hz:after {
  width: 100%;
  height: 1px;
}

.ReactCrop__rule-of-thirds-hz:before {
  top: 33.3333%;
}

.ReactCrop__rule-of-thirds-hz:after {
  top: 66.6667%;
}

.ReactCrop__drag-handle {
  position: absolute;
}

.ReactCrop__drag-handle:after {
  content: "";
  background-color: rgba(0, 0, 0, .2);
  border: 1px solid rgba(255, 255, 255, .7);
  outline: 1px solid rgba(0, 0, 0, 0);
  width: 10px;
  height: 10px;
  display: block;
  position: absolute;
}

.ReactCrop__drag-handle:focus:after {
  background: #2dbfff;
  border-color: #00f;
}

.ReactCrop .ord-nw {
  cursor: nw-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 0;
  left: 0;
}

.ReactCrop .ord-nw:after {
  top: 0;
  left: 0;
}

.ReactCrop .ord-n {
  cursor: n-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 0;
  left: 50%;
}

.ReactCrop .ord-n:after {
  top: 0;
}

.ReactCrop .ord-ne {
  cursor: ne-resize;
  margin-top: -5px;
  margin-right: -5px;
  top: 0;
  right: 0;
}

.ReactCrop .ord-ne:after {
  top: 0;
  right: 0;
}

.ReactCrop .ord-e {
  cursor: e-resize;
  margin-top: -5px;
  margin-right: -5px;
  top: 50%;
  right: 0;
}

.ReactCrop .ord-e:after {
  right: 0;
}

.ReactCrop .ord-se {
  cursor: se-resize;
  margin-bottom: -5px;
  margin-right: -5px;
  bottom: 0;
  right: 0;
}

.ReactCrop .ord-se:after {
  bottom: 0;
  right: 0;
}

.ReactCrop .ord-s {
  cursor: s-resize;
  margin-bottom: -5px;
  margin-left: -5px;
  bottom: 0;
  left: 50%;
}

.ReactCrop .ord-s:after {
  bottom: 0;
}

.ReactCrop .ord-sw {
  cursor: sw-resize;
  margin-bottom: -5px;
  margin-left: -5px;
  bottom: 0;
  left: 0;
}

.ReactCrop .ord-sw:after {
  bottom: 0;
  left: 0;
}

.ReactCrop .ord-w {
  cursor: w-resize;
  margin-top: -5px;
  margin-left: -5px;
  top: 50%;
  left: 0;
}

.ReactCrop .ord-w:after {
  left: 0;
}

.ReactCrop__disabled .ReactCrop__drag-handle {
  cursor: inherit;
}

.ReactCrop__drag-bar {
  position: absolute;
}

.ReactCrop__drag-bar.ord-n {
  width: 100%;
  height: 6px;
  margin-top: -3px;
  top: 0;
  left: 0;
}

.ReactCrop__drag-bar.ord-e {
  width: 6px;
  height: 100%;
  margin-right: -3px;
  top: 0;
  right: 0;
}

.ReactCrop__drag-bar.ord-s {
  width: 100%;
  height: 6px;
  margin-bottom: -3px;
  bottom: 0;
  left: 0;
}

.ReactCrop__drag-bar.ord-w {
  width: 6px;
  height: 100%;
  margin-left: -3px;
  top: 0;
  left: 0;
}

.ReactCrop--new-crop .ReactCrop__drag-bar, .ReactCrop--new-crop .ReactCrop__drag-handle, .ReactCrop--fixed-aspect .ReactCrop__drag-bar, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s, .ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w {
  display: none;
}

@media (pointer: coarse) {
  .ReactCrop .ord-n, .ReactCrop .ord-e, .ReactCrop .ord-s, .ReactCrop .ord-w {
    display: none;
  }

  .ReactCrop__drag-handle {
    width: 24px;
    height: 24px;
  }
}

/* [project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/Banner/index.scss.css [app-client] (css) */
@layer payload-default {
  .banner {
    vertical-align: middle;
    background: var(--theme-elevation-100);
    color: var(--theme-elevation-800);
    border: 0;
    border-radius: 4px;
    margin-bottom: 1.53846rem;
    padding: 10px;
    font-size: 1rem;
    line-height: 20px;
  }

  .banner--has-action {
    cursor: pointer;
    text-decoration: none;
  }

  .banner--has-icon {
    display: flex;
  }

  .banner--has-icon svg {
    display: block;
  }

  .banner--type-default.button--has-action:hover {
    background: var(--theme-elevation-900);
  }

  .banner--type-default.button--has-action:active {
    background: var(--theme-elevation-950);
  }

  .banner--type-error {
    background: var(--theme-error-100);
    color: var(--theme-error-600);
  }

  .banner--type-error svg .stroke {
    stroke: var(--theme-error-600);
    fill: none;
  }

  .banner--type-error svg .fill {
    fill: var(--theme-error-600);
  }

  .banner--type-error.button--has-action:hover {
    background: var(--theme-error-200);
  }

  .banner--type-error.button--has-action:active {
    background: var(--theme-error-300);
  }

  .banner--type-success {
    background: var(--theme-success-100);
    color: var(--theme-success-600);
  }

  .banner--type-success.button--has-action:hover, .banner--type-success.button--has-action:active {
    background: var(--theme-success-200);
  }
}

/* [project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/index.scss.css [app-client] (css) */
@layer payload-default {
  .field-diff__locale-label {
    background: var(--theme-elevation-100);
    border-radius: var(--style-radius-s);
    padding: calc(var(--base) * .2);
  }

  [dir="ltr"] .field-diff__locale-label {
    margin-right: calc(var(--base) * .25);
  }

  [dir="rtl"] .field-diff__locale-label {
    margin-left: calc(var(--base) * .25);
  }

  .field-diff-container {
    position: relative;
  }

  .field-diff-content {
    background: var(--theme-elevation-50);
    grid-template-columns: calc(50% - 10px) calc(50% - 10px);
    gap: 20px;
    padding: 10px;
    display: grid;
  }
}

/* [project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/FieldDiffLabel/index.scss.css [app-client] (css) */
@layer payload-default {
  .field-diff-label {
    margin-bottom: calc(var(--base) * .35);
    flex-direction: row;
    align-items: center;
    height: 100%;
    font-weight: 600;
    line-height: normal;
    display: flex;
  }
}

/* [project]/git/payload-test/node_modules/@payloadcms/ui/dist/elements/HTMLDiff/index.scss.css [app-client] (css) */
@layer payload-default {
  :root {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-600);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-800);
    --diff-delete-link-color: var(--theme-error-600);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-600);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-800);
    --diff-create-link-color: var(--theme-success-600);
  }

  html[data-theme="dark"] {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-650);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-900);
    --diff-delete-link-color: var(--theme-error-750);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-650);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-900);
    --diff-create-link-color: var(--theme-success-750);
  }

  .html-diff {
    letter-spacing: .02em;
    font-size: 14px;
  }

  .html-diff-no-value {
    color: var(--theme-elevation-400);
  }

  .html-diff pre {
    margin-top: 0;
    margin-bottom: 0;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff p:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h1:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h2:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h3:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h4:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h5:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff blockquote:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff pre:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff h6:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="create"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="create"]):before {
    content: "";
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="delete"]) {
    z-index: 1;
    position: relative;
  }

  .html-diff li:not([data-enable-match="false"]):has([data-match-type="delete"]):before {
    content: "";
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
  }

  .html-diff li::marker {
    color: var(--theme-text);
  }

  .html-diff [data-match-type="delete"]:not([data-enable-match="false"]):not(:is([data-enable-match="false"] *)) {
    color: var(--diff-delete-pill-color);
    -webkit-text-decoration-color: var(--diff-delete-pill-color);
    text-decoration-color: var(--diff-delete-pill-color);
    background-color: var(--diff-delete-pill-bg);
    border-radius: 4px;
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
    text-decoration-thickness: 1px;
  }

  .html-diff a[data-match-type="delete"] :not([data-enable-match="false"]) :not(:is([data-enable-match="false"] *)) {
    color: var(--diff-delete-link-color);
  }

  .html-diff a[data-match-type="create"]:not(img) :not([data-enable-match="false"]) :not(:is([data-enable-match="false"] *)) {
    color: var(--diff-create-link-color);
  }

  .html-diff [data-match-type="create"]:not(img):not([data-enable-match="false"]):not(:is([data-enable-match="false"] *)) {
    background-color: var(--diff-create-pill-bg);
    color: var(--diff-create-pill-color);
    border-radius: 4px;
  }

  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper {
    display: inline-flex;
  }

  .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    display: flex;
  }

  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper, .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    flex-direction: row;
    align-items: center;
    position: relative;
  }

  .html-diff .html-diff-create-inline-wrapper:after, .html-diff .html-diff-delete-inline-wrapper:after, .html-diff .html-diff-create-block-wrapper:after, .html-diff .html-diff-delete-block-wrapper:after {
    content: "";
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }
}

/*# sourceMappingURL=a04e6_83618580._.css.map*/