{"name": "payload-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@payloadcms/db-mongodb": "^3.59.1", "@payloadcms/next": "^3.59.1", "@payloadcms/richtext-lexical": "^3.59.1", "next": "15.5.4", "payload": "^3.59.1", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "typescript": "^5"}}